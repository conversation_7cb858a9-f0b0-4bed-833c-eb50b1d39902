﻿name: compiler-build

on: [push, pull_request]

concurrency:
  group: compiler-build-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/master' && !startsWith(github.ref, 'refs/heads/release/') }}

env:
  NUGET_CERT_REVOCATION_MODE: offline
  NUGET_PACKAGES: ${{ github.workspace }}/.nuget/packages

jobs:
  build-native:
    name: build-native-${{matrix.config.name}}
    runs-on: ${{matrix.config.os}}
    strategy:
      matrix:
        config:
          - {name: aarch64-macos, os: macos-14, buildType: Release}
          - {name: x86_64-linux, os: ubuntu-24.04, buildType: Release}
          # - {name: x86_64-windows, os: windows-latest, arch: x64, buildType: Release}

    steps:
      - uses: actions/checkout@v3
      - uses: seanmiddleditch/gha-setup-ninja@master

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'

      - name: Set up build environment (Windows, Visual Studio)
        uses: ilammy/msvc-dev-cmd@v1
        with:
          arch: ${{matrix.config.arch}}
        if: runner.os == 'Windows'

      - name: Set up build environment (Linux)
        run: |
          echo "CC=gcc-14" >> $GITHUB_ENV
          echo "CXX=g++-14" >> $GITHUB_ENV
        if: runner.os == 'Linux'

      - name: Install Conan
        shell: bash
        run: |
          pip install conan==2.6.0
          pip install cmake==3.30.3
          conan remote add sunnycase https://conan.sunnycase.moe --index 0
          conan remote update conancenter --url "https://center2.conan.io"

      - name: Configure
        shell: bash
        run: |
          conan install . --build=missing -s build_type=${{matrix.config.buildType}} -pr:a=toolchains/${{matrix.config.name}}.profile.jinja -o "&:runtime=False" -o "&:python=True" -o "&:tests=False"
          cmake --preset conan-release

      - name: Build & Install
        shell: bash
        run: |
          cmake --build build/${{matrix.config.buildType}} --config ${{matrix.config.buildType}}
          cmake --install build/${{matrix.config.buildType}} --prefix install

      - name: Upload nncase Native Build Artifact
        uses: actions/upload-artifact@v4
        with:
          name: nncase-native-${{matrix.config.name}}
          path: ${{github.workspace}}/install
          if-no-files-found: error

  build-compiler:
    needs: [build-native]
    name: build-${{matrix.config.name}}
    runs-on: ${{matrix.config.os}}
    strategy:
      matrix:
        dotnet-version: ['8.0']
        config:
          - {name: aarch64-macos, os: macos-14, shell: bash, rid: osx-arm64, buildType: Release}
          - {name: x86_64-linux, os: ubuntu-24.04, shell: bash, rid: linux-x64, buildType: Release}
          # - {name: x86_64-windows, os: windows-latest, arch: x64, shell: bash, rid: win-x64, buildType: Release}

    steps:
      - uses: actions/checkout@v2
      - uses: seanmiddleditch/gha-setup-ninja@master

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{matrix.dotnet-version}}

      - name: Cache NuGet packages
        uses: actions/cache@v3
        with:
          path: ${{ github.workspace }}/.nuget/packages
          key: ${{ runner.os }}-nuget-${{ hashFiles('**/packages.lock.json') }}
          restore-keys: |
            ${{ runner.os }}-nuget-

      - name: Install nncase native Artifact
        uses: actions/download-artifact@v4
        with:
          name: nncase-native-${{matrix.config.name}}
          path: ${{github.workspace}}/install

      - name: Build
        run: |
          dotnet restore -r ${{matrix.config.rid}}
          dotnet build -c ${{matrix.config.buildType}} --no-restore
          dotnet publish src/Nncase.Compiler -c ${{matrix.config.buildType}} --no-restore --sc false -r ${{matrix.config.rid}}
          dotnet publish src/Nncase.Studio -c ${{matrix.config.buildType}} --no-restore --sc false -r ${{matrix.config.rid}}

      - name: Set up Dotnet Test settings
        uses: 1arp/create-a-file-action@0.2
        with:
          file: 'test.runsettings'
          content: |
            <?xml version="1.0" encoding="utf-8"?>
            <!-- File name extension must be .runsettings -->
            <RunSettings>
              <RunConfiguration>
                <EnvironmentVariables>
                  <LD_LIBRARY_PATH>${{github.workspace}}/install/lib</LD_LIBRARY_PATH>
                  <DYLD_LIBRARY_PATH>${{github.workspace}}/install/lib</DYLD_LIBRARY_PATH>
                  <NNCASE_TILING_MAX_SOLUTIONS>1</NNCASE_TILING_MAX_SOLUTIONS>
                </EnvironmentVariables>
              </RunConfiguration>
            </RunSettings>

      - name: Set up test environment (Windows)
        shell: pwsh
        run: |
          echo "PATH=${env:PATH};${env:GITHUB_WORKSPACE}/install/bin" >> $env:GITHUB_ENV
        if: runner.os == 'Windows'

      - name: Dotnet Test
        working-directory: ${{github.workspace}}
        run: |
          dotnet tool install --global dotnet-coverage
          dotnet-coverage collect -s tools/dotnet_coverage.settings.xml -f cobertura -o coverage/unit.xml "dotnet test -c ${{matrix.config.buildType}} -s test.runsettings --no-build --verbosity normal --blame"
          dotnet-coverage merge -o coverage.unit.xml -f cobertura -r coverage/*.xml

      - name: Upload Coverage
        uses: actions/upload-artifact@v4
        if: matrix.config.name == 'x86_64-linux'
        with:
          name: nncase-coverage-unit
          path: coverage.unit.xml
          if-no-files-found: error

      - name: Upload nncase Build Artifact
        uses: actions/upload-artifact@v4
        with:
          name: nncase-${{matrix.config.name}}
          path: ${{github.workspace}}/src/Nncase.Compiler/bin/${{matrix.config.buildType}}/net${{matrix.dotnet-version}}/${{matrix.config.rid}}/publish
          if-no-files-found: error

  test-compiler:
    needs: [build-compiler]
    name: test-${{matrix.config.name}}
    runs-on: ${{matrix.config.os}}
    strategy:
      matrix:
        dotnet-version: ['8.0']
        config:
          - {name: aarch64-macos, os: macos-14}
          - {name: x86_64-linux, os: ubuntu-24.04}
          #- {name: x86_64-windows, os: windows-latest, arch: x64}

    env:
      VULKANSDK_VER: 1.3.280.0

    steps:
      - uses: actions/checkout@v3
      - uses: seanmiddleditch/gha-setup-ninja@master

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{matrix.dotnet-version}}

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
          cache: 'pip'
          cache-dependency-path: '**/requirements.test.txt'

      - name: Install nncase native Artifact
        uses: actions/download-artifact@v4
        with:
          name: nncase-native-${{matrix.config.name}}
          path: ${{github.workspace}}/install

      - name: Install nncase
        uses: actions/download-artifact@v4
        with:
          name: nncase-${{matrix.config.name}}
          path: ${{github.workspace}}/install

      - name: Set up test environment (macOS)
        run: |
          cp install/lib/*.dylib install/
          echo "PYTHONPATH=$GITHUB_WORKSPACE/install/lib:$GITHUB_WORKSPACE/install/python:$GITHUB_WORKSPACE/tests" >> $GITHUB_ENV
        if: runner.os == 'macOS'

      - name: Set up test environment (Linux)
        run: |
          cp install/lib/*.so install/
          echo "PYTHONPATH=$GITHUB_WORKSPACE/install/lib:$GITHUB_WORKSPACE/install/python:$GITHUB_WORKSPACE/tests" >> $GITHUB_ENV
        if: runner.os == 'Linux'

      - name: Set up test environment (Windows)
        shell: pwsh
        run: |
          Copy-Item install/lib/*.pyd install/bin/
          echo "PYTHONPATH=${env:GITHUB_WORKSPACE}/install/bin;${env:GITHUB_WORKSPACE}/install/python;${env:GITHUB_WORKSPACE}/tests" >> $env:GITHUB_ENV
          echo "PATH=${env:PATH};${env:GITHUB_WORKSPACE}/install/bin" >> $env:GITHUB_ENV
        if: runner.os == 'Windows'

      - name: Install Python Packages
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.test.txt

      - name: Create Test Environment
        run: mkdir test_results

      - name: Test
        working-directory: ${{github.workspace}}
        shell: bash
        env:
          NNCASE_COMPILER: ${{github.workspace}}/install/Nncase.Compiler.dll
          NNCASE_TILING_MAX_SOLUTIONS: 1
        run: |
          dotnet tool install --global dotnet-coverage
          dotnet-coverage collect -s tools/dotnet_coverage.settings.xml -f cobertura -o coverage/onnx_basic.xml pytest tests/importer/onnx_/basic/ --doctest-modules --junitxml=test_results/onnx_basic.xml
          dotnet-coverage collect -s tools/dotnet_coverage.settings.xml -f cobertura -o coverage/onnx_combine.xml pytest tests/importer/onnx_/combine/ --doctest-modules --junitxml=test_results/onnx_combine.xml
          dotnet-coverage collect -s tools/dotnet_coverage.settings.xml -f cobertura -o coverage/tflite_basic.xml pytest tests/importer/tflite_/basic/ --doctest-modules --junitxml=test_results/tflite_basic.xml
          #dotnet-coverage collect -s tools/dotnet_coverage.settings.xml -f cobertura -o coverage/tflite_combine.xml pytest tests/importer/tflite_/combine/ --doctest-modules --junitxml=test_results/tflite_combine.xml
          #dotnet-coverage collect -s tools/dotnet_coverage.settings.xml -f cobertura -o coverage/tflite_model.xml pytest tests/importer/tflite_/model/ --doctest-modules --junitxml=test_results/tflite_model.xml
          dotnet-coverage collect -s tools/dotnet_coverage.settings.xml -f cobertura -o coverage/ncnn_basic.xml pytest tests/importer/ncnn_/basic/ --doctest-modules --junitxml=test_results/ncnn_basic.xml
          dotnet-coverage merge -o coverage.integration.xml -f cobertura -r coverage/*.xml

      - name: Test LLM
        working-directory: ${{github.workspace}}
        shell: bash
        if: matrix.config.name == 'x86_64-linux'
        env:
          NNCASE_COMPILER: ${{github.workspace}}/install/Nncase.Compiler.dll
          NNCASE_TILING_MAX_SOLUTIONS: 1
        run: |
          dotnet-coverage collect -s tools/dotnet_coverage.settings.xml -f cobertura -o coverage/llm_ffi.xml pytest tests/other/ --doctest-modules --junitxml=test_results/llm_ffi.xml
          dotnet-coverage collect -s tools/dotnet_coverage.settings.xml -f cobertura -o coverage/llm_huggingface.xml pytest tests/importer/huggingface_/ --doctest-modules --junitxml=test_results/llm_huggingface.xml

      - name: Upload Coverage
        uses: actions/upload-artifact@v4
        if: matrix.config.name == 'x86_64-linux'
        with:
          name: nncase-coverage-integration
          path: coverage.integration.xml
          if-no-files-found: error

      - name: Publish Test Results
        uses: EnricoMi/publish-unit-test-result-action@v2
        if: always() && matrix.config.name == 'x86_64-linux'
        with:
          files: test_results/*.xml

  upload-coverage:
    needs: [test-compiler]
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v3
      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: "8.0"

      - name: Download Unit Test Coverage
        uses: actions/download-artifact@v4
        with:
          name: nncase-coverage-unit
          path: ${{github.workspace}}/coverage

      - name: Download Integration Test Coverage
        uses: actions/download-artifact@v4
        with:
          name: nncase-coverage-integration
          path: ${{github.workspace}}/coverage

      - name: Upload Codecov (Unit)
        uses: codecov/codecov-action@v3
        with:
          files: coverage/coverage.unit.xml
          flags: unit

      - name: Upload Codecov (Integration)
        uses: codecov/codecov-action@v3
        with:
          files: coverage/coverage.integration.xml
          flags: integration

      - name: Generate Coverage Report
        run: |
          dotnet tool install --global dotnet-coverage
          dotnet tool install -g dotnet-reportgenerator-globaltool
          dotnet-coverage merge -o coverage.xml -f cobertura -r coverage/*.xml
          reportgenerator -reports:coverage.xml -targetdir:"coveragereport" -reporttypes:Html

      - name: Upload Coverage Report
        uses: actions/upload-artifact@v4
        with:
          name: nncase-coverage-report
          path: coveragereport
          if-no-files-found: error
