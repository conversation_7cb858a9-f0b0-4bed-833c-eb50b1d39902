name: runtime-build

on: [ push, pull_request ]

concurrency:
  group: runtime-build-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/master' && !startsWith(github.ref, 'refs/heads/release/') }}

jobs:
  build-native-runtime:
    name: build-runtime-${{matrix.config.name}}
    runs-on: ${{matrix.config.os}}
    strategy:
      matrix:
        config:
          - { name: aarch64-macos, os: macos-14, buildType: Release }
          - { name: x86_64-linux, os: ubuntu-24.04, buildType: Release }
          # - { name: x86_64-windows, os: windows-latest, arch: x64, buildType: Release }

    steps:
      - uses: actions/checkout@v3
      - uses: seanmiddleditch/gha-setup-ninja@master

      - name: Set up build environment (Windows, Visual Studio)
        uses: ilammy/msvc-dev-cmd@v1
        with:
          arch: ${{matrix.config.arch}}
        if: runner.os == 'Windows'

      - name: Set up build environment (Linux)
        run: |
          echo "CC=gcc-14" >> $GITHUB_ENV
          echo "CXX=g++-14" >> $GITHUB_ENV
        if: runner.os == 'Linux'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Install Conan
        run: |
          pip install conan==2.6.0
          pip install cmake==3.30.3
          conan remote add sunnycase https://conan.sunnycase.moe --index 0
          conan remote update conancenter --url "https://center2.conan.io"

      - name: Configure
        shell: bash
        run: |
          conan install . --build=missing -s build_type=${{matrix.config.buildType}} -pr:a=toolchains/${{matrix.config.name}}.profile.jinja -o "&:runtime=True" -o "&:python=True" -o "&:tests=True"
          cmake --preset conan-runtime-release

      - name: Build & Install
        run: |
          cmake --build build/${{matrix.config.buildType}} --config ${{matrix.config.buildType}}
          cmake --install build/${{matrix.config.buildType}} --prefix install

      - name: Test
        run: |
          cd build/${{matrix.config.buildType}}
          ctest -C ${{matrix.config.buildType}} --test-dir ntt/test/ctest --output-on-failure -j4
        if: runner.os != 'Windows'

      #- name: Benchmark
      #  run: |
      #    ${{github.workspace}}/install/bin/benchnncase > benchnncase.log
      #    cat benchnncase.log

      - name: Upload nncaseruntime Build Artifact
        uses: actions/upload-artifact@v4
        with:
          name: nncaseruntime-${{matrix.config.name}}
          path: ${{github.workspace}}/install
          if-no-files-found: error

    #- name: Upload nncaseruntime Benchmark
    #  uses: actions/upload-artifact@v4
    #  with:
    #    name: nncaseruntime-benchmark-${{matrix.config.name}}
    #    path: ${{github.workspace}}/benchnncase.log
    #    if-no-files-found: error

  build-cross-linux-runtime:
    name: build-runtime-${{matrix.config.name}}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        config:
          - { name: riscv64-k230-linux, profile: riscv64-linux, toolchainPrefix: riscv64-unknown-linux-gnu-, toolchainFile: riscv64-unknown-linux_gnu_14.2.0, qemu: qemu-riscv64, loaderArgs: '-cpu;rv64,v=true,Zfh=true,vlen=128,elen=64,vext_spec=v1.0;-L', conanInstallArgs: '-o "&:k230_runtime=True"', buildType: Release }

    steps:
      - uses: actions/checkout@v3
      - uses: seanmiddleditch/gha-setup-ninja@master

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Install toolchain and QEMU
        shell: bash
        run: |
          wget https://dav.sunnycase.moe/d/ci/nncase/${{matrix.config.toolchainFile}}.tar.zst -O toolchain.tar.zst
          sudo apt install zstd
          sudo tar -I zstd -xf toolchain.tar.zst -C $GITHUB_WORKSPACE

          wget https://dav.sunnycase.moe/d/ci/nncase/${{matrix.config.qemu}}.tgz -O qemu.tgz
          sudo tar xf qemu.tgz -C /usr/local/bin
          sudo chmod a+x /usr/local/bin/${{matrix.config.qemu}}
          echo "TESTS_EXECUTABLE_LOADER=${{matrix.config.qemu}}" >> $GITHUB_ENV
          echo "TESTS_EXECUTABLE_LOADER_ARGUMENTS=${{matrix.config.loaderArgs}};$GITHUB_WORKSPACE/${{matrix.config.toolchainFile}}/sysroot" >> $GITHUB_ENV
          echo "CC=$GITHUB_WORKSPACE/${{matrix.config.toolchainFile}}/bin/${{matrix.config.toolchainPrefix}}gcc" >> $GITHUB_ENV
          echo "CXX=$GITHUB_WORKSPACE/${{matrix.config.toolchainFile}}/bin/${{matrix.config.toolchainPrefix}}g++" >> $GITHUB_ENV

      - name: Install Conan
        run: |
          pip install conan==2.6.0
          pip install cmake==3.30.3
          conan remote add sunnycase https://conan.sunnycase.moe --index 0
          conan remote update conancenter --url "https://center2.conan.io"

      - name: Configure
        shell: bash
        run: |
          conan install . --build=missing -s build_type=${{matrix.config.buildType}} -pr:h=toolchains/${{matrix.config.profile}}.profile.jinja -pr:b=toolchains/x86_64-linux.profile.jinja -o "&:runtime=True" -o "&:python=True" -o "&:tests=True" ${{matrix.config.conanInstallArgs}}
          cmake --preset conan-runtime-release

      - name: Build & Install
        run: |
          cmake --build build/${{matrix.config.buildType}} --config ${{matrix.config.buildType}}
          cmake --install build/${{matrix.config.buildType}} --prefix install

      - name: Test
        shell: bash
        run: |
          cd build/${{matrix.config.buildType}}
          ctest -C ${{matrix.config.buildType}} --test-dir ntt/test/ctest --output-on-failure -j4

      - name: Upload nncaseruntime Build Artifact
        uses: actions/upload-artifact@v4
        with:
          name: nncaseruntime-${{matrix.config.name}}
          path: ${{github.workspace}}/install
          if-no-files-found: error