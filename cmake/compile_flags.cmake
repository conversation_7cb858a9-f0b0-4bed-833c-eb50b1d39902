if (MSVC)
    add_definitions(/D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS /D_CRT_SECURE_NO_WARNINGS /DNOMINMAX)
    add_compile_options(/wd4267 /wd4251 /wd4244 /FC /utf-8 /W3 /WX -Wno-unused-function -Wno-unused-command-line-argument -Wno-braced-scalar-init -Wno-unused-local-typedefs)
    set(PYBIND11_CPP_STANDARD "/std:c++latest")
else()
    add_compile_options(-fvisibility=hidden)
    add_compile_options(-Wall -Wextra -pedantic -Werror -Wno-multichar -Wno-missing-field-initializers -Wno-unused-function -Wno-type-limits -Wno-unused-local-typedefs -Wno-sign-compare)
    if (APPLE)
        add_compile_options(-Wno-four-char-constants -Wno-sometimes-uninitialized -Wno-deprecated -Wno-braced-scalar-init)
    elseif (CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
        add_compile_options(-Wno-uninitialized -Wno-gnu-zero-variadic-macro-arguments -Wno-braced-scalar-init)
    else ()
        add_compile_options(-Wno-maybe-uninitialized -Wno-deprecated-copy)
        add_link_options(-Wl,--exclude-libs,ALL)
    endif()
endif()

if(${CMAKE_SYSTEM_PROCESSOR} MATCHES
   "(x86)|(X86)|(amd64)|(AMD64)|(x86_64)|(X86_64)")
    if (MSVC)
        add_compile_options(/arch:AVX2)
        add_compile_definitions(__SSE2__ __SSE4_1__ __FMA__ __AVX__ __AVX2__)
    else()
        add_compile_options(-mavx2 -mfma)
    endif()
endif()
