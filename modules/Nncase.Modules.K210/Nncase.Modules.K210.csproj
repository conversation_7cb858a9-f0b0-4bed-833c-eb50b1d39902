<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <RootNamespace>Nncase</RootNamespace>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Nncase.CodeGen\Nncase.CodeGen.csproj" />
    <ProjectReference Include="..\..\src\Nncase.EGraph\Nncase.EGraph.csproj" />
    <ProjectReference Include="..\..\src\Nncase.Graph\Nncase.Graph.csproj" />
    <ProjectReference Include="..\..\src\Nncase.Quantization\Nncase.Quantization.csproj" />
    <ProjectReference Include="..\Nncase.Modules.NTT\Nncase.Modules.NTT.csproj" />
  </ItemGroup>

  <ItemGroup>
      <ProjectReference Include="../../tools/Nncase.SourceGenerator/Nncase.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
  </ItemGroup>

</Project>
