﻿# This file is generated by Nncase CPU module builder.

cmake_minimum_required(VERSION 3.15)

project(nncase_cpu_module)

option(BUILD_SHARED "Build shared library in linux" OFF)
option(BUILD_STANDALONE "Build standalone executable" OFF)
if(BUILD_STANDALONE)
  add_compile_definitions(-DNNCASE_STANDALONE=1)
endif()

include(@Html.Raw(Model.CMakePath))

target_sources(nncase_ntt_module PRIVATE thread_main.cpp)
target_include_directories(nncase_ntt_module PRIVATE ${CMAKE_CURRENT_LIST_DIR})
