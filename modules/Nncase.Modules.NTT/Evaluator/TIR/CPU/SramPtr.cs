﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.TIR.NTT;

namespace Nncase.Evaluator.TIR.NTT;

public sealed class SramPtrEvaluator : ITypeInferencer<SramPtr>
{
    public IRType Visit(ITypeInferenceContext context, SramPtr target) => new PointerType(target.DataType);
}
