﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Linq;
using System.Numerics;
using System.Runtime.InteropServices;
using Nncase.CostModel;
using Nncase.IR;
using Nncase.TIR.NTT;
using Nncase.Utilities;
using OrtKISharp;

namespace Nncase.Evaluator.TIR.NTT;

public sealed class InstanceNormEvaluator : ITypeInferencer<InstanceNorm>
{
    /// <inheritdoc/>
    public IRType Visit(ITypeInferenceContext context, InstanceNorm target) => TupleType.Void;
}
