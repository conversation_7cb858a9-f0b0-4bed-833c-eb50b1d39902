﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.NTT;

namespace Nncase.Evaluator.IR.NTT;

public sealed class Im2colEvaluator : ITypeInferencer<Im2col>, ICostEvaluator<Im2col>, IEvaluator<Im2col>
{
    public IRType Visit(ITypeInferenceContext context, Im2col target)
    {
        var inputType = context.GetArgumentType(target, Im2col.Input);
        return inputType switch
        {
            DistributedType dt => Visit(dt, target),
            TensorType tt => Visit(tt, target),
            _ => inputType,
        };
    }

    public Cost Visit(ICostEvaluateContext context, Im2col target) => new Cost()
    {
        [CostFactorNames.MemoryLoad] = CostUtility.GetMemoryAccess(context.GetArgumentType<IRType>(target, Im2col.Input)),
        [CostFactorNames.MemoryStore] = CostUtility.GetMemoryAccess(context.GetArgumentType<IRType>(target, Im2col.Input)),
    };

    public IValue Visit(IEvaluateContext context, Im2col target)
    {
        var inputTensor = context.GetArgumentValueAsTensor(target, Im2col.Input);
        var lanes = inputTensor.ElementType.SizeInBytes / 4;
        int batch = (int)inputTensor.Shape[0].FixedValue;
        int inChannel = (int)inputTensor.Shape[1].FixedValue;
        int height = (int)inputTensor.Shape[2].FixedValue;
        int width = (int)inputTensor.Shape[3].FixedValue;
        int pad_h_before = target.Padding[0];
        int pad_h_after = target.Padding[1];
        int pad_w_before = target.Padding[2];
        int pad_w_after = target.Padding[3];
        long kernel_h = target.Kernel[0];
        long kernel_w = target.Kernel[1];
        int stride_h = target.Stride[0];
        int stride_w = target.Stride[1];
        long output_h = ((height + pad_h_before + pad_h_after -
                ((1 * (kernel_h - 1)) + 1)) / stride_h) + 1;
        long output_w = ((width + pad_w_before + pad_w_after -
         ((1 * (kernel_w - 1)) + 1)) / stride_w) + 1;
        var outputTensor = new float[inChannel * kernel_h * kernel_w * batch * output_h * output_w * lanes];

        var inputSpan = System.Runtime.InteropServices.MemoryMarshal.Cast<byte, float>(inputTensor.BytesBuffer);
        var outputSpan = new Span<float>(outputTensor);
        var data_col = 0;
        for (int ic = 0; ic < inChannel; ic++)
        {
            for (int kh = 0; kh < kernel_h; kh++)
            {
                for (int kw = 0; kw < kernel_w; kw++)
                {
                    for (int b = 0; b < batch; b++)
                    {
                        var data_im = inputSpan.Slice((b * inChannel * height * width * lanes) + (ic * height * width * lanes));
                        int ih = -pad_h_before + kh;
                        for (int oh = 0; oh < output_h; oh++)
                        {
                            int iw = -pad_w_before + kw;
                            for (int ow = 0; ow < output_w; ow++)
                            {
                                if (iw >= 0 && iw < width && ih >= 0 && ih < height)
                                {
                                    for (int i = 0; i < lanes; i++)
                                    {
                                        outputSpan[data_col++] = data_im[(ih * width * lanes) + (iw * lanes) + i];
                                    }
                                }
                                else
                                {
                                    for (int i = 0; i < lanes; i++)
                                    {
                                        outputSpan[data_col++] = 0;
                                    }
                                }

                                iw += stride_w;
                            }

                            ih += stride_h;
                        }
                    }
                }
            }
        }

        return Value.FromTensor(Tensor.FromBytes(inputTensor.ElementType, System.Runtime.InteropServices.MemoryMarshal.Cast<float, byte>(outputTensor).ToArray(), [inChannel * kernel_h * kernel_w, batch * output_h * output_w]));
    }

    private IRType Visit(DistributedType dt, Im2col target)
    {
        if (Visit(dt.TensorType, target) is not TensorType tensorType)
        {
            return new InvalidType("im2col typeinfer failed");
        }

        var outShape = tensorType.Shape.ToArray();
        var ndsbp = new SBP[tensorType.Shape.Rank];

        for (int i = 0; i < dt.AxisPolicies.Count; i++)
        {
            var sbp = dt.AxisPolicies[i];
            if (i == 0)
            {
                switch (sbp)
                {
                    case SBPSplit split:
                        outShape[1] /= split.Axes.Select(a => dt.Placement.Hierarchy[a]).Aggregate(1, (a, b) => a * b);
                        ndsbp[1] = split;
                        break;
                    case SBPPartial:
                        return new InvalidType($"can't be partial sum");
                    default:
                        ndsbp[1] = sbp;
                        break;
                }
            }
            else if (i == 1)
            {
                switch (sbp)
                {
                    case SBPSplit split:
                        outShape[0] /= split.Axes.Select(a => dt.Placement.Hierarchy[a]).Aggregate(1, (a, b) => a * b);
                        ndsbp[0] = split;
                        break;
                    case SBPPartial:
                        return new InvalidType($"can't be partial sum");
                    default:
                        ndsbp[0] = sbp;
                        break;
                }
            }
            else
            {
                switch (sbp)
                {
                    case SBPSplit split:
                        return new InvalidType($"can't split on {i}");
                    case SBPPartial:
                        return new InvalidType($"can't be partial sum");
                    default:
                        break;
                }
            }
        }

        return new DistributedType(tensorType, ndsbp, dt.Placement);
    }

    private IRType Visit(TensorType tt, Im2col target)
    {
        int height = (int)tt.Shape[2].FixedValue;
        int width = (int)tt.Shape[3].FixedValue;
        int pad_h_before = target.Padding[0];
        int pad_h_after = target.Padding[1];
        int pad_w_before = target.Padding[2];
        int pad_w_after = target.Padding[3];
        long kernel_h = target.Kernel[0];
        long kernel_w = target.Kernel[1];
        int stride_h = target.Stride[0];
        int stride_w = target.Stride[1];
        long output_h = ((height + pad_h_before + pad_h_after -
                ((1 * (kernel_h - 1)) + 1)) / stride_h) + 1;
        long output_w = ((width + pad_w_before + pad_w_after -
         ((1 * (kernel_w - 1)) + 1)) / stride_w) + 1;
        return tt with { Shape = new Dimension[] { tt.Shape[1] * kernel_h * kernel_w, tt.Shape[0] * output_h * output_w } };
    }
}
