cmake_minimum_required (VERSION 3.13)

add_subdirectory(kernel)

set(SRCS runtime_module.cpp
         runtime_function.cpp
         elfload.cpp
         elfloader.cpp
         elfreloc_aarch64.cpp
         elfreloc_amd64.cpp
         elfreloc_i386.cpp
         elfreloc_riscv64.cpp
         hardware_context.cpp
         shared_memory.cpp
         device_allocator.cpp
         device_buffer.cpp
         device_runtime_tensor.cpp
         )

if (BUILDING_RUNTIME)
    if (ENABLE_XPU_RUNTIME)
        if (DUCA_PATH)
            add_definitions(-DSYS_MODE)
            set(INCLUDE_DIR ${DUCA_PATH}/include/)
            include_directories(${INCLUDE_DIR})
        endif()
        add_library(runtime_xpu OBJECT ${SRCS})
        target_link_libraries(runtime_xpu PUBLIC nncaseruntime pthread rt)
        if (DUCA_PATH)
            target_link_libraries(runtime_xpu PUBLIC ${DUCA_PATH}/lib/libduca.so)
        else()
            target_link_libraries(runtime_xpu PUBLIC -ldl)
        endif()
        target_compile_definitions(runtime_xpu PUBLIC -DNNCASE_MODULES_XPU_DLL -DNNCASE_XPU_MODULE=1)
        # set_target_properties(runtime_xpu PROPERTIES POSITION_INDEPENDENT_CODE ON)
        install(TARGETS runtime_xpu EXPORT nncaseruntimeTargets)
    endif()
else()
    add_library(simulator_xpu OBJECT ${SRCS})
    target_link_libraries(simulator_xpu PUBLIC nncasebase nncaseruntime pthread)
    target_compile_definitions(simulator_xpu PUBLIC -DNNCASE_MODULES_XPU_DLL -DNNCASE_SIMULATOR -DNNCASE_XPU_MODULE=1)
    set_target_properties(simulator_xpu PROPERTIES POSITION_INDEPENDENT_CODE ON)
endif()
