// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.CommandLine;
using System.CommandLine.Invocation;
using System.Diagnostics;
using System.Runtime.InteropServices;
using Microsoft.Extensions.Configuration;
using Nncase.CodeGen;
using Nncase.Diagnostics;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.K230.F;
using Nncase.Mutators.K230;
using Nncase.Passes;
using Nncase.Passes.Analysis;
using Nncase.Passes.Rules.K230;
using Nncase.Passes.Rules.Lower;
using Nncase.Passes.Rules.Neutral;
using Nncase.Passes.Rules.Tile;
using Nncase.Quantization;
using Nncase.Runtime.K230;
using Nncase.Utilities;
using static Nncase.PatternMatch.Utility;
using static Nncase.Quantization.Utility;
using FoldConstCall = Nncase.Passes.Rules.Neutral.FoldConstCall;
using ModuleBuilder = Nncase.CodeGen.K230.K230ModuleBuilder;

namespace Nncase.Targets;

/// <summary>
/// Target for K230.
/// </summary>
public class K230Target : Target
{
    public const string Kind = "k230";

    private readonly K230ModuleCompiler _k230ModuleCompiler = new();

    public K230Target()
    {
        ModuleCompilers = [_k230ModuleCompiler];
    }

    /// <inheritdoc cref="ModuleType" />
    public static ModuleType ModuleType => ModuleType.Create(Kind);

    public override string Name => Kind;

    public override IReadOnlyList<IModuleCompiler> ModuleCompilers { get; }

    public override (System.CommandLine.Command Command, Func<InvocationContext, System.CommandLine.Command, ITargetOptions> Parser) RegisterCommandAndParser()
    {
        var cmd = new NTTTargetOptionsCommand(Kind);

        ITargetOptions ParseTargetCompileOptions(InvocationContext context, Command command)
        {
            var binder = new NTTTargetOptionsBinder(cmd);
            return binder.GetBoundValue(context);
        }

        return (cmd, ParseTargetCompileOptions);
    }

    /// <inheritdoc/>
    public override void ParseTargetDependentOptions(IConfigurationSection configure)
    {
    }

    /// <inheritdoc/>
    public override void RegisterTargetInDependentPass(IPassManager passManager, CompileOptions options)
    {
        passManager.AddWithName<DataflowPass>("TargetDependentNeutralOptimize").Configure(p =>
        {
            p.Add<BroadcastTransposeOutputNames>();
            p.Add<BroadcastReshapeOutputNames>();
            p.Add<BroadcastNopPadOutputNames>();
            p.Add<IntegralPromotion>();
            p.Add<FoldConstCall>();
            p.Add<FoldShapeOf>();
            p.Add<TransposeToReshape>();
            p.Add<ExpandToBroadcast>();
            p.Add<MatMulToConv2DWithMarker>();
            p.Add<BroadcastMatMulToConv2DWithMarker>();
            p.Add<MatMulToConv2D>();
            p.Add<BroadcastMatMulToConv2D>();
            p.Add<BroadcastMatMul>();
            p.Add<ReshapeBatchMatmul>();
            p.Add<SplitBatchMatMul>();
            p.Add<BatchNormToBinary>();
            p.Add<FoldTwoReshapes>();
            p.Add<FoldNopReshape>();
            p.Add<FoldTwoReduce>();
            p.Add<CombineBinaryReshape>();
            p.Add<CombineConstBinaryReshape>();
            p.Add<CombineUnaryReshape>();
            p.Add<CombineActivationsReshape>();
            p.Add<FoldNopBroadcast>();
            p.Add<SplitLargeGlobalReduceWindow2D>();
        });
    }

    public override void RegisterPostQuantizePass(IPassManager passManager, CompileOptions options)
    {
    }

    public override void RegisterAffineSelectionPass(IPassManager passManager, CompileOptions options)
    {
    }

    public override void RegisterAutoPackingRules(IRulesAddable pass, CompileOptions options)
    {
    }

    public override void RegisterAutoVectorizeRules(IRulesAddable pass, CompileOptions options)
    {
        // todo config it in the target options.
        var rank = 1;
        var lane = _k230ModuleCompiler.Lane;
        var maskVectorStyle = _k230ModuleCompiler.MaskVectorStyle;

        pass.Add<Passes.Rules.NTT.VectorizeConv2D>(rank, lane);
        pass.Add<Passes.Rules.NTT.VectorizeMatMul>(rank, lane);
        pass.Add<Passes.Rules.NTT.VectorizeLayerNorm>(rank, lane);

        pass.Add<Passes.Rules.NTT.VectorizeBinaryPropagation>();
        pass.Add<Passes.Rules.NTT.VectorizeComparePropagation>(maskVectorStyle);
        pass.Add<Passes.Rules.NTT.VectorizeConcatPropagation>();
        pass.Add<Passes.Rules.NTT.VectorizeExpandPropagation>();
        pass.Add<Passes.Rules.NTT.VectorizeGatherPropagation>();
        pass.Add<Passes.Rules.NTT.VectorizePadPropagation>();
        pass.Add<Passes.Rules.NTT.VectorizeReducePropagation>();
        pass.Add<Passes.Rules.NTT.VectorizeReshapePropagation>();
        pass.Add<Passes.Rules.NTT.VectorizeResizeImagePropagation>();

        // pass.Add<Passes.Rules.NTT.VectorizeScatterND>(rank, lane);
        pass.Add<Passes.Rules.NTT.VectorizeSlicePropagation>();

        // pass.Add<Passes.Rules.NTT.VectorizeSwish>(rank, lane);
        pass.Add<Passes.Rules.NTT.VectorizeTransposePropagation>();
        pass.Add<Passes.Rules.NTT.VectorizeUnaryPropagation>();
        pass.Add<Passes.Rules.NTT.VectorizeUnsqueezePropagation>();
        pass.Add<Passes.Rules.NTT.VectorizeWherePropagation>(maskVectorStyle);

        pass.Add<Passes.Rules.NTT.ConcatDevectorizePropagation>();
        pass.Add<Passes.Rules.NTT.BinaryDevectorizeLhsPropagation>();
        pass.Add<Passes.Rules.NTT.BinaryDevectorizeRhsPropagation>();
        pass.Add<Passes.Rules.NTT.VectorizedMatMulDevectorizePropagation>();
        pass.Add<Passes.Rules.NTT.ReshapeDevectorizePropagation>();
        pass.Add<Passes.Rules.NTT.SliceDevectorizePropagation>();
        pass.Add<Passes.Rules.NTT.SwishDevectorizePropagation>();
        pass.Add<Passes.Rules.NTT.TransposeDevectorizePropagation>();
        pass.Add<Passes.Rules.NTT.UnaryDevectorizePropagation>();

        pass.Add<Passes.Rules.Neutral.FoldConstCall>();
        pass.Add<Passes.Rules.NTT.FoldVectorizeDevectorize>();
        pass.Add<Passes.Rules.NTT.FoldVectorizeConcatDevectorize>();
        pass.Add<Passes.Rules.NTT.TransposeVectorizeMatMulInputs>();
        pass.Add<Passes.Rules.Neutral.FoldTwoReshapes>();
        pass.Add<Passes.Rules.Neutral.FoldTwoTransposes>();
    }

    public override void RegisterPostAutoVectorizePass(IPassManager passManager, CompileOptions options)
    {
    }

    public override void RegisterTIRSelectionPass(IPassManager passManager, CompileOptions options)
    {
        passManager.Add<NTTTIRSelectionPass>(options, K230Target.Kind);
    }

#if false
    private void AddActTransfrom(LowerPass pass)
    {
        pass.Add<FoldNopClamp>();
    }

    private void AddMFUTransposeTransfrom(LowerPass pass)
    {
        pass.Add<ToGNNETranspose>();
    }
#endif

    /// <inheritdoc/>
    public override void RegisterTargetDependentPass(IPassManager passManager, CompileOptions options)
    {
        passManager.Add<K230ModuleConvertPass>(K230Target.Kind);
        passManager.AddWithName<DataflowPass>("pre_fake_opt").Configure(p =>
        {
            p.Add<LeakyReluTransposeSwap>();
            p.Add<LeakyReluReshape>();
            p.Add<TransposeCastMotion>();
            p.Add<ExpandShapeOfGlobalReduceWindow>();
            p.Add<ReduceToGlobalReduceWindow>();
            p.Add<ReduceWithDim1ToGlobalReduceWindow>();
            p.Add<ReduceWithDim12ToGlobalReduceWindow>();
            p.Add<FoldConstCall>();
            p.Add<FoldNopTranspose>();
            p.Add<FoldTwoTransposes>();
            p.Add<CombineTransposeUnary>();
            p.Add<CombineTransposePad>();
            p.Add<CombineBinaryTranspose>();
            p.Add<CombineTransposeConstBinary>();
            p.Add<CombineTransposeReduce>();
            p.Add<CombineTransposeActivations>();
            p.Add<CombinePadTranspose>();
            p.Add<FoldNopPad>();
            p.Add<FoldConv2DPads>();
            p.Add<FoldReduceWindow2DPads>();
            p.Add<SplitLargeConv2D>();
            p.Add<SplitLargeConv2DTranspose>();
            p.Add<FoldReluConcatWithConv2d>();
        });

        if (options.QuantizeOptions.ModelQuantMode == ModelQuantMode.UsePTQ)
        {
            passManager.AddWithName<DataflowPass>("to_fake_preprocess_1").Configure(p =>
            {
                p.Add<ReplaceMarker>();
                p.Add<UniteConcatRange>();
            });

            passManager.AddWithName<DataflowPass>("to_fake_preprocess_2").Configure(p =>
            {
                p.AddAnalysis<IExprUserAnalysisResult>();
                p.Add<BroadcastConcatRange>();
            });

            passManager.AddWithName<EGraphRulesPass>("to_fake").Configure(p =>
            {
                p.Add<ToFakeLSTM>();
                p.Add<ToFakeConv2D>();
                p.Add<ToFakeConv2DTranspose>();
                p.Add<ToFakePdpReduce>();
                p.Add<ToFakeMatmul>();
                p.Add<PReluToFakeActivation>();
                p.Add<GeluToFakeActivation>();
                p.Add<MulToFakeActivation>();
                p.Add<DivToFakeActivation>();
                p.Add<AddToFakeActivation>();
                p.Add<SubToFakeActivation>();
                p.Add<MaxToFakeActivation>();
                p.Add<MinToFakeActivation>();
                p.Add<NegToFakeActivation>();
                p.Add<AbsToFakeActivation>();
                p.Add<GeneralAddSubMulDivToFakeActivation>();
                p.Add<LeakyReluToFakeActivation>();
                p.Add<ReluToFakeActivation>();
                p.Add<BatchNormToFakeActivation>();
                p.Add<ResizeToFakeActivation>();
                p.Add<BroadcastToFakeActivation>();
                p.Add<ACosToFakeActivation>();
                p.Add<ASinToFakeActivation>();
                p.Add<ClampToFakeActivation>();
                p.Add<EluToFakeActivation>();
                p.Add<HardSigmoidToFakeActivation>();
                p.Add<HardSwishToFakeActivation>();
                p.Add<SigmoidToFakeActivation>();
                p.Add<SwishToFakeActivation>();
                p.Add<TanhToFakeActivation>();
                p.Add<ResizeToFakeActivation>();
                p.Add<TileToFakeActivation>();
                p.Add<ToFakeAi2dResize>();
                p.Add<FoldNopReshape>();
            });

            // AdaRoundWeights
            passManager.AddWithName<EGraphPassWithAdaRound>("AdaRoundWeights");

            // BindQuantizeConfig
            passManager.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

            passManager.AddWithName<EGraphRulesPass>("fake_opt").Configure(p =>
            {
                p.Add<FoldTwoFakeActivation>();
                p.Add<FoldFakeConv2DAndFakeActivation>();
                p.Add<FoldFakeMatMulAndFakeActivation>();
                p.Add<FoldFakeConv2DTransposeAndFakeActivation>();
                p.Add<FoldMaxAndFakeActivation>();
            });

            if ((options.QuantizeOptions.QuantScheme != string.Empty && options.QuantizeOptions.QuantSchemeStrictMode == false) || (options.QuantizeOptions.QuantScheme == string.Empty && options.QuantizeOptions.ExportQuantScheme == false) || (options.QuantizeOptions.ExportQuantScheme == true && options.QuantizeOptions.ExportWeightRangeByChannel == true))
            {
                passManager.AddWithName<DataflowPass>("fake_range_by_channel").Configure(p =>
                {
                    p.Add<ReplaceFakeConv2DWeightsRangeToByChannel>();
                    p.Add<ReplaceFakeConv2DTransposeWeightsRangeToByChannel>();
                });
            }

            passManager.AddWithName<DataflowPass>("fake_range_fix").Configure(p =>
            {
                p.Add<DisableConvDWPermitInt16Quant>();
                p.Add<DisableConvPdp0ReducePermitInt16Quant>();
                p.Add<SquantFineTuneFakeConv2DWeights>();
                p.Add<SplitLargeActivation>();
            });
        }
    }

    /// <inheritdoc/>
    public override Task AdaRoundWeights(ICalibrationDatasetProvider calibrationDataset, List<ENode> rangeOfs, List<ENode> childrenOfRangeOfs, QuantizeOptions quantizeOptions)
    {
        // var samples = await calibrationDataset.Samples.ToListAsync();

        // var sample = await calibrationDataset.Samples.FirstAsync();
        // AdaRoundWeightsImpl(samples, childrenOfRangeOfs, quantizeOptions);
        return Task.CompletedTask;
    }

    /// <inheritdoc/>
    public override Task<Dictionary<ENode, List<Tuple<List<DataType>, List<List<QuantParam>>, float>>>> BindQuantMethodCosine(ICalibrationDatasetProvider calibrationDataset, List<ENode> rangeOfs, List<ENode> childrenOfRangeOfs, QuantizeOptions quantizeOptions)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc/>
    public override void RegisterQuantizePass(IPassManager passManager, CompileOptions options)
    {
        if (options.QuantizeOptions.ModelQuantMode == ModelQuantMode.UsePTQ)
        {
            passManager.AddWithName<DataflowPass>("LowerFakePdp").Configure(p =>
            {
                p.AddAnalysis<IExprUserAnalysisResult>();

                // ToGNNEPdpReduce must before ToGNNEConv2D
                p.Add<ToGNNEPdpReduce>();
            });
            passManager.AddWithName<DataflowPass>("LowerFakeMain").Configure(p =>
            {
                p.Add<ToGNNELSTM>();

                // p.Add<ToDynamicGNNEMatMul>();
                p.Add<ToGNNEConv2D>();
                p.Add<ToGNNEConv2DTranspose>();
                p.Add<ToGNNEMatMul>();
                p.Add<ToGNNEActivation>();
                p.Add<ToAi2dPad>();
                p.Add<ToAi2dResize>();
                p.Add<FoldNopReshape>();
                p.Add<RestoreFakeConv2D>();
            });
            passManager.AddWithName<DataflowPass>("LowerClearMarker").Configure(p =>
            {
                p.AddAnalysis<IExprUserAnalysisResult>();
                p.Add<RemoveMarker>();
                p.Add<FoldNopReshape>();
                p.Add<FoldTwoReshapes>();
                p.Add<FoldReshapeTransposeOfDepthAnything>();
            });
            passManager.AddWithName<EGraphRulesPass>("LowerEGraph");
            passManager.AddWithName<DataflowPass>("LowerdOptimize").Configure(p =>
            {
                p.AddAnalysis<IExprUserAnalysisResult>();
                p.Add<CombineQuantizeConcat>();
                p.Add<CombineQuantizeReshape>(true);
                p.Add<CombineQuantizeTranspose>();
                p.Add<FoldQuantDeQuant>();
                p.Add<FoldDeQuantQuant>();
                p.Add<ToGNNETranspose>();
                p.Add<ToGNNEPad>();
                p.Add<MarkActivationInputAL1Fuse>();
                p.Add<MarkActivationInputBL1Fuse>();
            });
            passManager.AddWithName<EGraphRulesPass>("LowerFake_6");
        }
    }

    public void RegisterTargetDependentAfterQuantPass(IPassManager passManager, CompileOptions options)
    {
        if (options.QuantizeOptions.ModelQuantMode == ModelQuantMode.UsePTQ)
        {
            // passManager.AddWithName<DataflowPass>("ProcessShiftBitsPass_1").Configure(p =>
            // {
            //     p.Add<ProcessShiftBitsGNNEMatmul>();
            // });
            passManager.AddWithName<DataflowPass>("PostLoweringProcess_1").Configure(p =>
            {
                p.AddAnalysis<IExprUserAnalysisResult>();
                p.Add<EliminateFloat32>();
                p.Add<FoldNopReshape>();
            });

            passManager.AddWithName<DataflowPass>("PostLoweringProcess_2").Configure(p =>
            {
                p.AddAnalysis<IExprUserAnalysisResult>();
                p.Add<FuseQuantIntoConv>();
                p.Add<FuseQuantIntoConvTranspose>();
                p.Add<FuseQuantIntoPdp0Reduce>();
                p.Add<FuseQuantIntoPdp0Dw>();
                p.Add<FuseQuantIntoPdp1>();
                p.Add<FuseQuantIntoAct1>();
                p.Add<FoldNopAct1>();

                // p.Add<CombineReshapeQuantize>();
            });
            passManager.AddWithName<DataflowPass>("PostLoweringProcess_3").Configure(p =>
            {
                p.AddAnalysis<IExprUserAnalysisResult>();
                p.Add<DWToPdp>();
                p.Add<FoldGNNEPdp0ReduceAndGNNEActivation>();
            });
            passManager.AddWithName<DataflowPass>("PostLoweringProcess_5").Configure(p =>
            {
                p.AddAnalysis<IExprUserAnalysisResult>();
                p.Add<StackToConcat>();
                p.Add<Expand1x1Conv>();
                p.Add<QuantToAct1>();
                p.Add<DeqToAct1>();
                p.Add<FoldTwoReshapes>();
                p.Add<FoldNopReshape>();
                p.Add<EliminateFloat32>();
            });
            passManager.AddWithName<EGraphRulesPass>("PostLoweringProcess_6");
            passManager.AddWithName<DataflowPass>("PostLoweringProcess_7").Configure(p =>
            {
                p.Add<ProcessConvShiftBits>();
                p.Add<ProcessConvTransposeShiftBits>();
                p.Add<ProcessAct1ShiftBits>();
                p.Add<ProcessPdp0DWShiftBits>();
                p.Add<ProcessPdp0ReduceShiftBits>();
                p.Add<ProcessMatmulShiftBits>();
                p.Add<ProcessLstmShiftBits>();
            });

            passManager.AddWithName<DataflowPass>("Fusion").Configure(p =>
            {
                p.Add<ConvFusion>();
                p.Add<Pdp0DwFusion>();
                p.Add<Pdp0ReduceFusion>();
                p.Add<PadFusion>();
                p.Add<TransposeFusion>();
                p.Add<Pdp1Fusion>();
                p.Add<ActSIFFusion>();
                p.Add<ActDIFFusion>();
                p.Add<ConvTransposeFusion>();
                p.Add<MatMulFusion>();
                p.Add<ResizeFusion>();
                p.Add<OptCast>();
                p.Add<GNNELoadStoreFusion>();

                // p.Add<GNNEFuseTwoFusion>("TileConv2dCase"),
                p.Add<GNNELSTMFusion>();
            });

            passManager.AddWithName<K230FusionToTirPass>("FusionToTirPass");

            // // Before CodeGen
            passManager.AddWithName<PrimFuncPass>("BufferStage").Configure(p =>
            {
                p.Add<Mutators.K230.FoldConstCall>(); // 常量折叠所有的指令参数.
            });
        }
    }

    public override void RegisterTargetDependentBeforeCodeGen(IPassManager passManager, CompileOptions options)
    {
        // dump the functions order for trace.
        // passManager.Add<DumpTraceInfoPass>();
        passManager.AddWithName<PrimFuncPass>("InstStage").Configure(p =>
        {
            p.Add<Mutators.K230.FoldConstCall>();
            p.Add<FoldBufferSlot>(); // 折叠自定义op
        });
    }

#if false
    private void AdaRoundWeightsImpl(List<IReadOnlyDictionary<Var, IValue>> samples, List<ENode> childrenOfRangeOfs, QuantizeOptions quantizeOptions)
    {
        int maxSamplesCount = Math.Min(samples.Count, 5);
        samples = samples.GetRange(0, maxSamplesCount);

        // save original weights in a copy list
        List<ENode> childrenOfRangeOfsCopy = new();
        for (int i = 0; i < childrenOfRangeOfs.Count; i++)
        {
            if (childrenOfRangeOfs[i].Expr is Call && ((Call)childrenOfRangeOfs[i].Expr).Target is FakeConv2D)
            {
                // save original input quant param
                var input = IR.F.Math.RangeOfMarker(((Marker)((Call)childrenOfRangeOfs[i].Expr).Arguments[0]).Target, ((Marker)((Call)childrenOfRangeOfs[i].Expr).Arguments[0]).Attribute);

                // save original weights
                var weights = Tensor.From(((TensorConst)((Marker)((Call)childrenOfRangeOfs[i].Expr).Arguments[1]).Target).Value.ToArray<float>(), ((TensorConst)((Marker)((Call)childrenOfRangeOfs[i].Expr).Arguments[1]).Target).CheckedShape);
                var act = ((Call)childrenOfRangeOfs[i].Expr).Arguments[2];
                var padding = ((Call)childrenOfRangeOfs[i].Expr).Arguments[3];
                var stride = ((Call)childrenOfRangeOfs[i].Expr).Arguments[4];
                var dilation = ((Call)childrenOfRangeOfs[i].Expr).Arguments[5];
                var groups = ((Call)childrenOfRangeOfs[i].Expr).Arguments[6];
                var padValue = ((Call)childrenOfRangeOfs[i].Expr).Arguments[7];
                var fakeConv2D = Tensors.FakeConv2D(input, weights, act, padding, stride, dilation, groups, padValue, ((FakeConv2D)((Call)childrenOfRangeOfs[i].Expr).Target).ActParam);
                List<EClass> newChildren = new();
                for (int j = 0; j < childrenOfRangeOfs[i].Children.Count; j++)
                {
                    // save original weights
                    if (j == 2)
                    {
                        var newEClass = new EClass(childrenOfRangeOfs[i].Children.ToArray()[j].Id);
                        newEClass.AddNode(ENode.Create(weights, null!));
                        newEClass.AddUsedBy(childrenOfRangeOfs[i]);
                        newChildren.Add(newEClass);
                    }
                    else
                    {
                        newChildren.Add(childrenOfRangeOfs[i].Children.ToArray()[j]);
                    }
                }

                var eNode = ENode.Create(fakeConv2D, newChildren.ToArray());

                // eNode.GetHashCode();
                // for (int j = 0; j < childrenOfRangeOfs[i].Children.Count; j++)
                // {
                //     eNode.SetClass(childrenOfRangeOfs[i].Children[j]);
                // }
                childrenOfRangeOfsCopy.Add(eNode);
            }
            else
            {
                childrenOfRangeOfsCopy.Add(childrenOfRangeOfs[i]);
            }
        }

        // get original f32 output for each layer
        // Dictionary<ENode, List<Tensor>> layerOutputGroundTruth = new Dictionary<ENode, List<Tensor>>();
        // foreach (var sample in samples)
        // {
        //     using var dumpScope = new DumpScope("eval_childof_rangeof");
        //     using var childrenEvaluator = new CalibrationEvaluator(sample, childrenOfRangeOfs);
        //     var childrenValues = childrenEvaluator.Evaluate();
        //     for (int i = 0; i < childrenValues.Keys.ToArray().Length; i++)
        //     {
        //         if ((childrenValues.Keys.ToArray()[i].Expr is Call) &&
        //             (((Call)(childrenValues.Keys.ToArray()[i].Expr)).Target is IR.K230.FakeConv2D))
        //         {
        //             if (!layerOutputGroundTruth.ContainsKey(childrenValues.Keys.ToArray()[i]))
        //             {
        //                 List<Tensor> tmp = new();
        //                 tmp.Add(((Nncase.IR.Marker)(((Call)(childrenValues.Keys.ToArray()[i].Expr)).Parameters[0])).AdaQuantInfo.AdaRoundRefTensor);
        //                 layerOutputGroundTruth.Add(childrenValues.Keys.ToArray()[i], tmp);
        //             }
        //             else
        //             {
        //                 layerOutputGroundTruth[childrenValues.Keys.ToArray()[i]].Add(((Nncase.IR.Marker)(((Call)(childrenValues.Keys.ToArray()[i].Expr)).Parameters[0])).AdaQuantInfo.AdaRoundRefTensor);
        //             }
        //         }
        //     }
        // }
        int layer = 0;
        foreach (var childenOfRangeOf in childrenOfRangeOfs)
        {
            var bits = 8;
            if (childenOfRangeOf.Expr is Call &&
                ((Call)childenOfRangeOf.Expr).Target is FakeConv2D)
            {
                // current node input feature map list
                List<Tensor> layerInput = new();
                var layerOutputGroundTruth = new Dictionary<ENode, List<Tensor>>();
                foreach (var sample in samples)
                {
                    using var childrenEvaluatorCopy = new CalibrationEvaluator(sample, childrenOfRangeOfsCopy);
                    var childrenValuesCopy = childrenEvaluatorCopy.Evaluate();

                    for (int i = 0; i < childrenValuesCopy.Keys.ToArray().Length; i++)
                    {
                        if (childrenValuesCopy.Keys.ToArray()[i].Expr is Call &&
                            ((Call)childrenValuesCopy.Keys.ToArray()[i].Expr).Target is FakeConv2D)
                        {
                            if (!layerOutputGroundTruth.ContainsKey(childrenValuesCopy.Keys.ToArray()[i]))
                            {
                                List<Tensor> tmp = new();
                                tmp.Add(((Marker)((Call)childrenValuesCopy.Keys.ToArray()[i].Expr).Arguments[0]).AdaQuantInfo!.AdaRoundRefTensor!);
                                layerOutputGroundTruth.Add(childrenValuesCopy.Keys.ToArray()[i], tmp);
                            }
                            else
                            {
                                layerOutputGroundTruth[childrenValuesCopy.Keys.ToArray()[i]].Add(((Marker)((Call)childrenValuesCopy.Keys.ToArray()[i].Expr).Arguments[0]).AdaQuantInfo!.AdaRoundRefTensor!);
                            }
                        }
                    }

                    // parse and get input feature map
                    List<ENode> tmpNodes = new();
                    var input = childenOfRangeOf.Children[1].Nodes[0];
                    tmpNodes.Add(input);
                    using var dumpScope = new DumpScope("eval_input");
                    using var inputEvaluator = new CalibrationEvaluator(sample, tmpNodes);
                    var inputValue = inputEvaluator.Evaluate();

                    // simulate input and psum quant/dequant
                    if (quantizeOptions.QuantType == DataTypes.Int16)
                    {
                        bits = 12;
                    }

                    var quantMode = quantizeOptions.WQuantType == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;

                    // input quant param
                    var inputRange = ((TensorConst)input.Children[1].Nodes[0].Expr).Value.ToArray<float>();
                    var inputQuantParam = QuantUtility.GetQuantParam(new ValueRange<float>(inputRange[0], inputRange[1]), bits, quantMode);
                    var inputBufFloat = MemoryMarshal.Cast<byte, float>(inputValue.Values.ToArray()[0].BytesBuffer);
                    for (int j = 0; j < inputBufFloat.Length; j++)
                    {
                        double inputBufQuant = Math.Round((inputBufFloat[j] / (double)inputQuantParam.Scale) + inputQuantParam.ZeroPoint);
                        double inputBufDeQuant = ((float)inputBufQuant - inputQuantParam.ZeroPoint) * (double)inputQuantParam.Scale;
                        inputBufFloat[j] = (float)inputBufDeQuant;
                    }

                    // assign quant param to marker, it's used for evaluator
                    if (((Marker)input.Expr).AdaQuantInfo == null)
                    {
                        ((Marker)input.Expr!).AdaQuantInfo = new AdaQuantInfo();
                    }

                    ((Marker)input.Expr).AdaQuantInfo!.InputQuantParameter = inputQuantParam;
                    layerInput.Add(inputValue.Values.First());
                }

                // var layerOutputGT = layerOutputGroundTruth[childenOfRangeOf];
                var layerOutputGT = layerOutputGroundTruth.ToArray()[layer++].Value;
                {
                    List<ENode> tmpWNodes = new();
                    var weights = childenOfRangeOf.Children[2].Nodes[0];
                    tmpWNodes.Add(weights);
                    using var dumpScope = new DumpScope("eval_weights");
                    using var weightsEvaluator = new CalibrationEvaluator(null!, tmpWNodes);
                    var weightsValue = weightsEvaluator.Evaluate();
                    var weightsValueBuf = MemoryMarshal.Cast<byte, float>(weightsValue.Values.ToArray()[0].BytesBuffer);

                    // var act = childenOfRangeOf.Children[3].Nodes[0].Expr;
                    var paddings = childenOfRangeOf.Children[4].Nodes[0].Expr;
                    var strides = childenOfRangeOf.Children[5].Nodes[0].Expr;
                    var dilations = childenOfRangeOf.Children[6].Nodes[0].Expr;
                    var groups = childenOfRangeOf.Children[7].Nodes[0].Expr;

                    // var fusedClamp = childenOfRangeOf.Children[8].Nodes[0].Expr;
                    // var padValue = childenOfRangeOf.Children[8].Nodes[0].Expr;
                    var wRanges = (TensorConst)weights.Children[1].Nodes[0].Expr;
                    if (quantizeOptions.WQuantType == DataTypes.UInt8)
                    {
                        wRanges = ((Marker)weights.Expr).MixQuantInfo?.U8FineTunedWeightsRangesByChannel == null ? wRanges : ((Marker)weights.Expr).MixQuantInfo?.U8FineTunedWeightsRangesByChannel;
                    }

                    if (quantizeOptions.WQuantType == DataTypes.Int8)
                    {
                        wRanges = ((Marker)weights.Expr).MixQuantInfo?.I8FineTunedWeightsRangesByChannel == null ? wRanges : ((Marker)weights.Expr).MixQuantInfo!.I8FineTunedWeightsRangesByChannel;
                    }

                    if (quantizeOptions.WQuantType == DataTypes.Int16)
                    {
                        wRanges = ((K230MixQuantInfo)((Marker)weights.Expr).MixQuantInfo!).I16FineTunedWeightsRangesByChannel == null ? wRanges : ((K230MixQuantInfo)((Marker)weights.Expr).MixQuantInfo!).I16FineTunedWeightsRangesByChannel;
                    }

                    if (quantizeOptions.WQuantType == DataTypes.Int16)
                    {
                        bits = 12;
                    }

                    var wQuantMode = quantizeOptions.WQuantType == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;
                    var weightsValueBufAda = QuantUtility.AdaRoundWeights(weightsValueBuf, wRanges!, weights.Expr!.CheckedShape, layerInput, layerOutputGT, wQuantMode, bits, true, paddings, strides, dilations, groups, 20, 2, 20000, 0, 0.2f, 0.1f, QuantUtility.AdaMode.Conv2D);
                    for (int i = 0; i < weightsValueBuf.Length; i++)
                    {
                        weightsValueBuf[i] = weightsValueBufAda[i];
                    }
                }
            }
        }
    }
#endif

    private Dictionary<ENode, List<Tuple<List<DataType>, List<List<QuantParam>>, float>>> BindQuantMethodCosineImpl(List<IReadOnlyDictionary<IVar, IValue>> samples, List<ENode> rangeOfs, List<ENode> childrenOfRangeOfs, QuantizeOptions quantizeOptions)
    {
        int maxSamplesCount = Math.Min(samples.Count, 5);
        samples = samples.GetRange(0, maxSamplesCount);
        var enodeQuantCosineDict = new Dictionary<ENode, List<Tuple<List<DataType>, List<List<QuantParam>>, float>>>();

        List<IReadOnlyDictionary<ENode, Tensor>> tmpChildrenValues = new();

        // int tmpIndex = 0;
        // get original f32 output for each layer
        // foreach (var childenOfRangeOf in childrenOfRangeOfs)
        {
            // todo: add other interested ops below
            // if ((childenOfRangeOf.Expr is Call) &&
            //  (((Call)(childenOfRangeOf.Expr)).Target is Nncase.IR.K230.FakeActivation ||
            //  ((Call)(childenOfRangeOf.Expr)).Target is Nncase.IR.K230.FakeConv2D ||
            //  ((Call)(childenOfRangeOf.Expr)).Target is Nncase.IR.K230.FakeConv2DTranspose ||
            //  ((Call)(childenOfRangeOf.Expr)).Target is Nncase.IR.K230.FakeDynamicGNNEMatMul ||
            //  ((Call)(childenOfRangeOf.Expr)).Target is Nncase.IR.K230.FakePdp))
            // (!(((Call)(childenOfRangeOf.Expr)).Target is Nncase.IR.Tensors.Concat)))
            {
                foreach (var sample in samples)
                {
                    using var dumpScope = new DumpScope("eval_childof_rangeof");
                    using var childrenEvaluator = new CalibrationEvaluator(sample, childrenOfRangeOfs);
                    var childrenValues = childrenEvaluator.Evaluate();
                    tmpChildrenValues.Add(childrenValues);
                }
            }
        }

        // childenOfRangeOfs are candidate nodes need to be evaluated quant error
        foreach (var childenOfRangeOf in childrenOfRangeOfs)
        {
            // todo: add other interested ops below
            if (childenOfRangeOf.Expr is Call &&
                (((Call)childenOfRangeOf.Expr).Target is FakeActivation ||
                 ((Call)childenOfRangeOf.Expr).Target is FakeAi2dPad ||
                 ((Call)childenOfRangeOf.Expr).Target is FakeAi2dResize ||
                 ((Call)childenOfRangeOf.Expr).Target is FakeConv2D ||
                 ((Call)childenOfRangeOf.Expr).Target is FakeConv2DTranspose ||
                 ((Call)childenOfRangeOf.Expr).Target is FakeDynamicGNNEMatMul ||
                 ((Call)childenOfRangeOf.Expr).Target is FakeLSTM ||
                 ((Call)childenOfRangeOf.Expr).Target is FakeMatMul ||
                 ((Call)childenOfRangeOf.Expr).Target is FakePdp))
            {
                // List<List<QuantParam>> below: outer list represents each inputs quant parameters, inner list represents each channel quant parameters.
                var quantConfigWithCosineSamples = new List<Tuple<List<DataType>, List<List<QuantParam>>, float>>();
                int tmpIndex = 0;
                foreach (var sample in samples)
                {
                    IReadOnlyDictionary<ENode, Tensor> values;
                    {
                        using var dumpScope = new DumpScope("eval_rangeof");
                        using var evaluator = new CalibrationEvaluator(sample, rangeOfs);
                        values = evaluator.Evaluate();
                    }

                    // var childrenEvaluator = new CalibrationEvaluator(sample, childrenOfRangeOfs, runPassOptions.SetPassName(runPassOptions.PassName + "/eval_childof_rangeof"));
                    // var childrenValues = childrenEvaluator.Evaluate();
                    var childrenValues = tmpChildrenValues[tmpIndex++];

                    // values are children op range values, childrenValues are children op tensor values.

                    // List<List<QuantParam>> below: outer list represents each inputs quant parameters, inner list represents each channel quant parameters.
                    var quantConfigWithCosine = new List<Tuple<List<DataType>, List<List<QuantParam>>, float>>();
                    var pattern = IsRangeOfMarker(IsWildcard(), IsWildcard());
                    List<Tuple<IReadOnlyDictionary<ENode, Tensor>, Tensor, bool>> inputs = new();

                    // parse and get inputs
                    for (int i = 1; i < childenOfRangeOf.Children.Count; i++)
                    {
                        List<ENode> tmpNodes = new();
                        var input = childenOfRangeOf.Children[i].Nodes[0];
                        tmpNodes.Add(input);
                        using var dumpScope = new DumpScope("eval_input");
                        var inputEvaluator = new CalibrationEvaluator(sample, tmpNodes);
                        var inputValue = inputEvaluator.Evaluate();
                        var needQuantInput = pattern.MatchLeaf(childenOfRangeOf.Children[i].Nodes[0].Expr);
                        if (needQuantInput)
                        {
                            var inputRange = values[childenOfRangeOf.Children[i].Nodes[0].Children[1].Nodes[0]];
                            inputs.Add(new Tuple<IReadOnlyDictionary<ENode, Tensor>, Tensor, bool>(inputValue, inputRange, needQuantInput));
                        }
                        else
                        {
                            inputs.Add(new Tuple<IReadOnlyDictionary<ENode, Tensor>, Tensor, bool>(inputValue, null!, needQuantInput));
                        }
                    }

                    // construct const inputs to simulate quant/dequant.
                    var hasNeedQuantInput = false;
                    var needQuantInputCount = 0;
                    for (int i = 0; i < inputs.Count; i++)
                    {
                        if (inputs[i].Item3)
                        {
                            needQuantInputCount++;
                        }
                    }

                    // set candidate quant config below.
                    Trace.Assert(needQuantInputCount == 0 || needQuantInputCount == 1 || needQuantInputCount == 2);
                    List<List<DataType>> simQuantTypeConfig = new();
                    if (needQuantInputCount == 1)
                    {
                        simQuantTypeConfig.AddRange(new List<List<DataType>> { new() { DataTypes.UInt8 }, new() { DataTypes.Int8 }, new() { DataTypes.Int16 }, new() { DataTypes.Float16 } });
                    }
                    else if (needQuantInputCount == 2)
                    {
                        simQuantTypeConfig.AddRange(new List<List<DataType>>
                        {
                            new() { DataTypes.UInt8, DataTypes.UInt8 }, new() { DataTypes.Int8, DataTypes.Int8 }, new() { DataTypes.Int16, DataTypes.UInt8 }, new() { DataTypes.UInt8, DataTypes.Int16 }, new() { DataTypes.Float16, DataTypes.Float16 },
                        });
                    }

                    for (int configNum = 0; configNum < simQuantTypeConfig.Count; configNum++)
                    {
                        var callPattern = IsCall(IsWildcard(), IsWildcard());
                        var isCallExpr = callPattern.MatchLeaf(childenOfRangeOf.Expr);
                        Trace.Assert(isCallExpr);

                        // if (((Call)(childenOfRangeOf.Expr)).PermitInt16Quant == false && simQuantTypeConfig[configNum][0] == DataTypes.Int16)
                        //     continue;
                        // todo: if MixQuantInfo is null, by default the node doesn't support int16, but in the future int16 is supported by default, need to remve below judgement.
                        // if (((Marker)(childenOfRangeOf.Children[1].Nodes[0].Expr)).MixQuantInfo == null && simQuantTypeConfig[configNum][0] == DataTypes.Int16)
                        //     continue;
                        if (((K230MixQuantInfo)((Marker)childenOfRangeOf.Children[1].Nodes[0].Expr).MixQuantInfo!)?.PermitInt16Quant == false && simQuantTypeConfig[configNum][0] == DataTypes.Int16)
                        {
                            continue;
                        }

                        if (needQuantInputCount == 1)
                        {
                            if (((Call)childenOfRangeOf.Expr).Target is FakeActivation && simQuantTypeConfig[configNum][0] == DataTypes.Int16)
                            {
                                continue;
                            }

                            if (!(((Call)childenOfRangeOf.Expr).Target is FakeActivation) && simQuantTypeConfig[configNum][0] == DataTypes.Float16)
                            {
                                continue;
                            }
                        }

                        if (needQuantInputCount == 2)
                        {
                            // todo: if MixQuantInfo is null, by default the node doesn't support int16, but in the future int16 is supported by default, need to remve below judgement.
                            // if (((Nncase.IR.Marker)(childenOfRangeOf.Children[2].Nodes[0].Expr)).MixQuantInfo == null && simQuantTypeConfig[configNum][1] == DataTypes.Int16)
                            //     continue;
                            if (((K230MixQuantInfo)((Marker)childenOfRangeOf.Children[2].Nodes[0].Expr).MixQuantInfo!)?.PermitInt16Quant == false && simQuantTypeConfig[configNum][1] == DataTypes.Int16)
                            {
                                continue;
                            }

                            if (((Call)childenOfRangeOf.Expr).Target is FakeActivation && simQuantTypeConfig[configNum][1] == DataTypes.Int16)
                            {
                                continue;
                            }

                            if (!(((Call)childenOfRangeOf.Expr).Target is FakeActivation) && simQuantTypeConfig[configNum][0] == DataTypes.Float16 && simQuantTypeConfig[configNum][1] == DataTypes.Float16)
                            {
                                continue;
                            }
                        }

                        List<Expr> parameters = new();
                        var quantParamList = new List<List<QuantParam>>();
                        List<Memory<float>> originalValueBufFloat = new();
                        for (int i = 0; i < inputs.Count; i++)
                        {
                            // rank 2 mean it's by channel range, currently it implies this input is weight
                            if (quantizeOptions.UseSquant && inputs[i].Item2?.Rank == 2)
                            {
                                var newRange = inputs[i].Item2;
                                if (simQuantTypeConfig[configNum][1] == DataTypes.UInt8)
                                {
                                    var newValue = new Dictionary<ENode, Tensor>();
                                    newValue.Add(inputs[i].Item1.Keys.First(), ((TensorConst)((Marker)childenOfRangeOf.Children[i + 1].Nodes[0].Expr).MixQuantInfo!.U8FineTunedWeights!).Value);
                                    if (((Marker)childenOfRangeOf.Children[i + 1].Nodes[0].Expr).MixQuantInfo?.U8FineTunedWeightsRangesByChannel != null)
                                    {
                                        newRange = ((TensorConst)((Marker)childenOfRangeOf.Children[i + 1].Nodes[0].Expr).MixQuantInfo?.U8FineTunedWeightsRangesByChannel!).Value;
                                    }

                                    inputs[i] = new Tuple<IReadOnlyDictionary<ENode, Tensor>, Tensor, bool>(newValue, newRange!, inputs[i].Item3);
                                }

                                if (simQuantTypeConfig[configNum][1] == DataTypes.Int8)
                                {
                                    var newValue = new Dictionary<ENode, Tensor>();
                                    newValue.Add(inputs[i].Item1.Keys.First(), ((TensorConst)((Marker)childenOfRangeOf.Children[i + 1].Nodes[0].Expr).MixQuantInfo?.I8FineTunedWeights!).Value);
                                    if (((Marker)childenOfRangeOf.Children[i + 1].Nodes[0].Expr).MixQuantInfo?.I8FineTunedWeightsRangesByChannel != null)
                                    {
                                        newRange = ((TensorConst)((Marker)childenOfRangeOf.Children[i + 1].Nodes[0].Expr).MixQuantInfo!.I8FineTunedWeightsRangesByChannel!).Value;
                                    }

                                    inputs[i] = new Tuple<IReadOnlyDictionary<ENode, Tensor>, Tensor, bool>(newValue, newRange!, inputs[i].Item3);
                                }

                                if (simQuantTypeConfig[configNum][1] == DataTypes.Int16)
                                {
                                    var newValue = new Dictionary<ENode, Tensor>();
                                    newValue.Add(inputs[i].Item1.Keys.First(), ((TensorConst)(((K230MixQuantInfo)((Marker)childenOfRangeOf.Children[i + 1].Nodes[0].Expr).MixQuantInfo!).I16FineTunedWeights!)).Value);
                                    if (((K230MixQuantInfo)((Marker)childenOfRangeOf.Children[i + 1].Nodes[0].Expr).MixQuantInfo!)?.I16FineTunedWeightsRangesByChannel != null)
                                    {
                                        newRange = ((TensorConst)((K230MixQuantInfo)((Marker)childenOfRangeOf.Children[i + 1].Nodes[0].Expr).MixQuantInfo!).I16FineTunedWeightsRangesByChannel!).Value;
                                    }

                                    inputs[i] = new Tuple<IReadOnlyDictionary<ENode, Tensor>, Tensor, bool>(newValue, newRange!, inputs[i].Item3);
                                }
                            }

                            Expr simInput = new TensorConst(inputs[i].Item1.Values.ToArray()[0]);
                            var quantParam = new List<QuantParam>();
                            quantParam.Add(new QuantParam(0, 1));

                            // needQuantInput
                            if (inputs[i].Item3)
                            {
                                var simQuantType = simQuantTypeConfig[configNum][i];
                                hasNeedQuantInput = true;

                                // current input values
                                var valueBufFloat = MemoryMarshal.Cast<byte, float>(inputs[i].Item1.Values.ToArray()[0].BytesBuffer);

                                // all original inputs values
                                originalValueBufFloat.Add(valueBufFloat.ToArray());
                                var quantMode = simQuantType == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;
                                var bits = simQuantType == DataTypes.Int16 ? 12 : 8;
                                if (inputs[i].Item2.Shape.Rank == 1)
                                {
                                    if (!(((Call)childenOfRangeOf.Expr).Target is FakeActivation && simQuantType == DataTypes.Float16))
                                    {
                                        quantParam[0] = QuantUtility.GetQuantParam(new ValueRange<float>((float)inputs[i].Item2[0], (float)inputs[i].Item2[1]), bits, quantMode);
                                        for (int j = 0; j < valueBufFloat.Length; j++)
                                        {
                                            var valueBufQuant = Math.Round((valueBufFloat[j] / (double)quantParam[0].Scale) + quantParam[0].ZeroPoint);
                                            var valueBufDeQuant = ((float)valueBufQuant - quantParam[0].ZeroPoint) * (double)quantParam[0].Scale;
                                            valueBufFloat[j] = (float)valueBufDeQuant;
                                        }
                                    }
                                }

                                // rank 2 mean it's by channel range, currently it implies this input is weight
                                else if (inputs[i].Item2.Shape.Rank == 2)
                                {
                                    quantParam.Clear();
                                    var oc = inputs[i].Item2.Shape[0].FixedValue;
                                    var eachChannelData = (int)(valueBufFloat.Length / oc);
                                    for (int c = 0; c < oc; c++)
                                    {
                                        quantParam.Add(QuantUtility.GetQuantParam(new ValueRange<float>((float)inputs[i].Item2[c, 0], (float)inputs[i].Item2[c, 1]), bits, quantMode));
                                    }

                                    for (int j = 0; j < valueBufFloat.Length; j++)
                                    {
                                        var valueBufQuant = Math.Round((valueBufFloat[j] / (double)quantParam[j / eachChannelData].Scale) + quantParam[j / eachChannelData].ZeroPoint);
                                        var valueBufDeQuant = ((float)valueBufQuant - quantParam[j / eachChannelData].ZeroPoint) * (double)quantParam[j / eachChannelData].Scale;
                                        valueBufFloat[j] = (float)valueBufDeQuant;
                                    }
                                }
                                else
                                {
                                    throw new AggregateException("Invalid range values");
                                }

                                parameters.Add(simInput);
                            }

                            // f32/i32 input
                            else
                            {
                                parameters.Add(simInput);
                            }

                            quantParamList.Add(quantParam);
                        }

                        if (hasNeedQuantInput)
                        {
                            var simGraph = new EGraph();
                            var simCall = new Call((Expr)childenOfRangeOf.Children[0].Nodes[0].Expr, parameters.ToArray());
                            simGraph.Add(simCall);
                            List<ENode> tmpNodes = new List<ENode>();
                            for (int i = 0; i < simGraph.Nodes.ToList().Count; i++)
                            {
                                var simNode = simGraph.Nodes.ToArray()[i];
                                if (simNode.Children.Count != 0)
                                {
                                    tmpNodes.Add(simNode);
                                }
                            }

                            using var dumpScope = new DumpScope("eval_sim");
                            using var simEvaluator = new CalibrationEvaluator(null!, tmpNodes);
                            var simValue = simEvaluator.Evaluate();
                            var simValueBuf = MemoryMarshal.Cast<byte, float>(simValue.Values.ToArray()[0].BytesBuffer);
                            var childrenValueBuf = MemoryMarshal.Cast<byte, float>(childrenValues[childenOfRangeOf].BytesBuffer);
                            var cos = GetCosineSimilarity(simValueBuf, childrenValueBuf);
                            quantConfigWithCosine.Add(new Tuple<List<DataType>, List<List<QuantParam>>, float>(simQuantTypeConfig[configNum], quantParamList, cos));
                            quantConfigWithCosineSamples.Add(new Tuple<List<DataType>, List<List<QuantParam>>, float>(simQuantTypeConfig[configNum], quantParamList, cos));

                            // need to recover egraph to the original one, otherwise, the inputs for next round sim will be after quant/dequant.
                            for (int i = 0; i < inputs.Count; i++)
                            {
                                // Expr simInput = new TensorConst(inputs[i].Item1.Values.ToArray()[0]);
                                if (inputs[i].Item3)
                                {
                                    var valueBufFloat = MemoryMarshal.Cast<byte, float>(inputs[i].Item1.Values.ToArray()[0].BytesBuffer);
                                    var tmpOriginalValueBufFloat = originalValueBufFloat[i].ToArray();
                                    for (int j = 0; j < valueBufFloat.Length; j++)
                                    {
                                        valueBufFloat[j] = tmpOriginalValueBufFloat[j];
                                    }
                                }
                            }
                        }
                    }
                }

                var quantConfigWithCosineAvg = new List<Tuple<List<DataType>, List<List<QuantParam>>, float>>();
                int configs = quantConfigWithCosineSamples.Count / samples.Count; // 3
                List<float> cosineSum = new List<float>(new float[configs]);

                for (int i = 0; i < quantConfigWithCosineSamples.Count; i++)
                {
                    cosineSum[i % configs] += quantConfigWithCosineSamples[i].Item3;
                }

                for (int i = 0; i < configs; i++)
                {
                    quantConfigWithCosineAvg.Add(new Tuple<List<DataType>, List<List<QuantParam>>, float>(quantConfigWithCosineSamples[i].Item1, quantConfigWithCosineSamples[i].Item2, cosineSum[i] / samples.Count));
                }

                if (quantConfigWithCosineAvg.Count != 0)
                {
                    Tuple<List<DataType>, List<List<QuantParam>>, float> bestQuantConfigWithCosine;
                    var quantConfigWithCosineFromQInt8 = new Tuple<List<DataType>, List<List<QuantParam>>, float>(new(), new(), 0);
                    var bestQuantConfigWithCosineFromQInt8 = quantConfigWithCosineFromQInt8;
                    var bestQuantConfigWithCosineFromAll = quantConfigWithCosineFromQInt8;
                    var bestQuantConfigWithCosineFromInt16 = quantConfigWithCosineFromQInt8;

                    // get better from u8 or i8
                    for (var i = 0; i < quantConfigWithCosineAvg.Count; i++)
                    {
                        // k230 specific strategy: we should choose 8 bits inputs first by default, so filter out 16 bits inputs at first, if 8 bits inputs is too bad, we will try to choose better i6 bits inputs then.
                        if (quantConfigWithCosineAvg[i].Item1.Count == 1 && (quantConfigWithCosineAvg[i].Item1[0] == DataTypes.Int16 || quantConfigWithCosineAvg[i].Item1[0] == DataTypes.Float16))
                        {
                            continue;
                        }

                        if (quantConfigWithCosineAvg[i].Item1.Count == 2 && (quantConfigWithCosineAvg[i].Item1[0] == DataTypes.Int16 || quantConfigWithCosineAvg[i].Item1[1] == DataTypes.Int16 || (quantConfigWithCosineAvg[i].Item1[0] == DataTypes.Float16 && quantConfigWithCosineAvg[i].Item1[1] == DataTypes.Float16)))
                        {
                            continue;
                        }

                        if (quantConfigWithCosineAvg[i].Item3 > bestQuantConfigWithCosineFromQInt8.Item3)
                        {
                            bestQuantConfigWithCosineFromQInt8 = quantConfigWithCosineAvg[i];
                        }
                    }

                    // get real best from all
                    for (var i = 0; i < quantConfigWithCosineAvg.Count; i++)
                    {
                        if (quantConfigWithCosineAvg[i].Item3 > bestQuantConfigWithCosineFromAll.Item3)
                        {
                            bestQuantConfigWithCosineFromAll = quantConfigWithCosineAvg[i];
                        }
                    }

                    // get i16 from all
                    for (var i = 0; i < quantConfigWithCosineAvg.Count; i++)
                    {
                        if (quantConfigWithCosineAvg[i].Item1.Count == 1 && quantConfigWithCosineAvg[i].Item1[0] != DataTypes.Int16)
                        {
                            continue;
                        }

                        if (quantConfigWithCosineAvg[i].Item1.Count == 2 && quantConfigWithCosineAvg[i].Item1[0] != DataTypes.Int16 && quantConfigWithCosineAvg[i].Item1[1] != DataTypes.Int16)
                        {
                            continue;
                        }

                        if (quantConfigWithCosineAvg[i].Item3 > bestQuantConfigWithCosineFromInt16.Item3)
                        {
                            bestQuantConfigWithCosineFromInt16 = quantConfigWithCosineAvg[i];
                        }
                    }

                    // priority: qint8->int16->fp16
                    float threshold = 0.99f;
                    if (bestQuantConfigWithCosineFromQInt8.Item3 < threshold * bestQuantConfigWithCosineFromAll.Item3)
                    {
                        if (bestQuantConfigWithCosineFromInt16.Item3 < threshold * bestQuantConfigWithCosineFromAll.Item3)
                        {
                            bestQuantConfigWithCosine = bestQuantConfigWithCosineFromAll;
                        }
                        else
                        {
                            bestQuantConfigWithCosine = bestQuantConfigWithCosineFromInt16;
                        }
                    }
                    else
                    {
                        bestQuantConfigWithCosine = bestQuantConfigWithCosineFromQInt8;
                    }

                    ((Call)childenOfRangeOf.Expr).EnodeQuantConfigWithCosine = quantConfigWithCosineAvg;
                    ((Call)childenOfRangeOf.Expr).EnodeBestQuantConfigWithCosine = bestQuantConfigWithCosine;

                    int index = 0;
                    for (int i = 0; i < childenOfRangeOf.Children.Count; i++)
                    {
                        var pattern = IsRangeOfMarker(IsWildcard(), IsWildcard());
                        var needQuantInput = pattern.MatchLeaf(childenOfRangeOf.Children[i].Nodes[0].Expr);
                        if (needQuantInput)
                        {
                            if (((Marker)childenOfRangeOf.Children[i].Nodes[0].Expr).MixQuantInfo == null)
                            {
                                ((Marker)childenOfRangeOf.Children[i].Nodes[0].Expr).MixQuantInfo = new K230MixQuantInfo();
                            }

                            ((Marker)childenOfRangeOf.Children[i].Nodes[0].Expr).MixQuantInfo!.HasBindedMixQuantInfo = true;
                            ((Marker)childenOfRangeOf.Children[i].Nodes[0].Expr).MixQuantInfo!.MarkerQuantType = bestQuantConfigWithCosine.Item1[index];
                            ((Marker)childenOfRangeOf.Children[i].Nodes[0].Expr).MixQuantInfo!.QuantParameter = bestQuantConfigWithCosine.Item2[index];
                            index++;
                        }
                    }

                    enodeQuantCosineDict.Add(childenOfRangeOf, quantConfigWithCosineAvg);
                }
            }
        }

        return enodeQuantCosineDict;
    }

    public class K230MixQuantInfo : MixQuantInfo
    {
        /// <summary>
        /// PermitInt16Quant.
        /// </summary>
        private bool _permitInt16Quant = true;

        /// <summary>
        /// I16FineTunedWeights.
        /// </summary>
        private TensorConst? _i16FineTunedWeights;

        /// <summary>
        /// I16FineTunedWeightsRangesByChannel.
        /// </summary>
        private TensorConst? _i16FineTunedWeightsRangesByChannel;

        public bool PermitInt16Quant
        {
            get => _permitInt16Quant;
            set => _permitInt16Quant = value;
        }

        public TensorConst? I16FineTunedWeights
        {
            get => _i16FineTunedWeights;
            set => _i16FineTunedWeights = value;
        }

        public TensorConst? I16FineTunedWeightsRangesByChannel
        {
            get => _i16FineTunedWeightsRangesByChannel;
            set => _i16FineTunedWeightsRangesByChannel = value;
        }
    }
}
