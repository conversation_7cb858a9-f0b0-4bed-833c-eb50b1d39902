﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Diagnostics;
using Nncase.IR;
using Nncase.IR.Imaging;
using Nncase.IR.K230;
using Nncase.Passes.Rules.K230;
using Nncase.TIR.Instructions;
using OrtKISharp;
using static Nncase.PatternMatch.Utility;
using Shape = Nncase.IR.Shape;

namespace Nncase.Evaluator;

public static class K230Kernels
{
    /// <summary>
    /// Apply_act0.
    /// </summary>
    /// <param name="value">.</param>
    /// <param name="actData">1.</param>
    /// <param name="channel">2.</param>
    /// <param name="shiftBits">3.</param>
    /// <returns>4.</returns>
    public static float ApplyAct0(float value, ReadOnlySpan<Half> actData, int channel, sbyte shiftBits)
    {
        int begin = 7 * channel;
        float v = ApplyGnneActivation(
            value,
            shiftBits,
            (float)actData[begin + 6], // x0
            (float)actData[begin + 0], // kl
            (float)actData[begin + 2], // bl
            (float)actData[begin + 1], // kr
            (float)actData[begin + 3]); // br

        return ApplyActivation(v, ((float)actData[begin + 4], (float)actData[begin + 5]));
    }

    public static float ApplyAct01(float value, ReadOnlySpan<float> actData, int channel, sbyte shiftBits)
    {
        int begin = 7 * channel;
        float v = ApplyGnneActivation(
            value,
            shiftBits,
            actData[begin + 6], // x0
            actData[begin + 0], // kl
            actData[begin + 2], // bl
            actData[begin + 1], // kr
            actData[begin + 3]); // br

        return ApplyActivation(v, (actData[begin + 4], actData[begin + 5]));
    }

    public static float FakeApplyAct0(float value, float[] actData, int channel, sbyte shiftBits)
    {
        int begin = 7 * channel;
        float v = (value * actData[begin + 0] / (1L << shiftBits)) +
                  (value.CompareTo(actData[begin + 6]) < 0 ? actData[begin + 2] : actData[begin + 3]);
        ValueRange<float> a = (actData[begin + 4], actData[begin + 5]);
        float min = a.Min;
        float max = a.Max;
        return v.CompareTo(max) > 0 ? max : v.CompareTo(min) > 0 ? v.CompareTo(max) > 0 ? max : v : min;
    }

    public static int GetWindowedOutputSize(int size, int filter, int stride, int dilation, bool same, bool ceilMode = false)
    {
        int effectiveFilterSize = ((filter - 1) * dilation) + 1;
        if (same)
        {
            return (size + stride - 1) / stride;
        }
        else
        {
            if (!ceilMode)
            {
                return (size - effectiveFilterSize + stride) / stride;
            }
            else
            {
                return (int)((float)(size - effectiveFilterSize + stride) / stride);
            }
        }
    }

    public static float ApplyMultiSegmentsAct1(int n, float value, ReadOnlySpan<Half> actData)
    {
        for (int i = 0; i < n - 1; i++)
        {
            if (value < (float)actData[i])
            {
                return (value * (float)actData[n - 1 + i]) + (float)actData[n - 1 + n + i];
            }
        }

        return (value * (float)actData[n - 1 + n - 1]) + (float)actData[n - 1 + n + n - 1];
    }

    public static float ApplyMultiSegmentsAct1(int n, float value, ReadOnlySpan<float> actData)
    {
        for (int i = 0; i < n - 1; i++)
        {
            if (value < actData[i])
            {
                return (value * actData[(n - 1 + i) % actData.Length]) + actData[(n - 1 + n + i) % actData.Length];
            }
        }

        return (value * actData[n - 1 + n - 1]) + actData[n - 1 + n + n - 1];
    }

    /// <summary>
    /// Fake_gnne_activation.
    /// </summary>
    /// <param name="is16Segments">.</param>
    /// <param name="inputa">1.</param>
    /// <param name="inputb">2.</param>
    /// <param name="hasUninitailizedInput">3.</param>
    /// <param name="outputShape">4.</param>
    /// <param name="actData">5.</param>
    /// <param name="outChannels">6.</param>
    /// <param name="actType">7.</param>
    /// <returns>8.</returns>
    public static Const FakeGnneActivation(bool is16Segments, Tensor inputa, Tensor inputb, bool hasUninitailizedInput, Shape outputShape, ReadOnlySpan<float> actData, int outChannels, GnneActivationType actType)
    {
        var inAShape = new GNNEShape(inputa.Shape.ToValueArray().Select(x => (int)x).ToArray());
        var inBShape = new GNNEShape(inputb.Shape.ToValueArray().Select(x => (int)x).ToArray());
        float[] dataA = inputa.ToArray<float>();
        float[] dataB = inputb.ToArray<float>();
        float[] output = new float[ComputeSize(outputShape)];
        int[] outputShapeArr = new int[outputShape.Rank];
        for (int i = 0; i < outputShape.Rank; i++)
        {
            outputShapeArr[i] = (int)outputShape[i].FixedValue;
        }

        var outShapeGNNE = new GNNEShape(outputShapeArr);

        // fix out_channels
        outChannels = outShapeGNNE[1];

        // one input
        if (hasUninitailizedInput)
        {
            if (inputa.Shape.ToValueArray() == outputShape)
            {
                int imgSize = inAShape[2] * inAShape[3];
                int batchSize = inAShape[1] * imgSize;
                for (int batch = 0; batch < inAShape[0]; batch++)
                {
                    int beginInputAPtr = batch * batchSize;
                    int beginOutputPtr = batch * outChannels * imgSize;
                    for (int oc = 0; oc < outChannels; oc++)
                    {
                        int inputAPtr = beginInputAPtr + (oc * imgSize);
                        int outputPtr = beginOutputPtr + (oc * imgSize);
                        for (int i = 0; i < imgSize; i++)
                        {
                            float oldData = dataA[inputAPtr];
                            float v = ApplyAct1(oldData, actData, oc, is16Segments);
                            output[outputPtr] = v;
                            inputAPtr++;
                            outputPtr++;
                        }
                    }
                }
            }

            // resize
            else
            {
                int inImgSize = inAShape[2] * inAShape[3];
                int outImgSize = outShapeGNNE[2] * outShapeGNNE[3];
                int inBatchSize = inAShape[1] * inImgSize;
                for (int batch = 0; batch < inAShape[0]; batch++)
                {
                    int beginInputAPtr = batch * inBatchSize;
                    int beginOutputPtr = batch * outChannels * outImgSize;
                    for (int oc = 0; oc < outChannels; oc++)
                    {
                        int inputAPtr = beginInputAPtr + (oc * inImgSize);
                        int outputPtr = beginOutputPtr + (oc * outImgSize);
                        for (int outH = 0; outH < outShapeGNNE[2]; outH++)
                        {
                            for (int outW = 0; outW < outShapeGNNE[3]; outW++)
                            {
                                float oldData =
                                    dataA[
                                        inputAPtr + (outH * inAShape[2] / outShapeGNNE[2] * inAShape[3]) +
                                        (outW * inAShape[3] / outShapeGNNE[3])];
                                float v = ApplyAct1(oldData, actData, oc, is16Segments);
                                output[outputPtr + (outH * outShapeGNNE[3]) + outW] = v;
                            }
                        }
                    }
                }
            }
        }

        // two input
        else
        {
            if (inputa.Shape.ToValueArray().SequenceEqual(inputb.Shape.ToValueArray()))
            {
                int imgSize = inAShape[2] * inAShape[3];
                int batchSize = inAShape[1] * imgSize;
                for (int batch = 0; batch < inAShape[0]; batch++)
                {
                    int beginInputAPtr = batch * batchSize;
                    int beginInputBPtr = batch * batchSize;
                    int beginOutputPtr = batch * outChannels * imgSize;
                    for (int oc = 0; oc < outChannels; oc++)
                    {
                        int inputAPtr = beginInputAPtr + (oc * imgSize);
                        int inputBPtr = beginInputBPtr + (oc * imgSize);
                        int outputPtr = beginOutputPtr + (oc * imgSize);
                        for (int i = 0; i < imgSize; i++)
                        {
                            float oldData;
                            if (actType == GnneActivationType.Mul)
                            {
                                oldData = dataA[inputAPtr] * dataB[inputBPtr];
                            }
                            else
                            {
                                oldData = dataA[inputAPtr] + dataB[inputBPtr];
                            }

                            float v = ApplyAct1(oldData, actData, oc, is16Segments);
                            output[outputPtr] = v;
                            inputAPtr++;
                            inputBPtr++;
                            outputPtr++;
                        }
                    }
                }
            }

            // broadcast
            else
            {
                int imgSize = outShapeGNNE[2] * outShapeGNNE[3];
                for (int batch = 0; batch < outShapeGNNE[0]; batch++)
                {
                    int beginOutputPtr = batch * outChannels * imgSize;
                    for (int oc = 0; oc < outChannels; oc++)
                    {
                        int outputPtr = beginOutputPtr + (oc * imgSize);
                        for (int oh = 0; oh < outShapeGNNE[2]; oh++)
                        {
                            for (int ow = 0; ow < outShapeGNNE[3]; ow++)
                            {
                                int inputABatchOff =
                                    inAShape[0] == 1 ? 0 : batch * inAShape[1] * inAShape[2] * inAShape[3];
                                int inputAOcOff = inAShape[1] == 1 ? 0 : oc * inAShape[2] * inAShape[3];
                                int inputAOhOff = inAShape[2] == 1 ? 0 : oh * inAShape[3];
                                int inputAOwOff = inAShape[3] == 1 ? 0 : ow;
                                int inputBBatchOff =
                                    inBShape[0] == 1 ? 0 : batch * inBShape[1] * inBShape[2] * inBShape[3];
                                int inputBOcOff = inBShape[1] == 1 ? 0 : oc * inBShape[2] * inBShape[3];
                                int inputBOhOff = inBShape[2] == 1 ? 0 : oh * inBShape[3];
                                int inputBOwOff = inBShape[3] == 1 ? 0 : ow;
                                float oldData;
                                float inputAData =
                                    inputa.ToArray<float>()[
                                        inputABatchOff + inputAOcOff + inputAOhOff + inputAOwOff];
                                float inputBData =
                                    inputb.ToArray<float>()[
                                        inputBBatchOff + inputBOcOff + inputBOhOff + inputBOwOff];
                                if (actType == GnneActivationType.Mul)
                                {
                                    oldData = inputAData * inputBData;
                                }
                                else
                                {
                                    oldData = inputAData + inputBData;
                                }

                                float v = ApplyAct1(oldData, actData, oc, is16Segments);
                                output[outputPtr] = v;
                                outputPtr++;
                            }
                        }
                    }
                }
            }
        }

        return Const.FromTensor(Tensor.From(output, outputShape.ToValueArray()));
    }

    public static Const Gnne_activation(bool is16Segments, Tensor inputa, Tensor inputb, bool has_uninitailized_input, ReadOnlySpan<float> actData, int outChannels, DeQuantizeParam deQuantizeParamA, DeQuantizeParam deQuantizeParamB, GnneActivationType actType, TensorType outputType, Shape outputShape)
    {
        int[] inAShape = inputa.Shape.ToValueArray().Select(x => (int)x).ToArray();
        float[] dataA = inputa.ToArray<float>();

        // one input
        if (has_uninitailized_input)
        {
            if (inAShape == outputShape)
            {
                int imgSize = inAShape[2] * inAShape[3];
                int batchSize = inAShape[1] * imgSize;
                if (outputType.DType == DataTypes.Int8)
                {
                    sbyte[] output = new sbyte[ComputeSize(outputShape)];
                    for (int batch = 0; batch < inAShape[0]; batch++)
                    {
                        int beginInputAPtr = batch * batchSize;
                        int beginOutputPtr = batch * outChannels * imgSize;
                        for (int oc = 0; oc < outChannels; oc++)
                        {
                            int inputAPtr = beginInputAPtr + (oc * imgSize);
                            int outputPtr = beginOutputPtr + (oc * imgSize);
                            for (int i = 0; i < imgSize; i++)
                            {
                                float oldData = dataA[inputAPtr];
                                oldData -= deQuantizeParamA.ZeroPoint;
                                oldData *= deQuantizeParamA.Scale;
                                sbyte v = (sbyte)ApplyAct1(oldData, actData, oc, is16Segments);
                                output[outputPtr] = v;
                                inputAPtr++;
                                outputPtr++;
                            }
                        }
                    }

                    return Const.FromTensor(Tensor.From(output, inputa.Shape));
                }
                else if (outputType.DType == DataTypes.Float16)
                {
                    var output = new Half[ComputeSize(outputShape)];
                    for (int batch = 0; batch < inAShape[0]; batch++)
                    {
                        int beginInputAPtr = batch * batchSize;
                        int beginOutputPtr = batch * outChannels * imgSize;
                        for (int oc = 0; oc < outChannels; oc++)
                        {
                            int inputAPtr = beginInputAPtr + (oc * imgSize);
                            int outputPtr = beginOutputPtr + (oc * imgSize);
                            for (int i = 0; i < imgSize; i++)
                            {
                                float oldData = dataA[inputAPtr];
                                oldData -= deQuantizeParamA.ZeroPoint;
                                oldData *= deQuantizeParamA.Scale;
                                var v = (Half)ApplyAct1(oldData, actData, oc, is16Segments);
                                output[outputPtr] = v;
                                inputAPtr++;
                                outputPtr++;
                            }
                        }
                    }

                    return Const.FromTensor(Tensor.From(output, inputa.Shape));
                }
                else if (outputType.DType == DataTypes.UInt8)
                {
                    byte[] output = new byte[ComputeSize(outputShape)];
                    for (int batch = 0; batch < inAShape[0]; batch++)
                    {
                        int beginInputAPtr = batch * batchSize;
                        int beginOutputPtr = batch * outChannels * imgSize;
                        for (int oc = 0; oc < outChannels; oc++)
                        {
                            int inputAPtr = beginInputAPtr + (oc * imgSize);
                            int outputPtr = beginOutputPtr + (oc * imgSize);
                            for (int i = 0; i < imgSize; i++)
                            {
                                float oldData = dataA[inputAPtr];
                                oldData -= deQuantizeParamA.ZeroPoint;
                                oldData *= deQuantizeParamA.Scale;
                                byte v = (byte)ApplyAct1(oldData, actData, oc, is16Segments);
                                output[outputPtr] = v;
                                inputAPtr++;
                                outputPtr++;
                            }
                        }
                    }

                    return Const.FromTensor(Tensor.From(output, inputa.Shape));
                }
                else
                {
                    short[] output = new short[ComputeSize(outputShape)];
                    for (int batch = 0; batch < inAShape[0]; batch++)
                    {
                        int beginInputAPtr = batch * batchSize;
                        int beginOutputPtr = batch * outChannels * imgSize;
                        for (int oc = 0; oc < outChannels; oc++)
                        {
                            int inputAPtr = beginInputAPtr + (oc * imgSize);
                            int outputPtr = beginOutputPtr + (oc * imgSize);
                            for (int i = 0; i < imgSize; i++)
                            {
                                float oldData = dataA[inputAPtr];
                                oldData -= deQuantizeParamA.ZeroPoint;
                                oldData *= deQuantizeParamA.Scale;
                                short v = (short)ApplyAct1(oldData, actData, oc, is16Segments);
                                output[outputPtr] = v;
                                inputAPtr++;
                                outputPtr++;
                            }
                        }
                    }

                    return Const.FromTensor(Tensor.From(output, inputa.Shape));
                }
            }

            // resize
            else
            {
                int inImgSize = inAShape[2] * inAShape[3];
                int outImgSize = (int)(outputShape[2].FixedValue * outputShape[3].FixedValue);
                int inBatchSize = inAShape[1] * inImgSize;
                if (outputType.DType == DataTypes.Int8)
                {
                    sbyte[] output = new sbyte[ComputeSize(outputShape)];
                    for (int batch = 0; batch < inAShape[0]; batch++)
                    {
                        int beginInputAPtr = batch * inBatchSize;
                        int beginOutputPtr = batch * outChannels * outImgSize;
                        for (int oc = 0; oc < outChannels; oc++)
                        {
                            int inputAPtr = beginInputAPtr + (oc * inImgSize);
                            int outputPtr = beginOutputPtr + (oc * outImgSize);
                            for (int outH = 0; outH < outputShape[2].FixedValue; outH++)
                            {
                                for (int outW = 0; outW < outputShape[3].FixedValue; outW++)
                                {
                                    float oldData = dataA[inputAPtr +
                                                          (outH * inAShape[2] / outputShape[2].FixedValue *
                                                           inAShape[3]) +
                                                          (outW * inAShape[3] / outputShape[3].FixedValue)];
                                    oldData -= deQuantizeParamA.ZeroPoint;
                                    oldData *= deQuantizeParamA.Scale;
                                    sbyte v = (sbyte)ApplyAct1(oldData, actData, oc, is16Segments);
                                    output[outputPtr + (outH * outputShape[3].FixedValue) + outW] = v;
                                }
                            }
                        }
                    }

                    return Const.FromTensor(Tensor.From(output, inputa.Shape));
                }
                else if (outputType.DType == DataTypes.Float16)
                {
                    var output = new Half[ComputeSize(outputShape)];
                    for (int batch = 0; batch < inAShape[0]; batch++)
                    {
                        int beginInputAPtr = batch * inBatchSize;
                        int beginOutputPtr = batch * outChannels * outImgSize;
                        for (int oc = 0; oc < outChannels; oc++)
                        {
                            int inputAPtr = beginInputAPtr + (oc * inImgSize);
                            int outputPtr = beginOutputPtr + (oc * outImgSize);
                            for (int outH = 0; outH < outputShape[2].FixedValue; outH++)
                            {
                                for (int outW = 0; outW < outputShape[3].FixedValue; outW++)
                                {
                                    float oldData =
                                        dataA[
                                            inputAPtr +
                                            (outH * inAShape[2] / outputShape[2].FixedValue * inAShape[3]) +
                                            (outW * inAShape[3] / outputShape[3].FixedValue)];
                                    oldData -= deQuantizeParamA.ZeroPoint;
                                    oldData *= deQuantizeParamA.Scale;
                                    var v = (Half)ApplyAct1(oldData, actData, oc, is16Segments);
                                    output[outputPtr + (outH * outputShape[3].FixedValue) + outW] = v;
                                }
                            }
                        }
                    }

                    return Const.FromTensor(Tensor.From(output, inputa.Shape));
                }
                else if (outputType.DType == DataTypes.UInt8)
                {
                    byte[] output = new byte[ComputeSize(outputShape)];
                    for (int batch = 0; batch < inAShape[0]; batch++)
                    {
                        int beginInputAPtr = batch * inBatchSize;
                        int beginOutputPtr = batch * outChannels * outImgSize;
                        for (int oc = 0; oc < outChannels; oc++)
                        {
                            int inputAPtr = beginInputAPtr + (oc * inImgSize);
                            int outputPtr = beginOutputPtr + (oc * outImgSize);
                            for (int outH = 0; outH < outputShape[2].FixedValue; outH++)
                            {
                                for (int outW = 0; outW < outputShape[3].FixedValue; outW++)
                                {
                                    float oldData = dataA[inputAPtr +
                                                          (outH * inAShape[2] / outputShape[2].FixedValue *
                                                           inAShape[3]) +
                                                          (outW * inAShape[3] / outputShape[3].FixedValue)];
                                    oldData -= deQuantizeParamA.ZeroPoint;
                                    oldData *= deQuantizeParamA.Scale;
                                    byte v = (byte)ApplyAct1(oldData, actData, oc, is16Segments);
                                    output[outputPtr + (outH * outputShape[3].FixedValue) + outW] = v;
                                }
                            }
                        }
                    }

                    return Const.FromTensor(Tensor.From(output, inputa.Shape));
                }
                else
                {
                    short[] output = new short[ComputeSize(outputShape)];
                    for (int batch = 0; batch < inAShape[0]; batch++)
                    {
                        int beginInputAPtr = batch * inBatchSize;
                        int beginOutputPtr = batch * outChannels * outImgSize;
                        for (int oc = 0; oc < outChannels; oc++)
                        {
                            int inputAPtr = beginInputAPtr + (oc * inImgSize);
                            int outputPtr = beginOutputPtr + (oc * outImgSize);
                            for (int outH = 0; outH < outputShape[2].FixedValue; outH++)
                            {
                                for (int outW = 0; outW < outputShape[3].FixedValue; outW++)
                                {
                                    float oldData = dataA[inputAPtr +
                                                          (outH * inAShape[2] / outputShape[2].FixedValue *
                                                           inAShape[3]) +
                                                          (outW * inAShape[3] / outputShape[3].FixedValue)];
                                    oldData -= deQuantizeParamA.ZeroPoint;
                                    oldData *= deQuantizeParamA.Scale;
                                    short v = (short)ApplyAct1(oldData, actData, oc, is16Segments);
                                    output[outputPtr + (outH * outputShape[3].FixedValue) + outW] = v;
                                }
                            }
                        }
                    }

                    return Const.FromTensor(Tensor.From(output, inputa.Shape));
                }
            }
        }

        // two inputs
        else
        {
            int[] inBShape = inputb.Shape.ToValueArray().Select(x => (int)x).ToArray();
            float[]? dataB = inputb.ToArray<float>();
            if (inAShape == inBShape)
            {
                int imgSize = inAShape[2] * inAShape[3];
                int batchSize = inAShape[1] * imgSize;

                if (outputType.DType == DataTypes.Int8)
                {
                    sbyte[] output = new sbyte[ComputeSize(outputShape)];
                    for (int batch = 0; batch < inAShape[0]; batch++)
                    {
                        int beginInputAPtr = batch * batchSize;
                        int beginInputBPtr = batch * batchSize;
                        int beginOutputPtr = batch * outChannels * imgSize;
                        for (int oc = 0; oc < outChannels; oc++)
                        {
                            int inputAPtr = beginInputAPtr + (oc * imgSize);
                            int inputBPtr = beginInputBPtr + (oc * imgSize);
                            int outputPtr = beginOutputPtr + (oc * imgSize);
                            for (int i = 0; i < imgSize; i++)
                            {
                                float oldData;
                                if (actType == GnneActivationType.Mul)
                                {
                                    oldData = (dataA[inputAPtr] - deQuantizeParamA.ZeroPoint) *
                                               deQuantizeParamA.Scale *
                                               (dataB[inputBPtr] - deQuantizeParamB.ZeroPoint) *
                                               deQuantizeParamB.Scale;
                                }
                                else
                                {
                                    oldData = ((dataA[inputAPtr] - deQuantizeParamA.ZeroPoint) *
                                               deQuantizeParamA.Scale) +
                                               ((dataB[inputBPtr] - deQuantizeParamB.ZeroPoint) *
                                               deQuantizeParamB.Scale);
                                }

                                sbyte v = (sbyte)ApplyAct1(oldData, actData, oc, is16Segments);
                                output[outputPtr] = v;
                                inputAPtr++;
                                inputBPtr++;
                                outputPtr++;
                            }
                        }
                    }

                    return Const.FromTensor(Tensor.From(output, inputa.Shape));
                }
                else if (outputType.DType == DataTypes.UInt8)
                {
                    byte[] output = new byte[ComputeSize(outputShape)];
                    for (int batch = 0; batch < inAShape[0]; batch++)
                    {
                        int beginInputAPtr = batch * batchSize;
                        int beginInputBPtr = batch * batchSize;
                        int beginOutputPtr = batch * outChannels * imgSize;
                        for (int oc = 0; oc < outChannels; oc++)
                        {
                            int inputAPtr = beginInputAPtr + (oc * imgSize);
                            int inputBPtr = beginInputBPtr + (oc * imgSize);
                            int outputPtr = beginOutputPtr + (oc * imgSize);
                            for (int i = 0; i < imgSize; i++)
                            {
                                float oldData;
                                if (actType == GnneActivationType.Mul)
                                {
                                    oldData = (dataA[inputAPtr] - deQuantizeParamA.ZeroPoint) *
                                               deQuantizeParamA.Scale *
                                               (dataB[inputBPtr] - deQuantizeParamB.ZeroPoint) *
                                               deQuantizeParamB.Scale;
                                }
                                else
                                {
                                    oldData = ((dataA[inputAPtr] - deQuantizeParamA.ZeroPoint) *
                                               deQuantizeParamA.Scale) +
                                               ((dataB[inputBPtr] - deQuantizeParamB.ZeroPoint) *
                                               deQuantizeParamB.Scale);
                                }

                                byte v = (byte)ApplyAct1(oldData, actData, oc, is16Segments);
                                output[outputPtr] = v;
                                inputAPtr++;
                                inputBPtr++;
                                outputPtr++;
                            }
                        }
                    }

                    return Const.FromTensor(Tensor.From(output, inputa.Shape));
                }
                else if (outputType.DType == DataTypes.Float16)
                {
                    var output = new Half[ComputeSize(outputShape)];
                    for (int batch = 0; batch < inAShape[0]; batch++)
                    {
                        int beginInputAPtr = batch * batchSize;
                        int beginInputBPtr = batch * batchSize;
                        int beginOutputPtr = batch * outChannels * imgSize;
                        for (int oc = 0; oc < outChannels; oc++)
                        {
                            int inputAPtr = beginInputAPtr + (oc * imgSize);
                            int inputBPtr = beginInputBPtr + (oc * imgSize);
                            int outputPtr = beginOutputPtr + (oc * imgSize);
                            for (int i = 0; i < imgSize; i++)
                            {
                                float oldData;
                                if (actType == GnneActivationType.Mul)
                                {
                                    oldData = (dataA[inputAPtr] - deQuantizeParamA.ZeroPoint) *
                                               deQuantizeParamA.Scale *
                                               (dataB[inputBPtr] - deQuantizeParamB.ZeroPoint) *
                                               deQuantizeParamB.Scale;
                                }
                                else
                                {
                                    oldData = ((dataA[inputAPtr] - deQuantizeParamA.ZeroPoint) *
                                               deQuantizeParamA.Scale) +
                                               ((dataB[inputBPtr] - deQuantizeParamB.ZeroPoint) *
                                               deQuantizeParamB.Scale);
                                }

                                var v = (Half)ApplyAct1(oldData, actData, oc, is16Segments);
                                output[outputPtr] = v;
                                inputAPtr++;
                                inputBPtr++;
                                outputPtr++;
                            }
                        }
                    }

                    return Const.FromTensor(Tensor.From(output, inputa.Shape));
                }

                if (outputType.DType == DataTypes.Int16)
                {
                    short[] output = new short[ComputeSize(outputShape)];
                    for (int batch = 0; batch < inAShape[0]; batch++)
                    {
                        int beginInputAPtr = batch * batchSize;
                        int beginInputBPtr = batch * batchSize;
                        int beginOutputPtr = batch * outChannels * imgSize;
                        for (int oc = 0; oc < outChannels; oc++)
                        {
                            int inputAPtr = beginInputAPtr + (oc * imgSize);
                            int inputBPtr = beginInputBPtr + (oc * imgSize);
                            int outputPtr = beginOutputPtr + (oc * imgSize);
                            for (int i = 0; i < imgSize; i++)
                            {
                                float oldData;
                                if (actType == GnneActivationType.Mul)
                                {
                                    oldData = (dataA[inputAPtr] - deQuantizeParamA.ZeroPoint) *
                                               deQuantizeParamA.Scale *
                                               (dataB[inputBPtr] - deQuantizeParamB.ZeroPoint) *
                                               deQuantizeParamB.Scale;
                                }
                                else
                                {
                                    oldData = ((dataA[inputAPtr] - deQuantizeParamA.ZeroPoint) *
                                               deQuantizeParamA.Scale) +
                                               ((dataB[inputBPtr] - deQuantizeParamB.ZeroPoint) *
                                               deQuantizeParamB.Scale);
                                }

                                short v = (short)ApplyAct1(oldData, actData, oc, is16Segments);
                                output[outputPtr] = v;
                                inputAPtr++;
                                inputBPtr++;
                                outputPtr++;
                            }
                        }
                    }

                    return Const.FromTensor(Tensor.From(output, inputa.Shape));
                }
            }

            // broadcast
            else
            {
                int imgSize = (int)(outputShape[2].FixedValue * outputShape[3].FixedValue);
                if (outputType.DType == DataTypes.Int8)
                {
                    sbyte[] output = new sbyte[ComputeSize(outputShape)];
                    for (int batch = 0; batch < outputShape[0].FixedValue; batch++)
                    {
                        int beginOutputPtr = batch * outChannels * imgSize;
                        for (int oc = 0; oc < outChannels; oc++)
                        {
                            int outputPtr = beginOutputPtr + (oc * imgSize);
                            for (int oh = 0; oh < outputShape[2].FixedValue; oh++)
                            {
                                for (int ow = 0; ow < outputShape[3].FixedValue; ow++)
                                {
                                    int inputABatchOff = inAShape[0] == 1 ? 0 : batch * inAShape[1] * inAShape[2] * inAShape[3];
                                    int inputAOcOff = inAShape[1] == 1 ? 0 : oc * inAShape[2] * inAShape[3];
                                    int inputAOhOff = inAShape[2] == 1 ? 0 : oh * inAShape[3];
                                    int inputAOwOff = inAShape[3] == 1 ? 0 : ow;
                                    int inputBBatchOff =
                                        inBShape[0] == 1 ? 0 : batch * inBShape[1] * inBShape[2] * inBShape[3];
                                    int inputBOcOff = inBShape[1] == 1 ? 0 : oc * inBShape[2] * inBShape[3];
                                    int inputBOhOff = inBShape[2] == 1 ? 0 : oh * inBShape[3];
                                    int inputBOwOff = inBShape[3] == 1 ? 0 : ow;
                                    float oldData;
                                    float inputAData =
                                        inputa.ToArray<float>()[
                                            inputABatchOff + inputAOcOff + inputAOhOff + inputAOwOff];
                                    float inputBData =
                                        inputb.ToArray<float>()[
                                            inputBBatchOff + inputBOcOff + inputBOhOff + inputBOwOff];
                                    if (actType == GnneActivationType.Mul)
                                    {
                                        oldData = (inputAData - deQuantizeParamA.ZeroPoint) *
                                                   deQuantizeParamA.Scale *
                                                   (inputBData - deQuantizeParamB.ZeroPoint) * deQuantizeParamB.Scale;
                                    }
                                    else
                                    {
                                        oldData =
                                            ((inputAData - deQuantizeParamA.ZeroPoint) * deQuantizeParamA.Scale) +
                                            ((inputBData - deQuantizeParamB.ZeroPoint) * deQuantizeParamB.Scale);
                                    }

                                    sbyte v = (sbyte)ApplyAct1(oldData, actData, oc, is16Segments);
                                    output[outputPtr] = v;
                                    outputPtr++;
                                }
                            }
                        }
                    }

                    return Const.FromTensor(Tensor.From(output, inputa.Shape));
                }

                if (outputType.DType == DataTypes.Float16)
                {
                    var output = new Half[ComputeSize(outputShape)];
                    for (int batch = 0; batch < outputShape[0].FixedValue; batch++)
                    {
                        int beginOutputPtr = batch * outChannels * imgSize;
                        for (int oc = 0; oc < outChannels; oc++)
                        {
                            int outputPtr = beginOutputPtr + (oc * imgSize);
                            for (int oh = 0; oh < outputShape[2].FixedValue; oh++)
                            {
                                for (int ow = 0; ow < outputShape[3].FixedValue; ow++)
                                {
                                    int inputABatchOff =
                                        inAShape[0] == 1 ? 0 : batch * inAShape[1] * inAShape[2] * inAShape[3];
                                    int inputAOcOff = inAShape[1] == 1 ? 0 : oc * inAShape[2] * inAShape[3];
                                    int inputAOhOff = inAShape[2] == 1 ? 0 : oh * inAShape[3];
                                    int inputAOwOff = inAShape[3] == 1 ? 0 : ow;
                                    int inputBBatchOff =
                                        inBShape[0] == 1 ? 0 : batch * inBShape[1] * inBShape[2] * inBShape[3];
                                    int inputBOcOff = inBShape[1] == 1 ? 0 : oc * inBShape[2] * inBShape[3];
                                    int inputBOhOff = inBShape[2] == 1 ? 0 : oh * inBShape[3];
                                    int inputBOwOff = inBShape[3] == 1 ? 0 : ow;
                                    float oldData;
                                    float inputAData =
                                        inputa.ToArray<float>()[
                                            inputABatchOff + inputAOcOff + inputAOhOff + inputAOwOff];
                                    float inputBData =
                                        inputb.ToArray<float>()[
                                            inputBBatchOff + inputBOcOff + inputBOhOff + inputBOwOff];
                                    if (actType == GnneActivationType.Mul)
                                    {
                                        oldData = (inputAData - deQuantizeParamA.ZeroPoint) *
                                                   deQuantizeParamA.Scale *
                                                   (inputBData - deQuantizeParamB.ZeroPoint) * deQuantizeParamB.Scale;
                                    }
                                    else
                                    {
                                        oldData =
                                            ((inputAData - deQuantizeParamA.ZeroPoint) * deQuantizeParamA.Scale) +
                                            ((inputBData - deQuantizeParamB.ZeroPoint) * deQuantizeParamB.Scale);
                                    }

                                    var v = (Half)ApplyAct1(oldData, actData, oc, is16Segments);
                                    output[outputPtr] = v;
                                    outputPtr++;
                                }
                            }
                        }
                    }

                    return Const.FromTensor(Tensor.From(output, inputa.Shape));
                }

                if (outputType.DType == DataTypes.UInt8)
                {
                    byte[] output = new byte[ComputeSize(outputShape)];
                    for (int batch = 0; batch < outputShape[0].FixedValue; batch++)
                    {
                        int beginOutputPtr = batch * outChannels * imgSize;
                        for (int oc = 0; oc < outChannels; oc++)
                        {
                            int outputPtr = beginOutputPtr + (oc * imgSize);
                            for (int oh = 0; oh < outputShape[2].FixedValue; oh++)
                            {
                                for (int ow = 0; ow < outputShape[3].FixedValue; ow++)
                                {
                                    int inputABatchOff =
                                        inAShape[0] == 1 ? 0 : batch * inAShape[1] * inAShape[2] * inAShape[3];
                                    int inputAOcOff = inAShape[1] == 1 ? 0 : oc * inAShape[2] * inAShape[3];
                                    int inputAOhOff = inAShape[2] == 1 ? 0 : oh * inAShape[3];
                                    int inputAOwOff = inAShape[3] == 1 ? 0 : ow;
                                    int inputBBatchOff =
                                        inBShape[0] == 1 ? 0 : batch * inBShape[1] * inBShape[2] * inBShape[3];
                                    int inputBOcOff = inBShape[1] == 1 ? 0 : oc * inBShape[2] * inBShape[3];
                                    int inputBOhOff = inBShape[2] == 1 ? 0 : oh * inBShape[3];
                                    int inputBOwOff = inBShape[3] == 1 ? 0 : ow;
                                    float oldData;
                                    float inputAData =
                                        inputa.ToArray<float>()[
                                            inputABatchOff + inputAOcOff + inputAOhOff + inputAOwOff];
                                    float inputBData =
                                        inputb.ToArray<float>()[
                                            inputBBatchOff + inputBOcOff + inputBOhOff + inputBOwOff];
                                    if (actType == GnneActivationType.Mul)
                                    {
                                        oldData = (inputAData - deQuantizeParamA.ZeroPoint) *
                                                   deQuantizeParamA.Scale *
                                                   (inputBData - deQuantizeParamB.ZeroPoint) * deQuantizeParamB.Scale;
                                    }
                                    else
                                    {
                                        oldData =
                                            ((inputAData - deQuantizeParamA.ZeroPoint) * deQuantizeParamA.Scale) +
                                            ((inputBData - deQuantizeParamB.ZeroPoint) * deQuantizeParamB.Scale);
                                    }

                                    byte v = (byte)ApplyAct1(oldData, actData, oc, is16Segments);
                                    output[outputPtr] = v;
                                    outputPtr++;
                                }
                            }
                        }
                    }

                    return Const.FromTensor(Tensor.From(output, inputa.Shape));
                }

                if (outputType.DType == DataTypes.Int16)
                {
                    short[] output = new short[ComputeSize(outputShape)];
                    for (int batch = 0; batch < outputShape[0].FixedValue; batch++)
                    {
                        int beginOutputPtr = batch * outChannels * imgSize;
                        for (int oc = 0; oc < outChannels; oc++)
                        {
                            int outputPtr = beginOutputPtr + (oc * imgSize);
                            for (int oh = 0; oh < outputShape[2].FixedValue; oh++)
                            {
                                for (int ow = 0; ow < outputShape[3].FixedValue; ow++)
                                {
                                    int inputABatchOff =
                                        inAShape[0] == 1 ? 0 : batch * inAShape[1] * inAShape[2] * inAShape[3];
                                    int inputAOcOff = inAShape[1] == 1 ? 0 : oc * inAShape[2] * inAShape[3];
                                    int inputAOhOff = inAShape[2] == 1 ? 0 : oh * inAShape[3];
                                    int inputAOwOff = inAShape[3] == 1 ? 0 : ow;
                                    int inputBBatchOff =
                                        inBShape[0] == 1 ? 0 : batch * inBShape[1] * inBShape[2] * inBShape[3];
                                    int inputBOcOff = inBShape[1] == 1 ? 0 : oc * inBShape[2] * inBShape[3];
                                    int inputBOhOff = inBShape[2] == 1 ? 0 : oh * inBShape[3];
                                    int inputBOwOff = inBShape[3] == 1 ? 0 : ow;
                                    float oldData;
                                    float inputAData =
                                        inputa.ToArray<float>()[
                                            inputABatchOff + inputAOcOff + inputAOhOff + inputAOwOff];
                                    float inputBData =
                                        inputb.ToArray<float>()[
                                            inputBBatchOff + inputBOcOff + inputBOhOff + inputBOwOff];
                                    if (actType == GnneActivationType.Mul)
                                    {
                                        oldData = (inputAData - deQuantizeParamA.ZeroPoint) *
                                                   deQuantizeParamA.Scale *
                                                   (inputBData - deQuantizeParamB.ZeroPoint) * deQuantizeParamB.Scale;
                                    }
                                    else
                                    {
                                        oldData =
                                            ((inputAData - deQuantizeParamA.ZeroPoint) * deQuantizeParamA.Scale) +
                                            ((inputBData - deQuantizeParamB.ZeroPoint) * deQuantizeParamB.Scale);
                                    }

                                    short v = (short)ApplyAct1(oldData, actData, oc, is16Segments);
                                    output[outputPtr] = v;
                                    outputPtr++;
                                }
                            }
                        }
                    }

                    return Const.FromTensor(Tensor.From(output, inputa.Shape));
                }
            }
        }

        return null!;
    }

    public static float ApplyActivation(float value, float[] clamp)
    {
        return System.Math.Clamp(value, clamp[0], clamp[1]);
    }

    public static float Tanh(float x, Tensor<Half> segFittingParamGt)
    {
        for (int i = 0; i < 15; i++)
        {
            if ((Half)x < segFittingParamGt.ToArray()[i])
            {
                return ((float)segFittingParamGt.ToArray()[15 + i] * x) +
                       (float)segFittingParamGt.ToArray()[15 + 16 + i];
            }
        }

        return ((float)segFittingParamGt.ToArray()[15 + 15] * x) + (float)segFittingParamGt.ToArray()[15 + 16 + 15];
    }

    public static float Sigmoid(float x, Tensor<Half> segFittingParamFt)
    {
        for (int i = 0; i < 15; i++)
        {
            if ((Half)x < segFittingParamFt.ToArray()[i])
            {
                return ((float)segFittingParamFt.ToArray()[15 + i] * x) +
                       (float)segFittingParamFt.ToArray()[15 + 16 + i];
            }
        }

        return ((float)segFittingParamFt.ToArray()[15 + 15] * x) + (float)segFittingParamFt.ToArray()[15 + 16 + 15];
    }

    public static Tensor<float>[] FakeGnneLstm(Tensor<float> input, Tensor<float> wXc, Tensor<Half> actXc, Tensor<float> wRc, Tensor<Half> actRc, Tensor<float> initH, Tensor<float> initC, Tensor<Half> segFittingParamFt, Tensor<Half> segFittingParamGt, Tensor<float> output, Tensor<float> outputH, Tensor<float> outputC, LSTMDirection direction, int outputSize)
    {
        float[] outputHTmp = new float[ComputeSize(initH.Shape)];
        float[] initHArray = initH.ToArray();
        float[] initCArray = initC.ToArray();
        float[] outputArray = output.ToArray();
        float[] outputHArray = outputH.ToArray();
        float[] outputCArray = outputC.ToArray();
        for (int i = 0; i < outputHTmp.Length; i++)
        {
            outputHTmp[i] = initHArray[i];
        }

        float[] outputCTmp = new float[ComputeSize(initC.Shape)];
        for (int i = 0; i < outputCTmp.Length; i++)
        {
            outputCTmp[i] = initCArray[i];
        }

        var seqLenLoop = new List<int>();
        for (int i = 0; i < input.Shape[1].FixedValue; i++)
        {
            seqLenLoop.Add(i);
        }

        if (direction == LSTMDirection.Reverse)
        {
            seqLenLoop.Reverse();
        }

        for (int d = 0; d < output.Shape[1].FixedValue; d++)
        {
            if (d == 1)
            {
                seqLenLoop.Reverse();
            }

            for (int b = 0; b < input.Shape[2].FixedValue; b++)
            {
                for (int l = 0; l < seqLenLoop.Count; l++)
                {
                    float[] outMul1 = new float[output.Shape[3].FixedValue * 4];
                    float[] outMul2 = new float[output.Shape[3].FixedValue * 4];
                    for (int o = 0; o < outMul1.Length; o++)
                    {
                        float[] inMul1 = new float[input.Shape[3].FixedValue];
                        float[] inMul2 = new float[input.Shape[3].FixedValue];
                        Array.Copy(input.ToArray(), (b * input.Shape[3].FixedValue) + (seqLenLoop[l] * input.Shape[2].FixedValue * input.Shape[3].FixedValue), inMul1, 0, input.Shape[3].FixedValue);
                        Array.Copy(wXc.ToArray(), (o * wXc.Shape[3].FixedValue) + (d * wXc.Shape[2].FixedValue * wXc.Shape[3].FixedValue), inMul2, 0, input.Shape[3].FixedValue);
                        outMul1[o] = OrtKI.MatMul(inMul1, inMul2).ToArray<float>()[0];
                        int base1 = (int)(d * wXc.Shape[2].FixedValue);
                        outMul1[o] = ApplyAct0(outMul1[o], actXc.ToArray<Half>(), base1 + o, 0);
                        float[] inMul3 = new float[input.Shape[3].FixedValue];
                        float[] inMul4 = new float[input.Shape[3].FixedValue];
                        Array.Copy(outputHTmp.ToArray(), (b * output.Shape[3].FixedValue) + (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue), inMul3, 0, output.Shape[3].FixedValue);
                        Array.Copy(wRc.ToArray(), (o * wRc.Shape[3].FixedValue) + (d * wRc.Shape[2].FixedValue * wRc.Shape[3].FixedValue), inMul4, 0, output.Shape[3].FixedValue);
                        outMul2[o] = OrtKI.MatMul(inMul3, inMul4).ToArray<float>()[0];
                        base1 = (int)(d * wRc.Shape[2].FixedValue);
                        outMul2[o] = ApplyAct0(outMul2[o], actRc.ToArray<Half>(), base1 + o, 0);
                        outMul1[o] += outMul2[o];
                    }

                    // ft = sigmoid(g[2])
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outMul1[o + (output.Shape[3].FixedValue * 2)] = Sigmoid(outMul1[o + (output.Shape[3].FixedValue * 2)], segFittingParamFt);
                    }

                    // ct = init_c * ft
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outMul1[o + (output.Shape[3].FixedValue * 2)] *= outputCTmp[o + (b * output.Shape[3].FixedValue) + (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue)];
                    }

                    // it = sigmoid(g[0])
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outMul1[o + (output.Shape[3].FixedValue * 0)] = Sigmoid(outMul1[o + (output.Shape[3].FixedValue * 0)], segFittingParamFt);
                    }

                    // c_t = tanh(g[3])
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outMul1[o + (output.Shape[3].FixedValue * 3)] = Tanh(outMul1[o + (output.Shape[3].FixedValue * 3)], segFittingParamGt);
                    }

                    // c_t_it = it * c_t
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outMul1[o + (output.Shape[3].FixedValue * 0)] *= outMul1[o + (output.Shape[3].FixedValue * 3)];
                    }

                    // ct = ct + c_t_it
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outputCTmp[o + (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue)] = outMul1[o + (output.Shape[3].FixedValue * 2)] + outMul1[o + (output.Shape[3].FixedValue * 0)];
                    }

                    // ot = sigmoid(g[1])
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outMul1[o + (output.Shape[3].FixedValue * 1)] = Sigmoid(outMul1[o + (output.Shape[3].FixedValue * 1)], segFittingParamFt);
                    }

                    // tanh_ct = tanh(ct_o)
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outMul1[o + (output.Shape[3].FixedValue * 3)] = Tanh(outputCTmp[o + (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue)], segFittingParamGt);
                    }

                    // ht = ot * tanh_ct
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outputHTmp[o + (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue)] = outMul1[o + (output.Shape[3].FixedValue * 3)] * outMul1[o + (output.Shape[3].FixedValue * 1)];
                    }

                    for (int i = 0; i < output.Shape[3].FixedValue; i++)
                    {
                        outputArray[
                                (b * output.Shape[3].FixedValue) +
                                (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + (seqLenLoop[l] *
                                output.Shape[1].FixedValue * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + i] =
                            outputHTmp[(d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + i];
                    }

                    if (l == seqLenLoop.Count - 1)
                    {
                        for (int i = 0; i < output.Shape[3].FixedValue; i++)
                        {
                            outputHArray[
                                    (b * output.Shape[3].FixedValue) +
                                    (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + i] =
                                outputHTmp[(d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + i];
                        }

                        for (int i = 0; i < output.Shape[3].FixedValue; i++)
                        {
                            outputCArray[
                                    (b * output.Shape[3].FixedValue) +
                                    (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + i] =
                                outputCTmp[(d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + i];
                        }
                    }
                }
            }
        }

        output = Tensor.From(outputArray, output.Shape);
        outputC = Tensor.From(outputCArray, outputC.Shape);
        outputH = Tensor.From(outputHArray, outputH.Shape);
        if (outputSize == 1)
        {
            return new[] { output };
        }
        else if (outputSize == 2)
        {
            return new[] { output, outputH };
        }
        else if (outputSize == 3)
        {
            return new[] { output, outputH, outputC };
        }

        return null!;
    }

    public static Tensor[] GnneLstmImpl(DataType dataTypeO, DataType dataTypeOH, Tensor<float> input, Tensor<float> wXc, Tensor<Half> actXc, Tensor<float> wRc, Tensor<Half> actRc0, Tensor<Half> actRc1, Tensor<float> initH, Tensor<float> initC, Tensor<Half> segFittingParamFt, Tensor<Half> segFittingParamGt, Tensor output, Tensor outputH, Tensor outputC, LSTMDirection direction, Tensor<Half> wXcQarg, Tensor<Half> wRcQarg, Tensor<Half> binAct, Tensor<Half> binQAct, int ifDeqBias, int hDeqBias0, int hDeqBias1, int xcShiftBits, int rcShiftBits0, int rcShiftBits1, int outputSize)
    {
        float[] outputHTmp = new float[ComputeSize(initH.Shape)];
        float[] initHArray = initH.ToArray();
        float[] initCArray = initC.ToArray();
        var outputCArray = outputC.ToArray<Half>();
        var outputArrayHalf = new Half[ComputeSize(output.Shape)];
        sbyte[] outputArrayI8 = new sbyte[ComputeSize(output.Shape)];
        byte[] outputArrayU8 = new byte[ComputeSize(output.Shape)];
        short[] outputArrayI16 = new short[ComputeSize(output.Shape)];
        var outputHArrayHalf = new Half[ComputeSize(outputH.Shape)];
        sbyte[] outputHArrayI8 = new sbyte[ComputeSize(outputH.Shape)];
        byte[]? outputHArrayU8 = new byte[ComputeSize(outputH.Shape)];
        short[] outputHArrayI16 = new short[ComputeSize(outputH.Shape)];
        for (int i = 0; i < outputHTmp.Length; i++)
        {
            outputHTmp[i] = initHArray[i];
        }

        float[] outputCTmp = new float[ComputeSize(initC.Shape)];
        for (int i = 0; i < outputCTmp.Length; i++)
        {
            outputCTmp[i] = initCArray[i];
        }

        var seqLenLoop = new List<int>();
        for (int i = 0; i < input.Shape[1].FixedValue; i++)
        {
            seqLenLoop.Add(i);
        }

        if (direction == LSTMDirection.Reverse)
        {
            seqLenLoop.Reverse();
        }

        for (int d = 0; d < output.Shape[1].FixedValue; d++)
        {
            if (d == 1)
            {
                seqLenLoop.Reverse();
            }

            for (int b = 0; b < input.Shape[2].FixedValue; b++)
            {
                for (int l = 0; l < seqLenLoop.Count; l++)
                {
                    float[] outMul1 = new float[output.Shape[3].FixedValue * 4];
                    float[] outMul2 = new float[output.Shape[3].FixedValue * 4];
                    for (int o = 0; o < output.Shape[3].FixedValue * 4; o++)
                    {
                        float[] inMul1 = new float[input.Shape[3].FixedValue];
                        float[] inMul2 = new float[input.Shape[3].FixedValue];
                        Array.Copy(input.ToArray(), (b * input.Shape[3].FixedValue) + (seqLenLoop[l] * input.Shape[2].FixedValue * input.Shape[3].FixedValue), inMul1, 0, input.Shape[3].FixedValue);
                        Array.Copy(wXc.ToArray(), (o * wXc.Shape[3].FixedValue) + (d * wXc.Shape[2].FixedValue * wXc.Shape[3].FixedValue), inMul2, 0, input.Shape[3].FixedValue);
                        var iEnumerable1 = inMul1.Select((_, i) => inMul1[i] -= ifDeqBias);
                        int o1 = o;
                        var iEnumerable2 = inMul2.Select((_, i) => inMul2[i] -= (float)wXcQarg.ToArray()[o1]);
                        outMul1[o] = OrtKI.MatMul(inMul1, inMul2).ToArray<float>()[0];
                        int base1 = (int)(d * wXc.Shape[2].FixedValue);
                        outMul1[o] = ApplyAct0(outMul1[o] / (1 << xcShiftBits), actXc.ToArray<Half>(), base1 + o, 0);
                        float[] inMul3 = new float[input.Shape[3].FixedValue];
                        float[] inMul4 = new float[input.Shape[3].FixedValue];
                        Array.Copy(outputHTmp.ToArray(), (b * output.Shape[3].FixedValue) + (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue), inMul3, 0, output.Shape[3].FixedValue);
                        Array.Copy(wRc.ToArray(), (o * wRc.Shape[3].FixedValue) + (d * wRc.Shape[2].FixedValue * wRc.Shape[3].FixedValue), inMul4, 0, output.Shape[3].FixedValue);
                        int l1 = l;
                        var iEnumerable3 = inMul3.Select((_, i) => inMul3[i] -= l1 == 0 ? hDeqBias0 : hDeqBias1);
                        int o2 = o;
                        var iEnumerable4 = inMul4.Select((_, i) => inMul4[i] -= (float)wRcQarg.ToArray()[o2]);
                        outMul2[o] = OrtKI.MatMul(inMul3, inMul4).ToArray<float>()[0];
                        int rcShiftBits = seqLenLoop[l] == seqLenLoop[0] ? rcShiftBits0 : rcShiftBits1;
                        base1 = (int)(d * wRc.Shape[2].FixedValue);
                        outMul2[o] = ApplyAct0(outMul2[o] / (1 << rcShiftBits), seqLenLoop[l] == seqLenLoop[0] ? actRc0.ToArray<Half>() : actRc1.ToArray<Half>(), base1 + o, 0);
                        outMul1[o] += outMul2[o];
                    }

                    // ft = sigmoid(g[2])
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outMul1[o + (output.Shape[3].FixedValue * 2)] = Sigmoid(outMul1[o + (output.Shape[3].FixedValue * 2)], segFittingParamFt);
                    }

                    // ct = init_c * ft
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outMul1[o + (output.Shape[3].FixedValue * 2)] *= outputCTmp[o + (b * output.Shape[3].FixedValue) + (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue)];
                        outMul1[o + (output.Shape[3].FixedValue * 2)] = ApplyAct1(outMul1[o + (output.Shape[3].FixedValue * 2)], binAct.ToArray<Half>(), o, false);
                    }

                    // it = sigmoid(g[0])
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outMul1[o + (output.Shape[3].FixedValue * 0)] = Sigmoid(outMul1[o + (output.Shape[3].FixedValue * 0)], segFittingParamFt);
                    }

                    // c_t = tanh(g[3])
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outMul1[o + (output.Shape[3].FixedValue * 3)] = Tanh(outMul1[o + (output.Shape[3].FixedValue * 3)], segFittingParamGt);
                    }

                    // c_t_it = it * c_t
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outMul1[o + (output.Shape[3].FixedValue * 0)] *= outMul1[o + (output.Shape[3].FixedValue * 3)];
                        outMul1[o + (output.Shape[3].FixedValue * 0)] = ApplyAct1(outMul1[o + (output.Shape[3].FixedValue * 0)], binAct.ToArray<Half>(), o, false);
                    }

                    // ct = ct + c_t_it
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outputCTmp[o + (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue)] = outMul1[o + (output.Shape[3].FixedValue * 2)] + outMul1[o + (output.Shape[3].FixedValue * 0)];
                    }

                    // ot = sigmoid(g[1])
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outMul1[o + (output.Shape[3].FixedValue * 1)] = Sigmoid(outMul1[o + (output.Shape[3].FixedValue * 1)], segFittingParamFt);
                    }

                    // tanh_ct = tanh(ct_o)
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outMul1[o + (output.Shape[3].FixedValue * 3)] = Tanh(
                            outputCTmp[o + (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue)],
                            segFittingParamGt);
                    }

                    // ht = ot * tanh_ct
                    for (int o = 0; o < output.Shape[3].FixedValue; o++)
                    {
                        outputHTmp[o + (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue)] =
                            outMul1[o + (output.Shape[3].FixedValue * 3)] * outMul1[o + (output.Shape[3].FixedValue * 1)];
                        outputHTmp[o + (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue)] =
                            ApplyAct1(outputHTmp[o + (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue)], binQAct.ToArray<Half>(), o, false);
                        int outputHInt =
                            (int)System.Math.Round(
                                outputHTmp[o + (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue)]);
                        if (dataTypeO == DataTypes.UInt8)
                        {
                            outputArrayU8[
                                (b * output.Shape[3].FixedValue) +
                                (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + (seqLenLoop[l] *
                                output.Shape[1].FixedValue * output.Shape[2].FixedValue * output.Shape[3].FixedValue) +
                                o] = (byte)System.Math.Min(System.Math.Max(0, outputHInt), 255);
                        }
                        else if (dataTypeO == DataTypes.Int8)
                        {
                            outputArrayI8[
                                (b * output.Shape[3].FixedValue) +
                                (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + (seqLenLoop[l] *
                                output.Shape[1].FixedValue * output.Shape[2].FixedValue * output.Shape[3].FixedValue) +
                                o] = (sbyte)System.Math.Min(System.Math.Max(-127, outputHInt), 127);
                        }
                        else if (dataTypeO == DataTypes.Int16)
                        {
                            outputArrayI16[
                                (b * output.Shape[3].FixedValue) +
                                (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + (seqLenLoop[l] *
                                    output.Shape[1].FixedValue * output.Shape[2].FixedValue * output.Shape[3].FixedValue) +
                                o] = (short)System.Math.Min(System.Math.Max(-2047, outputHInt), 2047);
                        }
                        else
                        {
                            outputArrayHalf[
                                (b * output.Shape[3].FixedValue) +
                                (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + (seqLenLoop[l] *
                                output.Shape[1].FixedValue * output.Shape[2].FixedValue * output.Shape[3].FixedValue) +
                                o] = (Half)outputHTmp[d * output.Shape[2].FixedValue * output.Shape[3].FixedValue];
                        }
                    }

                    if (l == seqLenLoop.Count - 1)
                    {
                        if (dataTypeOH == DataTypes.UInt8)
                        {
                            for (int i = 0; i < output.Shape[3].FixedValue; i++)
                            {
                                outputHArrayU8[
                                        (b * output.Shape[3].FixedValue) +
                                        (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + i] =
                                    outputArrayU8[(b * output.Shape[3].FixedValue) +
                                                    (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) +
                                                    (seqLenLoop[l] *
                                                    output.Shape[1].FixedValue * output.Shape[2].FixedValue *
                                                    output.Shape[3].FixedValue) +
                                                    i];
                            }
                        }
                        else if (dataTypeOH == DataTypes.Int8)
                        {
                            for (int i = 0; i < output.Shape[3].FixedValue; i++)
                            {
                                outputHArrayI8[
                                        (b * output.Shape[3].FixedValue) +
                                        (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + i] =
                                    outputArrayI8[(b * output.Shape[3].FixedValue) +
                                                    (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) +
                                                    (seqLenLoop[l] *
                                                    output.Shape[1].FixedValue * output.Shape[2].FixedValue *
                                                    output.Shape[3].FixedValue) +
                                                    i];
                            }
                        }
                        else if (dataTypeOH == DataTypes.Int16)
                        {
                            for (int i = 0; i < output.Shape[3].FixedValue; i++)
                            {
                                outputHArrayI16[
                                        (b * output.Shape[3].FixedValue) +
                                        (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + i] =
                                    outputArrayI16[
                                        (b * output.Shape[3].FixedValue) +
                                        (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + (seqLenLoop[l] *
                                        output.Shape[1].FixedValue * output.Shape[2].FixedValue *
                                        output.Shape[3].FixedValue) +
                                        i];
                            }
                        }
                        else
                        {
                            for (int i = 0; i < output.Shape[3].FixedValue; i++)
                            {
                                outputHArrayHalf[
                                        (b * output.Shape[3].FixedValue) +
                                        (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + i] =
                                    outputArrayHalf[(b * output.Shape[3].FixedValue) +
                                                      (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) +
                                                      (seqLenLoop[l] *
                                                      output.Shape[1].FixedValue * output.Shape[2].FixedValue *
                                                      output.Shape[3].FixedValue) +
                                                      i];
                            }
                        }

                        // output_c只能为fp16
                        for (int i = 0; i < output.Shape[3].FixedValue; i++)
                        {
                            outputCArray[
                                    (b * output.Shape[3].FixedValue) +
                                    (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue) + i] =
                                (Half)outputCTmp[i + (d * output.Shape[2].FixedValue * output.Shape[3].FixedValue)];
                        }
                    }
                }
            }
        }

        if (dataTypeO == DataTypes.Float16)
        {
            output = Tensor.From(outputArrayHalf, output.Shape);
        }
        else if (dataTypeO == DataTypes.Int8)
        {
            output = Tensor.From(outputArrayI8, output.Shape);
        }
        else if (dataTypeO == DataTypes.Int16)
        {
            output = Tensor.From(outputArrayI16, output.Shape);
        }
        else
        {
            output = Tensor.From(outputArrayU8, output.Shape);
        }

        if (dataTypeOH == DataTypes.Float16)
        {
            outputH = Tensor.From(outputHArrayHalf, outputH.Shape);
        }
        else if (dataTypeOH == DataTypes.Int8)
        {
            outputH = Tensor.From(outputHArrayI8, outputH.Shape);
        }
        else if (dataTypeOH == DataTypes.Int16)
        {
            outputH = Tensor.From(outputHArrayI16, outputH.Shape);
        }
        else
        {
            outputH = Tensor.From(outputHArrayU8, outputH.Shape);
        }

        outputC = Tensor.From(outputCArray, outputC.Shape);
        if (outputSize == 1)
        {
            return new[] { output };
        }

        if (outputSize == 2)
        {
            return new[] { output, outputH };
        }

        if (outputSize == 3)
        {
            return new[] { output, outputH, outputC };
        }

        return null!;
    }

    public static Tensor FakeAi2dResizeBilinear(Tensor input, int[] newSize, bool alignCorners, bool halfPixelCenters)
    {
        var outH = newSize[0];
        var outW = newSize[1];
        var scales = GetResizeScales(input.Shape.ToValueArray().Select(x => (int)x).ToArray(), outH, outW, alignCorners);
        var heightScales = scales[0];
        var widthScales = scales[1];

        var roundingOffset = 0f;
        var inputArray = input.ToArray<float>();
        var outputArray =
            new float[input.Shape.ToValueArray()[0] * input.Shape.ToValueArray()[1] * newSize[0] * newSize[1]];

        for (int batch = 0; batch < input.Shape.ToValueArray()[0]; batch++)
        {
            for (int oc = 0; oc < input.Shape.ToValueArray()[1]; oc++)
            {
                for (int oy = 0; oy < outH; oy++)
                {
                    var resultY = SetResizeBilinear(oy, heightScales, halfPixelCenters, (int)input.Shape.ToValueArray()[2], 0, 0, 0);
                    var inY = (float)resultY[0];
                    var inY0 = (int)resultY[1];
                    var inY1 = (int)resultY[2];
                    for (int ox = 0; ox < outW; ox++)
                    {
                        var resultX = SetResizeBilinear(ox, widthScales, halfPixelCenters, (int)input.Shape.ToValueArray()[3], 0, 0, 0);
                        var inX = (float)resultX[0];
                        var inX0 = (int)resultX[1];
                        var inX1 = (int)resultX[2];

                        var v0 = inputArray[
                            (batch * input.Shape.ToValueArray()[1] * input.Shape.ToValueArray()[2] *
                            input.Shape.ToValueArray()[3]) +
                            (oc * input.Shape.ToValueArray()[2] * input.Shape.ToValueArray()[3]) +
                            (inY0 * input.Shape.ToValueArray()[3]) + inX0];
                        var v1 = inputArray[(batch * input.Shape.ToValueArray()[1] * input.Shape.ToValueArray()[2] *
                                             input.Shape.ToValueArray()[3]) +
                                            (oc * input.Shape.ToValueArray()[2] * input.Shape.ToValueArray()[3]) +
                                            (inY1 * input.Shape.ToValueArray()[3]) + inX0];
                        var v2 = inputArray[(batch * input.Shape.ToValueArray()[1] * input.Shape.ToValueArray()[2] *
                                            input.Shape.ToValueArray()[3]) +
                                (oc * input.Shape.ToValueArray()[2] * input.Shape.ToValueArray()[3]) +
                            (inY0 * input.Shape.ToValueArray()[3]) + inX1];
                        var v3 = inputArray[(batch * input.Shape.ToValueArray()[1] * input.Shape.ToValueArray()[2] *
                                             input.Shape.ToValueArray()[3]) +
                                            (oc * input.Shape.ToValueArray()[2] * input.Shape.ToValueArray()[3]) +
                                            (inY1 * input.Shape.ToValueArray()[3]) + inX1];
                        var a0 = (1 - (inY - inY0)) * (1 - (inX - inX0));
                        var a1 = (inY - inY0) * (1 - (inX - inX0));
                        var a2 = (1 - (inY - inY0)) * (inX - inX0);
                        var a3 = (inY - inY0) * (inX - inX0);
                        outputArray[(batch * input.Shape.ToValueArray()[1] * outH *
                                     outW) +
                                    (oc * outH * outW) +
                                    (oy * outW) + ox] =
                            (float)((v0 * a0) + (v1 * a1) + (v2 * a2) + (v3 * a3) + roundingOffset);
                    }
                }
            }
        }

        return new Tensor<float>(
            outputArray,
            new[] { input.Shape.ToValueArray()[0], input.Shape.ToValueArray()[1], outH, outW });
    }

    public static Tensor FakeAi2dResizeNearestNeighbor(Tensor input, int[] newSize, bool alignCorners, bool halfPixelCenters)
    {
        var outH = newSize[0];
        var outW = newSize[1];
        var scales = GetResizeScales(input.Shape.ToValueArray().Select(x => (int)x).ToArray(), outH, outW, alignCorners);
        var heightScales = scales[0];
        var widthScales = scales[1];

        var inputArray = input.ToArray<float>();
        var outputArray =
            new float[input.Shape.ToValueArray()[0] * input.Shape.ToValueArray()[1] * newSize[0] * newSize[1]];

        for (int batch = 0; batch < input.Shape.ToValueArray()[0]; batch++)
        {
            for (int oc = 0; oc < input.Shape.ToValueArray()[1]; oc++)
            {
                for (int oy = 0; oy < outH; oy++)
                {
                    var inY = GetNearestNeighbor(oy, (int)input.Shape.ToValueArray()[2], heightScales, alignCorners, halfPixelCenters);
                    for (int ox = 0; ox < outW; ox++)
                    {
                        var inX = GetNearestNeighbor(ox, (int)input.Shape.ToValueArray()[3], widthScales, alignCorners, halfPixelCenters);
                        outputArray[(batch * input.Shape.ToValueArray()[1] * outH * outW) +
                                    (oc * outH * outW) +
                                    (oy * outW) + ox] =
                            inputArray[(batch * input.Shape.ToValueArray()[1] * input.Shape.ToValueArray()[2] *
                                        input.Shape.ToValueArray()[3]) +
                                       (oc * input.Shape.ToValueArray()[2] * input.Shape.ToValueArray()[3]) +
                                       (inY * input.Shape.ToValueArray()[3]) + inX];
                    }
                }
            }
        }

        return new Tensor<float>(
            outputArray,
            new[] { input.Shape.ToValueArray()[0], input.Shape.ToValueArray()[1], outH, outW });
    }

    public static Tensor Ai2dResizeBilinear(PrimType dataTypes, Tensor input, int[] newSize, bool alignCorners, bool halfPixelCenters, int inDeqBias, QuantParam quantParam)
    {
        var outH = newSize[0];
        var outW = newSize[1];
        var scales = GetResizeScales(input.Shape.ToValueArray().Select(x => (int)x).ToArray(), outH, outW, alignCorners);
        var heightScales = scales[0];
        var widthScales = scales[1];

        var roundingOffset = 0f;
        var inputArray = input.ToArray<float>();
        var outputDataArray =
            new float[input.Shape.ToValueArray()[0] * input.Shape.ToValueArray()[1] * newSize[0] * newSize[1]];

        for (int batch = 0; batch < input.Shape.ToValueArray()[0]; batch++)
        {
            for (int oc = 0; oc < input.Shape.ToValueArray()[1]; oc++)
            {
                for (int oy = 0; oy < outH; oy++)
                {
                    var resultY = SetResizeBilinear(oy, heightScales, halfPixelCenters, (int)input.Shape.ToValueArray()[2], 0, 0, 0);
                    var inY = (float)resultY[0];
                    var inY0 = (int)resultY[1];
                    var inY1 = (int)resultY[2];
                    for (int ox = 0; ox < outW; ox++)
                    {
                        var resultX = SetResizeBilinear(ox, widthScales, halfPixelCenters, (int)input.Shape.ToValueArray()[3], 0, 0, 0);
                        var inX = (float)resultX[0];
                        var inX0 = (int)resultX[1];
                        var inX1 = (int)resultX[2];

                        var v0 = inputArray[
                            (batch * input.Shape.ToValueArray()[1] * input.Shape.ToValueArray()[2] *
                            input.Shape.ToValueArray()[3]) +
                            (oc * input.Shape.ToValueArray()[2] * input.Shape.ToValueArray()[3]) +
                            (inY0 * input.Shape.ToValueArray()[3]) + inX0];
                        var v1 = inputArray[(batch * input.Shape.ToValueArray()[1] * input.Shape.ToValueArray()[2] *
                                             input.Shape.ToValueArray()[3]) +
                                            (oc * input.Shape.ToValueArray()[2] * input.Shape.ToValueArray()[3]) +
                                            (inY1 * input.Shape.ToValueArray()[3]) + inX0];
                        var v2 = inputArray[(batch * input.Shape.ToValueArray()[1] * input.Shape.ToValueArray()[2] *
                                            input.Shape.ToValueArray()[3]) +
                                (oc * input.Shape.ToValueArray()[2] * input.Shape.ToValueArray()[3]) +
                            (inY0 * input.Shape.ToValueArray()[3]) + inX1];
                        var v3 = inputArray[(batch * input.Shape.ToValueArray()[1] * input.Shape.ToValueArray()[2] *
                                             input.Shape.ToValueArray()[3]) +
                                            (oc * input.Shape.ToValueArray()[2] * input.Shape.ToValueArray()[3]) +
                                            (inY1 * input.Shape.ToValueArray()[3]) + inX1];
                        var a0 = (1 - (inY - inY0)) * (1 - (inX - inX0));
                        var a1 = (inY - inY0) * (1 - (inX - inX0));
                        var a2 = (1 - (inY - inY0)) * (inX - inX0);
                        var a3 = (inY - inY0) * (inX - inX0);
                        var data = (v0 * a0) + (v1 * a1) + (v2 * a2) + (v3 * a3) + roundingOffset;
                        outputDataArray[(batch * input.Shape.ToValueArray()[1] * outH *
                                         outW) +
                                        (oc * outH * outW) +
                                        (oy * outW) + ox] = data;
                    }
                }
            }
        }

        if (dataTypes == DataTypes.UInt8)
        {
            var outputArray = new byte[outputDataArray.Length];
            int i = 0;
            foreach (float v in outputDataArray)
            {
                outputArray[i++] = (byte)v;
            }

            return new Tensor<byte>(
                outputArray,
                new[] { input.Shape.ToValueArray()[0], input.Shape.ToValueArray()[1], outH, outW });
        }
        else if (dataTypes == DataTypes.Int8)
        {
            var outputArray = new sbyte[outputDataArray.Length];
            int i = 0;
            foreach (float v in outputDataArray)
            {
                outputArray[i++] = (sbyte)v;
            }

            return new Tensor<sbyte>(
                outputArray,
                new[] { input.Shape.ToValueArray()[0], input.Shape.ToValueArray()[1], outH, outW });
        }
        else if (dataTypes == DataTypes.Float16)
        {
            var outputArray = new Half[outputDataArray.Length];
            int i = 0;
            foreach (float v in outputDataArray)
            {
                outputArray[i++] = (Half)v;
            }

            return new Tensor<Half>(
                outputArray,
                new[] { input.Shape.ToValueArray()[0], input.Shape.ToValueArray()[1], outH, outW });
        }
        else if (dataTypes == DataTypes.Int16)
        {
            var outputArray = new short[outputDataArray.Length];
            int i = 0;
            foreach (float v in outputDataArray)
            {
                outputArray[i++] = (short)v;
            }

            return new Tensor<short>(
                outputArray,
                new[] { input.Shape.ToValueArray()[0], input.Shape.ToValueArray()[1], outH, outW });
        }
        else
        {
            return new Tensor<float>(
                outputDataArray,
                new[] { input.Shape.ToValueArray()[0], input.Shape.ToValueArray()[1], outH, outW });
        }
    }

    public static Tensor Ai2dResizeNearestNeighbor(PrimType dataTypes, Tensor input, int[] newSize, bool alignCorners, bool halfPixelCenters, int inDeqBias, QuantParam quantParam)
    {
        var outH = newSize[0];
        var outW = newSize[1];
        var scales = GetResizeScales(input.Shape.ToValueArray().Select(x => (int)x).ToArray(), outH, outW, alignCorners);
        var heightScales = scales[0];
        var widthScales = scales[1];

        var inputArray = input.ToArray<float>();
        var outputDataArray =
            new float[input.Shape.ToValueArray()[0] * input.Shape.ToValueArray()[1] * newSize[0] * newSize[1]];

        for (int batch = 0; batch < input.Shape.ToValueArray()[0]; batch++)
        {
            for (int oc = 0; oc < input.Shape.ToValueArray()[1]; oc++)
            {
                for (int oy = 0; oy < outH; oy++)
                {
                    var inY = GetNearestNeighbor(oy, (int)input.Shape.ToValueArray()[2], heightScales, alignCorners, halfPixelCenters);
                    for (int ox = 0; ox < outW; ox++)
                    {
                        var inX = GetNearestNeighbor(ox, (int)input.Shape.ToValueArray()[3], widthScales, alignCorners, halfPixelCenters);
                        outputDataArray[(batch * input.Shape.ToValueArray()[1] * outH *
                                         outW) +
                                        (oc * outH * outW) +
                                        (oy * outW) + ox] =
                            inputArray[(batch * input.Shape.ToValueArray()[1] * input.Shape.ToValueArray()[2] *
                                          input.Shape.ToValueArray()[3]) +
                                         (oc * input.Shape.ToValueArray()[2] * input.Shape.ToValueArray()[3]) +
                                         (inY * input.Shape.ToValueArray()[3]) + inX];
                    }
                }
            }
        }

        if (dataTypes == DataTypes.UInt8)
        {
            var outputArray = new byte[outputDataArray.Length];
            int i = 0;
            foreach (float v in outputDataArray)
            {
                outputArray[i++] = (byte)v;
            }

            return new Tensor<byte>(
                outputArray,
                new[] { input.Shape.ToValueArray()[0], input.Shape.ToValueArray()[1], outH, outW });
        }
        else if (dataTypes == DataTypes.Int8)
        {
            var outputArray = new sbyte[outputDataArray.Length];
            int i = 0;
            foreach (float v in outputDataArray)
            {
                outputArray[i++] = (sbyte)v;
            }

            return new Tensor<sbyte>(
                outputArray,
                new[] { input.Shape.ToValueArray()[0], input.Shape.ToValueArray()[1], outH, outW });
        }
        else if (dataTypes == DataTypes.Float16)
        {
            var outputArray = new Half[outputDataArray.Length];
            int i = 0;
            foreach (float v in outputDataArray)
            {
                outputArray[i++] = (Half)v;
            }

            return new Tensor<Half>(
                outputArray,
                new[] { input.Shape.ToValueArray()[0], input.Shape.ToValueArray()[1], outH, outW });
        }
        else if (dataTypes == DataTypes.Int16)
        {
            var outputArray = new short[outputDataArray.Length];
            int i = 0;
            foreach (float v in outputDataArray)
            {
                outputArray[i++] = (short)v;
            }

            return new Tensor<short>(
                outputArray,
                new[] { input.Shape.ToValueArray()[0], input.Shape.ToValueArray()[1], outH, outW });
        }
        else
        {
            return new Tensor<float>(
                outputDataArray,
                new[] { input.Shape.ToValueArray()[0], input.Shape.ToValueArray()[1], outH, outW });
        }
    }

    public static float[] GetResizeScales(int[] inShape, int outH, int outW, bool alignCorners)
    {
        var heightScale = (float)inShape[2] / outH;
        var widthScale = (float)inShape[3] / outW;
        if (alignCorners && outH > 1)
        {
            heightScale = (float)(inShape[2] - 1) / (outH - 1);
        }

        if (alignCorners && outW > 1)
        {
            widthScale = (float)(inShape[3] - 1) / (outW - 1);
        }

        return new[] { heightScale, widthScale };
    }

    public static double[] SetResizeBilinear(int value, double scale, bool halfPixelCenters, int shapeSize, double scaledValue, int v0, int v1)
    {
        if (halfPixelCenters)
        {
            scaledValue = ((value + 0.5) * scale) - 0.5;
        }
        else
        {
            scaledValue = value * scale;
        }

        double scaledValueFloor = System.Math.Floor(scaledValue);
        v0 = (int)System.Math.Max(scaledValueFloor, 0);
        v1 = (int)System.Math.Min(System.Math.Ceiling(scaledValue), shapeSize - 1);
        return new[] { scaledValue, v0, v1 };
    }

    public static int GetNearestNeighbor(float input, int shapeSize, float scale, bool alignCorners, bool halfPixelCenters)
    {
        var offset = halfPixelCenters ? 0.5f : 0.0f;
        var afterScale = (input + offset) * scale;
        var alignCornersVal = alignCorners ? System.Math.Round(afterScale) : System.Math.Floor(afterScale);
        int outputValue = (int)System.Math.Min((int)alignCornersVal, (int)(shapeSize - 1));
        if (halfPixelCenters)
        {
            outputValue = System.Math.Max(0, outputValue);
        }

        return outputValue;
    }

    public static long ComputeSize(Const input)
    {
        return ComputeSize(input.CheckedShape);
    }

    public static int ComputeSize(int[] shape)
    {
        return shape.Aggregate(1, (sum, x) => sum * x);
    }

    public static long ComputeSize(Shape shape)
    {
        return shape.ProdWithDynamicAsMaxValue();
    }

    public static int LinearIndex(int[] shape, int[] index)
    {
        int newIndex = index[0];
        for (int i = 1; i < shape.Length; i++)
        {
            newIndex = (newIndex * shape[i]) + index[i];
        }

        return newIndex;
    }

    public static int[] GetDefaultStrides(int[] shape)
    {
        int[] strides = new int[shape.Length];
        int dataSize = 1;
        for (int i = shape.Length - 1; i >= 0; i--)
        {
            strides[i] = dataSize;
            dataSize = strides[i] * shape[i];
            if (shape[i] == 1)
            {
                strides[i] = 0;
            }
        }

        return strides;
    }

    public static Const ConcatOutput<T>(List<T[]> outputTmp, long[] outShape)
        where T : unmanaged, IEquatable<T>
    {
        var init = Array.Empty<T>().AsEnumerable();
        var outputData = outputTmp.Aggregate(init, (sum, output) => sum.Concat(output)).ToArray();
        return Const.FromTensor(Tensor.From(outputData, outShape));
    }

    public static OrtKISharp.Tensor Proc(int oc)
    {
        return Enumerable
            .Repeat(0, oc)
            .Select(x => (float)x)
            .ToArray();
    }

    public static OrtKISharp.Tensor DefaultBias(int oc)
    {
        return Tensor.FromArray(Enumerable
            .Repeat(0, oc)
            .Select(x => (float)x)
            .ToArray()).ToOrtTensor();
    }

    public static bool IsAnyHalfPixel(ImageResizeTransformationMode mode)
        => mode is ImageResizeTransformationMode.HalfPixel or ImageResizeTransformationMode.PytorchHalfPixel;

    public static bool CanBeLoweredToCrop(ResizeImage r)
        => CanBeLoweredToCrop(r.ResizeMode, r.NearestMode, r.TransformationMode);

    // base on result of compare evaluator
    // CosSim < 0.9
    public static bool CanBeLoweredToCrop(ImageResizeMode resizeMode, ImageResizeNearestMode nearestMode, ImageResizeTransformationMode transformationMode)
    {
        if (transformationMode == ImageResizeTransformationMode.TFCropAndResize)
        {
            return false;
        }

        if (resizeMode == ImageResizeMode.Bilinear && IsAnyHalfPixel(transformationMode))
        {
            return false;
        }

        if (resizeMode == ImageResizeMode.NearestNeighbor)
        {
            if (nearestMode == ImageResizeNearestMode.Ceil)
            {
                return false;
            }

            if (nearestMode == ImageResizeNearestMode.Floor)
            {
                return transformationMode == ImageResizeTransformationMode.Asymmetric;
            }

            if (nearestMode == ImageResizeNearestMode.RoundPreferCeil &&
                transformationMode == ImageResizeTransformationMode.Asymmetric)
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// DynamicGnneMatmul.
    /// </summary>
    /// <typeparam name="TIA">TIA.</typeparam>
    /// <typeparam name="TIB">TIB.</typeparam>
    /// <typeparam name="TO">TO.</typeparam>
    /// <param name="inputA">input_a.</param>
    /// <param name="inputB">input_b.</param>
    /// <param name="output">output.</param>
    /// <param name="act">act.</param>
    /// <param name="inABias">in_a_bias.</param>
    /// <param name="aBatch0">aBatch0.</param>
    /// <param name="aBatch1">a_batch_1.</param>
    /// <param name="aRows">a_rows.</param>
    /// <param name="aCols">a_cols.</param>
    /// <param name="bBatch0">bBatch0.</param>
    /// <param name="bBatch1">b_batch_1.</param>
    /// <param name="bCols">b_cols.</param>
    /// <param name="inBBias">n_b_bias.</param>
    /// <param name="inAShiftBits">in_a_shift_bits.</param>
    /// <param name="inBShiftBits">in_b_shift_bits.</param>
    /// <param name="shiftBits">shift_bits.</param>
    /// <param name="dynamicChannel"> 动态channel, 自动扩展act.  </param>
    /// <exception cref="ArgumentOutOfRangeException">.</exception>
    public static void DynamicGnneMatmul<TIA, TIB, TO>(ReadOnlySpan<TIA> inputA, ReadOnlySpan<TIB> inputB, Span<TO> output, ReadOnlySpan<Half> act, ReadOnlySpan<byte> inABias, int aBatch0, int aBatch1, int aRows, int aCols, int bBatch0, int bBatch1, int bCols, byte inBBias, int inAShiftBits, int inBShiftBits, sbyte shiftBits, bool dynamicChannel = true)
        where TIA : unmanaged
        where TIB : unmanaged
        where TO : unmanaged
    {
        if (aBatch0 != bBatch0 && new[] { aBatch0, bBatch0 }.All(x => x != 1))
        {
            throw new ArgumentOutOfRangeException("inputA");
        }

        if (aBatch1 != bBatch1 && new[] { aBatch1, bBatch1 }.All(x => x != 1))
        {
            throw new ArgumentOutOfRangeException("inputA");
        }

        int[] aStride = TensorUtilities.GetDefaultStrides(new[] { aBatch0, aBatch1, aRows, aCols });
        int[] bStride = TensorUtilities.GetDefaultStrides(new[] { bBatch0, bBatch1, aCols, bCols });
        int batch0 = System.Math.Max(aBatch0, bBatch0);
        int batch1 = System.Math.Max(aBatch1, bBatch1);
        int[] outStride = TensorUtilities.GetDefaultStrides(new[] { batch0, batch1, aRows, bCols });
        for (int b0 = 0; b0 < batch0; b0++)
        {
            for (int b1 = 0; b1 < batch1; b1++)
            {
                for (int oy = 0; oy < aRows; oy++)
                {
                    int indexOut = TensorUtilities.GetLinearOffset(outStride, new[] { b0, b1, oy, 0 });
                    for (int ox = 0; ox < bCols; ox++)
                    {
                        float value = 0.0f;
                        int indexA = TensorUtilities.GetLinearOffset(
                            aStride,
                            new[]
                            {
                                b0 >= aBatch0 ? aBatch0 - 1 : b0, b1 >= aBatch1 ? aBatch1 - 1 : b1, oy, 0,
                            });
                        int indexB = TensorUtilities.GetLinearOffset(
                            bStride,
                            new[]
                            {
                                b0 >= bBatch0 ? bBatch0 - 1 : b0, b1 >= bBatch1 ? bBatch1 - 1 : b1, 0, ox,
                            });
                        for (int i = 0; i < aCols; i++)
                        {
                            var a = inputA[indexA + i];
                            var b = inputB[indexB + (i * bCols)];
                            value += ((float)Convert.ChangeType(a, typeof(float)) - inABias[oy]) *
                                     ((float)Convert.ChangeType(b, typeof(float)) - inBBias);
                        }

                        if (typeof(TO) == typeof(float))
                        {
                            output[indexOut + ox] = (TO)(object)ApplyAct0(value, act, dynamicChannel ? 0 : oy, shiftBits);
                        }
                        else if (typeof(TO) == typeof(Half))
                        {
                            output[indexOut + ox] = (TO)(object)(Half)ApplyAct0(value, act, dynamicChannel ? 0 : oy, shiftBits);
                        }
                        else if (typeof(TO) == typeof(byte) || typeof(TO) == typeof(sbyte) ||
                                 typeof(TO) == typeof(short))
                        {
                            output[indexOut + ox] = (TO)Convert.ChangeType(System.Math.Round(ApplyAct0(value, act, dynamicChannel ? 0 : oy, shiftBits)), typeof(TO));
                        }
                        else
                        {
                            throw new ArgumentOutOfRangeException("inputA");
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// FakeDynamicGnneMatmul.
    /// </summary>
    /// <exception cref="ArgumentOutOfRangeException">.</exception>
    public static void FakeDynamicGnneMatmul(IEvaluateContext context, Span<float> inputA, Span<float> inputB, Span<float> output, ReadOnlySpan<float> act, int aBatch0, int aBatch1, int aRows, int aCols, int bBatch0, int bBatch1, int bCols, bool dynamicChannel)
    {
        // when HasBindedMixQuantInfo is true, eval will do simulation of quant/dequant for some inputs, this is used for evaluate accumulated quant error for layers.
        if (context.CurrentCall.EnodeBestQuantConfigWithCosine != null)
        {
            var pattern = IsRangeOfMarker(IsWildcard(), IsWildcard());
            if (pattern.MatchLeaf(context.CurrentCall.Arguments[0]) &&
                ((Marker)context.CurrentCall.Arguments[0]).MixQuantInfo
                ?.HasBindedMixQuantInfo == true)
            {
                var quantParam = ((Marker)context.CurrentCall.Arguments[0]).MixQuantInfo?.QuantParameter;

                // input feature map quantParam count should be 1 since input feature map quant is by tensor.
                Trace.Assert(quantParam!.Count == 1);
                for (int i = 0; i < inputA.Length; i++)
                {
                    double inputAQuant =
                        ((double)inputA[i] / quantParam[0].Scale) +
                        quantParam[0].ZeroPoint;
                    if (!(quantParam[0].Scale == 1.0f && quantParam[0].ZeroPoint == 0))
                    {
                        inputAQuant = System.Math.Round(inputAQuant);
                    }

                    double inputADequant =
                        (inputAQuant - quantParam[0].ZeroPoint) * quantParam[0].Scale;
                    inputA[i] = (float)inputADequant;
                }
            }

            if (pattern.MatchLeaf(context.CurrentCall.Arguments[1]) &&
                ((Marker)context.CurrentCall.Arguments[1]).MixQuantInfo
                ?.HasBindedMixQuantInfo == true)
            {
                var quantParam = ((Marker)context.CurrentCall.Arguments[1]).MixQuantInfo?.QuantParameter;
                int oc = quantParam!.Count;
                int eachChannelData = inputB.Length / oc;
                for (int i = 0; i < inputB.Length; i++)
                {
                    double inputBQuant =
                        (inputB[i] / (double)quantParam[i / eachChannelData].Scale) +
                        quantParam[i / eachChannelData].ZeroPoint;
                    if (!(quantParam[i / eachChannelData].Scale == 1.0f &&
                          quantParam[i / eachChannelData].ZeroPoint == 0))
                    {
                        inputBQuant = System.Math.Round(inputBQuant);
                    }

                    double inputBDequant =
                        (inputBQuant - quantParam[i / eachChannelData].ZeroPoint) *
                        quantParam[i / eachChannelData].Scale;
                    inputB[i] = (float)inputBDequant;
                }
            }
        }

        if (aBatch0 != bBatch0 && new[] { aBatch0, bBatch0 }.All(x => x != 1))
        {
            throw new ArgumentOutOfRangeException("context");
        }

        if (aBatch1 != bBatch1 && new[] { aBatch1, bBatch1 }.All(x => x != 1))
        {
            throw new ArgumentOutOfRangeException("context");
        }

        int[] aStride = TensorUtilities.GetDefaultStrides(new[] { aBatch0, aBatch1, aRows, aCols });
        int[] bStride = TensorUtilities.GetDefaultStrides(new[] { bBatch0, bBatch1, aCols, bCols });
        int batch0 = System.Math.Max(aBatch0, bBatch0);
        int batch1 = System.Math.Max(aBatch1, bBatch1);
        int[] outStride = TensorUtilities.GetDefaultStrides(new[] { batch0, batch1, aRows, bCols });
        for (int b0 = 0; b0 < batch0; b0++)
        {
            for (int b1 = 0; b1 < batch1; b1++)
            {
                for (int oy = 0; oy < aRows; oy++)
                {
                    int indexOut = TensorUtilities.GetLinearOffset(outStride, new[] { b0, b1, oy, 0 });
                    for (int ox = 0; ox < bCols; ox++)
                    {
                        float value = 0.0f;
                        int indexA = TensorUtilities.GetLinearOffset(
                            aStride,
                            new[]
                            {
                                b0 >= aBatch0 ? aBatch0 - 1 : b0, b1 >= aBatch1 ? aBatch1 - 1 : b1, oy, 0,
                            });
                        int
                            indexB = TensorUtilities.GetLinearOffset(
                                bStride,
                                new[]
                            {
                                b0 >= bBatch0 ? bBatch0 - 1 : b0, b1 >= bBatch1 ? bBatch1 - 1 : b1, 0, ox,
                            });
                        for (int i = 0; i < aCols; i++)
                        {
                            float a = inputA[indexA + i];
                            float b = inputB[indexB + (i * bCols)];
                            value += a * b;
                        }

                        output[indexOut + ox] = ApplyAct01(value, act, dynamicChannel ? 0 : oy, 0);
                    }
                }
            }
        }
    }

    private static T Clamp<T>(T value, T min, T max)
        where T : unmanaged, IComparable<T>
    {
        var t1 = value.CompareTo(max) > 0 ? max : value;
        return t1.CompareTo(min) > 0 ? t1 : min;
    }

    private static T ApplyActivation<T>(T value, ValueRange<T> activation)
        where T : unmanaged, IEquatable<T>, IComparable<T>
    {
        return Clamp(value, activation.Min, activation.Max);
    }

    private static T ApplyGnneActivation<T>(float value, sbyte shiftBits, T x0, T kl, T bl, T kr, T br)
        where T : unmanaged, IComparable<T>
    {
        if (value.CompareTo(x0) < 0)
        {
            if (typeof(T) == typeof(Half))
            {
                return (T)(object)(Half)((value * (float)Convert.ChangeType(kl, typeof(float)) / (1 << shiftBits)) +
                                         (float)Convert.ChangeType(bl, typeof(float)));
            }
            else if (typeof(T) == typeof(float))
            {
                return (T)(object)((value * (float)(object)kl / (1L << shiftBits)) + (float)(object)bl);
            }
            else
            {
                throw new ArgumentOutOfRangeException(nameof(value));
            }
        }
        else
        {
            if (typeof(T) == typeof(Half))
            {
                return (T)(object)(Half)((value * (float)Convert.ChangeType(kr, typeof(float)) / (1L << shiftBits)) +
                                         (float)Convert.ChangeType(br, typeof(float)));
            }
            else if (typeof(T) == typeof(float))
            {
                return (T)(object)((value * (float)(object)kr / (1L << shiftBits)) + (float)(object)br);
            }
            else
            {
                throw new ArgumentOutOfRangeException(nameof(value));
            }
        }
    }

    private static float ApplyAct1(float value, ReadOnlySpan<float> actData, int channel, bool is16Segments)
    {
        // float v;
        if (is16Segments)
        {
            float v = ApplyMultiSegmentsAct1(16, value, actData);
            int offset = (16 * 2) + 15;
            return ApplyActivation(v, (actData[(offset + 0) % actData.Length], actData[(offset + 1) % actData.Length]));
        }
        else
        {
            int begin = 7 * channel;
            float v = ApplyGnneActivation(
                value,
                0,
                actData[begin + 0], // x0
                actData[begin + 1], // kl
                actData[begin + 3], // bl
                actData[begin + 2], // kr
                actData[begin + 4]); // br
            return ApplyActivation(v, (actData[begin + 5], actData[begin + 6]));
        }
    }

    private static float ApplyAct1(float value, ReadOnlySpan<Half> actData, int channel, bool is16Segments)
    {
        // float v;
        if (is16Segments)
        {
            float v = ApplyMultiSegmentsAct1(16, value, actData);
            int offset = (16 * 2) + 15;
            return ApplyActivation(v, ((float)actData[offset + 0], (float)actData[offset + 1]));
        }
        else
        {
            int begin = 7 * channel;
            float v = ApplyGnneActivation(
                value,
                0,
                (float)actData[begin + 0], // x0
                (float)actData[begin + 1], // kl
                (float)actData[begin + 3], // bl
                (float)actData[begin + 2], // kr
                (float)actData[begin + 4]); // br
            return ApplyActivation(v, ((float)actData[begin + 5], (float)actData[begin + 6]));
        }
    }
}
