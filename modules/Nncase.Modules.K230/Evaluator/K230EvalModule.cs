﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using DryIoc;
using Nncase.Hosting;

namespace Nncase.Evaluator.K230;

/// <summary>
/// NN module.
/// </summary>
internal sealed class K230EvalModule : IApplicationPart
{
    /// <inheritdoc/>
    public void ConfigureServices(IRegistrator registrator)
    {
        // Fake IR
        registrator.RegisterManyInterface<FakeDynamicGNNEMatMulEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<FakeActivationEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<FakeConv2DEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<FakeConv2DTransposeEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<FakePdpEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<FakeLSTMEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<FakeAi2dPadEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<FakeAi2dResizeEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<FakeMatMulEvaluator>(reuse: Reuse.Singleton);

        // HIR
        // registrator.RegisterManyInterface<DynamicGNNEMatMulEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<GNNEPdp1Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<GNNEPdp0DWEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<GNNETransposeEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<GNNELoadEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<GNNELoadWEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<GNNEStoreEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<GNNEPadEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<GNNEActivationEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<GNNEConv2DEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<GNNEConv2DTransposeEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<GNNELSTMEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<Ai2dPadEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<Ai2dResizeEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<GNNEMatMulEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<GNNEPdp0ReduceEvaluator>(reuse: Reuse.Singleton);

        // customed
        registrator.RegisterManyInterface<TIR.Instructions.LoadImmEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.LoadDdrAddrEvaluator>(reuse: Reuse.Singleton);

        // auto generated
        registrator.RegisterManyInterface<TIR.Instructions.LUIEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.AUIPCEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.ADDIEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.ADDEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.SUBEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MULEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.DIVEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.DIVUEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.REMEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.REMUEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.LWEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.LHEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.LHUEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.LBEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.LBUEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.SWEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.SHEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.SBEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.BEQEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.BNEEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.BLTEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.BLTUEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.BGEEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.BGEUEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.JALEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.JALREvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.INTREvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.ENDEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.FENCEEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.FENCE_IEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.EXTRWEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.CCR_DECLEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.CCR_SETEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.CCR_CLREvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MMU_CONFEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MMU_SETIDEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.SS_PACK_SHAPEEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.SS_PACK_STRIDEEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.L2_LOAD_CONFEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.L2_LOAD_W_CONFEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.L2_LOAD_WEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.L2_STORE_CONFEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.L2_LOADEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.L2_STOREEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.DM_CONF_BROADCASTEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.DM_LOAD_L1_CONFEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.DM_LOAD_W_CONFEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.DM_LOAD_W_CONF2Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.DM_LOAD_W_CONF_DEQEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.DM_STORE_OF_CONFEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.DM_LOAD_L1Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.DM_LOAD_WEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.DM_LOAD_ACT0Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.DM_STORE_OFEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_FETCHIF_CONF1Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.EXTRAWEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_FETCHIF_CONF2Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_FETCHIF_CONF3Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_FETCHIF_CONF4Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_FETCHIF_CONF_DEQEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_W_CONFEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_OF_CONF1Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_OF_CONF2Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_COMPUTE_CONFEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_COMPUTEEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_FORWARD_PSUMEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_PDP0_MODE_CONFEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_PDP0_FETCHIF_CONF1Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_PDP0_FETCHIF_CONF2Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_PDP0_FETCHIF_CONF3Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_PDP0_FETCHIF_CONF4Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_PDP0_CONF_DEQEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_PDP0_W_CONFEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_PDP0_OF_CONFEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.PU_PDP0_COMPUTEEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.ACT0_SRC1_CONFEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.ACT0_COMPUTEEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_MEMCPYEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_MEMSETEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_TRANSPOSE_CONFEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_TRANSPOSEEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_PDP1_CONF1Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_PDP1_CONF2Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_PDP1_CONF3Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_PDP1_CONF4Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_PDP1_CONF_DEQEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_PDP1_CONF_QUANTEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_PDP1_COMPUTEEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_ACT1_CONF_STRIDEEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_ACT1_CONF_SRC1Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_ACT1_CONF_SRC2Evaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_ACT1_CONF_DESTEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_ACT1_CONF_DEQEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_ACT1_CONF_QUANTEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_ACT1_CONFEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.MFU_ACT1_COMPUTEEvaluator>(reuse: Reuse.Singleton);
        registrator.RegisterManyInterface<TIR.Instructions.AI2D_COMPUTEEvaluator>(reuse: Reuse.Singleton);
    }
}
