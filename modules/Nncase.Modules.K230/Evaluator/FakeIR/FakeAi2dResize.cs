﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Diagnostics;
using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.TIR.Instructions;
using OrtKISharp;
using static Nncase.Evaluator.K230Kernels;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Evaluator.K230;

[TypeInferGenerator]
public partial class FakeAi2dResizeEvaluator : IEvaluator<FakeAi2dResize>, ITypeInferencer<FakeAi2dResize>, ICostEvaluator<FakeAi2dResize>
{
    public Cost Visit(ICostEvaluateContext context, FakeAi2dResize target)
    {
        var inputType = context.GetArgumentType<TensorType>(target, FakeAi2dResize.Input);
        var returnType = context.GetReturnType<TensorType>();
        return new()
        {
            [CostFactorNames.MemoryLoad] = CostUtility.GetMemoryAccess(inputType),
            [CostFactorNames.MemoryStore] = CostUtility.GetMemoryAccess(returnType),
            [CostFactorNames.CPUCycles] = CostUtility.GetCPUCycles(returnType, 1),
        };
    }

    public IValue Visit(IEvaluateContext context, FakeAi2dResize r)
    {
        var input = context.GetArgumentValueAsTensor(r, FakeAi2dResize.Input);
        int[] newSize = context.GetArgumentValueAsArray<int>(r, FakeAi2dResize.NewSize);

        // when HasBindedMixQuantInfo is true, eval will do simulation of quant/dequant for some inputs, this is used for evaluate accumulated quant error for layers.
        if (context.CurrentCall.EnodeBestQuantConfigWithCosine != null)
        {
            var pattern = IsRangeOfMarker(IsWildcard(), IsWildcard());
            if (pattern.MatchLeaf(context.CurrentCall.Arguments[0]) && ((Marker)context.CurrentCall.Arguments[0]).MixQuantInfo?.HasBindedMixQuantInfo == true)
            {
                var quantParam = ((Marker)context.CurrentCall.Arguments[0]).MixQuantInfo!.QuantParameter;

                // input feature map quantParam count should be 1 since input feature map quant is by tensor.
                Trace.Assert(quantParam.Count == 1);
                float[] inputFloat = input.ToArray<float>();
                for (int i = 0; i < inputFloat.Length; i++)
                {
                    double inputQuant = (inputFloat[i] / (double)quantParam[0].Scale) + quantParam[0].ZeroPoint;
                    if (!(quantParam[0].Scale.Equals(1.0f) && quantParam[0].ZeroPoint == 0))
                    {
                        inputQuant = System.Math.Round(inputQuant);
                    }

                    double inputDeQuant = ((double)inputQuant - quantParam[0].ZeroPoint) * (double)quantParam[0].Scale;
                    inputFloat[i] = (float)inputDeQuant;
                }

                input = Value.FromTensor(Tensor.From<float>(inputFloat, input.Shape)).AsTensor();
            }
        }

        if (r.ResizeMethod == (MFU_CROP_RESIZE)ImageResizeMode.Bilinear)
        {
            var result = FakeAi2dResizeBilinear(input, newSize, r.AlignCorners, r.HalfPixelCenters);
            return Value.FromTensor(result);
        }
        else
        {
            var result = FakeAi2dResizeNearestNeighbor(input, newSize, r.AlignCorners, r.HalfPixelCenters);
            return Value.FromTensor(result);
        }
    }

    private IRType Visit(ITypeInferenceContext context, FakeAi2dResize target, TensorType input)
    {
        if (input.Shape[2] == 1 && input.Shape[3] == 1)
        {
            return new InvalidType("FakeAi2dResize doesn't support 1x1 input");
        }

        var newSize = (Shape)context.GetArgument(target, FakeAi2dResize.NewSize);
        return TypeInference.ResizeType(input, newSize, null);
    }
}
