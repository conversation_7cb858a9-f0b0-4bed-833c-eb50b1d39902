// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;
using OrtKISharp;
using static Nncase.Evaluator.EvaluatorUtil;
using static Nncase.Evaluator.K230Kernels;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Evaluator.K230;

[TypeInferGenerator]
public partial class FakeConv2DTransposeEvaluator : IEvaluator<FakeConv2DTranspose>, ITypeInferencer<FakeConv2DTranspose>, ICostEvaluator<FakeConv2DTranspose>
{
    /// <inheritdoc />
    public Cost Visit(ICostEvaluateContext context, FakeConv2DTranspose target)
    {
        var inputType = context.GetArgumentType<TensorType>(target, FakeConv2DTranspose.Input);
        var weightsType = context.GetArgumentType<TensorType>(target, FakeConv2DTranspose.Weights);
        var weightsShape = weightsType.Shape;
        var outputType = context.GetReturnType<TensorType>();

        var macPerElement = weightsShape[1] * weightsShape[2] * weightsShape[3];
        return new()
        {
            [CostFactorNames.MemoryLoad] = CostUtility.GetMemoryAccess(inputType) + CostUtility.GetMemoryAccess(weightsType),
            [CostFactorNames.MemoryStore] = CostUtility.GetMemoryAccess(outputType),
            [CostFactorNames.CPUCycles] = CostUtility.GetCPUCycles(outputType, (float)(macPerElement.FixedValue * 2) / (24 * 32)),
        };
    }

    public IValue Visit(IEvaluateContext context, FakeConv2DTranspose conv)
    {
        var input = context.GetOrtArgumentValue(conv, FakeConv2DTranspose.Input);
        var weights = context.GetOrtArgumentValue(conv, FakeConv2DTranspose.Weights);
        long[] stride = context.GetArgumentValueAsArray<long>(conv, FakeConv2DTranspose.Stride);
        var pads = context.GetOrtArgumentValue(conv, FakeConv2DTranspose.Padding);
        long[] dilation = context.GetArgumentValueAsArray<long>(conv, FakeConv2DTranspose.Dilation);
        long groups = context.GetArgumentValueAsScalar<long>(conv, FakeConv2DTranspose.Groups);
        long[] outputShape = context.GetArgumentValueAsArray<long>(conv, FakeConv2DTranspose.OutputShape);
        long[] outputPaddings = context.GetArgumentValueAsArray<long>(conv, FakeConv2DTranspose.OutputPadding);
        long[] kernelShape = weights.Shape;

        long[] padding = context.GetArgumentValueAsArray<long>(conv, FakeConv2DTranspose.Padding).ToArray();
        long[] inputShape = input.Shape;
        long[] outputPadding = context.GetArgumentValueAsArray<long>(conv, FakeConv2DTranspose.OutputPadding);
        long[] weightsShape = weights.Shape;
        if (GetWindowedOutputSize(
                (int)outputShape[2] + (int)padding[0] + (int)padding[1] - (int)outputPadding[0],
                (int)weightsShape[2],
                (int)stride[0],
                (int)dilation[0],
                false) != inputShape[2]
            || GetWindowedOutputSize(
                (int)outputShape[3] + (int)padding[2] + (int)padding[3] - (int)outputPadding[1],
                (int)weightsShape[3],
                (int)stride[1],
                (int)dilation[1],
                false) != inputShape[3])
        {
            throw new InvalidOleVariantTypeException("Invalid conv2d transpose shape");
        }

        var act = context.GetArgumentValueAsTensor(conv, FakeConv2DTranspose.Act);

        // when HasBindedMixQuantInfo is true, eval will do simulation of quant/dequant for some inputs, this is used for evaluate accumulated quant error for layers.
        if (context.CurrentCall.EnodeBestQuantConfigWithCosine != null)
        {
            var pattern = IsRangeOfMarker(IsWildcard(), IsWildcard());
            if (pattern.MatchLeaf(context.CurrentCall.Arguments[0]) && ((Marker)context.CurrentCall.Arguments[0]).MixQuantInfo?.HasBindedMixQuantInfo == true)
            {
                var quantParam = ((Marker)context.CurrentCall.Arguments[0]).MixQuantInfo?.QuantParameter;

                // input feature map quantParam count should be 1 since input feature map quant is by tensor.
                Trace.Assert(quantParam!.Count == 1);
                float[] inputFloat = input.ToArray<float>();
                for (int i = 0; i < inputFloat.Length; i++)
                {
                    double inputQuant = (inputFloat[i] / (double)quantParam[0].Scale) + quantParam[0].ZeroPoint;
                    if (!(quantParam[0].Scale.Equals(1.0f) && quantParam[0].ZeroPoint == 0))
                    {
                        inputQuant = System.Math.Round(inputQuant);
                    }

                    double inputDeQuant = (inputQuant - quantParam[0].ZeroPoint) * quantParam[0].Scale;
                    inputFloat[i] = (float)inputDeQuant;
                }

                input = OrtKISharp.Tensor.MakeTensor(inputFloat, input.Shape);
            }

            if (pattern.MatchLeaf(context.CurrentCall.Arguments[1]) && ((Marker)context.CurrentCall.Arguments[1]).MixQuantInfo?.HasBindedMixQuantInfo == true)
            {
                var quantParam = ((Marker)context.CurrentCall.Arguments[1]).MixQuantInfo?.QuantParameter;
                float[] weightsFloat = weights.ToArray<float>();
                int oc = quantParam!.Count;
                int eachChannelData = weightsFloat.Length / oc;
                for (int i = 0; i < weightsFloat.Length; i++)
                {
                    double weightsQuant = (weightsFloat[i] / (double)quantParam[i / eachChannelData].Scale) + quantParam[i / eachChannelData].ZeroPoint;
                    if (!(quantParam[i / eachChannelData].Scale.Equals(1.0f) && quantParam[i / eachChannelData].ZeroPoint == 0))
                    {
                        weightsQuant = System.Math.Round(weightsQuant);
                    }

                    double weightsDeQuant = (weightsQuant - quantParam[i / eachChannelData].ZeroPoint) * quantParam[i / eachChannelData].Scale;
                    weightsFloat[i] = (float)weightsDeQuant;
                }

                weights = OrtKISharp.Tensor.MakeTensor(weightsFloat, weights.Shape);
            }
        }

        float[] inputDeq = input.ToArray<float>();
        float[] weightsDeq = weights.ToArray<float>();

        var outputSize = outputShape[0] * outputShape[1] * outputShape[2] * outputShape[3];
        float[] outCache = new float[outputSize];
        Array.Clear(outCache, 0, (int)outputSize);

        var gIC = inputShape[1] / groups;
        var gOC = outputShape[1] / groups;

        var weightsArray = weights.ToArray<float>();
        var inputsArray = input.ToArray<float>();
        int inputIndex = 0;
        for (int batch = 0; batch < inputShape[0]; batch++)
        {
            var outBatchP = outCache.AsSpan().Slice(batch * (int)outputShape[1] * (int)outputShape[2] * (int)outputShape[3]);

            for (int g = 0; g < groups; g++)
            {
                var outGroupP = outBatchP.Slice(g * (int)gOC * (int)outputShape[2] * (int)outputShape[3]);
                var wGroupP = weightsArray.AsSpan().Slice((int)g * (int)gOC * (int)gIC * (int)kernelShape[2] * (int)kernelShape[3]);

                for (int ic = 0; ic < gIC; ic++)
                {
                    for (int iy = 0; iy < inputShape[2]; iy++)
                    {
                        for (int ix = 0; ix < inputShape[3]; ix++)
                        {
                            int outYOrigin = (int)((iy * stride[0]) - padding[0]);
                            int outXOrigin = (int)((ix * stride[1]) - padding[2]);
                            int filterYStart = System.Math.Max(0, (int)((-outYOrigin + dilation[0] - 1) / dilation[0]));
                            int filterYEnd = (int)System.Math.Min(kernelShape[2], ((int)outputShape[2] - outYOrigin + dilation[0] - 1) / dilation[0]);
                            int filterXStart = (int)System.Math.Max(0, (-outXOrigin + dilation[1] - 1) / dilation[1]);
                            int filterXEnd = (int)System.Math.Min(kernelShape[3], ((int)outputShape[3] - outXOrigin + dilation[1] - 1) / dilation[1]);

                            float inV;
                            if (ix < 0 || ix >= inputShape[3] || iy < 0 || iy >= inputShape[2])
                            {
                                inV = 0f;
                            }
                            else
                            {
                                inV = inputsArray[inputIndex];
                            }

                            inputIndex++;

                            for (int oc = 0; oc < gOC; oc++)
                            {
                                var outCP = outGroupP.Slice((int)(oc * outputShape[2] * outputShape[3]));
                                var wOCP = wGroupP.Slice((int)(oc * gIC * kernelShape[2] * kernelShape[3]));
                                var wICP = wOCP.Slice((int)(ic * kernelShape[2] * kernelShape[3]));

                                for (int ky = filterYStart; ky < filterYEnd; ky++)
                                {
                                    for (int kx = filterXStart; kx < filterXEnd; kx++)
                                    {
                                        int outY = (int)(outYOrigin + (dilation[0] * ky));
                                        int outX = (int)(outXOrigin + (dilation[1] * kx));

                                        var w = wICP[(int)((ky * kernelShape[3]) + kx)];

                                        outCP[(int)((outY * outputShape[3]) + outX)] += (float)inV * w;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        var result = Tensor.From(outCache, outputShape.ToArray());
        float[] resultArray = result.ToArray<float>();
        float[] output = new float[ComputeSize(result.Shape)];
        int channelSize = (int)(result.Dimensions[2] * result.Dimensions[3]);
        for (int i = 0; i < resultArray.Length; i++)
        {
            long oc = i / channelSize;
            output[i] = FakeApplyAct0(resultArray[i], act.ToArray<float>(), (int)oc, 0);
        }

        float[] roundedOutput = output.Select(x => (float)System.Math.Round(x)).ToArray();
        return Value.FromTensor(Tensor.From(output, outputShape.ToArray()));
    }

    private IRType Visit(ITypeInferenceContext context, FakeConv2DTranspose target, TensorType input)
    {
        if (context.GetArgument(target, FakeConv2DTranspose.OutputShape) is TensorConst outShapeValue)
        {
            return new TensorType(input.DType, new RankedShape(outShapeValue.Value.ToArray<long>()));
        }
        else
        {
            return new InvalidType("Conv2dTranspose can't infer shape with dynamic outputShape");
        }
    }
}
