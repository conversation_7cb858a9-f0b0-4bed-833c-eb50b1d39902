﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Diagnostics;
using System.Runtime.InteropServices;
using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;
using OrtKISharp;
using static Nncase.Evaluator.K230Kernels;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Evaluator.K230;

[EvaluatorGenerator]
[TypeInferGenerator]
public sealed partial class FakeMatMulEvaluator : IEvaluator<FakeMatMul>, ITypeInferencer<FakeMatMul>, ICostEvaluator<FakeMatMul>
{
    /// <inheritdoc />
    public Cost Visit(ICostEvaluateContext context, FakeMatMul target)
    {
        var lhs = context.GetArgumentType<TensorType>(target, FakeMatMul.InputA);
        var rhs = context.GetArgumentType<TensorType>(target, FakeMatMul.InputB);
        var act = context.GetArgumentType<TensorType>(target, FakeMatMul.Act);
        var outputType = context.GetReturnType<TensorType>();

        var macPerElement = lhs.Shape[^1].IsFixed ? (uint)lhs.Shape[^1].FixedValue : 1;
        float puMac = 24 * 32;
        return new()
        {
            [CostFactorNames.MemoryLoad] = CostUtility.GetMemoryAccess(lhs) + CostUtility.GetMemoryAccess(rhs) +
                                           CostUtility.GetMemoryAccess(act),
            [CostFactorNames.MemoryStore] = CostUtility.GetMemoryAccess(outputType),
            [CostFactorNames.CPUCycles] = CostUtility.GetCPUCycles(outputType, macPerElement / puMac),
        };
    }

    private IValue Visit(IEvaluateContext context, OrtKISharp.Tensor inputA, OrtKISharp.Tensor inputB, Tensor<float> act)
    {
        // when HasBindedMixQuantInfo is true, eval will do simulation of quant/dequant for some inputs, this is used for evaluate accumulated quant error for layers.
        if (context.CurrentCall.EnodeBestQuantConfigWithCosine != null)
        {
            var pattern = IsRangeOfMarker(IsWildcard(), IsWildcard());
            if (pattern.MatchLeaf(context.CurrentCall.Arguments[0]) &&
                ((Marker)context.CurrentCall.Arguments[0]).MixQuantInfo?.HasBindedMixQuantInfo ==
                true)
            {
                var quantParam = ((Marker)context.CurrentCall.Arguments[0]).MixQuantInfo!
                    .QuantParameter;

                // input feature map quantParam count should be 1 since input feature map quant is by tensor.
                Trace.Assert(quantParam.Count == 1);
                float[] inputAFloat = inputA.ToArray<float>();
                for (int i = 0; i < inputAFloat.Length; i++)
                {
                    double inputAQuant = (inputAFloat[i] / (double)quantParam[0].Scale) + quantParam[0].ZeroPoint;
                    if (!(quantParam[0].Scale.Equals(1.0f) && quantParam[0].ZeroPoint == 0))
                    {
                        inputAQuant = System.Math.Round(inputAQuant);
                    }

                    double inputADeQuant = (inputAQuant - quantParam[0].ZeroPoint) * quantParam[0].Scale;
                    inputAFloat[i] = (float)inputADeQuant;
                }

                inputA = OrtKISharp.Tensor.MakeTensor(inputAFloat, inputA.Shape);
            }

            if (pattern.MatchLeaf(context.CurrentCall.Arguments[1]) &&
                ((Marker)context.CurrentCall.Arguments[1]).MixQuantInfo?.HasBindedMixQuantInfo ==
                true)
            {
                var quantParam = ((Marker)context.CurrentCall.Arguments[1]).MixQuantInfo?.QuantParameter;
                float[] inputBFloat = inputB.ToArray<float>();
                int oc = quantParam!.Count;
                int eachChannelData = inputBFloat.Length / oc;
                for (int i = 0; i < inputBFloat.Length; i++)
                {
                    double weightsQuant = (inputBFloat[i] / (double)quantParam[i / eachChannelData].Scale) + quantParam[i / eachChannelData].ZeroPoint;
                    if (!(quantParam[i / eachChannelData].Scale.Equals(1.0f) && quantParam[i / eachChannelData].ZeroPoint == 0))
                    {
                        weightsQuant = System.Math.Round(weightsQuant);
                    }

                    double weightsDeQuant = (weightsQuant - quantParam[i / eachChannelData].ZeroPoint) * quantParam[i / eachChannelData].Scale;
                    inputBFloat[i] = (float)weightsDeQuant;
                }

                inputB = OrtKISharp.Tensor.MakeTensor(inputBFloat, inputB.Shape);
            }
        }

        long aBatches = inputA.Shape[1];
        long bBatches = inputB.Shape[1];
        var result = OrtKI.MatMul(inputA, inputB).ToTensor();
        float[] resultArray = result.ToArray<float>();
        float[] output = new float[ComputeSize(result.Shape)];
        if (aBatches == bBatches || (aBatches > bBatches && bBatches == 1) ||
            (aBatches < bBatches && aBatches == 1))
        {
            for (int i = 0; i < output.Length; i++)
            {
                output[i] = FakeApplyAct0(resultArray[i], act.ToArray<float>(), i / (int)(result.Shape[2].FixedValue * result.Shape[3].FixedValue), 0);
            }
        }
        else
        {
            throw new InvalidOleVariantTypeException("Invalid matmul");
        }

        return Value.FromTensor(Tensor.From(output, result.Shape));
    }

    private IRType Visit(TensorType inputA, TensorType inputB)
    {
        if (inputA.Shape.IsUnranked || inputB.Shape.IsUnranked)
        {
            return new InvalidType("Shape InputA or InputB Can't Be Unranked");
        }

        if (inputA.Shape[3] != inputB.Shape[2])
        {
            return new InvalidType("FakeMatMul input a's cols must be equal to input b's rows");
        }

        var shape = new RankedShape(
            System.Math.Max(inputA.Shape[0].FixedValue, inputB.Shape[0].FixedValue),
            System.Math.Max(inputA.Shape[1].FixedValue, inputB.Shape[1].FixedValue),
            inputA.Shape[2],
            inputB.Shape[3]);
        return new TensorType(inputA.DType, shape);
    }
}
