﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Diagnostics;
using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Shapes;
using OrtKISharp;
using static Nncase.Evaluator.EvaluatorUtil;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Evaluator.K230;

[TypeInferGenerator]
public partial class FakeAi2dPadEvaluator : IEvaluator<FakeAi2dPad>, ITypeInferencer<FakeAi2dPad>, ICostEvaluator<FakeAi2dPad>
{
    public IValue Visit(IEvaluateContext context, FakeAi2dPad r)
    {
        var input = context.GetArgumentValue(r, FakeAi2dPad.Input).AsTensor().Cast<float>().ToOrtTensor();
        var pads = context.GetInt64OrtTensorArgumentValue(r, FakeAi2dPad.Padding);
        var value = context.GetArgumentValue(r, FakeAi2dPad.Value).AsTensor().Cast<float>().ToOrtTensor();

        // when HasBindedMixQuantInfo is true, eval will do simulation of quant/dequant for some inputs, this is used for evaluate accumulated quant error for layers.
        if (context.CurrentCall.EnodeBestQuantConfigWithCosine != null)
        {
            var pattern = IsRangeOfMarker(IsWildcard(), IsWildcard());
            if (pattern.MatchLeaf(context.CurrentCall.Arguments[0]) &&
                ((Marker)context.CurrentCall.Arguments[0]).MixQuantInfo?.HasBindedMixQuantInfo ==
                true)
            {
                var quantParam = ((Marker)context.CurrentCall.Arguments[0]).MixQuantInfo!.QuantParameter;

                // input feature map quantParam count should be 1 since input feature map quant is by tensor.
                Trace.Assert(quantParam!.Count == 1);
                float[] inputFloat = input.ToArray<float>();
                for (int i = 0; i < inputFloat.Length; i++)
                {
                    double inputQuant = (inputFloat[i] / (double)quantParam[0].Scale) + quantParam[0].ZeroPoint;
                    if (!(quantParam[0].Scale.Equals(1.0f) && quantParam[0].ZeroPoint == 0))
                    {
                        inputQuant = System.Math.Round(inputQuant);
                    }

                    double inputDeQuant = (inputQuant - quantParam[0].ZeroPoint) * quantParam[0].Scale;
                    inputFloat[i] = (float)inputDeQuant;
                }

                input = OrtKISharp.Tensor.MakeTensor(inputFloat, input.Shape);
            }
        }

        if (r.Mode == PadMode.Symmetric)
        {
            throw new NotImplementedException();
        }

        // if (r.mode == PadMode.Edge)
        // {
        //     return OrtKI.Cast(
        //         OrtKI.Pad(input, ToOnnxPadFormat(pads), value, "edge"),
        //         (int)OrtDataType.Float).ToValue();
        // }
        //
        // if (r.mode == PadMode.Reflect)
        // {
        //     return OrtKI.Cast(
        //         OrtKI.Pad(input, ToOnnxPadFormat(pads), value, "reflect"),
        //         (int)OrtDataType.Float).ToValue();
        // }
        return OrtKI.Cast(
            OrtKI.Pad(input, ToOnnxPadFormat(pads), value, r.Mode.ToString().ToLower(null)),
            (int)OrtDataType.Float).ToValue();
    }

    public Cost Visit(ICostEvaluateContext context, FakeAi2dPad target)
    {
        var inputType = context.GetArgumentType<TensorType>(target, FakeAi2dPad.Input);
        var outputType = context.GetReturnType<TensorType>();

        return new()
        {
            [CostFactorNames.MemoryLoad] = CostUtility.GetMemoryAccess(inputType) / 2,
            [CostFactorNames.MemoryStore] = CostUtility.GetMemoryAccess(outputType) / 2,
        };
    }

    private IRType Visit(ITypeInferenceContext context, FakeAi2dPad target, TensorType input)
    {
        var paddings = (Paddings)context.GetArgument(target, FakeAi2dPad.Padding);
        var value = (Expr)context.GetArgument(target, FakeAi2dPad.Value);
        return TypeInference.PadType(input, paddings, value);
    }
}
