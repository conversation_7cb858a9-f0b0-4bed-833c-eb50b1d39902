// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.
using System;
using System.Diagnostics;
using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.NN;
using Nncase.IR.Shapes;
using Nncase.Passes;
using OrtKISharp;
using static Nncase.Evaluator.EvaluatorUtil;
using static Nncase.Evaluator.K230Kernels;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Evaluator.K230;

[EvaluatorGenerator]
[TypeInferGenerator]
public partial class FakeConv2DEvaluator : IEvaluator<FakeConv2D>, ITypeInferencer<FakeConv2D>, ICostEvaluator<FakeConv2D>
{
    /// <inheritdoc />
    public Cost Visit(ICostEvaluateContext context, FakeConv2D target)
    {
        var inputType = context.GetArgumentType<TensorType>(target, FakeConv2D.Input);
        var weightsType = context.GetArgumentType<TensorType>(target, FakeConv2D.Weights);
        var outputType = context.GetReturnType<TensorType>();

        var weightsShape = weightsType.Shape;
        var macPerElement = (2 * weightsShape[1] * weightsShape[2] * weightsShape[3]) - 1;
        return new()
        {
            [CostFactorNames.MemoryLoad] =
                CostUtility.GetMemoryAccess(inputType) + CostUtility.GetMemoryAccess(weightsType),
            [CostFactorNames.MemoryStore] = CostUtility.GetMemoryAccess(outputType),
            [CostFactorNames.CPUCycles] =
                CostUtility.GetCPUCycles(outputType, (float)macPerElement.FixedValue / (32 * 24)),
        };
    }

    private Const Visit(IEvaluateContext context, OrtKISharp.Tensor input, OrtKISharp.Tensor weights, Tensor act, Tensor<long> stride, OrtKISharp.Tensor padding, Tensor<long> dilation, long groups)
    {
        var pattern = IsRangeOfMarker(IsWildcard(), IsWildcard());

        // when HasBindedMixQuantInfo is true, eval will do simulation of quant/dequant for some inputs, this is used for evaluate accumulated quant error for layers.
        if (context.CurrentCall.EnodeBestQuantConfigWithCosine != null)
        {
            if (pattern.MatchLeaf(context.CurrentCall.Arguments[0]) &&
                ((Marker)context.CurrentCall.Arguments[0]).MixQuantInfo?.HasBindedMixQuantInfo ==
                true)
            {
                var quantParam = ((Marker)context.CurrentCall.Arguments[0]).MixQuantInfo!.QuantParameter;

                // input feature map quantParam count should be 1 since input feature map quant is by tensor.
                Trace.Assert(quantParam.Count == 1);
                float[] inputFloat = input.ToArray<float>();
                for (int i = 0; i < inputFloat.Length; i++)
                {
                    double inputQuant = ((double)inputFloat[i] / quantParam[0].Scale) + quantParam[0].ZeroPoint;
                    if (!(quantParam[0].Scale == 1.0f && quantParam[0].ZeroPoint == 0))
                    {
                        inputQuant = System.Math.Round(inputQuant);
                    }

                    double inputDeQuant = (inputQuant - quantParam[0].ZeroPoint) * quantParam[0].Scale;
                    inputFloat[i] = (float)inputDeQuant;
                }

                input = OrtKISharp.Tensor.MakeTensor(inputFloat, input.Shape);
            }

            if (pattern.MatchLeaf(context.CurrentCall.Arguments[1]) &&
                ((Marker)context.CurrentCall.Arguments[1]).MixQuantInfo?.HasBindedMixQuantInfo ==
                true)
            {
                var quantParam = ((Marker)context.CurrentCall.Arguments[1]).MixQuantInfo!.QuantParameter;
                float[] weightsFloat = weights.ToArray<float>();
                int oc = quantParam.Count;
                int eachChannelData = weightsFloat.Length / oc;
                for (int i = 0; i < weightsFloat.Length; i++)
                {
                    double weightsQuant = (weightsFloat[i] / (double)quantParam[i / eachChannelData].Scale) + quantParam[i / eachChannelData].ZeroPoint;
                    if (!(quantParam[i / eachChannelData].Scale == 1.0f && quantParam[i / eachChannelData].ZeroPoint == 0))
                    {
                        weightsQuant = System.Math.Round(weightsQuant);
                    }

                    double weightsDeQuant = (weightsQuant - quantParam[i / eachChannelData].ZeroPoint) * quantParam[i / eachChannelData].Scale;
                    weightsFloat[i] = (float)weightsDeQuant;
                }

                weights = OrtKISharp.Tensor.MakeTensor(weightsFloat, weights.Shape);
            }
        }

        // when ada info is not null, eval will do simulation of quant/dequant for some inputs, this is used for evaluate accumulated quant error for layers.
        if (pattern.MatchLeaf(context.CurrentCall.Arguments[0]) && ((Marker)context.CurrentCall.Arguments[0]).AdaQuantInfo?.InputQuantParameter != null)
        {
            var quantParam = ((Marker)context.CurrentCall.Arguments[0]).AdaQuantInfo!.InputQuantParameter;
            float[] inputFloat = input.ToArray<float>();
            for (int i = 0; i < inputFloat.Length; i++)
            {
                double inputQuant = (inputFloat[i] / (double)quantParam.Scale) + quantParam.ZeroPoint;
                if (quantParam is not { Scale: 1.0f, ZeroPoint: 0 })
                {
                    inputQuant = System.Math.Round(inputQuant);
                }

                double inputDeQuant = (inputQuant - quantParam.ZeroPoint) * quantParam.Scale;
                inputFloat[i] = (float)inputDeQuant;
            }

            input = OrtKISharp.Tensor.MakeTensor(inputFloat, input.Shape);
        }

        var result = OrtKI.Conv(input, weights, Proc((int)weights.Shape[0]), "NOTSET", dilation.ToArray(), groups, new[] { weights.Shape[2], weights.Shape[3] }, ToOnnxPadFormat(padding), stride.ToArray()).ToTensor();

        if (context.CurrentCall.Arguments[0] is Marker)
        {
            if (((Marker)context.CurrentCall.Arguments[0]).AdaQuantInfo == null)
            {
                ((Marker)context.CurrentCall.Arguments[0]).AdaQuantInfo = new AdaQuantInfo();
            }

            ((Marker)context.CurrentCall.Arguments[0]).AdaQuantInfo!.AdaRoundRefTensor = result;
        }

        // var outChannel = Weights.Shape[0];
        float[] resultArray = result.ToArray<float>();
        float[] output = new float[ComputeSize(result.Shape)];
        int channelSize = (int)(result.Shape[2].FixedValue * result.Shape[3].FixedValue);
        for (int i = 0; i < resultArray.Length; i++)
        {
            int oc = i / channelSize;
            output[i] = FakeApplyAct0(resultArray[i], act.ToArray<float>(), oc, 0);
        }

        // output.Select((_, i) => output[i] = apply_act0(resultArray[i], Act.ToArray<Half>(), i / channel_size, 0)).AsParallel().ToArray();
        return Const.FromTensor(Tensor.From(output, result.Shape));
    }

    private IRType Visit(ITypeInferenceContext context, FakeConv2D target, TensorType input, TensorType weights)
    {
        var args = context.GetArguments(target, FakeConv2D.Stride, FakeConv2D.Padding, FakeConv2D.Dilation, FakeConv2D.Groups);
        return TypeInference.Conv2DType(input, weights, (Shape)args[0], (Paddings)args[1], (Shape)args[2], (Dimension)args[3]);
    }
}
