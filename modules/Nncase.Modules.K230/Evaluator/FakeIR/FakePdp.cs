// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Diagnostics;
using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Shapes;
using OrtKISharp;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Evaluator.K230;

[TypeInferGenerator]
public partial class FakePdpEvaluator : IEvaluator<FakePdp>, ITypeInferencer<FakePdp>, ICostEvaluator<FakePdp>
{
    /// <inheritdoc/>
    public Cost Visit(ICostEvaluateContext context, FakePdp target)
    {
        var inputType = context.GetArgumentType<TensorType>(target, FakePdp.Input);
        var outputType = context.GetReturnType<TensorType>();

        float macPerElement = 1;
        float puMac = 5;
        return new()
        {
            [CostFactorNames.MemoryLoad] = CostUtility.GetMemoryAccess(inputType),
            [CostFactorNames.MemoryStore] = CostUtility.GetMemoryAccess(outputType),
            [CostFactorNames.CPUCycles] = CostUtility.GetCPUCycles(outputType, macPerElement / puMac),
        };
    }

    public IValue Visit(IEvaluateContext context, FakePdp r)
    {
        var input = context.GetOrtArgumentValue(r, FakePdp.Input);
        long[] kernelSize = context.GetArgumentValueAsArray<long>(r, FakePdp.Filter);
        long[] strides = context.GetArgumentValueAsArray<long>(r, FakePdp.Stride);
        long[] pads = context.GetArgumentValueAsArray<long>(r, FakePdp.Padding);

        // when HasBindedMixQuantInfo is true, eval will do simulation of quant/dequant for some inputs, this is used for evaluate accumulated quant error for layers.
        if (context.CurrentCall.EnodeBestQuantConfigWithCosine != null)
        {
            var pattern = IsRangeOfMarker(IsWildcard(), IsWildcard());
            if (pattern.MatchLeaf(context.CurrentCall.Arguments[0]) && ((Marker)context.CurrentCall.Arguments[0]).MixQuantInfo?.HasBindedMixQuantInfo == true)
            {
                var quantParam = ((Marker)context.CurrentCall.Arguments[0]).MixQuantInfo!.QuantParameter;

                // input feature map quantParam count should be 1 since input feature map quant is by tensor.
                Trace.Assert(quantParam.Count == 1);
                float[] inputFloat = input.ToArray<float>();
                for (int i = 0; i < inputFloat.Length; i++)
                {
                    double inputQuant = (inputFloat[i] / (double)quantParam[0].Scale) + quantParam[0].ZeroPoint;
                    if (!(quantParam[0].Scale.Equals(1.0f) && quantParam[0].ZeroPoint == 0))
                    {
                        inputQuant = System.Math.Round(inputQuant);
                    }

                    double inputDeQuant = (inputQuant - quantParam[0].ZeroPoint) * quantParam[0].Scale;
                    inputFloat[i] = (float)inputDeQuant;
                }

                input = OrtKISharp.Tensor.MakeTensor(inputFloat, input.Shape);
            }
        }

        return (r.ReduceOp switch
        {
            ReduceOp.Min => throw new NotSupportedException("Unsupported MFU_PDP_OP MIN"),
            ReduceOp.Max => OrtKI.MaxPool(input, "NOTSET", 0, new long[] { 1, 1 }, kernelSize, pads, 0, strides)[0],
            ReduceOp.Mean => OrtKI.AveragePool(input, "NOTSET", 0, 0, kernelSize, pads, strides),
            ReduceOp.Sum => throw new NotSupportedException("Unsupported MFU_PDP_OP SUM"),
            _ => throw new ArgumentOutOfRangeException(nameof(r)),
        }).ToValue();
    }

    private IRType Visit(ITypeInferenceContext context, FakePdp target, TensorType input)
    {
        // var args = context.GetArguments(target, FakePdp.Filter, FakePdp.Stride, FakePdp.Padding);
        // return TypeInference.ReduceWindow2DType(Input, args[0], args[1], args[2], false);
        var args = context.GetArguments(target, FakePdp.Filter, FakePdp.Stride, FakePdp.Padding);
        var outputType = TypeInference.ReduceWindow2DType(input, (Shape)args[0], (Shape)args[1], (Paddings)args[2], false);
        return new TensorType(DataTypes.Float32, ((TensorType)outputType).Shape);
    }
}
