// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Diagnostics;
using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;
using static Nncase.Evaluator.K230Kernels;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Evaluator.K230;

[TypeInferGenerator]
public partial class FakeActivationEvaluator : IEvaluator<FakeActivation>, ITypeInferencer<FakeActivation>, ICostEvaluator<FakeActivation>
{
    /// <inheritdoc />
    public Cost Visit(ICostEvaluateContext context, FakeActivation target)
    {
        var lhsType = context.GetArgumentType<TensorType>(target, FakeActivation.InputA);
        var rhsType = context.GetArgumentType<IRType>(target, FakeActivation.InputB);
        var outputType = context.GetReturnType<TensorType>();
        return new()
        {
            [CostFactorNames.MemoryLoad] = CostUtility.GetMemoryAccess(lhsType) + (rhsType is TensorType t ? CostUtility.GetMemoryAccess(t) : 0),
            [CostFactorNames.MemoryStore] = CostUtility.GetMemoryAccess(outputType),
            [CostFactorNames.CPUCycles] = CostUtility.GetCPUCycles(outputType, 1 / 8f),
        };
    }

    /// <inheritdoc />
    public IValue Visit(IEvaluateContext context, FakeActivation a)
    {
        var inputA = context.GetArgumentValueAsTensor(a, FakeActivation.InputA);
        var inputB = context.GetArgumentValue(a, FakeActivation.InputB);
        bool hasUninitailizedInput = inputB is NoneValue || ((TensorType)inputB.Type).Shape.Rank == 0;
        var act = context.GetArgumentValueAsTensor(a, FakeActivation.Act);
        bool[] is16Segments = context.GetArgumentValueAsTensor(a, FakeActivation.Is16Segments).ToArray<bool>();
        int[] outChannel = context.GetArgumentValueAsTensor(a, FakeActivation.OutChannels).ToArray<int>();
        var outputShape = context.CurrentCall.CheckedShape;

        // when HasBindedMixQuantInfo is true, eval will do simulation of quant/dequant for some inputs, this is used for evaluate accumulated quant error for layers.
        if (context.CurrentCall.EnodeBestQuantConfigWithCosine != null)
        {
            var pattern = IsRangeOfMarker(IsWildcard(), IsWildcard());
            if (pattern.MatchLeaf(context.CurrentCall.Arguments[0]) &&
                ((Marker)context.CurrentCall.Arguments[0]).MixQuantInfo?.HasBindedMixQuantInfo ==
                true)
            {
                var quantParam = ((Marker)context.CurrentCall.Arguments[0]).MixQuantInfo!.QuantParameter;

                // input feature map quantParam count should be 1 since input feature map quant is by tensor.
                Trace.Assert(quantParam!.Count == 1);
                float[] inputAFloat = inputA.ToArray<float>();
                for (int i = 0; i < inputAFloat.Length; i++)
                {
                    double inputAQuant = (inputAFloat[i] / (double)quantParam[0].Scale) + quantParam[0].ZeroPoint;
                    if (!(quantParam[0].Scale.Equals(1.0f) && quantParam[0].ZeroPoint == 0))
                    {
                        inputAQuant = System.Math.Round(inputAQuant);
                    }

                    double inputADeQuant = (inputAQuant - quantParam[0].ZeroPoint) * quantParam[0].Scale;
                    inputAFloat[i] = (float)inputADeQuant;
                }

                inputA = Value.FromTensor(Tensor.From(inputAFloat, inputA.Shape)).AsTensor();
            }

            if (pattern.MatchLeaf(context.CurrentCall.Arguments[1]) &&
                ((Marker)context.CurrentCall.Arguments[1]).MixQuantInfo?.HasBindedMixQuantInfo ==
                true)
            {
                var quantParam = ((Marker)context.CurrentCall.Arguments[1]).MixQuantInfo!.QuantParameter;

                // input feature map quantParam count should be 1 since input feature map quant is by tensor.
                Trace.Assert(quantParam!.Count == 1);
                float[] inputBFloat = inputB.AsTensor().ToArray<float>();
                for (int i = 0; i < inputBFloat.Length; i++)
                {
                    double inputBQuant = (inputBFloat[i] / (double)quantParam[0].Scale) + quantParam[0].ZeroPoint;
                    if (!(quantParam[0].Scale.Equals(1.0f) && quantParam[0].ZeroPoint == 0))
                    {
                        inputBQuant = System.Math.Round(inputBQuant);
                    }

                    double inputBDeQuant = (inputBQuant - quantParam[0].ZeroPoint) * quantParam[0].Scale;
                    inputBFloat[i] = (float)inputBDeQuant;
                }

                inputB = Value.FromTensor(Tensor.From(inputBFloat, inputB.AsTensor().Shape));
            }
        }

        return Value.FromConst(FakeGnneActivation(is16Segments[0], inputA, hasUninitailizedInput ? new Tensor<float>(0) : inputB.AsTensor(), hasUninitailizedInput, outputShape, act.ToArray<float>(), outChannel[0], a.Type));
    }

    private IRType Visit(ITypeInferenceContext context, FakeActivation target, TensorType inputA)
    {
        if (context.GetArgument(target, FakeActivation.OutChannels) is not Const)
        {
            return new InvalidType("FakeActivation out_channels need a constant value");
        }

        var outputType = new TensorType(inputA.DType, target.OutputShape.ToArray());
        return outputType;
    }
}
