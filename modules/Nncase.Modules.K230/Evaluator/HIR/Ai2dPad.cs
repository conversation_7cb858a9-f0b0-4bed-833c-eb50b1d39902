﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Shapes;
using OrtKISharp;
using static Nncase.Evaluator.EvaluatorUtil;

namespace Nncase.Evaluator.K230;

[TypeInferGenerator]
public partial class Ai2dPadEvaluator : IEvaluator<Ai2dPad>, ITypeInferencer<Ai2dPad>, ICostEvaluator<Ai2dPad>
{
    public IValue Visit(IEvaluateContext context, Ai2dPad r)
    {
        var input = context.GetArgumentValue(r, Ai2dPad.Input).AsTensor();
        var pads = context.GetInt64OrtTensorArgumentValue(r, Ai2dPad.Padding);
        var value = context.GetArgumentValue(r, Ai2dPad.Value).AsTensor();
        float[] inputDeq = input.ToArray<float>();
        float[] valueArray = value.ToArray<float>();

        // if (r.mode != PadMode.Constant)
        // {
        //     input_deq.Select((_, i) =>
        //         ((input_deq[i] - InDeqBias[0]) / OutQuantParam[0].Scale + OutQuantParam[0].ZeroPoint));
        // }
        // else
        // {
        //     value_array.Select((_, i) =>
        //         ((value_array[i] - InDeqBias[0]) / OutQuantParam[0].Scale + OutQuantParam[0].ZeroPoint));
        // }
        if (r.OutputType == DataTypes.UInt8)
        {
            return OrtKI.Cast(
                OrtKI.Pad(
                    OrtKISharp.Tensor.MakeTensor(inputDeq, input.Dimensions.ToArray().Select(i => (long)i).ToArray()),
                    ToOnnxPadFormat(pads),
                    OrtKISharp.Tensor.MakeTensor(valueArray, value.Dimensions.ToArray().Select(i => (long)i).ToArray()),
                    r.Mode.ToString().ToLower(null)),
                (int)OrtDataType.UInt8).ToValue();

            // return OrtKI.Cast(
            //     OrtKI.Pad(
            //         input.Cast<float>().ToOrtTensor(),
            //         ToOnnxPadFormat(pads),
            //         value.Cast<float>().ToOrtTensor(),
            //         r.mode.ToString().ToLower()),
            //     (int)OrtDataType.UInt8).ToValue();
        }
        else if (r.OutputType == DataTypes.Int8)
        {
            return OrtKI.Cast(
                OrtKI.Pad(
                    OrtKISharp.Tensor.MakeTensor(inputDeq, input.Dimensions.ToArray().Select(i => (long)i).ToArray()),
                    ToOnnxPadFormat(pads),
                    OrtKISharp.Tensor.MakeTensor(valueArray, value.Dimensions.ToArray().Select(i => (long)i).ToArray()),
                    r.Mode.ToString().ToLower(null)),
                (int)OrtDataType.Int8).ToValue();
        }
        else if (r.OutputType == DataTypes.Int16)
        {
            return OrtKI.Cast(
                OrtKI.Pad(
                    OrtKISharp.Tensor.MakeTensor(inputDeq, input.Dimensions.ToArray().Select(i => (long)i).ToArray()),
                    ToOnnxPadFormat(pads),
                    OrtKISharp.Tensor.MakeTensor(valueArray, value.Dimensions.ToArray().Select(i => (long)i).ToArray()),
                    r.Mode.ToString().ToLower(null)),
                (int)OrtDataType.Int16).ToValue();
        }
        else
        {
            return OrtKI.Cast(
                OrtKI.Pad(
                    OrtKISharp.Tensor.MakeTensor(inputDeq, input.Dimensions.ToArray().Select(i => (long)i).ToArray()),
                    ToOnnxPadFormat(pads),
                    OrtKISharp.Tensor.MakeTensor(valueArray, value.Dimensions.ToArray().Select(i => (long)i).ToArray()),
                    r.Mode.ToString().ToLower(null)),
                (int)OrtDataType.Float16).ToValue();
        }
    }

    public Cost Visit(ICostEvaluateContext context, Ai2dPad target)
    {
        return new()
        {
            [CostFactorNames.CPUCycles] = 1,
        };
    }

    private IRType Visit(ITypeInferenceContext context, Ai2dPad target, TensorType input)
    {
        var paddings = (Paddings)context.GetArgument(target, Ai2dPad.Padding);
        var value = (Expr)context.GetArgument(target, Ai2dPad.Value);

        // if (paddings is TensorConst padding)
        // {
        //     var tpads = padding.Value.Cast<int>();
        //     var newShape = Input.Shape.ToList();
        //     int channel = tpads.Dimensions[0];
        //     for (int i = 0; i < channel; i++)
        //     {
        //         newShape[newShape.Count - channel + i] += tpads[i, 0] + tpads[i, 1];
        //     }
        //
        //     return new TensorType(Input.DType, new (newShape));
        // }
        return TypeInference.PadType(input, paddings, value);
    }
}
