﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Runtime.InteropServices;
using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;
using OrtKISharp;
using static Nncase.Evaluator.K230Kernels;
using Shape = Nncase.IR.Shape;

namespace Nncase.Evaluator.K230;

[TypeInferGenerator]
public partial class GNNEConv2DTransposeEvaluator : IEvaluator<GNNEConv2DTranspose>, ITypeInferencer<GNNEConv2DTranspose>, ICostEvaluator<GNNEConv2DTranspose>
{
    /// <inheritdoc/>
    public Cost Visit(ICostEvaluateContext context, GNNEConv2DTranspose target)
    {
        return new()
        {
            [CostFactorNames.CPUCycles] = 1,
        };
    }

    public IValue Visit(IEvaluateContext context, GNNEConv2DTranspose conv2DTranspose)
    {
        var input = context.GetArgumentValueAsTensor(conv2DTranspose, GNNEConv2DTranspose.Input);
        var weights = context.GetArgumentValueAsTensor(conv2DTranspose, GNNEConv2DTranspose.Weights);
        byte[] weightsBias = context.GetArgumentValueAsArray<byte>(conv2DTranspose, GNNEConv2DTranspose.WeightsBias);
        var act = context.GetArgumentValueAsArray<Half>(conv2DTranspose, GNNEConv2DTranspose.Act);
        byte deqBias = context.GetArgumentValueAsScalar<byte>(conv2DTranspose, GNNEConv2DTranspose.DeqBias);
        long shiftBits = context.GetArgumentValueAsScalar<long>(conv2DTranspose, GNNEConv2DTranspose.ShiftBits);
        long[] padding = context.GetArgumentValueAsArray<long>(conv2DTranspose, GNNEConv2DTranspose.Padding).ToArray();
        long[] stride = context.GetArgumentValueAsArray<long>(conv2DTranspose, GNNEConv2DTranspose.Stride);
        long[] dilation = context.GetArgumentValueAsArray<long>(conv2DTranspose, GNNEConv2DTranspose.Dilation);
        long groups = context.GetArgumentValueAsScalar<long>(conv2DTranspose, GNNEConv2DTranspose.Groups);
        long[] outputPadding = context.GetArgumentValueAsArray<long>(conv2DTranspose, GNNEConv2DTranspose.OutputPadding).ToArray();
        long[] outputShape = context.GetArgumentValueAsArray<long>(conv2DTranspose, GNNEConv2DTranspose.OutputShape);
        long[] inputShape = input.Shape.ToValueArray();
        long[] weightsShape = weights.Shape.ToValueArray();
        if (GetWindowedOutputSize((int)outputShape[2] + (int)padding[0] + (int)padding[1] - (int)outputPadding[0], (int)weightsShape[2], (int)stride[0], (int)dilation[0], false) != inputShape[2]
            || GetWindowedOutputSize((int)outputShape[3] + (int)padding[2] + (int)padding[3] - (int)outputPadding[1], (int)weightsShape[3], (int)stride[1], (int)dilation[1], false) != inputShape[3])
        {
            throw new InvalidOleVariantTypeException("Invalid conv2d transpose shape");
        }

        float[] inputDeq = input.ToArray<float>();
        inputDeq.Select((_, i) => inputDeq[i] -= deqBias).AsParallel().ToArray();
        float[] weightsDeq = weights.ToArray<float>();
        int qArgPerChannel = (int)(weights.Dimensions[1] * weights.Dimensions[2] * weights.Dimensions[3]);
        weightsDeq.Select((_, i) => weightsDeq[i] -= weightsBias[i / qArgPerChannel]).AsParallel().ToArray();

        var outputSize = outputShape[0] * outputShape[1] * outputShape[2] * outputShape[3];
        float[] outCache = new float[outputSize];
        Array.Clear(outCache, 0, (int)outputSize);

        var g_ic = inputShape[1] / groups;
        var g_oc = outputShape[1] / groups;
        int inputIndex = 0;
        for (int batch = 0; batch < inputShape[0]; batch++)
        {
            var out_batch_p = outCache.AsSpan().Slice(batch * (int)outputShape[1] * (int)outputShape[2] * (int)outputShape[3]);

            for (int g = 0; g < groups; g++)
            {
                var out_group_p = out_batch_p.Slice(g * (int)g_oc * (int)outputShape[2] * (int)outputShape[3]);
                var w_group_p = weightsDeq.ToArray<float>().AsSpan().Slice((int)g * (int)g_oc * (int)g_ic * (int)weightsShape[2] * (int)weightsShape[3]);

                for (int ic = 0; ic < g_ic; ic++)
                {
                    for (int iy = 0; iy < inputShape[2]; iy++)
                    {
                        for (int ix = 0; ix < inputShape[3]; ix++)
                        {
                            int out_y_origin = (int)((iy * stride[0]) - padding[0]);
                            int out_x_origin = (int)((ix * stride[1]) - padding[2]);
                            int filter_y_start = System.Math.Max(0, (int)((-out_y_origin + dilation[0] - 1) / dilation[0]));
                            int filter_y_end = (int)System.Math.Min(weightsShape[2], ((int)outputShape[2] - out_y_origin + dilation[0] - 1) / dilation[0]);
                            int filter_x_start = (int)System.Math.Max(0, (-out_x_origin + dilation[1] - 1) / dilation[1]);
                            int filter_x_end = (int)System.Math.Min(weightsShape[3], ((int)outputShape[3] - out_x_origin + dilation[1] - 1) / dilation[1]);

                            float in_v;
                            if (ix < 0 || ix >= inputShape[3] || iy < 0 || iy >= inputShape[2])
                            {
                                in_v = 0f;
                            }
                            else
                            {
                                in_v = inputDeq.ToArray<float>()[inputIndex];
                            }

                            inputIndex++;

                            for (int oc = 0; oc < g_oc; oc++)
                            {
                                var out_c_p = out_group_p.Slice((int)(oc * outputShape[2] * outputShape[3]));
                                var w_oc_p = w_group_p.Slice((int)(oc * g_ic * weightsShape[2] * weightsShape[3]));
                                var w_ic_p = w_oc_p.Slice((int)(ic * weightsShape[2] * weightsShape[3]));

                                for (int ky = filter_y_start; ky < filter_y_end; ky++)
                                {
                                    for (int kx = filter_x_start; kx < filter_x_end; kx++)
                                    {
                                        int out_y = (int)(out_y_origin + (dilation[0] * ky));
                                        int out_x = (int)(out_x_origin + (dilation[1] * kx));

                                        var w = w_ic_p[(int)((ky * weightsShape[3]) + kx)];

                                        out_c_p[(int)((out_y * outputShape[3]) + out_x)] += (float)in_v * w;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        var result = Tensor.From(outCache, outputShape.ToArray());
        float[] resultArray = result.ToArray<float>();
        float[] output = new float[ComputeSize(result.Shape)];
        int channelSize = (int)(result.Dimensions[2] * result.Dimensions[3]);
        for (int i = 0; i < resultArray.Length; i++)
        {
            int oc = i / channelSize;
            output[i] = ApplyAct0(resultArray[i], act.ToArray(), oc, (sbyte)shiftBits);
        }

        float[] roundedOutput = output.Select(x => (float)System.Math.Round(x)).ToArray();
        var halfOutput = output.Select(x => (Half)x).ToArray();

        var roundedOutputTensor = Tensor.From(roundedOutput, result.Shape);
        var halfOutputTensor = Tensor.From(halfOutput, result.Shape);

        if (conv2DTranspose.DestType == DataTypes.UInt8)
        {
            return Value.FromTensor(roundedOutputTensor.Cast<byte>(CastMode.KDefault));
        }

        if (conv2DTranspose.DestType == DataTypes.Int8)
        {
            return Value.FromTensor(roundedOutputTensor.Cast<sbyte>(CastMode.KDefault));
        }

        if (conv2DTranspose.DestType == DataTypes.Int16)
        {
            return Value.FromTensor(roundedOutputTensor.Cast<short>(CastMode.KDefault));
        }

        return Value.FromTensor(halfOutputTensor.Cast<Half>(CastMode.KDefault));
    }

    private IRType Visit(ITypeInferenceContext context, GNNEConv2DTranspose target, TensorType input, TensorType weights)
    {
        if (input.DType != DataTypes.Int8 && input.DType != DataTypes.UInt8 && input.DType != DataTypes.Int16)
        {
            var invalidType = new InvalidType("Unsupported input_type, should be one of [int8, uint8, int16]");
        }

        if (weights.DType != DataTypes.Int8 && weights.DType != DataTypes.UInt8 && weights.DType != DataTypes.Int16)
        {
            var invalidType = new InvalidType("Unsupported w_type, should be one of [int8, uint8, int16]");
        }

        if (input.DType == DataTypes.Int16 && weights.DType == DataTypes.Int16)
        {
            var invalidType = new InvalidType("int16 for both of input_type and w_type is not supported");
        }

        if (context.GetArgument(target, GNNEConv2DTranspose.OutputShape) is Const outShapeValue)
        {
            return new TensorType(target.DestType, new RankedShape(Value.FromConst(outShapeValue).AsTensor().ToArray<int>()));
        }
        else
        {
            return new InvalidType("Conv2dTranspose can't infer shape with dynamic outputShape");
        }
    }
}
