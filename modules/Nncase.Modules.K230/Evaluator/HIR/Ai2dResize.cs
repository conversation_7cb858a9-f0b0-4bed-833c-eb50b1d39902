﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.TIR.Instructions;
using static Nncase.Evaluator.K230Kernels;

namespace Nncase.Evaluator.K230;

[TypeInferGenerator]
public partial class Ai2dResizeEvaluator : IEvaluator<Ai2dResize>, ITypeInferencer<Ai2dResize>, ICostEvaluator<Ai2dResize>
{
    public Cost Visit(ICostEvaluateContext context, Ai2dResize target)
    {
        return new()
        {
            [CostFactorNames.CPUCycles] = 1,
        };
    }

    public IValue Visit(IEvaluateContext context, Ai2dResize r)
    {
        var input = context.GetArgumentValueAsTensor(r, Ai2dResize.Input);
        int[] newSize = context.GetArgumentValueAsArray<int>(r, Ai2dResize.NewSize);
        var inDeqBias = context.GetArgumentValueAsArray<int>(r, Ai2dResize.InDeqBias)[0];
        var quantParam = context.GetArgumentValueAsArray<QuantParam>(r, Ai2dResize.OutQuantParam)[0];

        if (r.ResizeMethod == (MFU_CROP_RESIZE)ImageResizeMode.Bilinear)
        {
            var result = Ai2dResizeBilinear(r.OutputDatatype, input, newSize, r.AlignCorners, r.HalfPixelCenters, inDeqBias, quantParam);
            return Value.FromTensor(result);
        }
        else
        {
            var result = Ai2dResizeNearestNeighbor(r.OutputDatatype, input, newSize, r.AlignCorners, r.HalfPixelCenters, inDeqBias, quantParam);
            return Value.FromTensor(result);
        }
    }

    private IRType Visit(ITypeInferenceContext context, Ai2dResize target, TensorType input)
    {
        var newSize = (Shape)context.GetArgument(target, Ai2dResize.NewSize);
        if (input.Shape[2] == 1 && input.Shape[3] == 1)
        {
            return new InvalidType("Ai2dResize doesn't support 1x1 Input");
        }

        return TypeInference.ResizeType(input, newSize, null);
    }
}
