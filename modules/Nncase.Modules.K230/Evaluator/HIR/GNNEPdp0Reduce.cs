﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Shapes;
using Nncase.TIR.Instructions;
using static Nncase.Evaluator.K230Kernels;

namespace Nncase.Evaluator.K230;

[TypeInferGenerator]
public partial class GNNEPdp0ReduceEvaluator : IEvaluator<GNNEPdp0Reduce>, ITypeInferencer<GNNEPdp0Reduce>, ICostEvaluator<GNNEPdp0Reduce>
{
    /// <inheritdoc/>
    public Cost Visit(ICostEvaluateContext context, GNNEPdp0Reduce target)
    {
        return new()
        {
            [CostFactorNames.CPUCycles] = 1,
        };
    }

    /// <inheritdoc />
    public IValue Visit(IEvaluateContext context, GNNEPdp0Reduce p)
    {
        var input = context.GetArgumentValueAsTensor<Half>(p, GNNEPdp0Reduce.Input);
        int[] kernelSize = context.GetArgumentValueAsArray<int>(p, GNNEPdp0Reduce.Filter);
        int[] stride = context.GetArgumentValueAsArray<int>(p, GNNEPdp0Reduce.Stride);
        var padding = context.GetArgumentValueAsTensor<int>(p, GNNEPdp0Reduce.Padding);
        bool[] countIncludePad = context.GetArgumentValueAsArray<bool>(p, GNNEPdp0Reduce.CountIncludePad);
        var deQuantParams = context.GetArgumentValue(p, GNNEPdp0Reduce.DepuantParams);
        var act = context.GetArgumentValueAsTensor<Half>(p, GNNEPdp0Reduce.Act);
        int shiftBits = context.GetArgumentValueAsScalar<int>(p, GNNEPdp0Reduce.ShiftBits);
        var padValue = context.GetArgumentValue(p, GNNEPdp0Reduce.Value).AsTensor().Cast<float>();
        var output = new Tensor<float>(context.CurrentCall.CheckedShape.ToValueArray());
        (Func<float, float, float>, Func<float, int, float>) op = p.ReduceOp switch
        {
            PU_PDP0_MODE.average =>
                ((a, b) => a + b,
                    (v, k) => v / k),
            PU_PDP0_MODE.min =>
                ((a, b) => System.Math.Min(a, b),
                    (v, _) => v),
            PU_PDP0_MODE.max =>
                ((a, b) => System.Math.Max(a, b),
                    (v, _) => v),
            PU_PDP0_MODE.sum =>
                ((a, b) => a + b,
                    (v, _) => v),
            _ => throw new ArgumentOutOfRangeException(nameof(context)),
        };
        Pdp0Impl(input, act.Buffer.Span, output, kernelSize[0], kernelSize[1], stride[0], stride[1], (padding[0, 0], padding[0, 1]), (padding[1, 0], padding[1, 1]), checked((sbyte)shiftBits), op.Item1, op.Item2, countIncludePad[0], deQuantParams is NoneValue ? new DeQuantizeParam(0, 1) : deQuantParams.AsTensor().ToScalar<DeQuantizeParam>(), padValue);
        if (p.DestType == DataTypes.Int8)
        {
            return Value.FromTensor(output.Cast<sbyte>(CastMode.KDefault));
        }

        if (p.DestType == DataTypes.UInt8)
        {
            return Value.FromTensor(output.Cast<byte>(CastMode.KDefault));
        }

        if (p.DestType == DataTypes.Int16)
        {
            return Value.FromTensor(output.Cast<short>(CastMode.KDefault));
        }

        return Value.FromTensor(output.Cast<Half>(CastMode.KDefault));
    }

    private void Pdp0Impl(Tensor<Half> input, ReadOnlySpan<Half> act, Tensor<float> output, int filterH, int filterW, int strideH, int strideW, (int Before, int After) paddingH, (int Before, int After) paddingW, sbyte shiftBits, Func<float, float, float> binaryOp, Func<float, int, float> windowOp, bool countIncludePad, DeQuantizeParam deQuantizeParam, Tensor<float> padValue)
    {
        long[] inShape = input.Shape.ToValueArray();
        int outH = (int)TypeInference.GetWindowedOutputSize(inShape[2] + paddingH.Before + paddingH.After, filterH, strideH, 1, false).FixedValue;
        int outW = (int)TypeInference.GetWindowedOutputSize(inShape[3] + paddingW.Before + paddingW.After, filterW, strideW, 1, false).FixedValue;

        for (int batch = 0; batch < inShape[0]; batch++)
        {
            for (int oc = 0; oc < inShape[1]; oc++)
            {
                for (int oy = 0; oy < outH; oy++)
                {
                    for (int ox = 0; ox < outW; ox++)
                    {
                        int inYOrigin = (oy * strideH) - paddingH.Before;
                        int inXOrigin = (ox * strideW) - paddingW.Before;
                        int filterYStart = System.Math.Max(0, -inYOrigin);
                        int filterYEnd = System.Math.Min(filterH, (int)inShape[2] - inYOrigin);
                        int filterXStart = System.Math.Max(0, -inXOrigin);
                        int filterXEnd = System.Math.Min(filterW, (int)inShape[3] - inXOrigin);
                        float value = (float)input[batch, oc, inYOrigin + filterYStart, inXOrigin + filterXStart];

                        int kernelCount = 0;

                        for (int ky = filterYStart; ky < filterYEnd; ky++)
                        {
                            for (int kx = filterXStart; kx < filterXEnd; kx++)
                            {
                                int inY = inYOrigin + ky;
                                int inX = inXOrigin + kx;

                                float inV = (float)input[batch, oc, inY, inX];
                                if (!(ky == filterYStart && kx == filterXStart))
                                {
                                    value = binaryOp(value - deQuantizeParam.ZeroPoint, inV - deQuantizeParam.ZeroPoint);
                                }

                                kernelCount++;
                            }
                        }

                        if (countIncludePad)
                        {
                            for (int i = 0; i < (filterH * filterW) - kernelCount; i++)
                            {
                                value = binaryOp(value, padValue.GetValue(0));
                            }

                            kernelCount = filterH * filterW;
                        }

                        var kernelCountV = (Half)kernelCount;

                        float tmpOutput = windowOp(value * deQuantizeParam.Scale, (int)kernelCountV);
                        tmpOutput = ApplyAct0(tmpOutput, act, oc, shiftBits);

                        output[batch, oc, oy, ox] = tmpOutput;
                    }
                }
            }
        }
    }

    private IRType Visit(ITypeInferenceContext context, GNNEPdp0Reduce target, TensorType input)
    {
        var args = context.GetArguments(target, GNNEPdp0Reduce.Filter, GNNEPdp0Reduce.Stride, GNNEPdp0Reduce.Padding);
        var outputType = TypeInference.ReduceWindow2DType(input, (Shape)args[0], (Shape)args[1], (Paddings)args[2], false);
        return new TensorType(target.DestType, ((TensorType)outputType).Shape);
    }
}
