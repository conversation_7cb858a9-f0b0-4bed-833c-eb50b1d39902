// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using NetFabric.Hyperlinq;
using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Shapes;
using OrtKISharp;
using static Nncase.Evaluator.K230Kernels;
using Dimension = Nncase.IR.Dimension;

namespace Nncase.Evaluator.K230;

[TypeInferGenerator]
public partial class GNNEConv2DEvaluator : IEvaluator<GNNEConv2D>, ITypeInferencer<GNNEConv2D>, ICostEvaluator<GNNEConv2D>
{
    /// <inheritdoc/>
    public Cost Visit(ICostEvaluateContext context, GNNEConv2D target)
    {
        return new()
        {
            [CostFactorNames.CPUCycles] = 1,
        };
    }

    public IValue Visit(IEvaluateContext context, GNNEConv2D conv)
    {
        var input = context.GetArgumentValueAsTensor(conv, GNNEConv2D.Input);
        var weights = context.GetArgumentValueAsTensor(conv, GNNEConv2D.Weights);
        byte[] weightsBias = context.GetArgumentValueAsArray<byte>(conv, GNNEConv2D.WeightsBias);
        var act = context.GetArgumentValueAsArray<Half>(conv, GNNEConv2D.Act);
        byte deqBias = context.GetArgumentValueAsScalar<byte>(conv, GNNEConv2D.DeqBias);
        long shiftBits = context.GetArgumentValueAsScalar<long>(conv, GNNEConv2D.ShiftBits);
        long[] padding = context.GetArgumentValueAsArray<long>(conv, GNNEConv2D.Padding);
        long[] stride = context.GetArgumentValueAsArray<long>(conv, GNNEConv2D.Stride);
        long[] dilation = context.GetArgumentValueAsArray<long>(conv, GNNEConv2D.Dilation);
        long groups = context.GetArgumentValueAsScalar<long>(conv, GNNEConv2D.Groups);

        // handle padded weights
        long bytesPerOc = input.Shape[1].FixedValue / groups * weights.Shape[2].FixedValue * weights.Shape[3].FixedValue * weights.ElementType.SizeInBytes;
        long newWeightsSize = weights.Shape[0].FixedValue * bytesPerOc;
        byte[] newWeightsBytes = new byte[newWeightsSize];
        for (int m = 0; m < weights.Shape[0].FixedValue; m++)
        {
            Array.Copy(weights.BytesBuffer.ToArray(), (long)m * weights.BytesBuffer.Length / weights.Shape[0].FixedValue, newWeightsBytes, m * bytesPerOc, bytesPerOc);
        }

        var newWeightsShape = weights.Shape.ToArray();
        newWeightsShape[1] = (Dimension)(input.Shape[1].FixedValue / groups);
        var newWeights = Tensor.FromBytes(new TensorType(weights.ElementType, newWeightsShape), newWeightsBytes);

        float[] inputDeq = input.ToArray<float>();
        inputDeq.Select((_, i) => inputDeq[i] -= deqBias).AsParallel().ToArray();
        float[] weightsDeq = newWeights.ToArray<float>();
        int qArgPerChannel = (int)(newWeights.Dimensions[1] * newWeights.Dimensions[2] * newWeights.Dimensions[3]);
        weightsDeq.Select((_, i) => weightsDeq[i] -= weightsBias[i / qArgPerChannel]).AsParallel().ToArray();

        var result = OrtKI.Conv(OrtKISharp.Tensor.MakeTensor(inputDeq, input.Dimensions.ToArray().Select(i => (long)i).ToArray()), OrtKISharp.Tensor.MakeTensor(weightsDeq, newWeights.Dimensions.ToArray().Select(i => (long)i).ToArray()), Proc((int)newWeights.Dimensions[0]), "NOTSET", dilation, groups, new long[] { newWeights.Dimensions[2], newWeights.Dimensions[3] }, new[] { padding[0], padding[2], padding[1], padding[3] }, stride).ToTensor();

        float[] resultArray = result.ToArray<float>();
        float[] output = new float[ComputeSize(result.Shape)];
        int channelSize = (int)(result.Dimensions[2] * result.Dimensions[3]);
        for (int i = 0; i < resultArray.Length; i++)
        {
            int oc = i / channelSize;
            output[i] = ApplyAct0(resultArray[i], act.ToArray(), oc, (sbyte)shiftBits);
        }

        // var get_windowed_output_size = (int size, int filter, int stride, int dilation, Padding padding) =>
        // {
        //     var effective_filter_size = (filter - 1) * dilation + 1;
        //     return (size + padding.Before + padding.After - effective_filter_size + stride) / stride;
        // };
        //
        // var in_shape = Input.Dimensions.ToArray();
        // var weights_shape = Weights.Dimensions.ToArray();
        // var out_h = get_windowed_output_size(in_shape[2], weights_shape[2], Stride[0], Dilation[0], new(Padding[0], Padding[1]));
        // var out_w = get_windowed_output_size(in_shape[3], weights_shape[3], Stride[1], Dilation[1], new(Padding[0], Padding[1]));
        // var g_ic = in_shape[1] / Groups;
        // var g_oc = weights_shape[0] / Groups;
        //
        // var output = new float[in_shape[0] * weights_shape[0] * out_h * out_w];
        // var out_idx = 0;
        // for (var batch = 0; batch < in_shape[0]; batch++)
        // {
        //     var in_batch_p = batch * in_shape[1] * in_shape[2] * in_shape[3];
        //     for (int og = 0; og < Groups; og++)
        //     {
        //         var in_group_p = in_batch_p + og * g_ic * in_shape[2] * in_shape[3];
        //         var w_group_p = og * g_oc * g_ic * weights_shape[2] * weights_shape[3];
        //         for (int oc = 0; oc < g_oc; oc++)
        //         {
        //             var w_oc_p = w_group_p + oc * g_ic * weights_shape[2] * weights_shape[3];
        //             for (var oy = 0; oy < out_h; oy++)
        //             {
        //                 for (var ox = 0; ox < out_w; ox++)
        //                 {
        //                     int in_y_origin = (oy * Stride[0]) - Padding[0];
        //                     int in_x_origin = (ox * Stride[1]) - Padding[2];
        //                     int filter_y_start = System.Math.Max(0, (-in_y_origin + Dilation[0] - 1) / Dilation[0]);
        //                     int filter_y_end = System.Math.Min(weights_shape[2], (in_shape[2] - in_y_origin + Dilation[0] - 1) / Dilation[0]);
        //                     int filter_x_start = System.Math.Max(0, (-in_x_origin + Dilation[1] - 1) / Dilation[1]);
        //                     int filter_x_end = System.Math.Min(weights_shape[3], (in_shape[3] - in_x_origin + Dilation[1] - 1) / Dilation[1]);
        //                     float value = 0.0f;
        //
        //                     for (int ic = 0; ic < g_ic; ic++)
        //                     {
        //                         var in_c_p = in_group_p + ic * in_shape[2] * in_shape[3];
        //                         var w_ic_p = w_oc_p + ic * weights_shape[2] * weights_shape[3];
        //                         for (int ky = filter_y_start; ky < filter_y_end; ky++)
        //                         {
        //                             for (int kx = filter_x_start; kx < filter_x_end; kx++)
        //                             {
        //                                 int in_y = in_y_origin + Dilation[0] * ky;
        //                                 int in_x = in_x_origin + Dilation[1] * kx;
        //
        //                                 float in_v = 0.0f;
        //                                 if (in_x < 0 || in_x >= in_shape[3]
        //                                              || in_y < 0 || in_y >= in_shape[2])
        //                                 {
        //                                     if (Input.ElementType == DataTypes.UInt8)
        //                                         in_v = PadValue.ToScalar<byte>();
        //                                     else if (Input.ElementType == DataTypes.Int8)
        //                                         in_v = PadValue.ToScalar<sbyte>();
        //                                     else
        //                                         in_v = PadValue.ToScalar<short>();
        //                                 }
        //                                 else
        //                                 {
        //                                     if (Input.ElementType == DataTypes.UInt8)
        //                                         in_v = Input.ToArray<byte>()[in_c_p + in_y * in_shape[3] + in_x];
        //                                     else if (Input.ElementType == DataTypes.Int8)
        //                                         in_v = Input.ToArray<sbyte>()[in_c_p + in_y * in_shape[3] + in_x];
        //                                     else
        //                                         in_v = Input.ToArray<short>()[in_c_p + in_y * in_shape[3] + in_x];
        //                                 }
        //
        //                                 float w = 0.0f;
        //                                 if (Weights.ElementType == DataTypes.UInt8)
        //                                     w = Weights.ToArray<byte>()[w_ic_p + ky * weights_shape[3] + kx];
        //                                 else if (Weights.ElementType == DataTypes.Int8)
        //                                     w = Weights.ToArray<sbyte>()[w_ic_p + ky * weights_shape[3] + kx];
        //                                 else
        //                                     w = Weights.ToArray<short>()[w_ic_p + ky * weights_shape[3] + kx];
        //                                 value += (in_v - DeqBias) * (w - WeightsBias.ToArray()[oc]);
        //                             }
        //                         }
        //                     }
        //
        //                     output[out_idx] = apply_act0(value, Act, oc, (sbyte)ShiftBits);
        //                     out_idx++;
        //                 }
        //             }
        //         }
        //     }
        // }
        float[] roundedOutput = output.Select(x => (float)System.Math.Round(x)).ToArray();
        var halfOutput = output.Select(x => (Half)x).ToArray();

        // var rounded_output_tensor = Tensor.From(rounded_output, new[] { in_shape[0], weights_shape[0], out_h, out_w });
        // var half_output_tensor = Tensor.From(half_output, new[] { in_shape[0], weights_shape[0], out_h, out_w });
        var roundedOutputTensor = Tensor.From(roundedOutput, result.Shape);
        var halfOutputTensor = Tensor.From(halfOutput, result.Shape);

        if (conv.DestType == DataTypes.UInt8)
        {
            return Value.FromTensor(roundedOutputTensor.Cast<byte>(CastMode.KDefault));
        }

        if (conv.DestType == DataTypes.Int8)
        {
            return Value.FromTensor(roundedOutputTensor.Cast<sbyte>(CastMode.KDefault));
        }

        if (conv.DestType == DataTypes.Int16)
        {
            return Value.FromTensor(roundedOutputTensor.Cast<short>(CastMode.KDefault));
        }

        return Value.FromTensor(halfOutputTensor.Cast<Half>(CastMode.KDefault));
    }

    /// <summary>
    /// Conv2D Type Infer with fp16's target.
    /// </summary>
    private static IRType Conv2DTypeFp16(TensorType input, TensorType weights, Expr stride, Expr padding, Expr dilation, Expr groups, DataType target)
    {
        var outShape = input.Shape.ToList();
        outShape[1] = weights.Shape[0];
        if (
            stride is TensorConst strideValue &&
            padding is TensorConst paddingValue &&
            dilation is TensorConst dilationCon &&
            groups is TensorConst groupsCon &&
            input.Shape[2].IsFixed &&
            input.Shape[3].IsFixed &&
            weights.Shape[2].IsFixed &&
            weights.Shape[3].IsFixed)
        {
            var tsStride = strideValue.Value.Cast<int>();
            var tsPadding = paddingValue.Value.Cast<int>();
            var tsDilation = dilationCon.Value.Cast<int>();
            int groupsV = groupsCon.Value.ToScalar<int>();

            if (!(input.Shape[1].FixedValue >= groupsV && input.Shape[1].FixedValue % groupsV == 0))
            {
                return new InvalidType($"The Input Channel / Groups Error ({input.Shape[1].FixedValue}/{groupsV})");
            }

            outShape[2] = (int)TypeInference.GetWindowedOutputSize(input.Shape[2].FixedValue + tsPadding[0, 0] + tsPadding[0, 1], weights.Shape[2].FixedValue, tsStride[0], tsDilation[0], false).FixedValue;
            outShape[3] = (int)TypeInference.GetWindowedOutputSize(input.Shape[3].FixedValue + tsPadding[1, 0] + tsPadding[1, 1], weights.Shape[3].FixedValue, tsStride[1], tsDilation[1], false).FixedValue;
        }
        else
        {
            outShape[2] = outShape[3] = Dimension.Unknown;
        }

        return new TensorType(target, new RankedShape(outShape));
    }

    private IRType Visit(ITypeInferenceContext context, GNNEConv2D target, TensorType input, TensorType weights)
    {
        // handle padded weights
        int groups = ((TensorConst)context.GetArgument(target, GNNEConv2D.Groups)).Value.ToScalar<int>();
        var newWeights = weights;
        if (input.Shape[1] / groups != weights.Shape[1])
        {
            newWeights = weights with { Shape = new RankedShape(weights.Shape[0].FixedValue, input.Shape[1].FixedValue / groups, weights.Shape[2].FixedValue, weights.Shape[3].FixedValue) };
        }

        if (input.DType != DataTypes.Int8 && input.DType != DataTypes.UInt8 && input.DType != DataTypes.Int16)
        {
            return new InvalidType("Unsupported input_type, should be one of [int8, uint8, int16]");
        }

        if (newWeights.DType != DataTypes.Int8 && newWeights.DType != DataTypes.UInt8 && newWeights.DType != DataTypes.Int16)
        {
            return new InvalidType("Unsupported w_type, should be one of [int8, uint8, int16]");
        }

        if (input.DType == DataTypes.Int16 && newWeights.DType == DataTypes.Int16)
        {
            return new InvalidType("int16 for both of input_type and w_type is not supported");
        }

        var iT = input.DType;
        var oT = target.DestType;
        var args = context.GetArguments(target, GNNEConv2D.Stride, GNNEConv2D.Padding, GNNEConv2D.Dilation, GNNEConv2D.Groups);

        if (oT == DataTypes.Int8 || oT == DataTypes.UInt8 || oT == DataTypes.Int16)
        {
            var outType = TypeInference.Conv2DType(input, newWeights, (Shape)args[0], (Paddings)args[1], (Shape)args[2], (Dimension)args[3]);
            if (outType is TensorType tensorType)
            {
                return tensorType with { DType = oT };
            }
            else
            {
                return outType;
            }
        }
        else if (oT == DataTypes.Float16)
        {
            return Conv2DTypeFp16(input, newWeights, (Expr)args[0], (Expr)args[1], (Expr)args[2], (Expr)args[3], oT);
        }
        else
        {
            return new InvalidType("Conv2d output type should be one of [int8, int16, float16, uint8]");
        }
    }
}
