﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Runtime.InteropServices;
using NetFabric.Hyperlinq;
using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;
using OrtKISharp;
using static Nncase.Evaluator.K230Kernels;

namespace Nncase.Evaluator.K230;

[TypeInferGenerator]
public sealed partial class GNNEMatMulEvaluator : IEvaluator<GNNEMatMul>, ITypeInferencer<GNNEMatMul>, ICostEvaluator<GNNEMatMul>
{
    /// <inheritdoc/>
    public IValue Visit(IEvaluateContext context, GNNEMatMul matMul)
    {
        var inputA = context.GetArgumentValueAsTensor(matMul, GNNEMatMul.InputA);
        var inputB = context.GetArgumentValueAsTensor(matMul, GNNEMatMul.InputB);
        byte[] inputABias = context.GetArgumentValueAsTensor<byte>(matMul, GNNEMatMul.InputABias).ToArray();
        byte[] deqBBias = context.GetArgumentValueAsTensor<byte>(matMul, GNNEMatMul.DeqBBias).ToArray();
        var act = context.GetArgumentValueAsTensor<Half>(matMul, GNNEMatMul.Act);
        var inShiftBits = context.GetArgumentValueAsTensor(matMul, GNNEMatMul.ShiftBits);
        int aBatches = (int)inputA.Shape[1].FixedValue;
        int bBatches = (int)inputB.Shape[1].FixedValue;
        int aRows = (int)inputA.Shape[2].FixedValue;
        int aCols = (int)inputA.Shape[3].FixedValue;
        int bCols = (int)inputB.Shape[3].FixedValue;
        long[] outShape = inputA.Shape.ToValueArray();
        outShape[^1] = inputB.Shape[^1].FixedValue;
        var outputType = new TensorType(matMul.OutputDType, outShape);

        float[] inputADeq = inputA.ToArray<float>();
        float[] inputBDeq = inputB.ToArray<float>();

        if (aBatches == bBatches || (aBatches < bBatches && aBatches == 1))
        {
            float[] array = inputADeq.Select((_, i) => inputADeq[i] -= inputABias[i * aRows / (aRows * aCols * bCols)]).ToArray();
            float[] array1 = inputBDeq.Select((_, i) => inputBDeq[i] -= deqBBias[i / (aRows * aCols * bCols)]).ToArray();
        }
        else if (aBatches > bBatches && bBatches == 1)
        {
            float[] array2 = inputADeq.Select((_, i) => inputADeq[i] -= inputABias.ToArray()[i / aRows]).ToArray();
            float[] array3 = inputBDeq.Select((_, i) => inputBDeq[i] -= deqBBias[0]).ToArray();
        }
        else
        {
            throw new InvalidOleVariantTypeException("Invalid matmul");
        }

        var result = OrtKI
            .MatMul(
                OrtKISharp.Tensor.MakeTensor(inputADeq, inputA.Dimensions.ToArray().Select(i => (long)i).ToArray()),
                OrtKISharp.Tensor.MakeTensor(inputBDeq, inputB.Dimensions.ToArray().Select(i => (long)i).ToArray()))
            .ToTensor();
        float[] resultArray = result.ToArray<float>();
        float[] output = new float[ComputeSize(result.Shape)];

        for (int i = 0; i < resultArray.Length; i++)
        {
            output[i] = ApplyAct0(resultArray[i], act.ToArray<Half>(), i / (int)result.Shape[3].FixedValue, inShiftBits.ToArray<sbyte>()[0]);
        }

        float[] roundedOutput = output.Select(x => (float)System.Math.Round(x)).ToArray();
        var halfOutput = output.Select(x => (Half)x).ToArray();
        var roundedOutputTensor = Tensor.From(roundedOutput, result.Shape);
        var halfOutputTensor = Tensor.From(halfOutput, result.Shape);
        if (outputType == DataTypes.UInt8)
        {
            return Value.FromTensor(roundedOutputTensor.Cast<byte>(CastMode.KDefault));
        }

        if (outputType == DataTypes.Int8)
        {
            return Value.FromTensor(roundedOutputTensor.Cast<sbyte>(CastMode.KDefault));
        }

        if (outputType == DataTypes.Int16)
        {
            return Value.FromTensor(roundedOutputTensor.Cast<short>(CastMode.KDefault));
        }

        return Value.FromTensor(halfOutputTensor.Cast<Half>(CastMode.KDefault));

        // Tensor output;
        // if (output_type.DType == DataTypes.UInt8)
        // {
        //     output = new Tensor<byte>(output_type.Shape.ToValueArray());
        // }
        //
        // else if (output_type.DType == DataTypes.Int8)
        // {
        //     output = new Tensor<sbyte>(output_type.Shape.ToValueArray());
        // }
        // else if (output_type.DType == DataTypes.Int16)
        // {
        //     output = new Tensor<short>(output_type.Shape.ToValueArray());
        // }
        // else
        // {
        //     output = new Tensor<Half>(output_type.Shape.ToValueArray());
        // }
        //
        //
        // if (a_batches == b_batches)
        // {
        //     int index = 0;
        //     for (int on = 0; on < a_batches; on++)
        //     {
        //         for (int oy = 0; oy < a_rows; oy++)
        //         {
        //             for (int ox = 0; ox < b_cols; ox++)
        //             {
        //                 float value = 0f;
        //
        //                 for (int i = 0; i < a_cols; i++)
        //                 {
        //                     var a = InputA.ToArray<float>()[on * a_rows * a_cols + oy * a_cols + i];
        //                     var b = InputB.ToArray<float>()[on * a_cols * b_cols + i * b_cols + ox];
        //                     value = value + ((float) a - (float)InputABias[index]) * ((float) b - (float) DeqBBias[on]);
        //                 }
        //
        //                 if (output_type.DType == DataTypes.Float16)
        //                 {
        //                     output[on * a_rows * b_cols + oy * b_cols + ox] =
        //                         (Half)(apply_act0(value, Act.ToArray<Half>(), on, ((Tensor) in_shift_bits).ToArray<sbyte>()[0]));
        //                 }
        //                 else if(output_type.DType == DataTypes.Int8)
        //                 {
        //                     output[on * a_rows * b_cols + oy * b_cols + ox] =
        //                         (sbyte)(apply_act0(value, Act.ToArray<Half>(), on, ((Tensor) in_shift_bits).ToArray<sbyte>()[0]));
        //                 }
        //                 else if(output_type.DType == DataTypes.UInt8)
        //                 {
        //                     output[on * a_rows * b_cols + oy * b_cols + ox] =
        //                         (byte)(apply_act0(value, Act.ToArray<Half>(), on, ((Tensor) in_shift_bits).ToArray<sbyte>()[0]));
        //                 }
        //                 else if(output_type.DType == DataTypes.Int16)
        //                 {
        //                     output[on * a_rows * b_cols + oy * b_cols + ox] =
        //                         (short)(apply_act0(value, Act.ToArray<Half>(), on, ((Tensor) in_shift_bits).ToArray<sbyte>()[0]));
        //                 }
        //             }
        //         }
        //
        //         index += a_rows;
        //     }
        // }
        // else if (a_batches > b_batches && b_batches == 1)
        // {
        //     int index = 0;
        //     for (int on = 0; on < a_batches; on++)
        //     {
        //         for (int oy = 0; oy < a_rows; oy++)
        //         {
        //             for (int ox = 0; ox < b_cols; ox++)
        //             {
        //                 float value = 0f;
        //
        //                 for (int i = 0; i < a_cols; i++)
        //                 {
        //                     var a = InputA.ToArray<float>()[on * a_rows * a_cols + oy * a_cols + i];
        //                     var b = InputB.ToArray<float>()[i * b_cols + ox];
        //                     value = value + ((float) a - (float)InputABias[index]) * ((float) b - (float) DeqBBias[0]);
        //                 }
        //
        //                 if (output_type.DType == DataTypes.Float16)
        //                 {
        //                     output[on * a_rows * b_cols + oy * b_cols + ox] =
        //                         (Half)(apply_act0(value, Act.ToArray<Half>(), on, ((Tensor) in_shift_bits).ToArray<sbyte>()[0]));
        //                 }
        //                 else if(output_type.DType == DataTypes.Int8)
        //                 {
        //                     output[on * a_rows * b_cols + oy * b_cols + ox] =
        //                         (sbyte)(apply_act0(value, Act.ToArray<Half>(), on, ((Tensor) in_shift_bits).ToArray<sbyte>()[0]));
        //                 }
        //                 else if(output_type.DType == DataTypes.UInt8)
        //                 {
        //                     output[on * a_rows * b_cols + oy * b_cols + ox] =
        //                         (byte)(apply_act0(value, Act.ToArray<Half>(), on, ((Tensor) in_shift_bits).ToArray<sbyte>()[0]));
        //                 }
        //                 else if(output_type.DType == DataTypes.Int16)
        //                 {
        //                     output[on * a_rows * b_cols + oy * b_cols + ox] =
        //                         (short)(apply_act0(value, Act.ToArray<Half>(), on, ((Tensor) in_shift_bits).ToArray<sbyte>()[0]));
        //                 }
        //             }
        //         }
        //
        //         index += a_rows;
        //     }
        // }
        // else if (a_batches < b_batches && a_batches == 1)
        // {
        //     for (int on = 0; on < b_batches; on++)
        //     {
        //         int index = 0;
        //         for (int oy = 0; oy < a_rows; oy++)
        //         {
        //             for (int ox = 0; ox < b_cols; ox++)
        //             {
        //                 float value = 0f;
        //
        //                 for (int i = 0; i < a_cols; i++)
        //                 {
        //                     var a = InputA.ToArray<float>()[oy * a_cols + i];
        //                     var b = InputB.ToArray<float>()[on * a_cols * b_cols + i * b_cols + ox];
        //                     value = value + ((float) a - (float)InputABias[index]) * ((float) b - (float) DeqBBias[on]);
        //                 }
        //
        //                 if (output_type.DType == DataTypes.Float16)
        //                 {
        //                     output[on * a_rows * b_cols + oy * b_cols + ox] =
        //                         (Half)(apply_act0(value, Act.ToArray<Half>(), on, ((Tensor) in_shift_bits).ToArray<sbyte>()[0]));
        //                 }
        //                 else if(output_type.DType == DataTypes.Int8)
        //                 {
        //                     output[on * a_rows * b_cols + oy * b_cols + ox] =
        //                         (sbyte)(apply_act0(value, Act.ToArray<Half>(), on, ((Tensor) in_shift_bits).ToArray<sbyte>()[0]));
        //                 }
        //                 else if(output_type.DType == DataTypes.UInt8)
        //                 {
        //                     output[on * a_rows * b_cols + oy * b_cols + ox] =
        //                         (byte)(apply_act0(value, Act.ToArray<Half>(), on, ((Tensor) in_shift_bits).ToArray<sbyte>()[0]));
        //                 }
        //                 else if(output_type.DType == DataTypes.Int16)
        //                 {
        //                     output[on * a_rows * b_cols + oy * b_cols + ox] =
        //                         (short)(apply_act0(value, Act.ToArray<Half>(), on, ((Tensor) in_shift_bits).ToArray<sbyte>()[0]));
        //                 }
        //             }
        //         }
        //     }
        // }
        // else
        // {
        //     new InvalidType("Invalid matmul");
        // }
        //
        // return Value.FromTensor(output);

        // if (a_batches == b_batches || (a_batches < b_batches && a_batches == 1))
        // {
        //
        // }
        // else if (a_batches > b_batches && b_batches == 1)
        // {
        //     InputBDeq.Select((_, i) => InputBDeq[i] -= DeqBBias[0]).ToArray();
        // }
        // else
        // {
        //     new InvalidType("Invalid matmul");
        // }
    }

    /// <inheritdoc />
    public Cost Visit(ICostEvaluateContext context, GNNEMatMul target)
    {
        return new()
        {
            [CostFactorNames.CPUCycles] = 1,
        };
    }

    private IRType Visit(TensorType inputA, TensorType inputB, GNNEMatMul target)
    {
        var inputAType = inputA.DType;
        var inputBType = inputB.DType;

        if (inputAType != DataTypes.Int8 && inputAType != DataTypes.UInt8 && inputAType != DataTypes.Int16)
        {
            return new InvalidType("Unsupported input_a_type, should be one of [int8, uint8, int16]");
        }

        if (inputBType != DataTypes.Int8 && inputBType != DataTypes.UInt8 && inputBType != DataTypes.Int16)
        {
            return new InvalidType("Unsupported input_b_type, should be one of [int8, uint8, int16]");
        }

        if (inputAType == DataTypes.Int16 && inputBType == DataTypes.Int16)
        {
            return new InvalidType("int16 for both of input_a_type and input_b_type is not supported");
        }

        if (target.OutputDType != DataTypes.Float16 && target.OutputDType != DataTypes.Float32)
        {
            return new InvalidType("Invalid Ouput Datatype");
        }

        // var shape = new Shape(InputA.Shape[0], 1, InputA.Shape[2], InputB.Shape[3]);
        // return new TensorType(InputA.DType, shape);
        Dimension[] output_shape = { inputA.Shape[^2], inputB.Shape[^1] };

        var bigShape = new RankedShape(inputA.Shape.Zip(inputB.Shape).Select(t => System.Math.Max(t.First.FixedValue, t.Second.FixedValue)));

        return new TensorType(target.OutputDType, bigShape.ToArray()[..(bigShape.Count - 2)].Concat(output_shape).ToArray());
    }
}
