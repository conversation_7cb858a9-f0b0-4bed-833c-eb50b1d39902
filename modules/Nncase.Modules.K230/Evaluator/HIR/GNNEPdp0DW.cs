﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;
using OrtKISharp;
using static Nncase.Evaluator.K230Kernels;
using Dimension = Nncase.IR.Dimension;

namespace Nncase.Evaluator.K230;

[TypeInferGenerator]
public partial class GNNEPdp0DWEvaluator : IEvaluator<GNNEPdp0DW>, ITypeInferencer<GNNEPdp0DW>, ICostEvaluator<GNNEPdp0DW>
{
    /// <inheritdoc/>
    public Cost Visit(ICostEvaluateContext context, GNNEPdp0DW target)
    {
        return new()
        {
            [CostFactorNames.CPUCycles] = 1,
        };
    }

    public IValue Visit(IEvaluateContext context, GNNEPdp0DW p)
    {
        var input = context.GetArgumentValueAsTensor(p, GNNEPdp0DW.Input);
        var weights = context.GetArgumentValueAsTensor(p, GNNEPdp0DW.Weights);
        byte[] weightsBias = context.GetArgumentValueAsArray<byte>(p, GNNEPdp0DW.WeightsBias);
        var act = context.GetArgumentValueAsArray<Half>(p, GNNEPdp0DW.Act);
        byte deqBias = context.GetArgumentValueAsScalar<byte>(p, GNNEPdp0DW.DeqBias);
        long shiftBits = context.GetArgumentValueAsScalar<long>(p, GNNEPdp0DW.ShiftBits);
        long[] padding = context.GetArgumentValueAsArray<long>(p, GNNEPdp0DW.Padding).ToArray();
        long[] stride = context.GetArgumentValueAsArray<long>(p, GNNEPdp0DW.Stride);
        long[] dilation = context.GetArgumentValueAsArray<long>(p, GNNEPdp0DW.Dilation);
        long groups = context.GetArgumentValueAsScalar<long>(p, GNNEPdp0DW.Groups);

        float[] inputDeq = input.ToArray<float>();
        inputDeq.Select((_, i) => inputDeq[i] -= deqBias).AsParallel().ToArray();
        float[] weightsDeq = weights.ToArray<float>();
        int qArgPerChannel = (int)(weights.Dimensions[1] * weights.Dimensions[2] * weights.Dimensions[3]);
        weightsDeq.Select((_, i) => weightsDeq[i] -= weightsBias[i / qArgPerChannel]).AsParallel().ToArray();

        var result = OrtKI.Conv(
                OrtKISharp.Tensor.MakeTensor(inputDeq, input.Dimensions.ToArray().Select(i => (long)i).ToArray()),
                OrtKISharp.Tensor.MakeTensor(weightsDeq, weights.Dimensions.ToArray().Select(i => (long)i).ToArray()),
                Proc((int)weights.Dimensions[0]),
                "NOTSET",
                dilation,
                groups,
                new long[] { weights.Dimensions[2], weights.Dimensions[3] },
                new[] { padding[0], padding[2], padding[1], padding[3] },
                stride)
            .ToTensor();

        float[] resultArray = result.ToArray<float>();
        float[] output = new float[ComputeSize(result.Shape)];
        int channelSize = (int)(result.Dimensions[2] * result.Dimensions[3]);
        for (int i = 0; i < resultArray.Length; i++)
        {
            int oc = i / channelSize;
            output[i] = ApplyAct0(resultArray[i], act.ToArray(), oc, (sbyte)shiftBits);
        }

        float[] roundedOutput = output.Select(x => (float)System.Math.Round(x)).ToArray();
        var halfOutput = output.Select(x => (Half)x).ToArray();

        // var rounded_output_tensor = Tensor.From(rounded_output, new[] { in_shape[0], weights_shape[0], out_h, out_w });
        // var half_output_tensor = Tensor.From(half_output, new[] { in_shape[0], weights_shape[0], out_h, out_w });
        var roundedOutputTensor = Tensor.From(roundedOutput, result.Shape);
        var halfOutputTensor = Tensor.From(halfOutput, result.Shape);

        if (p.DestType == DataTypes.UInt8)
        {
            return Value.FromTensor(roundedOutputTensor.Cast<byte>(CastMode.KDefault));
        }

        if (p.DestType == DataTypes.Int8)
        {
            return Value.FromTensor(roundedOutputTensor.Cast<sbyte>(CastMode.KDefault));
        }

        if (p.DestType == DataTypes.Int16)
        {
            return Value.FromTensor(roundedOutputTensor.Cast<short>(CastMode.KDefault));
        }

        return Value.FromTensor(halfOutputTensor.Cast<Half>(CastMode.KDefault));
    }

    private IRType Visit(ITypeInferenceContext context, GNNEPdp0DW target, TensorType input, TensorType weights)
    {
        if (input.DType != DataTypes.Int8 && input.DType != DataTypes.UInt8 && input.DType != DataTypes.Int16)
        {
            return new InvalidType("Unsupported input_type, should be one of [int8, uint8, int16]");
        }

        if (weights.DType != DataTypes.Int8 && weights.DType != DataTypes.UInt8 && weights.DType != DataTypes.Int16)
        {
            return new InvalidType("Unsupported w_type, should be one of [int8, uint8, int16]");
        }

        if (input.DType == DataTypes.Int16 && weights.DType == DataTypes.Int16)
        {
            return new InvalidType("int16 for both of input_type and w_type is not supported");
        }

        var oT = target.DestType;
        var args = context.GetArguments(target, GNNEPdp0DW.Stride, GNNEPdp0DW.Padding, GNNEPdp0DW.Dilation, GNNEPdp0DW.Groups);

        // var output_type = TypeInference.Conv2DType(Input, Weights, args[0], args[1], args[2], args[3]);
        IRType outputType;
        if (input.Shape.IsUnranked)
        {
            outputType = input with { Shape = Shape.Unknown(4) };
        }
        else
        {
            var outShape = input.Shape.ToList();

            // outShape[1] = Weights.Shape[0];
            if (
                args[0] is TensorConst strideValue &&
                args[1] is TensorConst paddingValue &&
                args[2] is TensorConst dilationCon &&
                args[3] is TensorConst groupsCon &&
                input.Shape[2].IsFixed &&
                input.Shape[3].IsFixed &&
                weights.Shape[2].IsFixed &&
                weights.Shape[3].IsFixed)
            {
                var tsStride = strideValue.Value.Cast<int>();
                var tsPadding = paddingValue.Value.Cast<int>();
                var tsDilation = dilationCon.Value.Cast<int>();
                int groupsV = groupsCon.Value.ToScalar<int>();
                if (!(input.Shape[1].FixedValue >= groupsV && input.Shape[1].FixedValue % groupsV == 0))
                {
                    return new InvalidType($"The Input Channel / Groups Error ({input.Shape[1].FixedValue}/{groupsV})");
                }

                outShape[2] = (int)TypeInference.GetWindowedOutputSize(input.Shape[2].FixedValue + tsPadding[0, 0] + tsPadding[0, 1], weights.Shape[2].FixedValue, tsStride[0], tsDilation[0], false).FixedValue;
                outShape[3] = (int)TypeInference.GetWindowedOutputSize(input.Shape[3].FixedValue + tsPadding[1, 0] + tsPadding[1, 1], weights.Shape[3].FixedValue, tsStride[1], tsDilation[1], false).FixedValue;
            }
            else
            {
                outShape[2] = outShape[3] = Dimension.Unknown;
            }

            outputType = new TensorType(oT, new RankedShape(outShape));
        }

        return outputType;
    }
}
