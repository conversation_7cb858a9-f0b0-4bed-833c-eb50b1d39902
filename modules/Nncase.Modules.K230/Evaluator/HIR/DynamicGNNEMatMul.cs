// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

#if false
using System.Runtime.InteropServices;
using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;

namespace Nncase.Evaluator.K230;

[EvaluatorGenerator]
[TypeInferGenerator]
public sealed partial class DynamicGNNEMatMulEvaluator : IEvaluator<DynamicGNNEMatMul>, ITypeInferencer<DynamicGNNEMatMul>, ICostEvaluator<DynamicGNNEMatMul>
{
    /// <inheritdoc />
    public Cost Visit(ICostEvaluateContext context, DynamicGNNEMatMul target)
    {
        var inputA = context.GetArgumentType<TensorType>(target, DynamicGNNEMatMul.InputA);
        var inputB = context.GetArgumentType<TensorType>(target, DynamicGNNEMatMul.InputB);
        var act = context.GetArgumentType<TensorType>(target, DynamicGNNEMatMul.Act);
        var inputABias = context.GetArgumentType<TensorType>(target, DynamicGNNEMatMul.InputABias);
        var inputBBias = context.GetArgumentType<TensorType>(target, DynamicGNNEMatMul.InputBBias);
        var outputType = context.GetReturnType<TensorType>();

        int macPerElement = inputA.Shape[^1].IsFixed ? (int)inputA.Shape[^1].FixedValue : 1;
        float puMac = 24 * 32;
        return new()
        {
            [CostFactorNames.MemoryLoad] = CostUtility.GetMemoryAccess(inputA) + CostUtility.GetMemoryAccess(inputB) +
                                           CostUtility.GetMemoryAccess(act) +
                                           CostUtility.GetMemoryAccess(inputABias) +
                                           CostUtility.GetMemoryAccess(inputBBias),
            [CostFactorNames.MemoryStore] = CostUtility.GetMemoryAccess(outputType),
            [CostFactorNames.CPUCycles] = CostUtility.GetCPUCycles(outputType, macPerElement / puMac),
        };
    }

    private static Tensor GnneMatmulV2<TIA, TIB>(ReadOnlySpan<TIA> inputA, ReadOnlySpan<TIB> inputB, TensorType outputType, ReadOnlySpan<Half> act, ReadOnlySpan<byte> inABias, int aBatch0, int aBatch1, int aRows, int aCols, int bBatch0, int bBatch1, int bCols, byte deqBBias, int inAShiftBits, int inBShiftBits, sbyte shiftBits, bool dynamicChannel)
      where TIA : unmanaged
      where TIB : unmanaged
    {
        switch (outputType.DType)
        {
            case var x when x == DataTypes.UInt8:
                {
                    var output = new Tensor<byte>(outputType.Shape.ToValueArray());
                    K230Kernels.DynamicGnneMatmul(inputA, inputB, output.Buffer.Span, act, inABias, aBatch0, aBatch1, aRows, aCols, bBatch0, bBatch1, bCols, deqBBias, inAShiftBits, inBShiftBits, shiftBits, dynamicChannel);
                    return output;
                }

            case var x when x == DataTypes.Int8:
                {
                    var output = new Tensor<sbyte>(outputType.Shape.ToValueArray());
                    K230Kernels.DynamicGnneMatmul(inputA, inputB, output.Buffer.Span, act, inABias, aBatch0, aBatch1, aRows, aCols, bBatch0, bBatch1, bCols, deqBBias, inAShiftBits, inBShiftBits, shiftBits, dynamicChannel);
                    return output;
                }

            case var x when x == DataTypes.Int16:
                {
                    var output = new Tensor<short>(outputType.Shape.ToValueArray());
                    K230Kernels.DynamicGnneMatmul(inputA, inputB, output.Buffer.Span, act, inABias, aBatch0, aBatch1, aRows, aCols, bBatch0, bBatch1, bCols, deqBBias, inAShiftBits, inBShiftBits, shiftBits, dynamicChannel);
                    return output;
                }

            case var x when x == DataTypes.Float16:
                {
                    var output = new Tensor<Half>(outputType.Shape.ToValueArray());
                    K230Kernels.DynamicGnneMatmul(inputA, inputB, output.Buffer.Span, act, inABias, aBatch0, aBatch1, aRows, aCols, bBatch0, bBatch1, bCols, deqBBias, inAShiftBits, inBShiftBits, shiftBits, dynamicChannel);
                    return output;
                }

            case var x when x == DataTypes.Float32:
                {
                    var output = new Tensor<float>(outputType.Shape.ToValueArray());
                    K230Kernels.DynamicGnneMatmul(inputA, inputB, output.Buffer.Span, act, inABias, aBatch0, aBatch1, aRows, aCols, bBatch0, bBatch1, bCols, deqBBias, inAShiftBits, inBShiftBits, shiftBits, dynamicChannel);
                    return output;
                }
        }

        throw new ArgumentOutOfRangeException(nameof(inputA));
    }

    private IValue Visit(Tensor inputA, Tensor inputB, Tensor<Half> act, Tensor<byte> inputABias, byte inputBBias, DynamicGNNEMatMul target, int shiftBits, int dynamicChannel)
    {
        const int InAShiftBits = 0;
        const int InBShiftBits = 0;
        long[] outShape = inputA.Shape.ToValueArray();
        outShape[^1] = inputB.Shape[^1].FixedValue;
        var outputType = new TensorType(target.OutputDType, outShape);
        int inABatch0 = (int)inputA.Shape[0].FixedValue;
        int inABatch1 = (int)inputA.Shape[1].FixedValue;
        int aRows = (int)inputA.Shape[2].FixedValue;
        int aCols = (int)inputA.Shape[3].FixedValue;
        int inBBatch0 = (int)inputB.Shape[0].FixedValue;
        int inBBatch1 = (int)inputB.Shape[1].FixedValue;
        int bCols = (int)inputB.Shape[3].FixedValue;
        return (inputA.ElementType, inputB.ElementType) switch
        {
            var x when x.Item1 == DataTypes.UInt8 && x.Item2 == DataTypes.UInt8 => Value.FromTensor(GnneMatmulV2<byte, byte>(inputA.BytesBuffer, inputB.BytesBuffer, outputType, act.Buffer.Span, inputABias.Buffer.Span, inABatch0, inABatch1, aRows, aCols, inBBatch0, inBBatch1, bCols, inputBBias, InAShiftBits, InBShiftBits, checked((sbyte)shiftBits), dynamicChannel == 1)),
            var x when x.Item1 == DataTypes.UInt8 && x.Item2 == DataTypes.Int8 => GnneMatmulV2<byte, sbyte>(inputA.BytesBuffer, MemoryMarshal.Cast<byte, sbyte>(inputB.BytesBuffer), outputType, act.Buffer.Span, inputABias.Buffer.Span, inABatch0, inABatch1, aRows, aCols, inBBatch0, inBBatch1, bCols, inputBBias, InAShiftBits, InBShiftBits, checked((sbyte)shiftBits), dynamicChannel == 1),
            var x when x.Item1 == DataTypes.UInt8 && x.Item2 == DataTypes.Int16 => GnneMatmulV2<byte, short>(inputA.BytesBuffer, MemoryMarshal.Cast<byte, short>(inputB.BytesBuffer), outputType, act.Buffer.Span, inputABias.Buffer.Span, inABatch0, inABatch1, aRows, aCols, inBBatch0, inBBatch1, bCols, inputBBias, InAShiftBits, InBShiftBits, checked((sbyte)shiftBits), dynamicChannel == 1),
            var x when x.Item1 == DataTypes.Int8 && x.Item2 == DataTypes.UInt8 => GnneMatmulV2<sbyte, byte>(MemoryMarshal.Cast<byte, sbyte>(inputA.BytesBuffer), inputB.BytesBuffer, outputType, act.Buffer.Span, inputABias.Buffer.Span, inABatch0, inABatch1, aRows, aCols, inBBatch0, inBBatch1, bCols, inputBBias, InAShiftBits, InBShiftBits, checked((sbyte)shiftBits), dynamicChannel == 1),
            var x when x.Item1 == DataTypes.Int8 && x.Item2 == DataTypes.Int8 => GnneMatmulV2<sbyte, sbyte>(MemoryMarshal.Cast<byte, sbyte>(inputA.BytesBuffer), MemoryMarshal.Cast<byte, sbyte>(inputB.BytesBuffer), outputType, act.Buffer.Span, inputABias.Buffer.Span, inABatch0, inABatch1, aRows, aCols, inBBatch0, inBBatch1, bCols, inputBBias, InAShiftBits, InBShiftBits, checked((sbyte)shiftBits), dynamicChannel == 1),
            var x when x.Item1 == DataTypes.Int8 && x.Item2 == DataTypes.Int16 => GnneMatmulV2<sbyte, short>(MemoryMarshal.Cast<byte, sbyte>(inputA.BytesBuffer), MemoryMarshal.Cast<byte, short>(inputB.BytesBuffer), outputType, act.Buffer.Span, inputABias.Buffer.Span, inABatch0, inABatch1, aRows, aCols, inBBatch0, inBBatch1, bCols, inputBBias, InAShiftBits, InBShiftBits, checked((sbyte)shiftBits), dynamicChannel == 1),
            var x when x.Item1 == DataTypes.Int16 && x.Item2 == DataTypes.UInt8 => GnneMatmulV2<short, byte>(MemoryMarshal.Cast<byte, short>(inputA.BytesBuffer), inputB.BytesBuffer, outputType, act.Buffer.Span, inputABias.Buffer.Span, inABatch0, inABatch1, aRows, aCols, inBBatch0, inBBatch1, bCols, inputBBias, InAShiftBits, InBShiftBits, checked((sbyte)shiftBits), dynamicChannel == 1),
            var x when x.Item1 == DataTypes.Int16 && x.Item2 == DataTypes.Int8 => GnneMatmulV2<short, sbyte>(MemoryMarshal.Cast<byte, short>(inputA.BytesBuffer), MemoryMarshal.Cast<byte, sbyte>(inputB.BytesBuffer), outputType, act.Buffer.Span, inputABias.Buffer.Span, inABatch0, inABatch1, aRows, aCols, inBBatch0, inBBatch1, bCols, inputBBias, InAShiftBits, InBShiftBits, checked((sbyte)shiftBits), dynamicChannel == 1),
            _ => throw new ArgumentOutOfRangeException($"Invalid Input A {inputA.ElementType} or Input B {inputB.ElementType}"),
        };
    }

    private IRType Visit(TensorType inputA, TensorType inputB, DynamicGNNEMatMul target)
    {
        var inputAType = inputA.DType;
        var inputBType = inputB.DType;

        if (inputAType != DataTypes.Int8 && inputAType != DataTypes.UInt8 && inputAType != DataTypes.Int16)
        {
            return new InvalidType("Unsupported input_a_type, should be one of [int8, uint8, int16]");
        }

        if (inputBType != DataTypes.Int8 && inputBType != DataTypes.UInt8 && inputBType != DataTypes.Int16)
        {
            return new InvalidType("Unsupported input_b_type, should be one of [int8, uint8, int16]");
        }

        if (inputAType == DataTypes.Int16 && inputBType == DataTypes.Int16)
        {
            return new InvalidType("int16 for both of input_a_type and input_b_type is not supported");
        }

        if (target.OutputDType != DataTypes.Float16 && target.OutputDType != DataTypes.Float32)
        {
            return new InvalidType("Invalid Ouput Datatype");
        }

        var outputShape = new[] { inputA.Shape[^2], inputB.Shape[^1] };

        var bigShape = inputA.Shape.Rank > inputB.Shape.Rank
            ? inputA.Shape
            : inputB.Shape;

        return new TensorType(target.OutputDType, bigShape.ToArray()[..(bigShape.Rank - 2)].Concat(outputShape).ToArray());
    }
}
#endif
