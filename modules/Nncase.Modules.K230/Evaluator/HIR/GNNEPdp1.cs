﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Shapes;
using Nncase.TIR.Instructions;

namespace Nncase.Evaluator.K230;

[TypeInferGenerator]
public partial class GNNEPdp1Evaluator : IEvaluator<GNNEPdp1>, ITypeInferencer<GNNEPdp1>, ICostEvaluator<GNNEPdp1>
{
    /// <inheritdoc/>
    public Cost Visit(ICostEvaluateContext context, GNNEPdp1 target)
    {
        return new()
        {
            [CostFactorNames.CPUCycles] = 1,
        };
    }

    public IValue Visit(IEvaluateContext context, GNNEPdp1 r)
    {
        var input = context.GetArgumentValueAsTensor(r, GNNEPdp1.Input);
        int[] kernelSize = context.GetArgumentValueAsArray<int>(r, GNNEPdp1.Filter);
        int[] stride = context.GetArgumentValueAsArray<int>(r, GNNEPdp1.Stride);
        var padding = context.GetArgumentValueAsTensor<int>(r, GNNEPdp1.Padding);
        bool[] countIncludePad = context.GetArgumentValueAsArray<bool>(r, GNNEPdp1.CountIncludePad);
        var quantParams = context.GetArgumentValue(r, GNNEPdp1.QuantParams);
        var deQuantParams = context.GetArgumentValue(r, GNNEPdp1.DequantParams);
        var padValue = context.GetArgumentValue(r, GNNEPdp1.Value).AsTensor().Cast<Half>();
        var output = new Tensor<float>(context.CurrentCall.CheckedShape.ToValueArray());
        (Func<float, float, float>, Func<float, int, float>) op = r.ReduceOp switch
        {
            MFU_PDP_OP.AVERAGE =>
                ((a, b) => a + b,
                    (v, k) => v / k),
            MFU_PDP_OP.MIN =>
                ((a, b) => System.Math.Min(a, b),
                    (v, _) => v),
            MFU_PDP_OP.MAX =>
                ((a, b) => System.Math.Max(a, b),
                    (v, _) => v),
            MFU_PDP_OP.SUM =>
                ((a, b) => a + b,
                    (v, _) => v),
            _ => throw new NotSupportedException(),
        };
        Pdp1Impl(input, output, r.DestType, kernelSize[0], kernelSize[1], stride[0], stride[1], (padding[0, 0], padding[0, 1]), (padding[1, 0], padding[1, 1]), op.Item1, op.Item2, countIncludePad[0], (quantParams is NoneValue ? null : quantParams.AsTensor())!, (deQuantParams is NoneValue ? null : deQuantParams.AsTensor())!, padValue);
        if (r.DestType == DataTypes.Int8)
        {
            return Value.FromTensor(output.Cast<sbyte>(CastMode.KDefault));
        }

        if (r.DestType == DataTypes.UInt8)
        {
            return Value.FromTensor(output.Cast<byte>(CastMode.KDefault));
        }

        if (r.DestType == DataTypes.Int16)
        {
            return Value.FromTensor(output.Cast<short>(CastMode.KDefault));
        }

        return Value.FromTensor(output.Cast<Half>(CastMode.KDefault));
    }

    private void Pdp1Impl(Tensor input, Tensor output, DataType destType, int filterH, int filterW, int strideH, int strideW, (int Before, int After) paddingH, (int Before, int After) paddingW, Func<float, float, float> binaryOp, Func<float, int, float> windowOp, bool countIncludePad, Tensor quantizeParam = null!, Tensor deQuantizeParam = null!, Tensor<Half> padValue = null!)
    {
        long[] inShape = input.Shape.ToValueArray();
        var inputType = input.ElementType;
        input = input.Cast<float>(castMode: CastMode.KDefault);
        int outH = (int)TypeInference.GetWindowedOutputSize(inShape[2] + paddingH.Before + paddingH.After, filterH, strideH, 1, false).FixedValue;
        int outW = (int)TypeInference.GetWindowedOutputSize(inShape[3] + paddingW.Before + paddingW.After, filterW, strideW, 1, false).FixedValue;

        for (int batch = 0; batch < inShape[0]; batch++)
        {
            for (int oc = 0; oc < inShape[1]; oc++)
            {
                for (int oy = 0; oy < outH; oy++)
                {
                    for (int ox = 0; ox < outW; ox++)
                    {
                        int inYOrigin = (oy * strideH) - paddingH.Before;
                        int inXOrigin = (ox * strideW) - paddingW.Before;
                        int filterYStart = System.Math.Max(0, -inYOrigin);
                        int filterYEnd = System.Math.Min(filterH, (int)inShape[2] - inYOrigin);
                        int filterXStart = System.Math.Max(0, -inXOrigin);
                        int filterXEnd = System.Math.Min(filterW, (int)inShape[3] - inXOrigin);
                        float value = (float)input[batch, oc, inYOrigin + filterYStart, inXOrigin + filterXStart];
                        if (inputType != DataTypes.Float16 && deQuantizeParam is not null)
                        {
                            value = (value - deQuantizeParam.ToArray<DeQuantizeParam>()[0].ZeroPoint) *
                                    deQuantizeParam.ToArray<DeQuantizeParam>()[0].Scale;
                        }

                        int kernelCount = 0;

                        for (int ky = filterYStart; ky < filterYEnd; ky++)
                        {
                            for (int kx = filterXStart; kx < filterXEnd; kx++)
                            {
                                int inY = inYOrigin + ky;
                                int inX = inXOrigin + kx;

                                float inV = (float)input[batch, oc, inY, inX];
                                if (inputType != DataTypes.Float16 && deQuantizeParam is not null)
                                {
                                    inV = (inV - deQuantizeParam.ToArray<DeQuantizeParam>()[0].ZeroPoint) * deQuantizeParam.ToArray<DeQuantizeParam>()[0].Scale;
                                }

                                if (!(ky == filterYStart && kx == filterXStart))
                                {
                                    value = binaryOp(value, inV);
                                }

                                kernelCount++;
                            }
                        }

                        if (countIncludePad)
                        {
                            for (int i = 0; i < (filterH * filterW) - kernelCount; i++)
                            {
                                value = binaryOp(value, (float)padValue.GetValue(0));
                            }

                            kernelCount = filterH * filterW;
                        }

                        int kernelCountV = kernelCount;
                        float tmpOutput = windowOp(value, kernelCountV);

                        if (destType == DataTypes.Float16)
                        {
                            output[batch, oc, oy, ox] = tmpOutput;
                        }
                        else if (destType == DataTypes.Int8)
                        {
                            output[batch, oc, oy, ox] =
                                System.Math.Clamp(
                                    (sbyte)System.Math.Round((tmpOutput * quantizeParam.ToArray<QuantizeParam>()[0].Scale) +
                                                                           quantizeParam.ToArray<QuantizeParam>()[0].ZeroPoint),
                                    (sbyte)-127,
                                    (sbyte)127);
                        }
                        else if (destType == DataTypes.Int16)
                        {
                            output[batch, oc, oy, ox] =
                                System.Math.Clamp(
                                    (short)System.Math.Round((tmpOutput * quantizeParam.ToArray<QuantizeParam>()[0].Scale) +
                                                                           quantizeParam.ToArray<QuantizeParam>()[0].ZeroPoint),
                                    (short)-32767,
                                    (short)32767);
                        }
                        else if (destType == DataTypes.UInt8)
                        {
                            output[batch, oc, oy, ox] =
                                System.Math.Clamp(
                                    (byte)System.Math.Round((tmpOutput * quantizeParam.ToArray<QuantizeParam>()[0].Scale) +
                                                                          quantizeParam.ToArray<QuantizeParam>()[0].ZeroPoint),
                                    (byte)0,
                                    (byte)255);
                        }
                    }
                }
            }
        }
    }

    private IRType Visit(ITypeInferenceContext context, GNNEPdp1 target, TensorType input)
    {
        var args = context.GetArguments(target, GNNEPdp1.Filter, GNNEPdp1.Stride, GNNEPdp1.Padding);
        var outputType = TypeInference.ReduceWindow2DType(input, (Shape)args[0], (Shape)args[1], (Paddings)args[2], false);
        return new TensorType(target.DestType, ((TensorType)outputType).Shape);
    }
}
