// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.CostModel;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Shapes;
using OrtKISharp;
using static Nncase.Evaluator.EvaluatorUtil;

namespace Nncase.Evaluator.K230;

[TypeInferGenerator]
public partial class GNNEPadEvaluator : IEvaluator<GNNEPad>, ITypeInferencer<GNNEPad>, ICostEvaluator<GNNEPad>
{
    public Cost Visit(ICostEvaluateContext context, GNNEPad target)
    {
        return new()
        {
            [CostFactorNames.CPUCycles] = 1,
        };
    }

    /// <inheritdoc />
    public IValue Visit(IEvaluateContext context, GNNEPad p)
    {
        var input = context.GetArgumentValue(p, GNNEPad.Input).AsTensor().Cast<float>().ToOrtTensor();
        var pads = context.GetInt64OrtTensorArgumentValue(p, GNNEPad.Pads);
        var value = context.GetArgumentValue(p, GNNEPad.Value).AsTensor().Cast<float>().ToOrtTensor();
        return OrtKI.Cast(
            OrtKI.Pad(input, ToOnnxPadFormat(pads), value, "constant"),
            (int)OrtDataType.Float16).ToValue();
    }

    private IRType Visit(ITypeInferenceContext context, GNNEPad target, TensorType input)
    {
        var paddings = (Paddings)context.GetArgument(target, GNNEPad.Pads);
        var value = (Expr)context.GetArgument(target, GNNEPad.Value);
        return TypeInference.PadType(input, paddings, value);
    }
}
