﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;

namespace Nncase.Passes;

public sealed partial class K230ModuleConvertPass : FunctionPass
{
    private readonly string _moduleKind;

    public K230ModuleConvertPass(string moduleKind)
    {
        _moduleKind = moduleKind;
    }

    protected override Task<BaseFunction> RunCoreAsync(BaseFunction input, RunPassContext context)
    {
        if (input.ModuleKind != _moduleKind)
        {
            return Task.FromResult(input.With(moduleKind: _moduleKind));
        }

        return Task.FromResult(input);
    }
}
