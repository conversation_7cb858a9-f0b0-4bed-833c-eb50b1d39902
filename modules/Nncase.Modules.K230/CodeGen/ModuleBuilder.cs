﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Text;
using Nncase.Diagnostics;
using Nncase.IR;
using Nncase.Targets;

namespace Nncase.CodeGen.K230;

/// <summary>
/// K230CoreModule builder.
/// </summary>
public sealed class K230ModuleBuilder : IModuleBuilder, IDisposable
{
    private readonly SectionManager _sectionManager;
    private readonly BinaryWriter _rdataWriter;
    private readonly BinaryWriter[] _threadLocalRdataWriters;
    private readonly BinaryWriter[] _blockLocalRdataWriters;

    public K230ModuleBuilder(CompileOptions options)
    {
        _sectionManager = new();
        _rdataWriter = _sectionManager.GetWriter(WellknownSectionNames.Rdata);
        var shardCount = TensorUtilities.GetProduct(((Targets.NTTTargetOptions)options.TargetOptions).Hierarchies[0]);
        _threadLocalRdataWriters = new BinaryWriter[shardCount];
        _blockLocalRdataWriters = new BinaryWriter[shardCount / ((Targets.NTTTargetOptions)options.TargetOptions).Hierarchies[0][^1]];
        for (int i = 0; i < shardCount; i++)
        {
            _threadLocalRdataWriters[i] = _sectionManager.GetWriter(WellknownSectionNames.ThreadLocalRdata, i);
        }

        for (int i = 0; i < _blockLocalRdataWriters.Length; i++)
        {
            _blockLocalRdataWriters[i] = _sectionManager.GetWriter(WellknownSectionNames.BlockLocalRdata, i);
        }

        CompileOptions = options;
    }

    public CompileOptions CompileOptions { get; }

    /// <inheritdoc/>
    public string ModuleKind => Runtime.K230.K230RtModule.Kind;

    /// <inheritdoc/>
    public ILinkableModule Build(IReadOnlyList<BaseFunction> functions)
    {
        var targetOptions = (NTTTargetOptions)CompileOptions.TargetOptions;

        // 1. write the module header
        using (var writer = _sectionManager.GetWriter(LinkedModule.ModuleHeaderSectionName))
        {
            var header = default(ModuleDescHeader);
            header.ThreadDim = (uint)targetOptions.Hierarchies[0][^1];
            header.BlockDim = targetOptions.Hierarchies[0].Length < 2 ? 1 : (uint)targetOptions.Hierarchies[0][^2];
            header.ChipDim = targetOptions.Hierarchies[0].Length < 3 ? 1 : (uint)targetOptions.Hierarchies[0][^3];
            writer.Write(ref header);
        }

        var linkableFunctions = functions.OfType<TIR.PrimFunction>().Select((f, i) => new FunctionBuilder((uint)i, _rdataWriter, _threadLocalRdataWriters, _blockLocalRdataWriters, (Targets.NTTTargetOptions)CompileOptions.TargetOptions).Build(f)).ToArray();
        _rdataWriter.Flush();
        var threadLocalRdataContents = Enumerable.Range(0, _threadLocalRdataWriters.Length).Select(i =>
        {
            _threadLocalRdataWriters[i].Flush();
            return _sectionManager.GetContent(WellknownSectionNames.ThreadLocalRdata, i)!;
        }).ToArray();
        var blockLocalRdataContents = Enumerable.Range(0, _blockLocalRdataWriters.Length).Select(i =>
        {
            _blockLocalRdataWriters[i].Flush();
            return _sectionManager.GetContent(WellknownSectionNames.BlockLocalRdata, i)!;
        }).ToArray();

        if (CompileOptions.DumpFlags.HasFlag(DumpFlags.CodeGen))
        {
            var dump_root = Path.Join(CompileOptions.DumpDir, ModuleKind);
            foreach (var func_builder in linkableFunctions.OfType<LinkableDeviceFunction>())
            {
                DumpAsm(dump_root, func_builder);
            }
        }

        return new LinkableModule(_sectionManager.GetContent(LinkedModule.ModuleHeaderSectionName)!, _sectionManager.GetContent(WellknownSectionNames.Rdata)!, threadLocalRdataContents, blockLocalRdataContents, linkableFunctions, CompileOptions);
    }

    public void Dispose() => ((IDisposable)_sectionManager.GetContent(".rdata")!).Dispose();

    private void DumpAsm(string dump_root, LinkableDeviceFunction function)
    {
        var dump_dir = Path.Join(dump_root, function.SourceFunction.Name);
        if (!Directory.Exists(dump_dir))
        {
            Directory.CreateDirectory(dump_dir);
        }

        using var asmfile = File.OpenWrite(Path.Join(dump_dir, "compile.text.asm"));
        using var bwfile = File.OpenWrite(Path.Join(dump_dir, "ddr_bandwidth.csv"));
        using var asmWriter = new StreamWriter(asmfile);
        using var bwWriter = new StreamWriter(bwfile);
        function.DeCompile(asmWriter, bwWriter);
    }
}
