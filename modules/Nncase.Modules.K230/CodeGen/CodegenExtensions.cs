// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.TIR;

namespace Nncase.CodeGen.K230;

public static class CodeGenExtensions
{
    public static int Size(this Nncase.TIR.Buffer buffer) => ((TensorConst)buffer.MemSpan.Size.FixedValue).Value.ToScalar<int>();

    public static ulong Start(this Nncase.TIR.Buffer buffer) => ((TensorConst)buffer.MemSpan.Start.FixedValue).Value.ToScalar<ulong>();

    public static int[] Shape(this Nncase.TIR.Buffer buffer) => buffer.Dimensions.ToArray().Select(d => (int)d.FixedValue).ToArray();
}
