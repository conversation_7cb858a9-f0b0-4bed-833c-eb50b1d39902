// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.
#pragma warning disable
using System.Diagnostics;
using System.Text;
using System.Text.RegularExpressions;
using Nncase.IR;
using Nncase.Runtime;
using Nncase.TIR;

namespace Nncase.CodeGen.K230;

internal sealed class Assembler
{
    public string AsmPath;

    public Assembler(string asm_path)
    {
        AsmPath = asm_path;
        AsmLines = new();
        SymobelMap = new();
        LabelMap = new();
    }

    abstract class AsmLine
    {
        public AsmLine(string line, int pc)
        {
            Line = line;
            Pc = pc;
            Update = string.Empty;
        }

        /// <summary>
        /// the line.
        /// </summary>
        public string Line;

        /// <summary>
        /// the update value.
        /// </summary>
        public string Update;

        /// <summary>
        /// pc.
        /// </summary>
        public int Pc;
    }

    sealed class AsmInst : AsmLine
    {
        public string InstName;

        public AsmInst(string inst_name, string line, int pc)
            : base(line, pc)
        {
            InstName = inst_name;
        }
    }

    sealed class AsmSymobl : AsmLine
    {
        public string SymbolName;

        public AsmSymobl(string symbol_name, string line, int pc)
            : base(line, pc)
        {
            SymbolName = symbol_name;
        }
    }

    sealed class AsmLabel : AsmLine
    {
        public string LabelName;

        public AsmLabel(string label_name, string line, int pc)
            : base(line, pc)
        {
            LabelName = label_name;
        }
    }

    sealed class AsmOther : AsmLine
    {
        public AsmOther(string line, int pc)
            : base(line, pc)
        {
        }
    }

    readonly List<AsmLine> AsmLines;
    readonly Dictionary<string, int> SymobelMap;
    readonly Dictionary<string, int> LabelMap;

    public void Assemble()
    {
        collect(AsmPath);
        int relocate_cnt = 0;
        bool need_relocate = false;
        do
        {
            if (need_relocate)
            {
                relocate();
                dump_relocate(Path.Combine(Path.GetDirectoryName(AsmPath) ?? "", Path.GetFileNameWithoutExtension(AsmPath) + "_" + (relocate_cnt++) + ".s"));
                need_relocate = false;
            }

            foreach (var (inst, i) in AsmLines.Select((l, i) => (l, i)).Where(t => t.l is AsmInst).Select(t => ((AsmInst)t.l, t.i)))
            {
                if (replaceSymobl(inst, i))
                {
                    need_relocate = true;
                    break;
                }

                if (replaceLabel(inst, i))
                {
                    need_relocate = true;
                    break;
                }
            }
        } while (need_relocate);
    }

    void collect(string asm_path)
    {
        using var rd = new StreamReader(File.OpenRead(asm_path));
        int pc = 0;
        while (rd.ReadLine() is string line)
        {
            switch (line)
            {
                case var x when x.StartsWith(".L."):
                    AsmLabel label = new(x[..^1], x, pc);
                    AsmLines.Add(label);
                    LabelMap.Add(label.LabelName, pc);
                    break;
                case var x when !x.StartsWith("  ") && x.EndsWith(":"):
                    AsmSymobl symobl = new(x[..^1], x, pc);
                    AsmLines.Add(symobl);
                    SymobelMap.Add(symobl.SymbolName, pc);
                    break;
                case var x when x.StartsWith("  I."):
                    AsmInst inst = new(x[4..x.IndexOf("(")], x, pc);
                    AsmLines.Add(inst);
                    pc += CodeGenUtil.InstLength(inst.InstName);
                    break;
                case var x when x.StartsWith("  ."):
                    AsmLines.Add(new AsmOther(line, pc));
                    break;
                default:
                    throw new ArgumentOutOfRangeException(line);
            }
        }
    }

    bool is_overflow(int imm, int bits = 12)
    {
        if (imm < 0)
        {
            // 如果是负数,则判断有效负数的位数小于 bits-1
            int mask = ~((1 << (bits - 1)) - 1);
            if (mask == (imm & mask))
            {
                return false;
            }

            return true;
        }

        // 如果是正数, 当bits为12位, 正数不能超过2048
        return imm > (1 << (bits - 1));
    }

    (uint high, uint low) split(int imm)
    {
        uint high = (uint)((imm >> 12) + ((imm >> 11) & 0x1)) & 0xfffff;
        uint low = (uint)(imm & 0x00000fff);
        return (high, low);
    }

    /// <summary>
    /// re locate the symobel map and label map.
    /// </summary>
    void relocate()
    {
        SymobelMap.Clear();
        LabelMap.Clear();
        int pc = 0;
        foreach (var line in AsmLines)
        {
            switch (line)
            {
                case AsmLabel label:
                    label.Pc = pc;
                    LabelMap[label.LabelName] = pc;
                    break;
                case AsmSymobl symobl:
                    // AsmLines.Add(new(x, pc, Kind.Symobl));
                    symobl.Pc = pc;
                    SymobelMap[symobl.SymbolName] = pc;
                    break;
                case AsmInst inst:
                    inst.Pc = pc;
                    pc += K230.CodeGenUtil.InstLength(inst.InstName);
                    break;
                default:
                    line.Pc = pc;
                    break;
            }
        }
    }

    bool replaceLabel(AsmInst inst, int index)
    {
        Regex finder = new Regex(@"  I.(\w+)\(.*(\.L\..*)\)");
        Match match = finder.Match(inst.Line);
        if (match == Match.Empty)
        {
            return false;
        }

        var label = match.Groups[2].Value;
        int rip;
        if (label.EndsWith(".high"))
        {
            rip = LabelMap[label[..^5]] - inst.Pc;
            inst.Update = inst.Line.Replace(label, split(rip).high.ToString());
        }
        else if (label.EndsWith(".low"))
        { // NOTE 按high取具体偏移.
            rip = LabelMap[label[..^4]] - inst.Pc;
            inst.Update = inst.Line.Replace(label, split(rip + 4).low.ToString());
        }
        else
        {
            rip = LabelMap[label] - inst.Pc;
            var bits = 12;
            if (inst.InstName == "JAL")
            {
                bits = 20;
            }

            if (is_overflow(rip, bits))
            {
                if (inst.InstName == "JAL")
                {
                    throw new NotSupportedException("overflow imm!");
                }
                else
                {
                    /* add the label high, low for the new jump.
                      I.BEQ(R.rax,R.r0,.L.else.2) # to false
                  to=>
                      I.BEQ(R.rax,R.r0,8)
                      I.JAL(R.r0,16)
                      I.AUIPC(R.10, .L.else.2.high)
                      I.ADDI(R.10,R.10,.L.else.2.low) # lea
                      I.JALR(R.r0,R.10,0)*/

                    // 1. update this jump.
                    inst.Line = inst.Line.Replace(label, "8");

                    // 2. insert the jump.
                    AsmLines.InsertRange(
                        index + 1,
                        new AsmInst[] {
                      new("JAL",    "  I.JAL(R.r0,16)", 0), // 0
                      new("AUIPC", $"  I.AUIPC(R.r10,{label}.high)", 0), // 4
                      new("ADDI",  $"  I.ADDI(R.r10,R.r10,{label}.low)", 0), // 8
                      new("JALR",  $"  I.JALR(R.r0,R.r10,0) ", 0), }); // 12

                                                                    // ? 20
                    return true;
                }
            }

            inst.Update = inst.Line.Replace(label, rip.ToString());
        }

        inst.Update += $" # {label} = {inst.Pc} + {rip}";
        return false;
    }

    bool replaceSymobl(AsmInst inst, int index)
    {
        Regex finder = new Regex(@"\(.*,?.*(@[a-zA-Z\d_]+\.(\w+)).*,?.*\)");
        Match match = finder.Match(inst.Line);
        if (match == Match.Empty)
        {
            return false;
        }

        var label = match.Groups[1].Value.Trim();
        var rip = SymobelMap[label[1..^(match.Groups[2].Length + 1)]] - inst.Pc;
        uint imm;
        if (match.Groups[2].Value == "high")
        {
            imm = split(rip).high; // NOTE high此时的pc要-8才是low.
        }
        else if (match.Groups[2].Value == "low")
        {
            imm = split(rip + 4).low;
        }
        else
        {
            throw new ArgumentOutOfRangeException();
        }

        inst.Update = inst.Line.Replace(label, imm.ToString());
        inst.Update += $" # {label} = {inst.Pc} + {rip}";
        return false;
    }

    public void dump_relocate(string output_path)
    {
        using var tw = new StreamWriter(File.Open(output_path, FileMode.Create));
        foreach (var line in AsmLines)
        {
            tw.WriteLine(line.Line);
        }
    }

    public void Dump(string output_path)
    {
        using var tw = new StreamWriter(File.Open(output_path, FileMode.Create));
        foreach (var line in AsmLines)
        {
            if (line is AsmInst inst)
            {
                tw.Write(String.Format("{0, 8:D8}", inst.Pc));
            }

            tw.WriteLine(line.Update == string.Empty ? line.Line : line.Update);
        }
    }
}

#if false
public sealed class CSourceGModelBuilder : IRTModule
{
    public ModuleType ModuleType => ModuleType.Create("k230");

    public byte[] Source => throw new NotImplementedException();

    public bool IsSerialized { get; private set; }

    public IReadOnlyList<IRTFunction> Functions => throw new NotImplementedException();

    public ValueTask InitializeAsync()
    {
        throw new NotImplementedException();
    }

    public ValueTask UninitializeAsync()
    {
        throw new NotImplementedException();
    }

    public string CSourceFilePath { get; private set; }

    public string AsmFilePath { get; private set; }

    public string AsmOutFilePath { get; private set; }

    // public string AsmScFilePath { get; private set; }
    // public string AsmScOutFilePath { get; private set; }

    /// <summary>
    /// function signature, note in k230, the we will put the output into the c function params
    /// NOTE signature 要被用来生成glb params.
    /// <code>
    /// e.g signature like (f32[1,2,3,4]) -> f32[2,2,3,4]
    /// the primfunc like T.PrimFunc(input f32[1,2,3,4],output f32[1,2,3,4])
    /// </code>
    /// </summary>
    public CallableType? Signature { get; set; }

    public PrimFunction Entry => _entry!;

    public GModelBuilder GModelBuilder;// _scmodelBuilder;
    private PrimFunction? _entry;

    public string Backend { get; set; }

    private static string _save_csoure_context(Stream src)
    {
        var path = CodeGen.CodeGenUtil.GetTempFileName(".c");
        using (var dest = File.Open(path, FileMode.Create, FileAccess.Write))
        {
            src.CopyTo(dest);
        }

        return path;
    }

    public CSourceGModelBuilder(Stream s)
        : this(_save_csoure_context(s), null)
    {
        // todo 因为现在调用外部编译器还得把文件写出去,再chibicc读回来,后续需要优化.
    }

    public CSourceGModelBuilder(string c_source_path, CallableType? signature = null)
    {
        CSourceFilePath = c_source_path;
        AsmFilePath = CodeGen.CodeGenUtil.GetTempFileName(".s");
        AsmOutFilePath = AsmFilePath + ".o";

        // AsmScFilePath = CodeGenUtil.GetTempFileName(".s");
        // AsmScOutFilePath = AsmScFilePath + ".o";
        IsSerialized = false;
        GModelBuilder = null!;

        // _scmodelBuilder = null!;
        Signature = signature;
        Backend = "cmodel";
    }

    public string Dump(string name, string dumpDirPath)
    {
        if (!Directory.Exists(dumpDirPath))
        {
            Directory.CreateDirectory(dumpDirPath);
        }

        if (!IsSerialized)
        {
            AsmFilePath = Path.Join(dumpDirPath, name + Path.GetExtension(AsmFilePath));
            AsmOutFilePath = Path.Join(dumpDirPath, name + Path.GetExtension(AsmOutFilePath));

            // AsmScFilePath = Path.Join(dumpDirPath, "sc", name + Path.GetExtension(AsmScFilePath));
            // AsmScOutFilePath = Path.Join(dumpDirPath, "sc", name + Path.GetExtension(AsmScOutFilePath));
            // if (Path.GetDirectoryName(AsmScFilePath) is var dir && dir is not null && !Directory.Exists(dir))
            //     Directory.CreateDirectory(dir);
            Serialize();
        }

        if (Path.GetDirectoryName(AsmFilePath) != dumpDirPath)
        {
            var copy_file = (string _name, string indent, string old_path) =>
            {
                var new_path = Path.Join(dumpDirPath, indent, _name + Path.GetExtension(old_path));
                if (File.Exists(new_path))
                {
                    File.Delete(new_path);
                }

                File.Copy(old_path, new_path);
                return new_path;
            };
            AsmFilePath = copy_file(name, "", AsmFilePath);
            AsmOutFilePath = copy_file(name, "", AsmOutFilePath);

            // AsmScFilePath = copy_file(name, "sc", AsmScFilePath);
            // AsmScOutFilePath = copy_file(name, "sc", AsmScOutFilePath);
        }

        // _scmodelBuilder.Dump(name, Path.Join(dumpDirPath, "sc"));
        return GModelBuilder.Dump(name, dumpDirPath);
    }

    private string getFullExePath(string exe_name)
    {
        if (Environment.GetEnvironmentVariable("PATH") is string paths)
        {
            foreach (var path in paths.Split(OperatingSystem.IsWindows() ? ';' : ':'))
            {
                var full_path = Path.Combine(path, exe_name);
                if (File.Exists(full_path))
                {
                    return full_path;
                }
            }
        }

        return Path.Join(Path.GetDirectoryName(this.GetType().Assembly.Location), exe_name);
    }

    private void compile()
    {
        var errMsg = new StringBuilder();
        var logMsg = new StringBuilder();
        using var errWriter = new StringWriter(errMsg);
        using var logWriter = new StringWriter(logMsg);
        using var proc = new Process();
        proc.StartInfo.FileName = getFullExePath("chibicc");
        proc.StartInfo.Arguments = $"{CSourceFilePath} -cc1 -cc1-input {CSourceFilePath} -cc1-output {AsmFilePath}";
        proc.StartInfo.UseShellExecute = false;
        proc.StartInfo.RedirectStandardInput = true;
        proc.StartInfo.RedirectStandardError = true;
        proc.StartInfo.RedirectStandardOutput = true;
        proc.ErrorDataReceived += (sender, e) => errWriter.WriteLine(e.Data);
        proc.OutputDataReceived += (sender, msg) => logWriter.Write(msg);
        proc.Start();
        proc.BeginOutputReadLine();
        proc.BeginErrorReadLine();
        proc.WaitForExit();
        if (proc.ExitCode != 0)
        {
            throw new InvalidOperationException(errMsg.ToString());
        }
    }

    private void assemble()
    {
        var input_path = AsmFilePath;
        var output_path = AsmOutFilePath;
        var @as = new Assembler(input_path);
        @as.Assemble();
        @as.Dump(output_path);
    }

    // private void assemble_sc()
    // {
    //     // 1. find the gnne start then insert the inst.
    //     using (var reader = new StreamReader(File.OpenRead(AsmFilePath)))
    //     {
    //         using (var writer = new StreamWriter(File.OpenWrite(AsmScFilePath)))
    //         {
    //             // reader unitl the write glb parameters.
    //             while (reader.ReadLine() is var line && line is not null)
    //             {
    //                 writer.WriteLine(line);
    //                 if (line.StartsWith("gnne_start:"))
    //                     break;
    //             }
    //             for (int i = 0; i < 6; i++)
    //             {
    //                 writer.WriteLine(reader.ReadLine());
    //             }
    //             // push the glb params
    //             var args = K230.CodeGenUtil.ToStackArgs(K230.CodeGenUtil.ToPrimFuncParameters(Signature));
    //             uint count = 1;
    //             for (int i = 0; i < args.Count; i++)
    //             {
    //                 for (int j = 0; j < 4; j++)
    //                 {
    //                     writer.WriteLine("  I.ADDI(R.rax,R.r0,0)");
    //                     writer.WriteLine($"  I.SB(R.rbp,R.rax,-{count}) # push params[{count}]");
    //                     count++;
    //                     if (count > 1024)
    //                         throw new ArgumentOutOfRangeException();
    //                 }
    //             }
    //             while (reader.ReadLine() is var line && line is not null)
    //             {
    //                 writer.WriteLine(line);
    //             }
    //         }
    //     }

    // var @as = new Assembler(AsmScFilePath);
    //     @as.Assemble();
    //     @as.Dump(AsmScOutFilePath);
    // }
    public void Serialize()
    {
        if (!IsSerialized)
        {
            compile();
            assemble();
            var as_parser = new AssemblyParser(AsmOutFilePath, Signature);
            GModelBuilder = new GModelBuilder(null, as_parser, false);
            GModelBuilder.Serialize();

            // assemble_sc();
            // _scmodelBuilder = new GModelBuilder(null, new(AsmScOutFilePath, Signature), true);
            // _scmodelBuilder.Serialize();
            IsSerialized = true;
        }
    }

    public Tensor[] Invoke(params Tensor[] inputs)
    {
        if (!IsSerialized)
        {
            Serialize();
        }

        // _scmodelBuilder.FillInputs(inputs);
        GModelBuilder.Backend = Backend;
        return GModelBuilder.Invoke(inputs);
    }
}
#endif
