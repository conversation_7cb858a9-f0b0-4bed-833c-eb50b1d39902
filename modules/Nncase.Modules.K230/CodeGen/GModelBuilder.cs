// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.
#pragma warning disable
using DryIoc.ImTools;
using NetFabric.Hyperlinq;
using Nncase.IR;
using Nncase.Runtime;
using Nncase.Utilities;
using static NetFabric.Hyperlinq.ArrayExtensions;

namespace Nncase.CodeGen.K230;

internal sealed class InstSerializeVisitor : ExprVisitor<bool, bool>
{
    private readonly BinaryWriter Writer;

    public InstSerializeVisitor(BinaryWriter binaryWriter)
    {
        Writer = binaryWriter;
    }

    protected override bool VisitLeafCall(Call expr)
    {
        if (expr.Target is TIR.Instructions.ISerializeInst target)
        {
            target.Serialize(Writer, expr);
        }
        else
        {
            throw new InvalidOperationException($"The {expr.Target.GetType().Name} is invalid in here!");
        }

        return true;
    }
}

public record GModelRTFunction(string name, Delegate handle) : IRTFunction
{
    public string Name
    {
        get => name; set { }
    }

    public Delegate Handle
    {
        get => handle; set { }
    }

    public IReadOnlyList<IRType> ParameterTypes { get; } = null!;

    public IRType ReturnType { get; } = null!;

    public ValueTask InitializeAsync()
    {
        throw new NotImplementedException();
    }

    public ValueTask UninitializeAsync()
    {
        throw new NotImplementedException();
    }

    public ValueTask InvokeAsync(IReadOnlyList<IValue> parameters, IValue ret)
    {
        throw new NotImplementedException();
    }
}

public static class GModelBuilderExtensions
{
    public static int ToCModelTypeCode(this DataType dtype) => dtype switch
    {
        var x when x == DataTypes.UInt8 => 0,
        var x when x == DataTypes.Int8 => 1,
        var x when x == DataTypes.Int16 => 2,
        var x when x == DataTypes.Float16 => 3,
        var x when x == DataTypes.Float32 => 4,
        _ => throw new NotSupportedException(),
    };

    public static int ToNncaseTypeCode(this DataType dtype) => dtype switch
    {
        var x when x == DataTypes.Int8 => 0,
        var x when x == DataTypes.Int16 => 1,
        var x when x == DataTypes.Int32 => 2,
        var x when x == DataTypes.Int64 => 3,
        var x when x == DataTypes.UInt8 => 4,
        var x when x == DataTypes.UInt16 => 5,
        var x when x == DataTypes.UInt32 => 6,
        var x when x == DataTypes.UInt64 => 7,
        var x when x == DataTypes.Float16 => 8,
        var x when x == DataTypes.Float32 => 9,
        var x when x == DataTypes.Float64 => 10,
        var x when x == DataTypes.BFloat16 => 11,
        _ => throw new System.NotSupportedException(),
    };

    public static IEnumerable<TIR.Buffer> InputOf(this ReadOnlySpan<Expr> arr) => arr.ToArray().Where(e => e is TIR.Buffer b && b.MemSpan.Buffer.Location == TIR.MemoryLocation.Input).Select(e => (TIR.Buffer)e);

    public static IEnumerable<TIR.Buffer> InputBufferOf(this ReadOnlySpan<Expr> arr) => arr.ToArray().Where(e => e is TIR.Buffer b && b.MemSpan.Buffer.Location == TIR.MemoryLocation.Input && !(b.Dimensions.Length == 0)).Select(e => (TIR.Buffer)e);

    public static IEnumerable<TIR.Buffer> InputScalarOf(this ReadOnlySpan<Expr> arr) => arr.ToArray().Where(e => e is TIR.Buffer b && b.MemSpan.Buffer.Location == TIR.MemoryLocation.Input && b.Dimensions.Length == 0).Select(e => (TIR.Buffer)e);

    public static IEnumerable<TIR.Buffer> OutputOf(this ReadOnlySpan<Expr> arr) => arr.ToArray().Where(e => e is TIR.Buffer b && b.MemSpan.Buffer.Location == TIR.MemoryLocation.Output).Select(e => (TIR.Buffer)e);
}

internal class DdrRegionStartInfo
{
    public uint input;
    public uint output;
    public uint rdata;
    public uint data;
    public uint text;
}

#if false
public sealed class GModelBuilder : IRTModule
{
    public ModuleType ModuleType => ModuleType.Create("k230");

    public byte[] Source
    {
        get
        {
            Serialize();
            return File.ReadAllBytes(BinFilePath);
        }
    }

    public bool IsSerialized { get; private set; }

    public IReadOnlyList<IRTFunction> Functions => _functions;

    public ValueTask InitializeAsync()
    {
        throw new NotImplementedException();
    }

    public ValueTask UninitializeAsync()
    {
        throw new NotImplementedException();
    }

    private List<IRTFunction> _functions;

    /// <summary>
    /// the program used stack size.
    /// </summary>
    public int StackSize { get; init; }

    public bool EmbeddedParams { get; init; }

    /// <summary>
    /// Only emit the Instructions text.
    /// </summary>
    public bool OnlyInstructions { get; init; }

    public AssemblyParser AssemblyParser { get; init; }

    /// <summary>
    /// the gmodel interp backend.
    /// </summary>
    public string Backend { get; set; }

    private TIR.PrimFunction _entry;

    /// <summary>
    /// the output bin file path.
    /// </summary>
    public string BinFilePath { get; set; }

    private string _ddrCtrlPath { get; set; }

    private string _argFilePath { get; set; }

    private string _descFilePath { get; set; }

    private DdrRegionStartInfo _startInfo { get; init; }

    /// <summary>
    /// start: gmodel中的start
    /// seg_offset: 在input pool/output pool/data pool中的start
    /// size: size.
    /// </summary>
    private Dictionary<TIR.Buffer, (uint start, uint seg_offset, uint size)> _allocation = new(ReferenceEqualityComparer.Instance);

    /// <summary>
    /// Initializes a new instance of the <see cref="GModelBuilder"/> class.
    /// ctor.
    /// </summary>
    /// <param name="entry">entry.</param>
    /// <param name="assemblyParser">assemblyParser.</param>
    /// <param name="embedded_params"> control the params is embedded in the gmodel.</param>
    public GModelBuilder(TIR.PrimFunction? entry = null, AssemblyParser? assemblyParser = null, bool embedded_params = false)
    {
        IsSerialized = false;
        StackSize = assemblyParser is null ? 256 * 1024 : assemblyParser.ParserStackSize();
        OnlyInstructions = assemblyParser is not null && assemblyParser.Params == null;
        AssemblyParser = assemblyParser!;
        EmbeddedParams = embedded_params;
        _entry = entry is null ? AssemblyParser.ParserFunction() : entry;
        BinFilePath = CodeGen.CodeGenUtil.GetTempFileName(".bin");
        _ddrCtrlPath = CodeGen.CodeGenUtil.GetTempFileName(".pc_addr_ctrl");
        _argFilePath = CodeGen.CodeGenUtil.GetTempFileName(".invoke_command");
        _descFilePath = CodeGen.CodeGenUtil.GetTempFileName(".desc");
        _startInfo = new();
        _functions = new();
        Backend = "cmodel";
    }

    /// <inheritdoc cref="Dump" />
    public string Dump(string name, string dumpDirPath)
    {
        if (!Directory.Exists(dumpDirPath))
        {
            Directory.CreateDirectory(dumpDirPath);
        }

        if (!IsSerialized)
        {
            BinFilePath = Path.Join(dumpDirPath, name + Path.GetExtension(BinFilePath));
            _ddrCtrlPath = Path.Join(dumpDirPath, name + Path.GetExtension(_ddrCtrlPath));
            _argFilePath = Path.Join(dumpDirPath, name + Path.GetExtension(_argFilePath));
            _descFilePath = Path.Join(dumpDirPath, name + Path.GetExtension(_descFilePath));
            Serialize();
        }

        if (Path.GetDirectoryName(BinFilePath) != dumpDirPath)
        {
            var copy_file = (string old_path, bool copy) =>
            {
                var new_path = Path.Join(dumpDirPath, name + Path.GetExtension(old_path));
                if (File.Exists(new_path))
                {
                    File.Delete(new_path);
                }

                if (copy)
                {
                    File.Copy(old_path, new_path);
                }

                return new_path;
            };

            BinFilePath = copy_file(BinFilePath, true);
            if (!OnlyInstructions)
            {
                _ddrCtrlPath = copy_file(_ddrCtrlPath, false);
                _argFilePath = copy_file(_argFilePath, true);
                writeInvokeArgs(); // rewrite invoke args.
                writeVdutInvokeArgs();
                if (!EmbeddedParams)
                {
                    _descFilePath = copy_file(_descFilePath, true);
                }
            }
        }

        return BinFilePath;
    }

    private void writeBin()
    {
        using BinaryWriter gw = new(File.OpenWrite(BinFilePath));

        // 分别是[指令起始, input起始, output起始, rdata起始, data起始.]
        uint mem_data = 0;

        // input 但是写入的gmodel的顺序为 [input, rdata, text. ] 后面的data和output并不用写入gmodel中.
        _startInfo.input = 0;

        // 这里跳过input
        gw.Position(_startInfo.input + _entry.Parameters.InputBufferOf().ToArray().Aggregate(0L, (acc, bf) => acc + ((TensorConst)bf.MemSpan.Size).Value.ToScalar<int>()));

        // rdata
        gw.AlignPosition(8);
        _startInfo.rdata = (uint)gw.Position();
        gw.Write(Array.Empty<byte>());

        // text
        gw.AlignPosition(8);
        _startInfo.text = (uint)gw.Position();
        WriteInstText(gw);

        // data
        _startInfo.data = (uint)gw.Position();

        // output
        _startInfo.output = _startInfo.data + mem_data;
    }

    private void WriteInstText(BinaryWriter gw)
    {
        var visitor = new InstSerializeVisitor(gw);
        visitor.Visit(_entry);
    }

    /// <summary>
    /// NOTE 这里分配buffer的时候把所有的input都连续分配到gmodel的最前面, 然后把output 分配到gmodel结束后的位置.
    /// </summary>/
    private void AllocateBuffer()
    {
        List<(uint start, uint seg_offset, uint size)> basements = new();

        uint offset = 0; // 找到function第一个input在input pool size的起始位置作为offset
        uint input_i_start = 0;

        foreach (var input_buffer in _entry.Parameters.InputBufferOf())
        { // 每个input的起点, size. ⚠️这里起点要减去在global input pool上的偏移. 然后从input的起始开始累计.
            basements.Add((input_i_start - offset + _startInfo.input, input_i_start, (uint)input_buffer.Size()));
            _allocation.Add(input_buffer, basements.Last());
            // input_buffer.Start() = checked((int)input_i_start);
            input_i_start += (uint)input_buffer.Size();
        }

        // 输出同样
        uint output_i_start = 0;
        foreach (var output_buffer in _entry.Parameters.OutputOf())
        {
            basements.Add((output_i_start - offset + _startInfo.output, output_i_start, (uint)output_buffer.Size()));
            _allocation.Add(output_buffer, basements.Last());
            // output_buffer.Start() = checked((int)output_i_start);
            output_i_start += (uint)output_buffer.Size();
        }

        // 最后加上rdata的起点的size, 以及mem data的起点和size.
        basements.Add((_startInfo.rdata, 0, 0));
        basements.Add((_startInfo.data, 0, 0));
    }

    /// <summary>
    /// 这里把gnne main 函数参数写入glb中.
    /// </summary>
    private void writeGLBCtrl(Tensor[] inputs)
    {
        // pc_addr_ctrl, 这里的pc addr ctrl就是把basement 地址都写入.
        using var addr_ctrl = File.OpenWrite(_ddrCtrlPath);
        using var writer = new StreamWriter(addr_ctrl);

        var const_buffer_map = new Dictionary<TIR.Buffer, int>(ReferenceEqualityComparer.Instance);
        foreach (var item in inputs.Zip(_entry.Parameters.ToArray().Select(p => (TIR.Buffer)p).ToArray()))
        {
            if (item.Item2.Dimensions.Length == 0)
            {
                if (item.Item1.BytesBuffer.Length != 4)
                {
                    throw new InvalidDataException("Const Scalar Data Length Must = 4");
                }

                const_buffer_map[item.Item2] = BitConverter.ToInt32(item.Item1.BytesBuffer.ToArray(), 0);
            }
        }

        // NOTE 兼容各种平台测试, 在stack args将output的addr减去start, 算子中利用outputs pool的basement获得正确地址.
        var new_allocation = new Dictionary<TIR.Buffer, (uint start, uint seg_offset, uint size)>(_allocation, ReferenceEqualityComparer.Instance);
        foreach (var key in new_allocation.Keys.Where(b => b.MemSpan.Location == TIR.MemoryLocation.Output))
        {
            var p = new_allocation[key];
            new_allocation[key] = (p.start - _startInfo.output, p.seg_offset, p.size);
        }
        var args = K230.CodeGenUtil.ToStackArgs(_entry.Parameters.ToArray().Select(p => (TIR.Buffer)p), new_allocation, const_buffer_map);

        // 倒序写入,然后再倒序.
        var glb_addr = StackSize - 16;
        foreach (var item in args.Select(a => $"{a:X8}").Chunk(4))
        {
            var context = string.Empty;
            for (int i = 0; i < 4; i++)
            {
                var v = i < item.Length ? item[i] : $"{0:X8}";
                context += v;
            }

            writer.Write($"{glb_addr:X8} ");
            writer.WriteLine(context);
            glb_addr -= 16;
        }

        writer.WriteLine($"00000000 {0:X8}{_startInfo.output:X8}{0:X8}{0:X8}"); // 0, output_basement, input_basement, preserve
        writer.WriteLine($"00400100 {_startInfo.text:X32}");
        writer.WriteLine($"00400120 00000001000000010000000000000000");
    }

    /// <summary>
    /// write the input/output desc.
    /// </summary>
    private void writeDesc()
    {
        using var writer = new StreamWriter(File.OpenWrite(_descFilePath));

        // std::fstream desc_out(func_dump_dir / "gmodel.desc", std::ios::out | std::ios::binary);
        writer.WriteLine($"{_entry.Parameters.InputBufferOf().Count()} {_entry.Parameters.OutputOf().Count()}");
        foreach (var input in _entry.Parameters.InputBufferOf())
        {
            var shape = input.Shape();
            writer.WriteLine($"{DataTypes.GetDisplayName(input.ElemType)} ({shape[0]}, {shape[1]}, {shape[2]}, {shape[3]})");
        }

        foreach (var output in _entry.Parameters.OutputOf())
        {
            var shape = output.Shape();
            writer.WriteLine($"{DataTypes.GetDisplayName(output.ElemType)} ({shape[0]}, {shape[1]}, {shape[2]}, {shape[3]})");
        }

        foreach (var input in _entry.Parameters.InputBufferOf())
        {
            writer.WriteLine($"{_allocation[input].start} {_allocation[input].seg_offset} {_allocation[input].size}");
        }

        foreach (var output in _entry.Parameters.OutputOf())
        {
            writer.WriteLine($"{_allocation[output].start} {_allocation[output].seg_offset} {_allocation[output].size}");
        }

        writer.WriteLine($"{_startInfo.rdata} {0} {_startInfo.data - _startInfo.rdata - 0}"); // note we have no rdata.
        writer.WriteLine($"{_startInfo.data} {0} {0}"); // note we have no data.
        writer.WriteLine($"{_startInfo.text} {0} {_startInfo.data - _startInfo.text}"); // text length.
    }

    private void writeInvokeArgs()
    {
        // 写入启动指令
        //  ./cmodel [bin file path] [pc 指针位置(设置为text段开始)] [输出地址] [输出大小] [输出文件夹路径⚠️记得加`/`] [输出type code]
        //                arg 1               arg 2                 arg 3     arg 4          arg 5              arg6
        using var writer = new StreamWriter(File.OpenWrite(_argFilePath));

        // 写入gmodel
        writer.WriteLine(BinFilePath);

        // 指令起始位置
        writer.WriteLine(_startInfo.text);

        // 写入输出起点
        var o_start = StringUtility.Join(",", _entry.Parameters.OutputOf().Select(b => _allocation[b].start));
        writer.WriteLine(o_start == string.Empty ? "0" : o_start);

        // 写入输出size
        var o_size = StringUtility.Join(",", _entry.Parameters.OutputOf().Select(b => b.Size() / b.ElemType.SizeInBytes));
        writer.WriteLine(o_size == string.Empty ? "0" : o_size);

        // 写入dir
        writer.WriteLine(Path.GetDirectoryName(BinFilePath) + "/");

        // 输出typecode
        var type_code = StringUtility.Join(",", _entry.Parameters.OutputOf().Select(b => b.ElemType.ToCModelTypeCode()));
        writer.WriteLine(type_code == string.Empty ? "0" : type_code);

        // 添加控制流.
        writer.WriteLine("wait-key");
    }

    private void writeVdutGLBCtrl()
    {
        // sc model 的 glb ctrl 需要按照某种标准执行.
        using var writer = new StreamWriter(File.OpenWrite(_ddrCtrlPath));
        writer.WriteLine("00000000 00000000000000000000000000000000");
        writer.WriteLine("00000010 00000000000000000000000000000000");
        writer.WriteLine($"00400100 {_startInfo.text:X32}");
        writer.WriteLine($"00400120 00000001000000010000000000000000");
    }

    private void writeVdutInvokeArgs()
    {
        var outputs = _entry.Parameters.OutputOf();
        if (outputs.Count() == 1)
        {
            var output = outputs.First();
            var arch_sim_path = Path.GetFullPath(Path.Combine(GetThisFilePath(), "../../../../../maix3-arch-sim/"));
            using var writer = new StreamWriter(File.OpenWrite(_argFilePath + ".sc"));
            writer.WriteLine($"{arch_sim_path}ic_env/ld-linux-x86-64.so.2");
            writer.WriteLine($"--library-path {arch_sim_path}ic_env/ {arch_sim_path}ic_env/Vkpu_top {BinFilePath} {_startInfo.text} {_startInfo.output} {output.Size() / output.ElemType.SizeInBytes} {Path.GetDirectoryName(BinFilePath)}/ {output.ElemType.ToCModelTypeCode()} +perf +ckp +hang_10000");
        }
    }

    public void Serialize()
    {
        if (IsSerialized)
        {
            return;
        }

        if (OnlyInstructions)
        {
            using (BinaryWriter gw = new(File.OpenWrite(BinFilePath)))
            {
                WriteInstText(gw);
            }
        }
        else
        {
            writeBin();
            AllocateBuffer();
            if (!EmbeddedParams)
            {
                writeDesc();
            }
            else
            {
                // 2. modify the glb params;
                AssemblyParser.OverrideGlbParams(K230.CodeGenUtil.ToStackArgs(_entry.Parameters.ToArray().Select(p => (TIR.Buffer)p), _allocation));
                _entry = AssemblyParser.ParserFunction(); // re parser

                // 3. rewrite the text.
                using (var gw = new BinaryWriter(File.OpenWrite(BinFilePath)))
                {
                    gw.Position(_startInfo.text);
                    var visitor = new InstSerializeVisitor(gw);
                    visitor.Visit(_entry);
                }

                writeVdutGLBCtrl();
            }

            writeVdutInvokeArgs();
            writeInvokeArgs();
        }

        IsSerialized = true;
    }

    public void FillInputs(params Tensor[] inputs)
    {
        // check inputs
        if (inputs.Length != _entry.Parameters.InputOf().Count())
        {
            throw new InvalidOperationException("Input Argument Number Error!");
        }

        using (var bw = new BinaryWriter(File.OpenWrite(BinFilePath)))
        {
            foreach (var tp in inputs.Zip(_entry.Parameters.ToArray().Select(p => (TIR.Buffer)p)).Where(p => p.Item1.Rank != 0))
            {
                var (i_ts, i_buf) = (tp.Item1, tp.Item2);
                bw.Position(_allocation[i_buf].start);
                if (i_ts.BytesBuffer.Length != i_buf.Size())
                {
                    throw new InvalidOperationException("The Input Tensor Is Not Equal The Input Buffer!");
                }

                bw.Write(i_ts.BytesBuffer);
            }
        }
    }

    private static string GetThisFilePath([System.Runtime.CompilerServices.CallerFilePath] string path = "")
    {
        return path;
    }

    /// <summary>
    /// invoke the module entry.
    /// </summary>
    /// <param name="inputs">input args.</param>
    /// <returns> results.</returns>
    /// <exception cref="InvalidOperationException"></exception>
    public Tensor[] Invoke(params Tensor[] inputs)
    {
        if (OnlyInstructions)
        {
            throw new InvalidOperationException("The Only Instructions Mode Can't Be Invoke!");
        }

        FillInputs(inputs);
        writeGLBCtrl(inputs);

        string exe = Backend switch
        {
            "cmodel" => "k230_cmodel_cli",
            "sc_model" => $"{Path.GetFullPath(Path.Combine(GetThisFilePath(), "../../../../../maix3-arch-sim/"))}ic_env/ld-linux-x86-64.so.2",
            _ => throw new ArgumentOutOfRangeException(),
        };

        string args = Backend switch
        {
            "cmodel" => _argFilePath,
            "sc_model" => File.ReadLines(_argFilePath + ".sc").Skip(1).First(),
            _ => throw new ArgumentOutOfRangeException(),
        };

        var engine = new Simulator.GModelEngine(exe, Path.GetDirectoryName(_argFilePath) ?? "");
        engine.Run(args);

        BinaryReader result_bin = new(
          File.OpenRead(
            Path.Combine(
              Path.GetDirectoryName(BinFilePath)!,
              Backend switch
              {
                  "cmodel" => "nncase_result_0.bin",
                  "sc_model" => "nncase_result_0_sc.bin",
                  _ => throw new ArgumentOutOfRangeException(),
              })));

        // get the outputs
        return (from o_buf in _entry.Parameters.OutputOf()
                select Tensor.FromBytes(o_buf.ElemType, result_bin.ReadBytes(o_buf.Size()), o_buf.Shape().Select(x => (long)x).ToArray())).ToArray();
    }
}
#endif
