// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

namespace Nncase.CodeGen.K230;

public static class CodeGenUtil
{
    /// <summary>
    /// convert the primfunction parameters to the glb memory storage format.
    /// </summary>
    /// <param name="parameters">parameters.</param>
    /// <param name="allocation">allocation.</param>
    /// <param name="constBufferMap">constBufferMap.</param>
    /// <returns>.</returns>
    public static List<int> ToStackArgs(IEnumerable<TIR.Buffer> parameters, IReadOnlyDictionary<TIR.Buffer, (uint Start, uint Seg_offset, uint Size)>? allocation = null, IReadOnlyDictionary<TIR.Buffer, int>? constBufferMap = null)
    {
        List<int> args = new();

        // 最后的参数push到最上面.
        // NOTE 用栈传值调用约定,倒序入栈
        foreach (var (param, i) in parameters.Reverse().Select((p, i) => (p, i)))
        {
            List<int> one_arg = new();
            if (param.CheckedShape.IsScalar)
            {
                if (constBufferMap is not null)
                {
                    if (!constBufferMap.ContainsKey(param))
                    {
                        throw new InvalidDataException("The K230 Dynamic function Only accept Int32 Params");
                    }

                    one_arg.Add(constBufferMap[param]);
                }
                else
                {
                    one_arg.Add(0);
                }
            }
            else
            {
                foreach (var s in param.CheckedShape)
                {
                    one_arg.Add((int)s.FixedValue);
                }

                one_arg.Add(allocation is not null ? (int)allocation[param].Start : 0);
                one_arg.Add(param.ElemType.ToNncaseTypeCode());
            }

            one_arg.Reverse(); // 栈上的参数顺序为: dtype, addr , {n,c,h,w}
            args.AddRange(one_arg);
        }

        return args;
    }

    public static IR.CallableType ToSignature(Tensor[] inputs, Tensor[] outputs)
    {
        var to_ttype = (Tensor t) => t.Rank switch
        {
            0 => IR.TensorType.Scalar(t.ElementType),
            _ => new IR.TensorType(t.ElementType, t.Shape),
        };

        return new IR.CallableType(
          new IR.TupleType(outputs.Select(to_ttype)),
          new(inputs.Select(to_ttype).ToArray()));
    }

    /// <summary>
    /// convert signature to prim func parameters.
    /// </summary>
    /// <param name="signature">signature.</param>
    /// <returns>.</returns>
    public static IEnumerable<TIR.Buffer> ToPrimFuncParameters(IR.CallableType signature) => signature.Parameters
          .OfType<IR.TensorType>()
          .Select(tt =>
             TIR.T.CreateBuffer(tt, TIR.MemoryLocation.Input, out var _))
          .Concat(
            ((IR.TupleType)signature.ReturnType)
            .OfType<IR.TensorType>()
            .Select(tt =>
             TIR.T.CreateBuffer(tt, TIR.MemoryLocation.Output, out var _)));

    /// <summary>
    /// get the inst length.
    /// </summary>
    /// <param name="inst_name">inst_name.</param>
    /// <returns>.</returns>
    /// <exception cref="ArgumentOutOfRangeException">ArgumentOutOfRangeException.</exception>
    public static int InstLength(string inst_name) => inst_name switch { "LUI" => 4, "AUIPC" => 4, "ADDI" => 4, "ADD" => 4, "SUB" => 4, "MUL" => 4, "DIV" => 4, "DIVU" => 4, "REM" => 4, "REMU" => 4, "LW" => 4, "LH" => 4, "LHU" => 4, "LB" => 4, "LBU" => 4, "SW" => 4, "SH" => 4, "SB" => 4, "BEQ" => 4, "BNE" => 4, "BLT" => 4, "BLTU" => 4, "BGE" => 4, "BGEU" => 4, "JAL" => 4, "JALR" => 4, "INTR" => 2, "END" => 2, "FENCE" => 2, "FENCE_I" => 2, "EXTRW" => 4, "CCR_DECL" => 2, "CCR_SET" => 2, "CCR_CLR" => 2, "MMU_CONF" => 4, "MMU_SETID" => 2, "SS_PACK_SHAPE" => 4, "SS_PACK_STRIDE" => 4, "L2_LOAD_CONF" => 4, "L2_LOAD_W_CONF" => 4, "L2_LOAD_W" => 4, "L2_STORE_CONF" => 4, "L2_LOAD" => 4, "L2_STORE" => 4, "DM_CONF_BROADCAST" => 2, "DM_LOAD_L1_CONF" => 4, "DM_LOAD_W_CONF" => 4, "DM_LOAD_W_CONF2" => 4, "DM_LOAD_W_CONF_DEQ" => 4, "DM_STORE_OF_CONF" => 4, "DM_LOAD_L1" => 4, "DM_LOAD_W" => 4, "DM_LOAD_ACT0" => 4, "DM_STORE_OF" => 4, "PU_FETCHIF_CONF1" => 4, "PU_FETCHIF_CONF2" => 4, "PU_FETCHIF_CONF3" => 4, "PU_FETCHIF_CONF4" => 4, "PU_FETCHIF_CONF_DEQ" => 4, "PU_W_CONF" => 4, "PU_OF_CONF1" => 4, "PU_OF_CONF2" => 4, "PU_COMPUTE_CONF" => 4, "PU_COMPUTE" => 2, "PU_FORWARD_PSUM" => 4, "PU_PDP0_MODE_CONF" => 4, "PU_PDP0_FETCHIF_CONF1" => 4, "PU_PDP0_FETCHIF_CONF2" => 4, "PU_PDP0_FETCHIF_CONF3" => 4, "PU_PDP0_FETCHIF_CONF4" => 4, "PU_PDP0_CONF_DEQ" => 4, "PU_PDP0_W_CONF" => 4, "PU_PDP0_OF_CONF" => 4, "PU_PDP0_COMPUTE" => 2, "ACT0_SRC1_CONF" => 4, "ACT0_COMPUTE" => 4, "MFU_MEMCPY" => 4, "MFU_MEMSET" => 4, "MFU_TRANSPOSE_CONF" => 4, "MFU_TRANSPOSE" => 4, "MFU_PDP1_CONF1" => 4, "MFU_PDP1_CONF2" => 4, "MFU_PDP1_CONF3" => 4, "MFU_PDP1_CONF4" => 4, "MFU_PDP1_CONF_DEQ" => 4, "MFU_PDP1_CONF_QUANT" => 4, "MFU_PDP1_COMPUTE" => 4, "MFU_ACT1_CONF_STRIDE" => 4, "MFU_ACT1_CONF_SRC1" => 4, "MFU_ACT1_CONF_SRC2" => 4, "MFU_ACT1_CONF_DEST" => 4, "MFU_ACT1_CONF_DEQ" => 4, "MFU_ACT1_CONF_QUANT" => 4, "MFU_ACT1_CONF" => 4, "MFU_ACT1_COMPUTE" => 4, "AI2D_COMPUTE" => 2, _ => throw new NotSupportedException(), };
}
