﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace Nncase.CodeGen.K230;

[StructLayout(LayoutKind.Sequential)]
internal unsafe struct ModuleDescHeader
{
    [MarshalAs(UnmanagedType.U4)]
    public uint ThreadDim;

    [<PERSON><PERSON>(UnmanagedType.U4)]
    public uint BlockDim;

    [MarshalAs(UnmanagedType.U4)]
    public uint ChipDim;

    [MarshalAs(UnmanagedType.U4)]
    public uint Reserved0;
}

internal sealed class LinkedModule : ILinkedModule
{
    public const string ModuleHeaderSectionName = ".desc";

    public unsafe LinkedModule(IReadOnlyList<ILinkedFunction> functions, Stream desc, Stream text, Stream rdata, IReadOnlyList<Stream> threadLocalRdatas, IReadOnlyList<Stream> blockLocalRdatas, ulong rdataAlign)
    {
        Functions = functions;
        Sections =
        [
            new LinkedSection(desc, ModuleHeaderSectionName, 0, 8, (uint)sizeof(ModuleDescHeader)),
            new LinkedSection(text, WellknownSectionNames.Text, 0, 8, (ulong)text.Length),
            new LinkedSection(rdata, WellknownSectionNames.Rdata, 0, (uint)rdataAlign, (ulong)rdata.Length),
            new LinkedMultipleContentsSection(threadLocalRdatas, WellknownSectionNames.ThreadLocalRdata, 0, (uint)rdataAlign),
            new LinkedMultipleContentsSection(blockLocalRdatas, WellknownSectionNames.BlockLocalRdata, 0, (uint)rdataAlign),
        ];
    }

    public string ModuleKind => Runtime.K230.K230RtModule.Kind;

    public uint Version => Runtime.K230.K230RtModule.Version;

    public IReadOnlyList<ILinkedFunction> Functions { get; }

    public IReadOnlyList<ILinkedSection> Sections { get; }
}
