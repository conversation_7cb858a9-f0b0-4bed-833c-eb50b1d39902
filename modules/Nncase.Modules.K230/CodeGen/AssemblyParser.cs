// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.
#pragma warning disable
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using Nncase.IR;
using Nncase.TIR;

namespace Nncase.CodeGen.K230;

public sealed class AssemblyParser
{
    /// <summary>
    /// AsmOutPath.
    /// </summary>
    public string AsmOutPath;

    /// <summary>
    /// Params.
    /// </summary>
    public TIR.Buffer[]? Params;

    /// <summary>
    /// find the gmodel.o instructions.
    /// <example>
    /// match `00080400  I.ADDI(R.rax,R.rbp,-4) # lea @bytes_per_element , in local`
    /// result group[1] = "00080400"
    /// result group[2] = "I.ADDI(R.rax,R.rbp,-4)"
    /// result group[3] = "ADDI"
    /// result group[4] = "R.rax,R.rbp,-4"
    /// </example>
    /// </summary>
    public readonly Regex InstFinder = new(@"^(\d{8})\s\s(I\.(\w+)\((.*?)\))", RegexOptions.Multiline);

    public AssemblyParser(string asm_out_path, CallableType? signature)
    {
        AsmOutPath = asm_out_path;
        Params = signature is null ? null : K230.CodeGenUtil.ToPrimFuncParameters(signature).ToArray();
    }

    /// <summary>
    /// override the glb params.
    /// </summary>
    public void OverrideGlbParams(IReadOnlyList<int> args)
    {
        string[] lines = File.ReadAllLines(AsmOutPath);

        // find the start instrutions =
        int startIndex = 0;
        for (int i = 0; i < lines.Length; i++)
        {
            if (lines[i].StartsWith("gnne_start:"))
            {
                startIndex = i;
                break;
            }
        }

        startIndex += 6;
        int offset = 1;
        var finder = new Regex(@"(^\d{8}\s\sI.ADDI\(R.rax,R.r0,)(0)(\))");

        foreach (byte[] bs in args.Select(BitConverter.GetBytes))
        {
            // NOTE get bytes后低位放前面,因此需要逆序push.
            for (int i = 4 - 1; i >= 0; i--)
            {
                byte b = bs[i];
                lines[startIndex + offset] = finder.Replace(lines[startIndex + offset], $"${{1}}{b}$3");
                offset += 2;
            }
        }

        File.WriteAllLines(AsmOutPath, lines);
    }

    public IEnumerable<Expr> ParserInst()
    {
        var matches = InstFinder.Matches(File.ReadAllText(AsmOutPath));
        var callmap = new Dictionary<string, MethodCallExpression>();
        return matches.OfType<Match>().Select(m =>
        {
            var s_method = m.Groups[3].Value;
            var s_args = m.Groups[4].Value.Split(',').Where(s => s != string.Empty).ToArray();

            var method = typeof(I).GetMethod(s_method)!;

            // express call构造时不支持默认参数,因此需要concat
            var args = s_args.Zip(method.GetParameters()).Select<(string, System.Reflection.ParameterInfo), Expression>(
                t => t.Item1.Trim() switch
                {
                    var x when x.StartsWith("R.") => Expression.Field(null, typeof(R).GetField(x[2..])!),
                    var x when t.Item2.ParameterType == typeof(Expr) => Expression.Convert(Expression.Constant(System.Convert.ToInt32(x), typeof(int)), typeof(Expr)),
                    var x when t.Item2.ParameterType.IsEnum => Expression.Constant(Enum.Parse(t.Item2.ParameterType, x), t.Item2.ParameterType),
                    _ => throw new ArgumentOutOfRangeException(),
                })
            .Concat(method.GetParameters().Skip(s_args.Length).Select(
                  p => p.DefaultValue switch
                  {
                      System.DBNull => Expression.Constant(null, p.ParameterType),
                      _ => Expression.Constant(p.DefaultValue, p.ParameterType),
                  }));

            var callExpression = Expression.Call(method, args);
            return Expression.Lambda<Func<Expr>>(callExpression).Compile()();
        });
    }

    public PrimFunction ParserFunction()
    {
        return T.PrimFunc("main", "k230", Params != null ? Params.Select(p => new Var(p.CheckedType)).ToArray() : new IVar[] { }).Body(ParserInst().ToArray()).Build();
    }

    public int ParserStackSize()
    {
        using var rd = new StreamReader(File.OpenRead(AsmOutPath));
        var line = rd.ReadLine();
        var match = Regex.Match(line!, @"^\s\s.stack_size\s(\d+)");
        return Convert.ToInt32(match.Groups[1].Value);
    }
}
