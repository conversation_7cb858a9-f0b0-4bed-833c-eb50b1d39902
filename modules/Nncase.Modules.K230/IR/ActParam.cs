﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Collections;
using static Nncase.IR.K230.ActHelper;
using static Nncase.IR.K230.ShiftBitsHelper;
using static Nncase.Passes.Rules.K230.QuantHelper;

namespace Nncase.IR.K230;

public static class ActHelper
{
    public static Shape GetAct16SegementsShape => [1, 1, 1, (16 * 3) - 1 + 2];

    public static float[,] NestSelect(float[,] arr, Func<float, float> f)
    {
        float[,] ret = new float[arr.GetLength(0), arr.GetLength(1)];
        for (int i = 0; i < arr.GetLength(0); i++)
        {
            for (int j = 0; j < arr.GetLength(1); j++)
            {
                ret[i, j] = f(arr[i, j]);
            }
        }

        return ret;
    }
}

public abstract class ActParamBase
{
    // ki, channels
    private float[,] _xs = null!;
    private float[,] _ks = null!;
    private float[,] _bs = null!;
    private ValueRange<float>[] _fusedClamp = null!;
    private QuantizeParam _qp;

    private bool _isDeq;

    private int _channels;

    public ActParamBase(int channel, QuantParam quantParam, bool isDeq = false)
    {
        if (quantParam.Equals(new(0, 0)))
        {
            quantParam = new(0, 1);
        }

        IsDeq = isDeq;
        Channels = channel;
        Qp = GetQuantParam(quantParam);
    }

    protected ActParamBase(ActParamBase other)
    {
        Qp = new QuantizeParam(other.Qp);
        Xs = new float[other.Xs.GetLength(0), other.Xs.GetLength(1)];
        Array.Copy(other.Xs, 0, Xs, 0, other.Xs.Length);
        Ks = new float[other.Ks.GetLength(0), other.Ks.GetLength(1)];
        Array.Copy(other.Ks, 0, Ks, 0, other.Ks.Length);
        Bs = new float[other.Bs.GetLength(0), other.Bs.GetLength(1)];
        Array.Copy(other.Bs, 0, Bs, 0, other.Bs.Length);
        FusedClamp = new ValueRange<float>[other.FusedClamp.Length];
        Array.Copy(other.FusedClamp, FusedClamp, FusedClamp.Length);
        IsDeq = other.IsDeq;
        Channels = other.Channels;

        // N = other.N;
        // Shape = other.Shape;
    }

    public float[,] Xs
    {
        get => _xs;
        set => _xs = value ?? throw new ArgumentNullException(nameof(value));
    }

    public float[,] Ks
    {
        get => _ks;
        set => _ks = value ?? throw new ArgumentNullException(nameof(value));
    }

    public float[,] Bs
    {
        get => _bs;
        set => _bs = value ?? throw new ArgumentNullException(nameof(value));
    }

    public ValueRange<float>[] FusedClamp
    {
        get => _fusedClamp;
        set => _fusedClamp = value ?? throw new ArgumentNullException(nameof(value));
    }

    public bool IsDeq
    {
        get => _isDeq;
        set => _isDeq = value;
    }

    public int Channels
    {
        get => _channels;
        set => _channels = value;
    }

    public QuantizeParam Qp
    {
        get => _qp;
        set => _qp = value;
    }

    public virtual int N { get; } = 2;

    public virtual Shape Shape { get; } = null!;

    public int DataSizePerChannel => (3 * N) - 1 + 2;

    public float[] GetAct0Data => Enumerable.Range(0, Channels).Select(j =>
    {
        float[] kv = new float[N];
        float[] bv = new float[N];
        float[] xv = new float[N - 1];
        for (int i = 0; i < N; i++)
        {
            kv[i] = Ks[i, j];
            bv[i] = Bs[i, j];
        }

        for (int i = 0; i < N - 1; i++)
        {
            xv[i] = Xs[i, j];
        }

        float[] fv = new[] { FusedClamp[j].Min, FusedClamp[j].Max };
        return kv.Concat(bv).Concat(fv).Concat(xv).ToArray();
    }).Aggregate(Array.Empty<float>(), (a, b) => a.Concat(b).ToArray());

    // act1 [threshold..., k..., b..., clamp_min, clamp_max]
    public float[] GetAct1Data => Enumerable.Range(0, Channels).Select(j =>
    {
        float[] kv = new float[N];
        float[] bv = new float[N];
        float[] xv = new float[N - 1];
        for (int i = 0; i < N; i++)
        {
            kv[i] = Ks[i, j];
            bv[i] = Bs[i, j];
        }

        for (int i = 0; i < N - 1; i++)
        {
            xv[i] = Xs[i, j];
        }

        float[] fv = new[] { FusedClamp[j].Min, FusedClamp[j].Max };
        return xv.Concat(kv).Concat(bv).Concat(fv).ToArray();
    }).Aggregate(Array.Empty<float>(), (a, b) => a.Concat(b).ToArray());

    public void InitValues()
    {
        Xs = new float[N - 1, Channels];
        Ks = new float[N, Channels];
        Bs = new float[N, Channels];
        for (int i = 0; i < N; i++)
        {
            for (int j = 0; j < Channels; j++)
            {
                Ks[i, j] = Qp.Scale;
                Bs[i, j] = !IsDeq ? Qp.ZeroPoint : 0f;
            }
        }

        SetFusedClamp(ValueRange<float>.Full);
    }

    public void SetFusedClamp(ValueRange<Half> range)
    {
        FusedClamp = Enumerable.Range(0, Channels)
            .Select(_ => new ValueRange<float> { Min = (float)range.Min, Max = (float)range.Max }).ToArray();
    }

    public void SetFusedClamp(ValueRange<float> range)
    {
        FusedClamp = Enumerable.Repeat(range, Channels).ToArray();
    }

    public void SetFusedClamp(DataType type)
    {
        var clamp = ValueRange<float>.Full;
        if (type == DataTypes.UInt8)
        {
            clamp.Min = 0;
            clamp.Min = 255;
        }
        else if (type == DataTypes.Int8)
        {
            clamp.Min = -127;
            clamp.Max = 127;
        }
        else if (type == DataTypes.Int16)
        {
            clamp.Min = -2047;
            clamp.Max = 2047;
        }

        SetFusedClamp(clamp);
    }

    public void FusedScale(float scale)
    {
        Ks = NestSelect(Ks, k => k * scale);
    }

    public void ShiftBitsAdaptXs(float scale)
    {
        for (int i = 0; i < Xs.GetLength(1); i++)
        {
            Xs[0, i] /= scale;
        }
    }

    public void FusedScaleRight(float scale)
    {
        Ks = NestSelect(Ks, k => k / scale);
    }

    public void ShiftBitsAdaptXsRight(float scale)
    {
        for (int i = 0; i < Xs.GetLength(1); i++)
        {
            Xs[0, i] *= scale;
        }
    }

    public void FusedShiftBits(sbyte shiftBits)
    {
        FusedScale(1 << shiftBits);
        ShiftBitsAdaptXs(1 << shiftBits);
    }

    public void FusedShiftBitsRight(sbyte shiftBits)
    {
        FusedScaleRight(1 << shiftBits);
        ShiftBitsAdaptXsRight(1 << shiftBits);
    }

    public void FusedChannelScale(float[] scales)
    {
        for (int i = 0; i < Ks.GetLength(0); i++)
        {
            for (int j = 0; j < scales.Length; j++)
            {
                Ks[i, j] *= scales[j];
            }
        }
    }

    public void FusedXs()
    {
        for (int i = 0; i < Xs.GetLength(1); i++)
        {
            if (!Ks[0, i].Equals(Ks[1, i]))
            {
                Xs[0, i] = (Bs[1, i] - Bs[0, i]) / (Ks[0, i] - Ks[1, i]);
            }
        }
    }

    public void FusedBias(QuantizeParam qParam)
    {
        Bs = NestSelect(Bs, b => (b * qParam.Scale) + qParam.ZeroPoint);
    }

    public void FusedFusedClamp(QuantizeParam qParam)
    {
        for (int i = 0; i < FusedClamp.Length; i++)
        {
            FusedClamp[i].Min = (FusedClamp[i].Min * qParam.Scale) + qParam.ZeroPoint;
            FusedClamp[i].Max = (FusedClamp[i].Max * qParam.Scale) + qParam.ZeroPoint;
        }
    }

    public void FusedActParam(ActParamBase rhs)
    {
        if (Channels != rhs.Channels)
        {
            throw new InvalidDataException("act param should same channel");
        }

        for (int seg = 0; seg < N; ++seg)
        {
            for (int i = 0; i < Channels; ++i)
            {
                // scale
                Xs[seg, i] *= rhs.Xs[seg, i];

                // bias
                Bs[seg, i] += rhs.Bs[seg, i];
            }
        }

        for (int i = 0; i < Channels; ++i)
        {
            FusedClamp[i].Max = System.Math.Max(FusedClamp[i].Max, rhs.FusedClamp[i].Max);
            FusedClamp[i].Min = System.Math.Min(FusedClamp[i].Min, rhs.FusedClamp[i].Min);
        }
    }

    public void FusedQuantParam(QuantParam quantParam)
    {
        var qP = GetQuantParam(quantParam);
        FusedFusedClamp(qP);

        // (v * s1 + b1) * s2 + b2 = (v * s1 * s2) + (b1 * s2 + b2)
        FusedScale(qP.Scale);
        FusedBias(qP);
    }

    public Tensor<float> ToFakeActData()
    {
        float[] dt = GetAct0Data;
        return Tensor.From(dt, Shape.ToValueArray().AsSpan()[2..]);
    }

    // public float[] GetAct0Data => ks.Zip(bs).Zip(fusedClamp).Zip(xs)
    //     .Select(x => (x.Item1.Item1.Item1, x.Item1.Item1.Item2, x.Item1.Item2, x.Item2))
    //     .Select((x, i) => new[]{x.Item1[i], x.Item2[i], x.Item3[i]}).Fold(new float[] { }, (a, b) => a.Concat(b).ToArray());
    // public float[] GetAct0Data => Enumerable.Range(0, Channels).Select(c =>
    //     ks[c].Concat(bs[c]).Concat(new[] { (float)fusedClamp[c].Min, (float)fusedClamp[c].Max }).Concat(xs[c]).ToArray()
    // ).Fold(new float[] { }, (a, b) => a.Concat(b).ToArray());

    // act0 [k0, k1, b0, b1, clamp_min, clamp_max, threshold]
    public Tensor<Half> ToAct0Data()
    {
        var data = GetAct0Data.Select(f => (Half)f).ToArray();
        return Tensor.From(data, Shape.ToValueArray());
    }

    public Tensor<Half> ToAct1Data()
    {
        var data = GetAct1Data.Select(f => (Half)f).ToArray();
        return Tensor.From(data, Shape.ToValueArray());
    }

    public abstract int FusedShiftBits();

    /// <inheritdoc />
    public override bool Equals(object? obj)
    {
        if (obj is not ActParamBase @base)
        {
            return false;
        }

        if (!Qp.Equals(@base.Qp) &&
            StructuralComparisons.StructuralEqualityComparer.Equals(FusedClamp, @base.FusedClamp) &&
            IsDeq == @base.IsDeq &&
            Channels == @base.Channels &&
            N == @base.N &&
            Shape.Equals(@base.Shape))
        {
            return false;
        }

        return Comparer(Xs, @base.Xs) && Comparer(Ks, @base.Ks) && Comparer(Bs, @base.Bs);
    }

    /// <inheritdoc />
    public override int GetHashCode()
    {
        var hash = default(HashCode);
        hash.Add(Qp);
        foreach (float x in Xs)
        {
            hash.Add(StructuralComparisons.StructuralEqualityComparer.GetHashCode(x));
        }

        foreach (float x in Ks)
        {
            hash.Add(StructuralComparisons.StructuralEqualityComparer.GetHashCode(x));
        }

        foreach (float x in Bs)
        {
            hash.Add(StructuralComparisons.StructuralEqualityComparer.GetHashCode(x));
        }

        hash.Add(StructuralComparisons.StructuralEqualityComparer.GetHashCode(FusedClamp));
        hash.Add(IsDeq);
        hash.Add(Channels);
        hash.Add(N);
        hash.Add(Shape);
        return hash.ToHashCode();
    }

    public bool HasBiasOnly()
    {
        float[]? arr = Ks.Clone() as float[];
        return arr!.All(v => v.Equals(1f));
    }

    private static bool Comparer(float[,] a, float[,] b)
    {
        if (a.GetLength(0) != b.GetLength(0) || a.GetLength(1) != b.GetLength(1))
        {
            return false;
        }

        for (int i = 0; i < a.GetLength(0); i++)
        {
            for (int j = 0; j < a.GetLength(1); j++)
            {
                if (!a[i, j].Equals(b[i, j]))
                {
                    return false;
                }
            }
        }

        return true;
    }
}

public class ActParam2 : ActParamBase
{
    public ActParam2(ActParam2 other)
        : base(other)
    {
    }

    public ActParam2(int channel, QuantParam quantizeParam = default, bool isDeq = false)
        : base(channel, quantizeParam, isDeq)
    {
        InitValues();
    }

    public override int N => 2;

    public override Shape Shape => [1, 1, Channels, DataSizePerChannel];

    public static ActParam2 GetDefaultConvActParam(int oc, TensorConst bias)
    {
        var actParam = new ActParam2(oc, new QuantParam(0, 1.0f));
        float[] biasData = bias.Value.ToArray<float>();

        // add by channel
        for (int i = 0; i < actParam.Bs.GetLength(0); i++)
        {
            for (int j = 0; j < actParam.Bs.GetLength(1); j++)
            {
                actParam.Bs[i, j] += biasData[j];
            }
        }

        return actParam;
    }

    public static ActParam2 GetDefaultConvActParam(Expr weights, TensorConst bias)
    {
        int c = (int)weights.CheckedShape[0].FixedValue;
        return GetDefaultConvActParam(c, bias);
    }

    public static ActParam2 GetFakeConvActParam(TensorConst weights, TensorConst bias, bool splitWeightsToAct, float[] newWeights)
    {
        int c = (int)weights.CheckedShape[0].FixedValue;
        var actParam = GetDefaultConvActParam(c, bias);

        if (splitWeightsToAct)
        {
            float[] scales = new float[weights.CheckedShape[0].FixedValue];
            ReadOnlySpan<float> weightsSpan = weights.Value.ToArray<float>();
            float channelMaxAbs = 0.0f;
            var eachChannelSize = weights.CheckedShape[1].FixedValue * weights.CheckedShape[2].FixedValue * weights.CheckedShape[3].FixedValue;
            for (int i = 0; i < weightsSpan.Length; i++)
            {
                if (System.Math.Abs(weightsSpan[i]) > channelMaxAbs)
                {
                    channelMaxAbs = System.Math.Abs(weightsSpan[i]);
                }

                if ((i + 1) % eachChannelSize == 0)
                {
                    if (channelMaxAbs < 1.0f)
                    {
                        channelMaxAbs = 1.0f;
                    }

                    scales[i / eachChannelSize] = channelMaxAbs;
                    channelMaxAbs = 0.0f;
                }
            }

            for (int i = 0; i < c; i++)
            {
                actParam.Ks[0, i] *= scales[i];
                actParam.Ks[1, i] *= scales[i];
            }

            for (int i = 0; i < weightsSpan.Length; i++)
            {
                newWeights[i] /= scales[i / eachChannelSize];
            }
        }

        return actParam;
    }

    public static ActParam2 GetFakeConvTransposeActParam(Expr weights, TensorConst bias)
    {
        int c = (int)weights.CheckedShape[0].FixedValue;
        return GetDefaultConvActParam(c, bias);
    }

    public override int FusedShiftBits()
    {
        sbyte shiftBits = GetShiftBits(this);
        FusedShiftBits(shiftBits);
        return shiftBits;
    }

    public void ForEachChannel(Action<ActParam2, int> f)
    {
        for (int i = 0; i < Channels; i++)
        {
            f(this, i);
        }
    }
}

public class ActParam16 : ActParamBase
{
    public ActParam16(ActParam16 other)
        : base(other)
    {
    }

    public ActParam16(int channel, QuantParam quantizeParam = default, bool isDeq = false)
        : base(channel, quantizeParam, isDeq)
    {
        InitValues();
    }

    public override int N => 16;

    public override Shape Shape => GetAct16SegementsShape;

    public override int FusedShiftBits()
    {
        sbyte shiftBits = GetAct1ShiftBits(this);
        FusedShiftBits(shiftBits);
        return shiftBits;
    }

    public void ForEachChannel(Action<ActParam16, int> f)
    {
        for (int i = 0; i < Channels; i++)
        {
            f(this, i);
        }
    }
}

/// <inheritdoc />
public class ActivationParameter : ActParamBase
{
    public ActivationParameter(ActivationParameter other)
        : base(other)
    {
    }

    public ActivationParameter(int channel, QuantParam quantParam = default, bool isDeq = false)
        : base(channel, quantParam, isDeq)
    {
        InitValues();
    }

    public override int N => 2;

    public override Shape Shape => GetAct16SegementsShape;

    public override int FusedShiftBits()
    {
        sbyte shiftBits = GetAct1ShiftBits(this);
        FusedShiftBits(shiftBits);
        return shiftBits;
    }
}

public class ActFoldParam
{
    private float[,] _xs = null!;
    private float[,] _ks = null!;
    private float[,] _bs = null!;
    private ValueRange<Half>[] _fusedClamp = null!;

    public ActFoldParam()
    {
    }

    public ActFoldParam(int channels)
    {
        var zeros = Enumerable.Repeat(0, channels).Select(x => (float)x);
        var ones = Enumerable.Repeat(1, channels).Select(x => (float)x);

        Xs = new float[1, channels];
        Ks = new float[2, channels];
        Bs = new float[2, channels];
        for (int i = 0; i < 2; i++)
        {
            for (int j = 0; j < channels; j++)
            {
                Ks[i, j] = 1f;
            }
        }

        SetFusedClamp(ValueRange<Half>.Full);

        // fusedClamp = new List<ValueRange<float>>(c);
        // for (int i = 0; i < c; i++)
        // fusedClamp.Add(ValueRange<float>.Full);
    }

    public Shape Shape => [1, 1, Channels, DataSizePerChannel];

    public float[,] Xs
    {
        get => _xs;
        set => _xs = value ?? throw new ArgumentNullException(nameof(value));
    }

    public float[,] Ks
    {
        get => _ks;
        set => _ks = value ?? throw new ArgumentNullException(nameof(value));
    }

    public float[,] Bs
    {
        get => _bs;
        set => _bs = value ?? throw new ArgumentNullException(nameof(value));
    }

    public ValueRange<Half>[] FusedClamp
    {
        get => _fusedClamp;
        set => _fusedClamp = value ?? throw new ArgumentNullException(nameof(value));
    }

    public ValueRange<Half>[] GetFusedClampArray
    {
        get => FusedClamp;
    }

    private int DataSizePerChannel => 7;

    private int Channels => Xs.GetLength(1);

    public void SetFusedClamp(ValueRange<Half> clamp)
    {
        FusedClamp = Enumerable.Repeat(clamp, Channels).ToArray();
    }

    public void SetFusedClamp1(ValueRange<float> clamp)
    {
        FusedClamp = Enumerable.Range(0, Channels)
            .Select(_ => new ValueRange<Half> { Min = (Half)clamp.Min, Max = (Half)clamp.Max }).ToArray();
    }
}
