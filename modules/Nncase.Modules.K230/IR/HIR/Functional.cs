﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.TIR.Instructions;

namespace Nncase.IR.K230.F;

public partial class Tensors
{
    public static Call GNNETranspose(Expr input, MFU_TRANS_PERMUTE perm)
        => new Call(new GNNETranspose(perm), input);

    public static Call GNNEPdp1(PrimType destType, MFU_PDP_OP reduceOp, Expr input, Expr filter, Expr stride, Expr padding, Expr quantParams, Expr depuantParams, Expr value, Expr shiftBits, Expr countIncludePad)
        => new Call(new GNNEPdp1(reduceOp, destType), input, filter, stride, padding, quantParams, depuantParams, value, shiftBits, countIncludePad);

    public static Call GNNEPdp0Reduce(PU_PDP0_MODE reduceOp, PrimType outputType, ActParam2 actParam, Expr input, Expr filter, Expr stride, Expr padding, Expr depuantParams, Expr value, Expr shiftBits, Expr countIncludePad, Expr act)
        => new Call(new GNNEPdp0Reduce(reduceOp, outputType, actParam), input, filter, stride, padding, depuantParams, value, shiftBits, countIncludePad, act);

    public static Call GNNEStore(DataType destType, Expr input)
        => GNNEStore((PrimType)destType, input, new long[] { 1, 1, 1, 1 });

    public static Call GNNEStore(DataType destType, Expr input, Expr stride)
        => new Call(new GNNEStore((PrimType)destType), input, stride);

    public static Call GNNEPad(Expr input, Expr pads, Expr value)
        => new Call(new GNNEPad(PadMode.Constant), input, pads, value);

    public static Call GNNELoad(PrimType destType, Expr input)
        => new Call(new GNNELoad(destType), input);

    public static Call GNNEStoreQuant(DataType destType, Expr input)
        => new Call(new GNNEStore((PrimType)destType), input, new RankedShape(1, 1, 1, 1));

    public static Call GNNELoadW(PrimType destType, Expr input)
        => new Call(new GNNELoadW(destType), input);

    public static Call GNNELoadIFDeq(DataType destType, Expr input)
        => new Call(new GNNELoad((PrimType)destType), input);

    public static Call GNNEActivation(Expr inputa, Expr inputb, Expr act, Expr inAShiftBits, Expr inBShiftBits, Expr outShiftBits, Expr deqAParams, Expr deqBParams, Expr outChannels, GnneActivationType type, Expr is16Segments, DataType destType, ActParamBase actParam, int[] outputShape)
        => new Call(new GNNEActivation(type, (PrimType)destType, actParam, outputShape), inputa, inputb, act, inAShiftBits, inBShiftBits, outShiftBits, deqAParams, deqBParams, outChannels, is16Segments);

    public static Call SimpleSingleInputGNNEAct(Expr input, ActParamBase actParam, int channels, QuantParam qp, DataType destType, ActParam2 actParam2, int[] outputShape) =>
        GNNEActivation(input, None.Default, GNNELoadW(DataTypes.Float16, actParam.ToAct1Data()), 0, 0, 0, Tensor.FromArray(new[] { new DeQuantizeParam(qp.ZeroPoint, qp.Scale) }), Tensor.FromArray(new[] { new DeQuantizeParam(qp.ZeroPoint, qp.Scale) }), channels, GnneActivationType.Uninitialized, false, destType, actParam2, outputShape);

    public static Call GNNEConv2D(PrimType destType, Expr input, Expr weights, Expr weightsQInt8, Expr weightsBias, Expr weightsBiasQint8, Expr act, Expr actQint8, Expr deqBias, Expr shiftBits, Expr shiftBitsQint8, Expr qint8Qp, Expr padding, Expr stride, Expr dilation, Expr groups, Expr is16Quant, Expr padValue, ActParam2 actParam, ActParam2 actParamQInt8)
        => new Call(new GNNEConv2D(actParam, actParamQInt8, destType), input, weights, weightsBias, weightsBiasQint8, act, actQint8, deqBias, shiftBits, shiftBitsQint8, qint8Qp, padding, stride, dilation, groups, is16Quant, padValue, weightsQInt8);

    public static Call GNNEPdp0DW(PrimType destType, Expr input, Expr weights, Expr weightsQInt8, Expr weightsBias, Expr weightsBiasQint8, Expr act, Expr actQint8, Expr deqBias, Expr shiftBits, Expr shiftBitsQint8, Expr qint8Qp, Expr padding, Expr stride, Expr dilation, Expr groups, Expr is16Quant, Expr padValue, ActParam2 actParam, ActParam2 actParamQInt8)
        => new Call(new GNNEPdp0DW(actParam, actParamQInt8, destType), input, weights, weightsBias, weightsBiasQint8, act, actQint8, deqBias, shiftBits, shiftBitsQint8, qint8Qp, padding, stride, dilation, groups, is16Quant, padValue, weightsQInt8);

    public static Call GNNEConv2DTranspose(PrimType destType, Expr input, Expr weights, Expr weightsQInt8, Expr weightsBias, Expr weightsBiasQint8, Expr act, Expr actQint8, Expr deqBias, Expr shiftBits, Expr shiftBitsQint8, Expr qint8Qp, Expr padding, Expr stride, Expr dilation, Expr groups, Expr is16Quant, Expr padValue, ActParam2 actParam, ActParam2 actParamQInt8, Expr outputPadding, Expr outputShape)
        => new Call(new GNNEConv2DTranspose(actParam, actParamQInt8, destType), input, weights, weightsBias, weightsBiasQint8, act, actQint8, deqBias, shiftBits, shiftBitsQint8, qint8Qp, padding, stride, dilation, groups, is16Quant, padValue, weightsQInt8, outputPadding, outputShape);

    public static Call Ai2dPad(Expr input, Expr padding, Expr value, Expr inDeqBias, Expr outQuantParam, PrimType outputType, PadMode mode)
        => new Call(new Ai2dPad(mode, outputType), input, padding, value, inDeqBias, outQuantParam);

    public static Call Ai2dResize(PrimType destType, MFU_CROP_RESIZE resizeMethod, bool alignCorners, bool halfPixelCenters, Expr input, Expr newSize, Expr inDeqBias, Expr outQuantParam)
        => new Call(new Ai2dResize(alignCorners, resizeMethod, halfPixelCenters, destType), input, newSize, inDeqBias, outQuantParam);

    public static Call GNNELSTM(PrimType destTypeO, PrimType destTypeOH, Expr input, Expr wXc, Expr actXc, Expr wRc, Expr actRc0, Expr actRc1, Expr initialH, Expr initialC, Expr segFittingParamFt, Expr segFittingParamGt, Expr wXcQarg, Expr wRcQarg, Expr actBin, Expr actBinQ, ActParam2 activationParamXc, ActParam2 activationParamRc0, ActParam2 activationParamRc1, Expr ifDeqBias, Expr xcShiftBits, Expr hDeqBias0, Expr hDeqBias1, Expr cShiftBits, Expr rcShiftBits0, Expr rcShiftBits1, Expr outHShiftBits, Expr outCShiftBits, ActParam2 activationParamBin, ActParam2 activationParamBinQ, LSTMDirection direction, Expr hasStatic, Expr outputSize)
        => new Call(new GNNELSTM(direction, activationParamBin, activationParamBinQ, activationParamXc, activationParamRc0, activationParamRc1, destTypeO, destTypeOH), input, wXc, actXc, wRc, actRc0, actRc1, initialH, initialC, segFittingParamFt, segFittingParamGt, wXcQarg, wRcQarg, actBin, actBinQ, ifDeqBias, xcShiftBits, hDeqBias0, hDeqBias1, cShiftBits, rcShiftBits0, rcShiftBits1, outHShiftBits, outCShiftBits, hasStatic, outputSize);

    public static Call GNNEMatMul(ActParam2 actParam, PrimType destType, Expr inputA, Expr inputB, Expr act, Expr inputABias, Expr inAShiftBits, Expr inBShiftBits, Expr shiftBits, Expr deqBBias)
        => new Call(new GNNEMatMul(actParam, destType), inputA, inputB, act, inputABias, inAShiftBits, inBShiftBits, shiftBits, deqBBias);
}
