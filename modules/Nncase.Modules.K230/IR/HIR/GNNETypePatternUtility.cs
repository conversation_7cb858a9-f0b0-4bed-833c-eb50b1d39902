﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Runtime.CompilerServices;
using Nncase.TIR.Instructions;
using static Nncase.IR.TypePatternUtility;

[assembly: InternalsVisibleTo("Nncase.Tests.K230")]

namespace Nncase.IR.K230;

public static class GNNETypePatternUtility
{
    public static TypePattern ValidDType() => HasDataType(DataTypes.Int8) | HasDataType(DataTypes.Float16) |
                                              HasDataType(DataTypes.UInt8) | HasDataType(DataTypes.Float32) | HasDataType(DataTypes.Int16);

    public static TypePattern ValidTCUInput() => HasRank(4) & HasDataType(DataTypes.Float16);

    public static TypePattern ValidPSum() => IsIRType();

    // public static TypePattern ValidAct() => HasRank(4) & HasDataType(DataTypes.Float16);
    public static TypePattern ValidAct() => HasDataType(DataTypes.Float16);

    public static TypePattern ValidBias() => HasRank(2) & (HasDataType(DataTypes.UInt8) | HasDataType(DataTypes.Float16));

    public static TypePattern ValidWeights() =>
        HasRank(4) & (HasDataType(DataTypes.Float16) | HasDataType(DataTypes.Float32) | HasDataType(DataTypes.Int8) | HasDataType(DataTypes.Int16) | HasDataType(DataTypes.UInt8));

    public static TypePattern ValidMFUInput() => HasRank(4) & (HasDataType(DataTypes.UInt8) | HasDataType(DataTypes.Int8) | HasDataType(DataTypes.Float16));

    // public static TypePattern ValidMFUConstant() => HasShape(new Shape(4)) & HasDataType(DataTypes.Float16);

    // public static TypePattern ValidGNNEShape() => HasShape(new Shape(4)) & IsIntegral();
    public static TypePattern ValidFakeInput() => HasRank(4) & HasDataType(DataTypes.Float32);

    public static TypePattern ValidFakeMFUConstant() => HasShape([4]) & HasDataType(DataTypes.Float32);

    public static TypePattern ValidFakeFusedClamp() => HasShape([2]) & HasDataType(DataTypes.Float32);

    // public static TypePattern ValidFusedClamp() => HasShape(new Shape(2)) & HasDataType(DataTypes.Float16);
    public static TypePattern ValidFusedClamp() => HasRank(2) & HasDataType(DataTypes.Float16);

    public static TypePattern ValidFakePSum() => IsIRType();

    public static TypePattern ValidFakeAct() => HasRank(2) & HasDataType(DataTypes.Float32);

    /// <summary>
    /// GetGNNEDeqParams.
    /// </summary>
    /// <param name="scale">0.</param>
    /// <param name="zeroPoint">1.</param>
    /// <returns>new DeQuantizeParam(zeroPoint, scale).</returns>
    public static DeQuantizeParam GetGNNEDeqParams(float scale, int zeroPoint)
    {
        return new DeQuantizeParam(zeroPoint, scale);
    }

    public static DeQuantizeParam[] GNNEGetDeqParams(int c, float scale, int zeroPoint)
    {
        return Enumerable.Repeat(GetGNNEDeqParams(scale, zeroPoint), c).ToArray();
    }

    public static DeQuantizeParam[] GNNEGetDeqParams(int c, QuantParam quantParam)
    {
        return GNNEGetDeqParams(c, quantParam.Scale, quantParam.ZeroPoint);
    }

    /// <summary>
    /// GetGNNEQuantParams.
    /// </summary>
    /// <param name="scale">scale.</param>
    /// <param name="zeroPoint">zeroPoint.</param>
    /// <returns>new QuantizeParam(zeroPoint, 1.0f / scale).</returns>
    public static QuantizeParam GetGNNEQuantParams(float scale, int zeroPoint)
    {
        return new QuantizeParam(zeroPoint, 1.0f / scale);
    }

    /// <summary>
    /// GNNEGetQuantParams.
    /// </summary>
    /// <param name="c">c.</param>
    /// <param name="scale">scale.</param>
    /// <param name="zeroPoint">zeroPoint.</param>
    /// <returns>Enumerable.Repeat(GetGNNEQuantParams(scale, zeroPoint), c).ToArray().</returns>
    public static QuantizeParam[] GNNEGetQuantParams(int c, float scale, int zeroPoint)
    {
        return Enumerable.Repeat(GetGNNEQuantParams(scale, zeroPoint), c).ToArray();
    }

    /// <summary>
    /// Get_bytes_per_element.
    /// </summary>
    /// <param name="type">DataType.</param>
    /// <returns>1,2,4.</returns>
    /// <exception cref="ArgumentOutOfRangeException">type.GetDisplayName().</exception>
    public static int Get_bytes_per_element(DataType type) => type switch
    {
        var x when x == DataTypes.Int8 || x == DataTypes.UInt8 => 1,
        var x when x == DataTypes.Int16 || x == DataTypes.Float16 => 2,
        var x when x == DataTypes.Float32 || x == DataTypes.UInt32 || x == DataTypes.Int32 => 4,
        _ => throw new ArgumentOutOfRangeException(type.GetDisplayName()),
    };

    public static void CheckIsValidTransposeType(DataType inputType)
    {
        if (Get_bytes_per_element(inputType) > 2)
        {
            throw new NotSupportedException("Unsupported transpose type");
        }
    }

    public static long AlignDiv(long numerator, long denorminator)
    {
        long val = numerator / denorminator;
        return numerator % denorminator != 0 ? val + 1 : val;
    }

    public static QuantizeParam[] GNNEGetQuantParams(int c, QuantParam quantParam)
    {
        return Enumerable.Repeat(GetGNNEQuantParams(quantParam.Scale, quantParam.ZeroPoint), c).ToArray();
    }

    public static bool IsDepthWise(int ic, int oc, int groups)
    {
        return ic == oc && oc == groups && groups != 1;
    }

    public static bool IsDepthWise(Expr input, Expr weights, int groups, bool isConv2DTranspose = false)
    {
        if (!isConv2DTranspose)
        {
            return IsDepthWise(
                input.CheckedShape[1].FixedValue,
                weights.CheckedShape[0].FixedValue,
                groups);
        }
        else
        {
            return IsDepthWise(
            input.CheckedShape[1].FixedValue,
            weights.CheckedShape[1].FixedValue,
            groups);
        }
    }

    internal static long[] ApplyPerm(MFU_TRANS_PERMUTE perm)
    {
        return ApplyPerm1(perm, new long[] { 0, 1, 2, 3 });
    }

    internal static T[] ApplyPerm1<T>(MFU_TRANS_PERMUTE perm, T[] v)
    {
        if (v.Length != 4)
        {
            throw new InvalidOperationException($"applyPerm need Rank4, but get {v.Length}");
        }

        var n = v[0];
        var c = v[1];
        var h = v[2];
        var w = v[3];
        return perm switch
        {
            MFU_TRANS_PERMUTE.NCHW => new[] { n, c, h, w },
            MFU_TRANS_PERMUTE.NCWH => new[] { n, c, w, h },
            MFU_TRANS_PERMUTE.NHCW => new[] { n, h, c, w },
            MFU_TRANS_PERMUTE.NHWC => new[] { n, h, w, c },
            MFU_TRANS_PERMUTE.NWCH => new[] { n, w, c, h },
            MFU_TRANS_PERMUTE.NWHC => new[] { n, w, h, c },
            MFU_TRANS_PERMUTE.CNHW => new[] { c, n, h, w },
            MFU_TRANS_PERMUTE.CNWH => new[] { c, n, w, h },
            MFU_TRANS_PERMUTE.CHNW => new[] { c, h, n, w },
            MFU_TRANS_PERMUTE.CHWN => new[] { c, h, w, n },
            MFU_TRANS_PERMUTE.CWNH => new[] { c, w, n, h },
            MFU_TRANS_PERMUTE.CWHN => new[] { c, w, h, n },
            MFU_TRANS_PERMUTE.HNCW => new[] { h, n, c, w },
            MFU_TRANS_PERMUTE.HNWC => new[] { h, n, w, c },
            MFU_TRANS_PERMUTE.HCNW => new[] { h, c, n, w },
            MFU_TRANS_PERMUTE.HCWN => new[] { h, c, w, n },
            MFU_TRANS_PERMUTE.HWNC => new[] { h, w, n, c },
            MFU_TRANS_PERMUTE.HWCN => new[] { h, w, c, n },
            MFU_TRANS_PERMUTE.WNCH => new[] { w, n, c, h },
            MFU_TRANS_PERMUTE.WNHC => new[] { w, n, h, c },
            MFU_TRANS_PERMUTE.WCNH => new[] { w, c, n, h },
            MFU_TRANS_PERMUTE.WCHN => new[] { w, c, h, n },
            MFU_TRANS_PERMUTE.WHNC => new[] { w, h, n, c },
            MFU_TRANS_PERMUTE.WHCN => new[] { w, h, c, n },
            _ => throw new ArgumentOutOfRangeException(nameof(perm), perm, null),
        };
    }

    internal static MFU_TRANS_PERMUTE ToMFUPerm(int[] perm)
    {
        if (perm.Length != 4)
        {
            throw new InvalidOperationException($"applyPerm need Rank4, but get {perm.Length}");
        }

        char ToPerm(int v)
        {
            switch (v)
            {
                case 0: return 'N';
                case 1: return 'C';
                case 2: return 'H';
                case 3: return 'W';
                default:
                    throw new InvalidOperationException($"perm value should < 4, but get {v}");
            }
        }

        string p = new(perm.Select(ToPerm).ToArray());
        if (Enum.TryParse(p, out MFU_TRANS_PERMUTE mfuPerm))
        {
            return mfuPerm;
        }
        else
        {
            throw new InvalidOperationException("InvalidPerm");
        }
    }

    internal static int[] GetGNNEShape(Expr input)
    {
        return GetGNNEShape(input.CheckedShape.ToValueArray());
    }

    internal static int[] GetGNNEShape(int[] dims)
    {
        if (dims.Length > 4)
        {
            throw new InvalidOperationException("dims Length should <= 4");
        }

        return Enumerable.Repeat(1, 4 - dims.Length).Concat(dims).ToArray();
    }

#if false
    // public static IRType MeshNetBinaryTypeInfer(TensorType inputA, TensorType inputB)
    // {
    //     var inputAShape = inputA.Shape;
    //     var inputBShape = inputB.Shape;
    //
    //     var destDims = System.Math.Max(inputAShape.Rank, inputBShape.Rank);
    //     var inAExt = destDims - inputAShape.Rank;
    //     var inBExt = destDims - inputBShape.Rank;
    //
    //     var outShape = new List<Dimension>();
    //     for (int i = 0; i < destDims; i++)
    //     {
    //             var inADim = i - inAExt;
    //             var inBDim = i - inBExt;
    //
    //             var inA = inADim < 0 ? 1 : inputAShape[inADim];
    //             var inB = inBDim < 0 ? 1 : inputBShape[inBDim];
    //             if (inA == inB)
    //                 outShape.Add(inA);
    //             else if (inA == 1)
    //                 outShape.Add(inB);
    //             else if (inB == 1)
    //                 outShape.Add(inA);
    //             else
    //                 return new InvalidType("MeshNetBinary inputs are not compatible to broadcast");
    //         }
    //
    //         return new TensorType(inputA.DType, new Shape(outShape));
    //     }

    // public static IRType MatMulTypeInfer(TensorType inputA, TensorType inputB)
    // {
    //     if (inputA.Shape[1] != 1 || inputB.Shape[1] != 1)
    //     {
    //         return new InvalidType("FakeMatMul inputs C must be 1");
    //     }
    //
    //     if (inputB.Shape[1] != 1)
    //     {
    //         return new InvalidType("FakeMatMul input b' N must be 1");
    //     }
    //
    //     if (inputA.Shape[3] != inputB.Shape[2])
    //     {
    //         return new InvalidType("FakeMatMul input a's cols must be equal to input b's rows");
    //     }
    //
    //     var shape = new Shape(inputA.Shape[0], 1, inputA.Shape[2], inputB.Shape[3]);
    //     return new TensorType(inputA.DType, shape);
    // }

    public static Option<int[]> IsSupportedReduce(int[] axis, Expr input)
    {
        return IsSupportedReduce(axis, input.CheckedShape.Rank);
    }

    public static Option<int[]> IsSupportedReduce(int[] axis, int inputRank)
    {
        if (inputRank > 4)
        {
            return Option.None;
        }

        var axisValue = axis;
        Array.Sort(axisValue);
        var axisBegin = inputRank - axisValue.Length;
        for (int i = 0; i < axisValue.Length; i++)
        {
            if (axisValue[i] != axisBegin + i)
            {
                return Option.None;
            }
        }

        return Option.Some(axisValue);
    }

    public static int[] InvPerm(int[] perm)
    {
        var newPerm = perm;
        for (int i = 0; i < perm.Length; i++)
        {
            newPerm[perm[i]] = i;
        }

        return newPerm;
    }

    public static bool IsAnyHalfPixel(ImageResizeTransformationMode mode)
        => mode is ImageResizeTransformationMode.HalfPixel or ImageResizeTransformationMode.PytorchHalfPixel;

    public static bool IsAnyRound(ImageResizeNearestMode mode)
        => mode is ImageResizeNearestMode.RoundPreferCeil or ImageResizeNearestMode.RoundPreferFloor;

    public static bool CanBeLoweredToCrop(ResizeImage r) => CanBeLoweredToCrop(r.ResizeMode, r.NearestMode, r.TransformationMode);

    base on result of compare evaluator
    CosSim < 0.9
    public static bool CanBeLoweredToCrop(ImageResizeMode ResizeMode, ImageResizeNearestMode NearestMode, ImageResizeTransformationMode TransformationMode)
    {
        if (TransformationMode == ImageResizeTransformationMode.TFCropAndResize)
        {
            return false;
        }
        if (ResizeMode == ImageResizeMode.Bilinear && IsAnyHalfPixel(TransformationMode))
        {
            return false;
        }

        if (ResizeMode == ImageResizeMode.NearestNeighbor)
        {
            if (NearestMode == ImageResizeNearestMode.Ceil)
            {
                return false;
            }

            if (NearestMode == ImageResizeNearestMode.Floor)
            {
                return TransformationMode == ImageResizeTransformationMode.Asymmetric;
            }

            if (NearestMode == ImageResizeNearestMode.RoundPreferCeil &&
                TransformationMode == ImageResizeTransformationMode.Asymmetric)
            {
                return false;
            }
        }
        return true;
    }

    public static bool CanBeLoweredToMNBroadcast(ResizeImage r) =>
        CanBeLoweredToMNBroadcast(r.ResizeMode, r.NearestMode, r.TransformationMode);

    public static bool CanBeLoweredToMNBroadcast(ImageResizeMode ResizeMode, ImageResizeNearestMode NearestMode, ImageResizeTransformationMode TransformationMode)
    {
        if (ResizeMode != ImageResizeMode.NearestNeighbor)
        {
            return false;
        }

        if (IsAnyRound(NearestMode) && IsAnyHalfPixel(TransformationMode))
        {
            return true;
        }

        if (NearestMode == ImageResizeNearestMode.Floor &&
            TransformationMode == ImageResizeTransformationMode.Asymmetric)
        {
            return true;
        }

        return false;
    }

    public static float ScalarParamOf(Call call, ParameterInfo info)
    {
        return ScalarParamOf<float>(call, info.Index);
    }

    public static T ScalarParamOf<T>(Call call, ParameterInfo info) where T : unmanaged, IEquatable<T>
    {
        return ScalarParamOf<T>(call, info.Index);
    }
    public static T ScalarParamOf<T>(Call call, int index) where T : unmanaged, IEquatable<T>
    {
        return ((TensorConst)call.Parameters[index]).Value.ToScalar<T>();
    }
#endif

}
