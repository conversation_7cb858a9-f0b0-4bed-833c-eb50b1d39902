// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.
#if false
using Nncase.CodeGen;
using Nncase.PatternMatch;
using static Nncase.IR.TypePatternUtility;

namespace Nncase.IR.K230;

/// <summary>
/// [B,M,K] x [B,K,N] => [B,M,N]
/// 支持B必须为动态,M可以为动态/静态的matmul.
/// todo 考虑后续如何支持glb内部的fusion.
/// </summary>
[PatternFunctionalGenerator]
public sealed partial class DynamicGNNEMatMul : CustomOp
{
    /// <summary>
    /// gnne指令.
    /// </summary>
    public static readonly ParameterInfo Text = new(typeof(DynamicGNNEMatMul), 0, "text", HasDataType(DataTypes.UInt8) & HasRank(1));

    /// <summary>
    /// input A : {?,?,M,K}
    /// input A : {?,?,?,K}.
    /// </summary>
    public static readonly ParameterInfo InputA = new(typeof(DynamicGNNEMatMul), 1, "input_a", HasRank(4));

    /// <summary>
    /// Input B : {?,?,K,N}
    /// Input B : {?,?,K,?}.
    /// </summary>
    public static readonly ParameterInfo InputB = new(typeof(DynamicGNNEMatMul), 2, "input_b", HasRank(4));

    /// <summary>
    /// input A : {?,?,M,K}
    /// 此时 in a bias在算子内部所用batch用同一个.
    /// input A : {?,?,?,K}
    /// 因为 in a bias需要给pu的都配置上, 不方便在算子内部broadcast, 因此需要外部传入broadcast好的结果
    /// 因此 input_a_bias 输入固定为 {1,1,1,M}.
    /// </summary>
    public static readonly ParameterInfo InputABias = new(typeof(DynamicGNNEMatMul), 3, "input_a_bias", HasRank(4) & HasDataType(DataTypes.UInt8));

    /// <summary>
    /// input b bias是by layer的, 因此固定为u8的scalar
    /// todo 目前gnne c编译器不支持u8类型, 所以这里类型改成i32.
    /// </summary>
    public static readonly ParameterInfo InputBBias = new(typeof(DynamicGNNEMatMul), 4, "input_b_bias", IsScalar() & HasDataType(DataTypes.Int32));

    /// <summary>
    /// input A : {?,?,M,K} 时 act params {1, 1, M, 7} , 在算子内部batch共用, 此时dynamic_channel=false
    /// input A : {?,?,?,K} 时 act params {1, 1, 1, 7} , 在算子内部batch共用, 此时设置dynamic_channel=true,可以使用硬件act0的is by channel功能支持channel的broadcast.
    /// 类型固定为fp16.
    /// </summary>
    public static readonly ParameterInfo Act = new(typeof(DynamicGNNEMatMul), 5, "act", HasDataType(DataTypes.Float16) & HasRank(4));

    /// <summary>
    /// shift bits
    /// 通常为0.
    /// </summary>
    public static readonly ParameterInfo ShiftBits = new(typeof(DynamicGNNEMatMul), 6, "shift_bits", HasDataType(DataTypes.Int32) & IsScalar());

    /// <summary>
    /// 指示channel是否是动态的.
    /// </summary>
    public static readonly ParameterInfo DynamicChannel = new(typeof(DynamicGNNEMatMul), 7, "dynamic_channel", HasDataType(DataTypes.Int32) & IsScalar());

    public PrimType OutputDType { get; }

    /// <inheritdoc/>
    public override ModuleType ModuleType => Targets.K230Target.ModuleType;

    public override string RegisteredName => "K230DynamicGNNEMatMul";

    public override byte[] SerializeFields()
    {
        var stream = new MemoryStream();
        using (var bw = new BinaryWriter(stream))
        {
            var emiter = new CodeGen.StackVM.StackVMEmitter(bw);
            emiter.Write(OutputDType);
        }

        return stream.ToArray();
    }
}
#endif
