// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Reactive;
using Nncase;
using Nncase.Diagnostics;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.TIR;

namespace Nncase.Passes;

internal sealed class DumpINIPass : ModulePass
{
    protected override Task<IRModule> RunCoreAsync(IRModule module, RunPassContext options)
    {
        if (DumpScope.Current.IsEnabled(DumpFlags.PassIR))
        {
            string dir = DumpScope.Current.Directory + "/fake_model";
            var visitor = new INIVisitor(dir);
            visitor.Visit(module.Entry!);
        }

        return Task.FromResult(module);
    }

    protected override Task OnPassStartAsync(IRModule input, RunPassContext context)
    {
        return Task.CompletedTask;
    }

    protected override Task OnPassEndAsync(IRModule post, RunPassContext context)
    {
        return Task.CompletedTask;
    }
}

internal sealed class INIVisitor : ExprVisitor<Unit, Unit>
{
    private readonly string _dir;

    private int _layerNum;

    public INIVisitor(string dir)
    {
        _dir = dir;
        Directory.CreateDirectory(dir);
    }

    public void Dump(string content)
    {
        using var writer = new StreamWriter(_dir + "/" + _layerNum + ".ini");
        writer.WriteLine(content);
    }

    protected override Unit DefaultVisitLeaf(BaseExpr expr) => default;

    protected override Unit VisitLeafCall(Call expr)
    {
        if (expr is Call call)
        {
            string content;
            switch (call.Target)
            {
                case FakeMatMul:
                    content = "fake_matmul";
                    break;
                default:
                    throw new NotImplementedException("not supported Op: " + call.Target.ToString());
            }

            _layerNum++;

            Dump(content);
        }
        else if (expr is Call { Target: Op op })
        {
            // _primfuncNames.Add(op.GetType().Name);
        }
        else
        {
            throw new ArgumentOutOfRangeException(nameof(expr), $"Target Type {expr.Target.GetType().Name} not support!");
        }

        return default;
    }
}
