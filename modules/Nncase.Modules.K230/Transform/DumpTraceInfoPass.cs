// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Reactive;
using Nncase;
using Nncase.Diagnostics;
using Nncase.IR;
using Nncase.TIR;

namespace Nncase.Passes;

internal sealed class DumpTraceInfoPass : ModulePass
{
    protected override Task<IRModule> RunCoreAsync(IRModule module, RunPassContext options)
    {
        if (DumpScope.Current.IsEnabled(DumpFlags.PassIR))
        {
            var visitor = new TraceInfoVisitor();
            visitor.Visit(module.Entry!);
            using (var writer = new StreamWriter(DumpScope.Current.OpenFile("trace_info.py")))
            {
                visitor.DumpTraceInfo(writer);
            }

            using (var writer = new StreamWriter(DumpScope.Current.OpenFile("draw_trace.py")))
            {
                visitor.DumpDrawTrace(writer);
            }

            using (var writer = new StreamWriter(DumpScope.Current.OpenFile("draw_roofline.py")))
            {
                visitor.DumpDrawRoofLine(writer);
            }

            using (var writer = new StreamWriter(DumpScope.Current.OpenFile("estimate_fps.py")))
            {
                visitor.DumpEstimateFps(writer);
            }
        }

        return Task.FromResult(module);
    }

    protected override Task OnPassStartAsync(IRModule input, RunPassContext context)
    {
        return Task.CompletedTask;
    }

    protected override Task OnPassEndAsync(IRModule post, RunPassContext context)
    {
        return Task.CompletedTask;
    }
}

internal sealed class TraceInfoVisitor : ExprVisitor<Unit, Unit>
{
    private readonly List<(string, bool, Evaluator.Metric)> _callTragetInfos = new();

    public void DumpTraceInfo(StreamWriter writer)
    {
        writer.WriteLine(@"from typing import List, Tuple

class DataItem:
    number: int
    name: int
    exec_time: float
    type: bool
    begin: float
    end: float
    def __init__(self, number, name, type, begin, end, exec_time) -> None:
        self.number = number
        self.name = name
        self.type = type
        self.begin = begin
        self.end = end
        self.exec_time = exec_time

class VersionData:
  name: str
  data: DataItem

  def __init__(self, name, data) -> None:
    self.name = name
    self.data = data

  def print(self):
    print(f'| number | name | type | begin | end | exec |')
    print(f'|-|-|-|-|-|-|')
    time = 0
    for p in self.data:
      print(f'| {p.number} | {p.name} | {p.type} | {p.begin} | {p.end} |  {p.exec_time} |')
      time += p.exec_time
    print(f'| total |  | {time} | | |')");

        writer.WriteLine("v0 = VersionData(\"v0\",[");
        int c = 0;
        foreach (var (name, type, _) in _callTragetInfos)
        {
            writer.WriteLine($"DataItem({c++}, \"{name}\", {type}, 0.0, 0.0, 0.0),");
        }

        writer.WriteLine(@"])
if __name__ == '__main__':
  v0.print()");
    }

    public void DumpEstimateFps(StreamWriter writer)
    {
        writer.WriteLine(@"import numpy as np
import os
import sys
import pandas as pd
from trace_info import DataItem, v0
from draw_roofline import RoofLineModel, get_mac_df_loc, get_mac_df_item, OFF_CHIP_BW, FLOPS

CPU_FREQ = 1.6 * 1e9

cpu_ops = [");
        var keys = typeof(Evaluator.MetricFactorNames).GetProperties().Select(p => p.Name);

        foreach (var (op, type, m) in _callTragetInfos)
        {
            if (type)
            {
                writer.WriteLine($"    ['{op}', dict({string.Join(",", m.Factors.Select(kv => kv.Key + "=" + kv.Value.ToString()))})],");
            }
        }

        writer.WriteLine(@"]

if __name__ == '__main__':
    mac_file_path: str = os.path.abspath(os.path.join(
        os.path.dirname(__file__), '..', '10_FusionToTirPass', 'mac.csv'))
    if len(sys.argv) == 2:
        mac_file_path = sys.argv[1]

    mac_df = pd.read_csv(mac_file_path, delimiter=', ', engine='python')
    roofline = RoofLineModel(mac_file_path, mac_df, v0)

    es_time = 0
    cpu_entrys = []
    for cpu_op in cpu_ops:
        d: dict = cpu_op[1]
        CPU_FLOPS = CPU_FREQ * d.get('Parallel', 1)
        FLOPs = d.get('FLOPs', 0)
        ideal_compute_time = FLOPs / CPU_FLOPS
        ideal_memory_time = d.get('OffChipMemoryTraffic', 0) / OFF_CHIP_BW
        exec_time = max(ideal_compute_time, ideal_memory_time) * 1e3  # s to ms
        actual_FLOPS = FLOPs / (max(exec_time, 1e-6) * 1e-3)
        es_time += exec_time
        cpu_entrys.append((CPU_FLOPS, actual_FLOPS))

    kpu_entrys = []
    for item in roofline.info.data:
        if not item.type:
          exec_time = roofline.get_ideal_exec_time(item) * 1e3  # s to ms
          es_time += exec_time
          FLOPs = float(get_mac_df_item(mac_df, 'FLOPs', item.name))
          actual_FLOPS = FLOPs / (exec_time * 1e-3)
          kpu_entrys.append((FLOPS, actual_FLOPS))
    mac_without_cpu = np.mean([e[1] / e[0] for e in kpu_entrys])
    mac_with_cpu = np.mean([e[1] / e[0] for e in (cpu_entrys + kpu_entrys)])

    print(f'Estimate Exec Time: {es_time:.3f} ms, Fps : {1000 / es_time:.3f}, Mac usage with cpu ops : {mac_with_cpu:.3f}, Mac usage without cpu ops : {mac_without_cpu:.3f}')");
    }

    public void DumpDrawTrace(StreamWriter writer)
    {
        writer.WriteLine(@"import os
import sys
import numpy as np
import pandas as pd
from typing import NamedTuple, Callable, Tuple, List
import re
import json
import shutil
import bokeh.plotting as plt
import bokeh.layouts as pltLayout
from bokeh.models import ColumnDataSource, DataTable, HoverTool, TableColumn, DataCube, GroupingInfo, Range1d
from bokeh.palettes import Category20
from draw_roofline import ON_CHIP_BW, OFF_CHIP_BW, FLOPS, FREQ, get_mac_df_loc, get_mac_df_item, plot_datacude, VersionData, DataItem, v0


class MetaData(NamedTuple):
    ideal_FLOPS: int
    actual_FLOPS: int
    ideal_bandwidth: int
    actual_bandwidth: int


class Entry(NamedTuple):
    name: str
    ph: str  # B or E
    pid: str  # kmodel
    tid: str  # kpu
    ts: int
    dur: int
    meta_data: MetaData

    def to_dict(self) -> dict:
        return {
            'name': self.name,
            'ph': self.ph,
            'pid': self.pid,
            'tid': self.tid,
            'ts': self.ts,
            'dur': self.dur,
            'args': {'mac_ratio': self.meta_data.mac_ratio},
        }


class TraceModel:
    def __init__(self, mac_file_path: str, mac_df: pd.DataFrame, info: VersionData) -> None:
        self.mac_file_dir = os.path.dirname(mac_file_path)
        self.mac_df = mac_df
        self.info = info

    def plot_rects(self, fig: plt.figure, entrys: List[Entry], y_start: int, heights_calc: Callable[[List[Entry]], List[float]], color: str = 'purple') -> object:
        # fusion time
        width = [e.dur for e in entrys]
        height = heights_calc(entrys)
        x = [e.ts + (e.dur / 2) for e in entrys]
        y = [y_start + (h / 2) for h in height]
        name = [e.name for e in entrys]
        color_list = Category20[20]
        source = ColumnDataSource(pd.DataFrame({'x': x, 'y': y, 'height': height,
                                  'width': width, 'name': name, 'color': [color_list[i % len(color_list)] for i in range(len(x))],
                                                'mac_ratio': [f'{e.meta_data.actual_FLOPS / e.meta_data.ideal_FLOPS:.3f}' if e.meta_data != None else '0' for e in entrys],
                                                'ddr_ratio': [f'{e.meta_data.actual_bandwidth / e.meta_data.ideal_bandwidth:.3f}' if e.meta_data != None else '0' for e in entrys],
                                                'actual_mac': [f'{(e.meta_data.actual_FLOPS if e.meta_data else 0) * 1e-12 : 0.3f} FLOPS' for e in entrys],
                                                'ideal_mac': [f'{FLOPS * 1e-12 : 0.3f} FLOPS'] * len(entrys),
                                                'actual_ddr': [f'{(e.meta_data.actual_bandwidth if e.meta_data else 0) * 1e-9:0.3f} Gb/s' for e in entrys],
                                                'ideal_ddr': [f'{OFF_CHIP_BW * 1e-9 : 0.3f} Gb/s'] * len(entrys),
                                                }))
        return fig.rect(x='x', y='y', width='width', height='height',
                        color=color, source=source, hover_color='yellow')

    def calc_bandwidths(self, entrys: List[Entry]) -> Tuple[List[float], List[float]]:
        ideal_bandwidths = np.array([e.meta_data.ideal_bandwidth if e.meta_data else 0 for e in entrys])
        acutal_bandwidths = np.array([e.meta_data.actual_bandwidth if e.meta_data else 0 for e in entrys])
        maxv = np.max(ideal_bandwidths)
        ideal_bandwidths = ideal_bandwidths / maxv
        acutal_bandwidths = acutal_bandwidths / maxv
        return (ideal_bandwidths, acutal_bandwidths)

    def calc_FLOPs(self, entrys: List[Entry]) -> Tuple[List[float], List[float]]:
        ideal_FLOPs = np.array([e.meta_data.ideal_FLOPS if e.meta_data else 0 for e in entrys])
        actual_FLOPs = np.array([e.meta_data.actual_FLOPS if e.meta_data else 0 for e in entrys])
        maxv = np.max(ideal_FLOPs)
        ideal_FLOPs = ideal_FLOPs / maxv
        actual_FLOPs = actual_FLOPs / maxv
        return (ideal_FLOPs, actual_FLOPs)

    def plot_trace(self):
        entrys: List[Entry] = []
        for i in range(len(self.info.data)):
            data: DataItem = self.info.data[i]
            entry: Entry = None
            op_name = self.info.data[i].name
            if data.type == False:
                FLOPs = float(get_mac_df_item(self.mac_df, 'FLOPs', op_name))
                actual_FLOPS = FLOPs / (float(data.exec_time) * 1e-3)
                bandwidth = float(get_mac_df_item(self.mac_df, 'OffChipMemTraffic', op_name))
                bandwidth += float(get_mac_df_item(self.mac_df, 'OffChipMemLWTraffic', op_name))
                actual_bandwidth = bandwidth / (float(data.exec_time) * 1e-3)
                entry = Entry(op_name, 'X', 'kmodel', 'kpu', data.begin, data.exec_time, MetaData(
                    FLOPS, actual_FLOPS, OFF_CHIP_BW, actual_bandwidth))
            else:
                entry = Entry(op_name, 'X', 'kmodel', 'cpu', data.begin, data.exec_time, None)
            entrys.append(entry)

        # plot back ground
        mac_with_cpu = sum([float(get_mac_df_item(self.mac_df, 'FLOPs', e.name))
                           for e in entrys if e.meta_data != None]) / (FLOPS * self.info.data[-1].end * 1e-3)
        mac_without_cpu = np.mean(
            [e.meta_data.actual_FLOPS / e.meta_data.ideal_FLOPS for e in entrys if e.meta_data != None])
        ddr_without_cpu = np.mean(
            [e.meta_data.actual_bandwidth / e.meta_data.ideal_bandwidth for e in entrys if e.meta_data != None])
        title = f'Mac usage with cpu ops : {mac_with_cpu:.3f}, Mac usage without cpu ops : {mac_without_cpu:.3f}, DDr usage without cpu ops: {ddr_without_cpu:.3f} '
        print(title)
        fig = plt.figure(tools='pan,wheel_zoom,box_zoom,reset,save',
                         toolbar_location='above', y_range=['0 Fusion', '1', '2 FLOPS', '3', '4 BandWidth'], width=1200, height=600, title=title)
        fig.xaxis.axis_label = 'Timeline(unit: ms)'
        fig.yaxis.axis_label = ''
        fig.x_range = Range1d(self.info.data[-1].end * -0.05, self.info.data[-1].end * 1.05)
        rects = self.plot_rects(fig, entrys, 0, lambda es: [1] * len(es), 'color')  # time
        hover1 = HoverTool(tooltips=[
            ('Name', '@name'),
            ('Mac Ratio', '@mac_ratio'),
            ('Ddr Ratio', '@ddr_ratio'),
            ('Time', '@width'),
        ], renderers=[rects])
        self.plot_rects(fig, entrys, 2, lambda es: self.calc_FLOPs(es)[0], 'orange')  # ideal FLOPs
        rects = self.plot_rects(fig, entrys, 2, lambda es: self.calc_FLOPs(es)
                                [1], '#123456')  # actual FLOPs
        hover2 = HoverTool(tooltips=[
            ('Name', '@name'),
            ('Actual Mac', '@actual_mac'),
            ('Ideal Mac', '@ideal_mac'),
        ], renderers=[rects])
        self.plot_rects(fig, entrys, 4, lambda es: self.calc_bandwidths(es)
                        [0], 'orange')  # ideal bandwidth
        rects = self.plot_rects(fig, entrys, 4, lambda es: self.calc_bandwidths(es)
                                [1], '#654321')  # actual bandwidth
        hover3 = HoverTool(tooltips=[
            ('Name', '@name'),
            ('Actual DDR', '@actual_ddr'),
            ('Ideal DDR', '@ideal_ddr'),
        ], renderers=[rects])
        fig.tools.append(hover1)
        fig.tools.append(hover2)
        fig.tools.append(hover3)

        return pltLayout.column(fig, plot_datacude(self.mac_file_dir, self.mac_df))


def parse_kpu_time(path: str) -> List[List[float]]:
    with open(path, 'r') as f:
        lines: List[str] = f.readlines()
        kpu_times = []
        for l in lines:
            if not l.startswith('kpu time: '):
                continue
            kpu_times.append([float(s) for s in l[10:].strip().split(',')])
        return kpu_times


if __name__ == '__main__':
    '''
    cmd:
    1. python draw_trace.py #(default)
    2. python draw_trace.py #(local_trace = 1)
        Move 10_FusionToTirPass/mac.csv, 13_DumpTraceInfoPass/*.py and k230_new_arch_kpu_time.txt together.
    '''
    local_trace = 0
    if not local_trace:
        mac_file_path: str = os.path.abspath(os.path.join(
            os.path.dirname(__file__), '..', '10_FusionToTirPass', 'mac.csv'))
        if len(sys.argv) == 2:
            mac_file_path = sys.argv[1]
    else:
        work_dir_path = os.path.dirname(__file__)
        mac_file_path: str = os.path.abspath(os.path.join(work_dir_path, 'mac.csv'))
        kpu_times_file = os.path.abspath(os.path.join(work_dir_path, 'k230_new_arch_kpu_time.txt'))
        if not os.path.exists(mac_file_path):
            raise Exception('\033[1;31mNeed mac.csv.\033[0m\n' +
                            'It was created in compiling, location is \033[4;33m{}\033[0m'.format('infer/k230/ptq/8_TargetDependentAfterQuantPass/13_DumpTraceInfoPass/'))
        if os.path.exists(kpu_times_file):
            kpu_times = parse_kpu_time(kpu_times_file)
            for i in v0.data:
                i.exec_time = kpu_times[i.number][-1]
        else:
            raise Exception('\033[1;31mNeed kpu times file.\033[0m\n' +
                            'You should get this dump file on board: \033[4;33m{}\033[0m \nMove it in dir: \033[4;32m{}\033[0m'.format(
                                kpu_times_file.split('/')[-1], os.path.abspath(work_dir_path)))

    mac_df = pd.read_csv(mac_file_path, delimiter=', ', engine='python')

    tracemodel = TraceModel(mac_file_path, mac_df, v0)
    fig = tracemodel.plot_trace()
    plt.show(fig)");
    }

    public void DumpDrawRoofLine(StreamWriter writer)
    {
        writer.WriteLine(@"import os
import sys
import numpy as np
from typing import List
import pandas as pd
from typing import NamedTuple, Callable, Tuple
import re
import json
import shutil
import bokeh.plotting as plt
import bokeh.layouts as pltLayout
from bokeh.models import ColumnDataSource, DataTable, HoverTool, TableColumn, DataCube, GroupingInfo
from bokeh.palettes import Category20
from trace_info import VersionData, DataItem, v0

ON_CHIP_BW = 25.6 * 1e9  # BW:32B/cycle, Freq:800M
OFF_CHIP_BW = 3.2 * 1e9
FLOPS = 0.6 * 1e12
FREQ = 800 * 1e6


def plot_datacude(dir_path: str, mac_df: pd.DataFrame) -> DataCube:
    # fusion name, op name, dims
    fusion_names = []
    op_names = []
    dims = []
    for f_name in mac_df['Name']:
        with open(os.path.join(dir_path, f_name + '.csv'), 'r') as f:
            lines = f.readlines()
            for i in range(0, len(lines), 3):
                op_name = lines[i].strip()
                dim_name = lines[i + 1].strip()
                dim = lines[i + 2].strip()
                fusion_names.append(f_name)
                op_names.append(op_name)
                dims.append((' ' if dim_name.startswith(',') else (dim_name + ' ')) + dim)

    source = ColumnDataSource(data=dict(fusion_names=fusion_names, op_names=op_names, dims=dims, ))

    target = ColumnDataSource(data=dict(row_indices=[], labels=[]))

    columns = [
        TableColumn(field='fusion_names', title='Fusion Name', sortable=False),
        TableColumn(field='op_names', title='Op Name', sortable=False),
        TableColumn(field='dims', title='Dimension', sortable=False),
    ]

    grouping = [
        GroupingInfo(getter='fusion_names'),
    ]

    cube = DataCube(source=source, columns=columns, grouping=grouping, target=target, width=1200)
    return cube


def get_mac_df_loc(mac_df: pd.DataFrame, name: str) -> pd.Index:
    return mac_df['Name'].loc[lambda n: n == name].index


def get_mac_df_item(mac_df: pd.DataFrame, colum: str, name: str) -> float:
    return mac_df[colum].loc[get_mac_df_loc(mac_df, name)].values[0]


class RoofLineModel:
    mac_file_dir: str

    def __init__(self, mac_file_path: str, mac_df: pd.DataFrame, info: VersionData) -> None:
        self.mac_file_dir = os.path.dirname(mac_file_path)
        self.mac_df = mac_df
        self.info = info

    def get_ideal_compute_time(self, item: DataItem) -> float:
        return get_mac_df_item(self.mac_df, 'FLOPs', item.name) / FLOPS

    def get_ideal_memory_time(self, item: DataItem) -> Tuple[float, float]:
        OnChipMemTraffic = get_mac_df_item(self.mac_df, 'OnChipMemTraffic', item.name)
        OffChipMemTraffic = get_mac_df_item(self.mac_df, 'OffChipMemTraffic', item.name)
        OffChipMemLWTraffic = get_mac_df_item(self.mac_df, 'OffChipMemLWTraffic', item.name)
        return (OnChipMemTraffic / ON_CHIP_BW), (OffChipMemTraffic / OFF_CHIP_BW), (OffChipMemLWTraffic / OFF_CHIP_BW)

    def get_ideal_exec_time(self, item: DataItem) -> float:
        ideal_compute_time = self.get_ideal_compute_time(item)
        ideal_complete_onchip_time, ideal_complete_offchip_time, ideal_complete_offchip_lw_time = self.get_ideal_memory_time(
            item)
        if (get_mac_df_item(self.mac_df, 'OffChipLoadStoreCnt', item.name) == 2): # one load+one store
            ideal_exec_time = ideal_complete_offchip_time + max([ideal_complete_offchip_lw_time, ideal_complete_onchip_time, ideal_compute_time])
        else:
            ideal_exec_time = max([ideal_compute_time,
                                ideal_complete_offchip_time + ideal_complete_offchip_lw_time, ideal_complete_onchip_time])
        return ideal_exec_time

    def get_empty_roofline_data(self):
        return {
            'Op Type': [],
            'Dimension': [],
            'Op Intensity': [],
            'Throughput': [],
            'Execute Time': []
        }

    def get_roofline_data(self) -> dict:
        roofline_data = self.get_empty_roofline_data()
        for item in self.info.data:
            if item.type:
                continue
            ideal_exec_time = self.get_ideal_exec_time(item)
            FLOPs = get_mac_df_item(self.mac_df, 'FLOPs', item.name)
            ideal_thrpt = FLOPs / ideal_exec_time
            op_intensity = FLOPs / get_mac_df_item(self.mac_df, 'OnChipMemTraffic', item.name)
            roofline_data['Op Type'].append(item.name + '_ideal')
            roofline_data['Dimension'].append('?')
            roofline_data['Op Intensity'].append(op_intensity)
            roofline_data['Throughput'].append(ideal_thrpt * 1e-12)  # to T
            roofline_data['Execute Time'].append(ideal_exec_time * 1e3)  # s to ms

            actual_time = item.exec_time
            if (actual_time != 0):
                thrpt = FLOPs / (actual_time * 1e-3)
                roofline_data['Op Type'].append(item.name)
                roofline_data['Dimension'].append('?')
                roofline_data['Op Intensity'].append(op_intensity)
                roofline_data['Throughput'].append(thrpt * 1e-12)  # to T
                roofline_data['Execute Time'].append(actual_time)
        return roofline_data

    def plot_roofline_background(self, title, max_x) -> plt.figure:
        op_intensity = FLOPS / OFF_CHIP_BW
        flops = FLOPS / 1e12
        max_x = max(max_x, op_intensity * 2.5)
        turning_points = [[0, 0], [op_intensity, flops], [max_x, flops]]
        turning_points = np.array(turning_points)
        fig = plt.figure(tools='pan,wheel_zoom,box_zoom,reset,save',
                         toolbar_location='above', width=1200, height=600, title=title)

        fig.line(turning_points[:, 0], turning_points[:, 1],
                 legend_label=f'offchip bandwidth {OFF_CHIP_BW / 1e9} GB/s', line_color='grey')

        op_intensity = FLOPS / ON_CHIP_BW
        turning_points = [[0, 0], [op_intensity, flops], [max_x, flops]]
        turning_points = np.array(turning_points)
        fig.line(turning_points[:, 0], turning_points[:, 1],
                 legend_label=f'onchip bandwidth {ON_CHIP_BW / 1e9} GB/s', line_dash='4 4', line_color='grey')

        fig.xaxis.axis_label = 'Op Intensity (FLOPs/Byte)'
        fig.yaxis.axis_label = f'TFLOPS'
        return fig

    def plot_roofline(self, title: str) -> plt.figure:
        df = pd.DataFrame(self.get_roofline_data())
        max_x = max(df['Op Intensity'])
        fig = self.plot_roofline_background(title, max_x)
        df = df.assign(colors=['blue' if x.endswith('ideal') else 'orange' for x in df['Op Type']])
        source = ColumnDataSource(df)

        datacube = plot_datacude(self.mac_file_dir, self.mac_df)

        points = fig.scatter(x='Op Intensity', y='Throughput',
                             color='colors', size=10, source=source)
        hover = HoverTool(tooltips=[
            ('Op Type', '@{Op Type}'),
            ('Dimension', '@Dimension'),
        ], renderers=[points])
        fig.tools.append(hover)
        return pltLayout.column(fig, datacube)


if __name__ == '__main__':
    mac_file_path: str = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '10_FusionToTirPass', 'mac.csv'))
    if len(sys.argv) == 2:
        mac_file_path = sys.argv[1]

    mac_df = pd.read_csv(mac_file_path, delimiter=', ', engine='python')

    roofline = RoofLineModel(mac_file_path, mac_df, v0)
    fig = roofline.plot_roofline('roofline')
    plt.show(fig)");
    }

    protected override Unit DefaultVisitLeaf(BaseExpr expr) => default;

    protected override Unit VisitLeafCall(Call expr)
    {
        if (expr is Call { Target: PrimFunctionWrapper { Target: PrimFunction primFunc } })
        {
            _callTragetInfos.Add((primFunc.Name, false, Nncase.Evaluator.Metric.Zero));
        }
        else if (expr is Call { Target: Op op } call)
        {
            var metric = CompilerServices.EvaluateOpMetric(op, new MetricEvaluateContext(call));
            _callTragetInfos.Add((op.GetType().Name, true, metric));
        }
        else
        {
            throw new ArgumentOutOfRangeException(nameof(expr), $"Target Type {expr.Target.GetType().Name} not support!");
        }

        return default;
    }
}

internal sealed class MetricEvaluateContext : Evaluator.IMetricEvaluateContext
{
    public MetricEvaluateContext(Call call)
    {
        CurrentCall = call;
    }

    public Call CurrentCall { get; }

    public T GetArgument<T>(Op op, ParameterInfo parameter)
        where T : Expr
    {
        return (T)CurrentCall[parameter];
    }

    public T GetArgumentType<T>(Op op, ParameterInfo parameter)
        where T : IRType
    {
        return (T)CurrentCall[parameter].CheckedType;
    }

    public T GetReturnType<T>()
        where T : IRType
    {
        return (T)CurrentCall.CheckedType;
    }
}
