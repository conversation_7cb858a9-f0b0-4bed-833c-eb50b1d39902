﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Runtime.InteropServices;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using static Nncase.IR.F.Tensors;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.K230.GNNETypePatternUtility;
using static Nncase.Passes.Utility;
using Fx = System.Func<Nncase.IR.BaseExpr, Nncase.IR.BaseExpr>;

namespace Nncase.Passes.Rules;

public static class GetReplaceHelper
{
    /// <summary>
    /// insert ReshapeToGNNEShape before and after call
    /// Reshape(Call(Reshape(input, ToGNNEShape(input.Shape)), input.Shape)
    /// return a call like function that take a input and return a call.
    /// </summary>
    /// <param name="inputCtor"> a function take a input and return a call .</param>
    /// <param name="originOutShape">originOutShape.</param>
    /// <returns> return a call like function that only take a input args .</returns>
    public static Fx WithTmpGNNEShape(Fx inputCtor, int[] originOutShape) => WithTmp4DShape(inputCtor, originOutShape.Select(x => (long)x).ToArray());

    public static Fx WithTmpGNNEShape(Fx inputCtor) =>
        input => WithTmpGNNEShape(inputCtor, input.CheckedShape.ToValueArray().Select(x => (int)x).ToArray())(input);

    public static Expr ToGNNEShape(Expr input)
    {
        return Reshape(input, GetGNNEShape(input.CheckedShape.ToValueArray()));
    }

    public static Expr ToF16(Expr input)
    {
        var cast = Cast(input, DataTypes.Float16);
        if (input is TensorConst)
        {
            return cast.Evaluate().AsTensor();
        }

        return cast;
    }

    public static Tensor ToTensor(Expr expr)
    {
        if (expr is TensorConst constValue)
        {
            return constValue.Value;
        }

        throw new InvalidOperationException("Expr is not a TensorCosnt");
    }

    public static T[] ToArray<T>(Expr expr)
        where T : unmanaged, IEquatable<T>
    {
        return ToTensor(expr).ToArray<T>();
    }

    public static T ToScalar<T>(Expr expr)
        where T : unmanaged, IEquatable<T>
    {
        return ToTensor(expr).ToScalar<T>();
    }

    public static Fx WithFullLower(Fx inputCtor, int[] outShape)
    {
        return WithTmpGNNEShape(WithLoadStore(inputCtor), outShape);
    }

    public static Fx WithLoadStore(Fx inputCtor)
    {
        Fx WithLoadStoreImpl(Fx inCtor) =>
            input =>
                GNNEStore(
                    input.CheckedDataType,
                    (Expr)inCtor(GNNELoad(input.CheckedDataType == DataTypes.Float32 ? DataTypes.Float16 : (PrimType)input.CheckedDataType, (Expr)input)));

        return Apply(WithLoadStoreImpl, inputCtor);
    }

    public static Expr LoadActIF(Expr input) => GNNELoad(DataTypes.Float16, input);

    public static Expr LoadAct0(ActParam2 actParam) => GNNELoadW(DataTypes.Float16, actParam.ToAct0Data());

    public static Expr LoadAct1(ActParamBase actParam) => GNNELoadW(DataTypes.Float16, actParam.ToAct1Data());

    public static Expr SuppressPattern(RunPassContext options, Expr expr, IPattern pattern)
    {
        options.MatchOptions.SuppressPattern(expr, pattern);
        return expr;
    }

    public static ActParam2? FoldAct0WithAct1(ActParam2 act0Param, bool is16Segments, TensorConst outChannels, ActParam2 act1Param, TensorConst deqParams = null!)
    {
        if (is16Segments == true)
        {
            return null;
        }

        int oc1 = act0Param.Channels;
        int oc2 = outChannels.Value.ToScalar<int>();
        if (oc1 != oc2 && oc2 != 1)
        {
            return null;
        }

        if (deqParams != null)
        {
            for (int i = 0; i < oc1; i++)
            {
                float scale = MemoryMarshal.Cast<byte, float>(deqParams.Value.BytesBuffer)[1];
                int zeroPoint = MemoryMarshal.Cast<byte, int>(deqParams.Value.BytesBuffer)[0];
                act0Param.Ks[0, i] *= scale;
                act0Param.Ks[1, i] *= scale;
                act0Param.Bs[0, i] = (act0Param.Bs[0, i] - zeroPoint) * scale;
                act0Param.Bs[1, i] = (act0Param.Bs[1, i] - zeroPoint) * scale;
                act0Param.FusedClamp[i].Max = (act0Param.FusedClamp[i].Max - zeroPoint) * scale;
                act0Param.FusedClamp[i].Min = (act0Param.FusedClamp[i].Min - zeroPoint) * scale;
            }
        }

        var actFoldParam = new ActFoldParam(oc1);
        for (int i = 0; i < oc1; i++)
        {
            float kl = oc2 == 1 ? act1Param.Ks[0, 0] : act1Param.Ks[0, i];
            float kr = oc2 == 1 ? act1Param.Ks[1, 0] : act1Param.Ks[1, i];
            float bl = oc2 == 1 ? act1Param.Bs[0, 0] : act1Param.Bs[0, i];
            float br = oc2 == 1 ? act1Param.Bs[1, 0] : act1Param.Bs[1, i];
            float x0 = oc2 == 1 ? act1Param.Xs[0, 0] : act1Param.Xs[0, i];
            float fusedClampMin = oc2 == 1 ? act1Param.FusedClamp[0].Min : act1Param.FusedClamp[i].Min;
            float fusedClampMax = oc2 == 1 ? act1Param.FusedClamp[0].Max : act1Param.FusedClamp[i].Max;

            // get fold clamp first.
            // in fact, no need to discuss so many conditions as below since some conditions are unfoldable.
            // case1
            if (x0 >= act0Param.FusedClamp[i].Max)
            {
                if (kl > 0)
                {
                    float a1Max = double.IsPositiveInfinity(act0Param.FusedClamp[i].Max) ? act0Param.FusedClamp[i].Max : (act0Param.FusedClamp[i].Max * kl) + bl;
                    float a1Min = double.IsNegativeInfinity(act0Param.FusedClamp[i].Min) ? act0Param.FusedClamp[i].Min : (act0Param.FusedClamp[i].Min * kl) + bl;
                    actFoldParam.FusedClamp[i].Max = a1Max < fusedClampMax ? (Half)a1Max : (Half)fusedClampMax;
                    actFoldParam.FusedClamp[i].Min = a1Min > fusedClampMin ? (Half)a1Min : (Half)fusedClampMin;
                    if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                    {
                        return null;
                    }
                }
                else if (kl < 0)
                {
                    float a1Min = double.IsPositiveInfinity(act0Param.FusedClamp[i].Max) ? -act0Param.FusedClamp[i].Max : (act0Param.FusedClamp[i].Max * kl) + bl;
                    float a1Max = double.IsNegativeInfinity(act0Param.FusedClamp[i].Min) ? -act0Param.FusedClamp[i].Min : (act0Param.FusedClamp[i].Min * kl) + bl;
                    actFoldParam.FusedClamp[i].Max = a1Max < fusedClampMax ? (Half)a1Max : (Half)fusedClampMax;
                    actFoldParam.FusedClamp[i].Min = a1Min > fusedClampMin ? (Half)a1Min : (Half)fusedClampMin;
                    if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                    {
                        return null;
                    }
                }
                else
                {
                    actFoldParam.FusedClamp[i].Max = (Half)fusedClampMax;
                    actFoldParam.FusedClamp[i].Min = (Half)fusedClampMin;
                }
            }

            // case2
            else if (x0 <= act0Param.FusedClamp[i].Min)
            {
                switch (kr)
                {
                    case > 0:
                        {
                            float a1Max = double.IsPositiveInfinity(act0Param.FusedClamp[i].Max) ? act0Param.FusedClamp[i].Max : (act0Param.FusedClamp[i].Max * kr) + br;
                            float a1Min = double.IsNegativeInfinity(act0Param.FusedClamp[i].Min) ? act0Param.FusedClamp[i].Min : (act0Param.FusedClamp[i].Min * kr) + br;
                            actFoldParam.FusedClamp[i].Max = a1Max < fusedClampMax ? (Half)a1Max : (Half)fusedClampMax;
                            actFoldParam.FusedClamp[i].Min = a1Min > fusedClampMin ? (Half)a1Min : (Half)fusedClampMin;
                            if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                            {
                                return null;
                            }

                            break;
                        }

                    case < 0:
                        {
                            float a1Min = double.IsPositiveInfinity(act0Param.FusedClamp[i].Max) ? -act0Param.FusedClamp[i].Max : (act0Param.FusedClamp[i].Max * kr) + br;
                            float a1Max = double.IsNegativeInfinity(act0Param.FusedClamp[i].Min) ? -act0Param.FusedClamp[i].Min : (act0Param.FusedClamp[i].Min * kr) + br;
                            actFoldParam.FusedClamp[i].Max = a1Max < fusedClampMax ? (Half)a1Max : (Half)fusedClampMax;
                            actFoldParam.FusedClamp[i].Min = a1Min > fusedClampMin ? (Half)a1Min : (Half)fusedClampMin;
                            if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                            {
                                return null;
                            }

                            break;
                        }

                    default:
                        actFoldParam.FusedClamp[i].Max = (Half)fusedClampMax;
                        actFoldParam.FusedClamp[i].Min = (Half)fusedClampMin;
                        break;
                }
            }

            // case3
            else
            {
                switch (kl)
                {
                    case > 0 when kr > 0:
                        {
                            float a1Max = double.IsPositiveInfinity(act0Param.FusedClamp[i].Max) ? act0Param.FusedClamp[i].Max : (act0Param.FusedClamp[i].Max * kr) + br;
                            float a1Min = double.IsNegativeInfinity(act0Param.FusedClamp[i].Min) ? act0Param.FusedClamp[i].Min : (act0Param.FusedClamp[i].Min * kl) + bl;
                            actFoldParam.FusedClamp[i].Max = a1Max < fusedClampMax ? (Half)a1Max : (Half)fusedClampMax;
                            actFoldParam.FusedClamp[i].Min = a1Min > fusedClampMin ? (Half)a1Min : (Half)fusedClampMin;
                            if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                            {
                                return null;
                            }

                            break;
                        }

                    case < 0 when kr < 0:
                        {
                            float a1Min = double.IsPositiveInfinity(act0Param.FusedClamp[i].Max) ? -act0Param.FusedClamp[i].Max : (act0Param.FusedClamp[i].Max * kr) + br;
                            float a1Max = double.IsNegativeInfinity(act0Param.FusedClamp[i].Min) ? -act0Param.FusedClamp[i].Min : (act0Param.FusedClamp[i].Min * kl) + bl;
                            actFoldParam.FusedClamp[i].Max = a1Max < fusedClampMax ? (Half)a1Max : (Half)fusedClampMax;
                            actFoldParam.FusedClamp[i].Min = a1Min > fusedClampMin ? (Half)a1Min : (Half)fusedClampMin;
                            if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                            {
                                return null;
                            }

                            break;
                        }

                    case > 0 when kr < 0:
                        {
                            float a1Min1 = double.IsPositiveInfinity(act0Param.FusedClamp[i].Max) ? -act0Param.FusedClamp[i].Max : (act0Param.FusedClamp[i].Max * kr) + br;
                            float a1Min2 = double.IsNegativeInfinity(act0Param.FusedClamp[i].Min) ? act0Param.FusedClamp[i].Min : (act0Param.FusedClamp[i].Min * kl) + bl;
                            float a1Min = a1Min1 > a1Min2 ? a1Min1 : a1Min2;
                            actFoldParam.FusedClamp[i].Max = (Half)fusedClampMax;
                            actFoldParam.FusedClamp[i].Min = a1Min > fusedClampMin ? (Half)a1Min : (Half)fusedClampMin;
                            if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                            {
                                return null;
                            }

                            break;
                        }

                    case < 0 when kr > 0:
                        {
                            float a1Max1 = double.IsPositiveInfinity(act0Param.FusedClamp[i].Max) ? act0Param.FusedClamp[i].Max : (act0Param.FusedClamp[i].Max * kr) + br;
                            float a1Max2 = double.IsNegativeInfinity(act0Param.FusedClamp[i].Min) ? -act0Param.FusedClamp[i].Min : (act0Param.FusedClamp[i].Min * kl) + bl;
                            float a1Max = a1Max1 > a1Max2 ? a1Max2 : a1Max1;
                            actFoldParam.FusedClamp[i].Max = a1Max < fusedClampMax ? (Half)a1Max : (Half)fusedClampMax;
                            actFoldParam.FusedClamp[i].Min = (Half)fusedClampMin;
                            if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                            {
                                return null;
                            }

                            break;
                        }

                    case > 0 when kr == 0:
                        {
                            float a1Min = double.IsNegativeInfinity(act0Param.FusedClamp[i].Min) ? act0Param.FusedClamp[i].Min : (act0Param.FusedClamp[i].Min * kl) + bl;
                            actFoldParam.FusedClamp[i].Max = (Half)fusedClampMax;
                            actFoldParam.FusedClamp[i].Min = a1Min > fusedClampMin ? (Half)a1Min : (Half)fusedClampMin;
                            if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                            {
                                return null;
                            }

                            break;
                        }

                    case 0 when kr > 0:
                        {
                            float a1Max = double.IsPositiveInfinity(act0Param.FusedClamp[i].Max) ? act0Param.FusedClamp[i].Max : (act0Param.FusedClamp[i].Max * kr) + br;
                            actFoldParam.FusedClamp[i].Max = a1Max < fusedClampMax ? (Half)a1Max : (Half)fusedClampMax;
                            actFoldParam.FusedClamp[i].Min = (Half)fusedClampMin;
                            if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                            {
                                return null;
                            }

                            break;
                        }

                    case < 0 when kr == 0:
                        {
                            float a1Max = double.IsNegativeInfinity(act0Param.FusedClamp[i].Min) ? -act0Param.FusedClamp[i].Min : (act0Param.FusedClamp[i].Min * kl) + bl;
                            actFoldParam.FusedClamp[i].Max = a1Max < fusedClampMax ? (Half)a1Max : (Half)fusedClampMax;
                            actFoldParam.FusedClamp[i].Min = (Half)fusedClampMin;
                            if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                            {
                                return null;
                            }

                            break;
                        }

                    case 0 when kr < 0:
                        {
                            float a1Min = double.IsPositiveInfinity(act0Param.FusedClamp[i].Max) ? -act0Param.FusedClamp[i].Max : (act0Param.FusedClamp[i].Max * kr) + br;
                            actFoldParam.FusedClamp[i].Max = (Half)fusedClampMax;
                            actFoldParam.FusedClamp[i].Min = a1Min > fusedClampMin ? (Half)a1Min : (Half)fusedClampMin;
                            if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                            {
                                return null;
                            }

                            break;
                        }

                    case 0 when kr == 0:
                        actFoldParam.FusedClamp[i].Max = (Half)fusedClampMax;
                        actFoldParam.FusedClamp[i].Min = (Half)fusedClampMin;
                        break;
                }
            }
        }

        for (int i = 0; i < oc1; i++)
        {
            float kl = oc2 == 1 ? act1Param.Ks[0, 0] : act1Param.Ks[0, i];
            float kr = oc2 == 1 ? act1Param.Ks[1, 0] : act1Param.Ks[1, i];
            float bl = oc2 == 1 ? act1Param.Bs[0, 0] : act1Param.Bs[0, i];
            float br = oc2 == 1 ? act1Param.Bs[1, 0] : act1Param.Bs[1, i];
            float x0 = oc2 == 1 ? act1Param.Xs[0, 0] : act1Param.Xs[0, i];
            float fusedClampMin = oc2 == 1 ? act1Param.FusedClamp[0].Min : act1Param.FusedClamp[i].Min;
            float fusedClampMax = oc2 == 1 ? act1Param.FusedClamp[0].Max : act1Param.FusedClamp[i].Max;
            switch (act0Param.Ks[0, i])
            {
                case > 0 when act0Param.Ks[1, i] > 0:
                    {
                        if (act0Param.Xs[0, i] != 0 && Math.Abs(x0 - ((act0Param.Xs[0, i] * act0Param.Ks[0, i]) + act0Param.Bs[0, i])) > float.Epsilon)
                        {
                            return null;
                        }

                        if (act0Param.Bs[0, i] > x0 || act0Param.Bs[1, i] < x0)
                        {
                            if ((kl != kr || bl != br) && (act0Param.Ks[0, i] != act0Param.Ks[1, i] || act0Param.Bs[0, i] != act0Param.Bs[1, i]))
                            {
                                return null;
                            }
                        }

                        // if (act0Param.fusedClamp[i].Max < x0 || act0Param.fusedClamp[i].Min > x0)
                        //     return null;
                        if (act0Param.Ks[0, i] == act0Param.Ks[1, i] && act0Param.Bs[0, i] == act0Param.Bs[1, i] && (kl != kr || bl != br))
                        {
                            actFoldParam.Ks[0, i] = act0Param.Ks[0, i] * kl;
                            actFoldParam.Ks[1, i] = act0Param.Ks[1, i] * kr;
                            actFoldParam.Bs[0, i] = (act0Param.Bs[0, i] * kl) + bl;
                            actFoldParam.Bs[1, i] = (act0Param.Bs[1, i] * kr) + br;
                            actFoldParam.Xs[0, i] = (x0 - act0Param.Bs[0, i]) / act0Param.Ks[0, i];
                        }
                        else
                        {
                            actFoldParam.Ks[0, i] = act0Param.Ks[0, i] * kl;
                            actFoldParam.Ks[1, i] = act0Param.Ks[1, i] * kr;
                            actFoldParam.Bs[0, i] = (act0Param.Bs[0, i] * kl) + bl;
                            actFoldParam.Bs[1, i] = (act0Param.Bs[1, i] * kr) + br;
                            actFoldParam.Xs[0, i] = act0Param.Xs[0, i];
                        }

                        break;
                    }

                case < 0 when act0Param.Ks[1, i] < 0:
                    {
                        if (act0Param.Xs[0, i] != 0 && Math.Abs(x0 - ((act0Param.Xs[0, i] * act0Param.Ks[0, i]) + act0Param.Bs[0, i])) > float.Epsilon)
                        {
                            return null;
                        }

                        if (act0Param.Bs[0, i] < x0 || act0Param.Bs[1, i] > x0)
                        {
                            if ((kl != kr || bl != br) && (act0Param.Ks[0, i] != act0Param.Ks[1, i] || act0Param.Bs[0, i] != act0Param.Bs[1, i]))
                            {
                                return null;
                            }
                        }

                        // if (act0Param.fusedClamp[i].Max < x0 || act0Param.fusedClamp[i].Min > x0)
                        //     return null;
                        if (act0Param.Ks[0, i] == act0Param.Ks[1, i] && act0Param.Bs[0, i] == act0Param.Bs[1, i] && (kl != kr || bl != br))
                        {
                            actFoldParam.Ks[0, i] = act0Param.Ks[0, i] * kr;
                            actFoldParam.Ks[1, i] = act0Param.Ks[1, i] * kl;
                            actFoldParam.Bs[0, i] = (act0Param.Bs[0, i] * kr) + br;
                            actFoldParam.Bs[1, i] = (act0Param.Bs[1, i] * kl) + bl;
                            actFoldParam.Xs[0, i] = (x0 - act0Param.Bs[0, i]) / act0Param.Ks[0, i];
                        }
                        else
                        {
                            actFoldParam.Ks[0, i] = act0Param.Ks[0, i] * kl;
                            actFoldParam.Ks[1, i] = act0Param.Ks[1, i] * kr;
                            actFoldParam.Bs[0, i] = (act0Param.Bs[0, i] * kl) + bl;
                            actFoldParam.Bs[1, i] = (act0Param.Bs[1, i] * kr) + br;
                            actFoldParam.Xs[0, i] = act0Param.Xs[0, i];
                        }

                        break;
                    }

                case 0 when act0Param.Ks[1, i] > 0:
                    {
                        if (act0Param.Bs[0, i] >= x0 || act0Param.Bs[1, i] + (act0Param.Xs[0, i] * act0Param.Ks[1, i]) <= x0)
                        {
                            if (kl != kr || bl != br)
                            {
                                return null;
                            }
                        }

                        // if (act0Param.fusedClamp[i].Max < x0 || act0Param.fusedClamp[i].Min > x0)
                        //     return null;
                        actFoldParam.Ks[0, i] = 0;
                        actFoldParam.Ks[1, i] = act0Param.Ks[1, i] * kr;
                        actFoldParam.Bs[0, i] = (act0Param.Bs[0, i] * kl) + bl;
                        actFoldParam.Bs[1, i] = (act0Param.Bs[1, i] * kr) + br;
                        actFoldParam.Xs[0, i] = act0Param.Xs[0, i];
                        break;
                    }

                case > 0 when act0Param.Ks[1, i] == 0:
                    {
                        if (act0Param.Bs[0, i] + (act0Param.Xs[0, i] * act0Param.Ks[0, i]) >= x0 || act0Param.Bs[1, i] <= x0)
                        {
                            if (kl != kr || bl != br)
                            {
                                return null;
                            }
                        }

                        // if (act0Param.fusedClamp[i].Max < x0 || act0Param.fusedClamp[i].Min > x0)
                        //     return null;
                        actFoldParam.Ks[0, i] = act0Param.Ks[0, i] * kl;
                        actFoldParam.Ks[1, i] = 0;
                        actFoldParam.Bs[0, i] = (act0Param.Bs[0, i] * kl) + bl;
                        actFoldParam.Bs[1, i] = (act0Param.Bs[1, i] * kr) + br;
                        actFoldParam.Xs[0, i] = act0Param.Xs[0, i];
                        break;
                    }

                case 0 when act0Param.Ks[1, i] < 0:
                    {
                        if (act0Param.Bs[0, i] <= x0 || act0Param.Bs[1, i] - (act0Param.Xs[0, i] * act0Param.Ks[1, i]) >= x0)
                        {
                            if (kl != kr || bl != br)
                            {
                                return null;
                            }
                        }

                        // if (act0Param.fusedClamp[i].Max < x0 || act0Param.fusedClamp[i].Min > x0)
                        //     return null;
                        actFoldParam.Ks[0, i] = 0;
                        actFoldParam.Ks[1, i] = act0Param.Ks[1, i] * kl;
                        actFoldParam.Bs[0, i] = (act0Param.Bs[0, i] * kr) + br;
                        actFoldParam.Bs[1, i] = (act0Param.Bs[1, i] * kl) + bl;
                        actFoldParam.Xs[0, i] = act0Param.Xs[0, i];
                        break;
                    }

                case < 0 when act0Param.Ks[1, i] == 0:
                    {
                        if (act0Param.Bs[0, i] - (act0Param.Xs[0, i] * act0Param.Ks[0, i]) <= x0 || act0Param.Bs[1, i] >= x0)
                        {
                            if (kl != kr || bl != br)
                            {
                                return null;
                            }
                        }

                        // if (act0Param.fusedClamp[i].Max < x0 || act0Param.fusedClamp[i].Min > x0)
                        //     return null;
                        actFoldParam.Ks[0, i] = act0Param.Ks[0, i] * kr;
                        actFoldParam.Ks[1, i] = 0;
                        actFoldParam.Bs[0, i] = (act0Param.Bs[0, i] * kr) + br;
                        actFoldParam.Bs[1, i] = (act0Param.Bs[1, i] * kl) + bl;
                        actFoldParam.Xs[0, i] = act0Param.Xs[0, i];
                        break;
                    }

                case 0 when act0Param.Ks[1, i] == 0:
                    {
                        if (act0Param.Bs[0, i] >= x0 || act0Param.Bs[1, i] <= x0)
                        {
                            if (kl != kr || bl != br)
                            {
                                return null;
                            }
                        }

                        // if (act0Param.fusedClamp[i].Max <= x0 || act0Param.fusedClamp[i].Min >= x0)
                        //     return null;
                        actFoldParam.Ks[0, i] = 0;
                        actFoldParam.Ks[1, i] = 0;
                        actFoldParam.Bs[0, i] = (act0Param.Bs[0, i] * kl) + bl;
                        actFoldParam.Bs[1, i] = (act0Param.Bs[1, i] * kr) + br;
                        actFoldParam.Xs[0, i] = act0Param.Xs[0, i];
                        break;
                    }

                default:
                    {
                        if (kl != kr || bl != br)
                        {
                            return null;
                        }

                        actFoldParam.Ks[0, i] = act0Param.Ks[0, i] * kl;
                        actFoldParam.Ks[1, i] = act0Param.Ks[1, i] * kr;
                        actFoldParam.Bs[0, i] = (act0Param.Bs[0, i] * kl) + bl;
                        actFoldParam.Bs[1, i] = (act0Param.Bs[1, i] * kr) + br;
                        actFoldParam.Xs[0, i] = act0Param.Xs[0, i];
                        break;
                    }
            }
        }

        var actParam = new ActParam2(oc1);
        for (int i = 0; i < oc1; i++)
        {
            actParam.Ks[0, i] = actFoldParam.Ks[0, i];
            actParam.Ks[1, i] = actFoldParam.Ks[1, i];
            actParam.Bs[0, i] = actFoldParam.Bs[0, i];
            actParam.Bs[1, i] = actFoldParam.Bs[1, i];
            actParam.Xs[0, i] = actFoldParam.Xs[0, i];
            actParam.FusedClamp[i].Min = (float)actFoldParam.FusedClamp[i].Min;
            actParam.FusedClamp[i].Max = (float)actFoldParam.FusedClamp[i].Max;
        }

        actParam.Qp = act0Param.Qp;
        return actParam;
    }
}
