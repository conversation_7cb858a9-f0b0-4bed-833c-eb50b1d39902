﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Math;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class ToFakeMatmul : IRewriteRule
{
    /// <inheritdoc/>
    public IPattern Pattern { get; }
        = IsRangeOfMarker(
            "outputMarker",
            IsMatMul(
                "matmul",
                "call",
                _ => true,
                IsRangeOfMarker("inputAMarker", IsWildcard("inputA") with { TypePattern = HasFixedShape() }, IsTensorConst("inputARange")),
                IsRangeOfMarker("inputBMarker", IsWildcard("inputB") with { TypePattern = HasFixedShape() }, IsTensorConst("inputBRange"))),
            IsTensorConst("outputRange"));

    private Expr? GetReplace(Marker inputAMarker, Expr inputA, Marker inputBMarker, Expr inputB, Marker outputMarker, Call call, Tensor<float> inputARange, Tensor<float> inputBRange, Tensor<float> outputRange)
    {
        if (inputAMarker.MixQuantInfo != null && ((inputAMarker.MixQuantInfo!.MarkerQuantType == DataTypes.Float16) || (inputAMarker.MixQuantInfo!.MarkerQuantType == DataTypes.Float32)))
        {
            return null;
        }

        if (inputBMarker.MixQuantInfo != null && ((inputBMarker.MixQuantInfo!.MarkerQuantType == DataTypes.Float16) || (inputBMarker.MixQuantInfo!.MarkerQuantType == DataTypes.Float32)))
        {
            return null;
        }

        var newInputAShape = new[] { 1, 1, 1, 1 };
        Array.Copy(inputAMarker!.CheckedShape.ToValueArray(), 0, newInputAShape, newInputAShape.Length - inputAMarker.CheckedShape.Rank, inputAMarker.CheckedShape.Rank);
        var newInputBShape = new[] { 1, 1, 1, 1 };
        Array.Copy(inputBMarker!.CheckedShape.ToValueArray(), 0, newInputBShape, newInputBShape.Length - inputBMarker.CheckedShape.Rank, inputBMarker.CheckedShape.Rank);
        var newOutputShape = new[] { 1, 1, 1, 1 };
        Array.Copy(call.CheckedShape.ToValueArray(), 0, newOutputShape, newOutputShape.Length - call.CheckedShape.Rank, call.CheckedShape.Rank);

        // activation param
        var ap = new ActParam2(newOutputShape[1] * newOutputShape[2], new(0, 1));

        // no fused bias, so no need to exclude them from matmul

        // reshape inputs to 4 dims
        var inputAReShape = IR.F.Tensors.Reshape(inputAMarker, newInputAShape);
        var inputAReshapeMarker = IR.F.Math.RangeOfMarker(inputAReShape, inputARange).With(
            adaQuantInfo: inputAMarker.AdaQuantInfo,
            mixQuantInfo: inputAMarker.MixQuantInfo);
        var inputBReshape = IR.F.Tensors.Reshape(inputBMarker, newInputBShape);
        var inputBReshapeMarker = IR.F.Math.RangeOfMarker(inputBReshape, inputBRange).With(
            adaQuantInfo: inputBMarker.AdaQuantInfo,
            mixQuantInfo: inputBMarker.MixQuantInfo);

        // fake matmul
        var matmul = IR.K230.F.Tensors.FakeMatMul(inputAReshapeMarker, inputBReshapeMarker, ap.ToFakeActData(), ap);

        // reshape output
        var outputReshapeMarker = IR.F.Math.RangeOfMarker(matmul, outputRange).With(
            adaQuantInfo: outputMarker.AdaQuantInfo,
            mixQuantInfo: outputMarker.MixQuantInfo);
        var outputReshape = IR.F.Tensors.Reshape(outputReshapeMarker, call.CheckedShape);
        return IR.F.Math.RangeOfMarker(outputReshape, outputRange).With(
            adaQuantInfo: outputMarker.AdaQuantInfo,
            mixQuantInfo: outputMarker.MixQuantInfo);
    }
}
