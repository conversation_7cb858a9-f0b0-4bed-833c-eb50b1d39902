﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Diagnostics;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using static Nncase.IR.F.Math;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Math;
using static Nncase.PatternMatch.Utility;
using Binary = Nncase.IR.Math.Binary;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class BinaryToFakeActivation : RewriteRule<Pattern>
{
    public BinaryToFakeActivation()
    {
        Pattern = IsRangeOfMarker(
                name: "outputMarker",
                IsBinary(
                    "bn",
                    "call",
                    b => b.BinaryOp == Op,
                    IsRangeOfMarker("inputMarkerL", IsWildcard("lhs") with { TypePattern = HasFixedShape() & HasRank(r => r <= 4, "Only support rank <= 4") }, IsConst("inputRangeL")),
                    IsRangeOfMarker("inputMarkerR", IsWildcard("rhs") with { TypePattern = HasFixedShape() & HasRank(r => r <= 4, "Only support rank <= 4") }, IsConst("inputRangeR"))) with
                { TypePattern = HasDataType(DataTypes.Float32), },
                IsTensorConst("outputRange"));
    }

    /// <inheritdoc/>
    public override Pattern Pattern { get; }

    public virtual BinaryOp Op => BinaryOp.Add;

    public virtual bool CanSupportedConstantLhs => true;

    public virtual void ProcessActParam(ActParam2 actParam, bool isCRHS, int i, float v)
    {
    }

    public Expr? GetReplace(Binary bn, Call call, Expr lhs, Expr rhs, Tensor<float> inputRangeL, Marker inputMarkerL, Tensor<float> inputRangeR, Marker inputMarkerR, Tensor<float> outputRange, Marker outputMarker)
    {
        if (!TryMatch(bn, lhs, rhs))
        {
            return null;
        }

        bool isConstRHS = false;
        TensorConst? cData = null;

        if (rhs is TensorConst rhsData)
        {
            isConstRHS = true;
            cData = rhsData;
        }
        else if (lhs is TensorConst lhsData)
        {
            cData = lhsData;
        }

        if (System.Math.Abs(inputRangeL[0]) >= 65504
            || System.Math.Abs(inputRangeL[1]) >= 65504
            || System.Math.Abs(inputRangeR[0]) >= 65504
            || System.Math.Abs(inputRangeR[1]) >= 65504
            || System.Math.Abs(outputRange[0]) >= 65504
            || System.Math.Abs(outputRange[1]) >= 65504)
        {
            return null;
        }

        if (CompileSession.CompileOptions.QuantizeOptions.QuantScheme != string.Empty
            && (inputMarkerL?.MixQuantInfo?.MarkerQuantType == DataTypes.Float32
            || inputMarkerR?.MixQuantInfo?.MarkerQuantType == DataTypes.Float32))
        {
            return null;
        }

        var newInAShape = new[] { 1, 1, 1, 1 };
        Array.Copy(lhs.CheckedShape.ToValueArray(), 0, newInAShape, newInAShape.Length - lhs.CheckedShape.Rank, lhs.CheckedShape.Rank);
        var newInBShape = new[] { 1, 1, 1, 1 };
        Array.Copy(rhs.CheckedShape.ToValueArray(), 0, newInBShape, newInBShape.Length - rhs.CheckedShape.Rank, rhs.CheckedShape.Rank);
        var outBcShape = lhs.CheckedShape.Rank > rhs.CheckedShape.Rank ? lhs.CheckedShape.ToValueArray() : rhs.CheckedShape.ToValueArray();
        var outputSmallShape = lhs.CheckedShape.Rank > rhs.CheckedShape.Rank ? rhs.CheckedShape.ToValueArray() : lhs.CheckedShape.ToValueArray();
        for (int i = 0; i < outBcShape.Length; i++)
        {
            if (i >= outBcShape.Length - outputSmallShape.Length)
            {
                if (outBcShape[i] <
                    outputSmallShape[i - (outBcShape.Length - outputSmallShape.Length)])
                {
                    outBcShape[i] = outputSmallShape[i - (outBcShape.Length - outputSmallShape.Length)];
                }
            }
        }

        var newOutShape = new[] { 1, 1, 1, 1 };
        Array.Copy(outBcShape, 0, newOutShape, newOutShape.Length - outBcShape.Length, outBcShape.Length);

        var c = newOutShape[1];
        var actParam = new ActParam2(c);
        if (cData!.CheckedShape.IsScalar)
        {
            var v = cData.Value.ToScalar<float>();
            actParam.ForEachChannel((act, i) => ProcessActParam(act, isConstRHS, i, v));
        }
        else if (cData.CheckedShape.ProdWithDynamicAsMaxValue() == 1)
        {
            var v = cData.Value.ToArray<float>()[0];
            actParam.ForEachChannel((act, i) => ProcessActParam(act, isConstRHS, i, v));
        }
        else
        {
            var v = cData.Value.ToArray<float>();
            actParam.ForEachChannel((act, i) => ProcessActParam(act, isConstRHS, i, v[i]));
        }

        var act = new Tensor<float>(actParam.GetAct1Data, new long[] { c, 7 });
        var newInputMarker = isConstRHS ?
            RangeOfMarker(lhs, inputRangeL).With(adaQuantInfo: inputMarkerL?.AdaQuantInfo, mixQuantInfo: inputMarkerL?.MixQuantInfo) :
            RangeOfMarker(rhs, inputRangeR).With(adaQuantInfo: inputMarkerR?.AdaQuantInfo, mixQuantInfo: inputMarkerR?.MixQuantInfo);
        return IR.F.Math.RangeOfMarker(FakeActivation(newInputMarker, None.Default, act, c, 0, 0, 0, false, GnneActivationType.Uninitialized, actParam, call.CheckedShape.ToValueArray().Select(x => (int)x).ToArray()), outputRange).With(
            adaQuantInfo: outputMarker.AdaQuantInfo,
            mixQuantInfo: outputMarker.MixQuantInfo);
    }

    private bool TryMatch(Binary bn, Expr lhs, Expr rhs)
    {
        var lShape = lhs.CheckedShape.ToValueArray().Select(x => (int)x).ToArray();
        var rShape = rhs.CheckedShape.ToValueArray().Select(x => (int)x).ToArray();

        if (IsChannelwiseBinary(lShape, rShape))
        {
            if (rhs is TensorConst && rhs.CheckedDataType == DataTypes.Float32)
            {
                return true;
            }
        }
        else if (CanSupportedConstantLhs && IsChannelwiseBinary(rShape, lShape))
        {
            if (lhs is TensorConst && lhs.CheckedDataType == DataTypes.Float32)
            {
                return true;
            }
        }

        return false;
    }

    private bool IsChannelwiseBinary(int[] shapeA, int[] shapeB)
    {
        if (shapeA.Length == 4
            && shapeB.Length == 3)
        {
            return shapeB[0] == shapeA[1]
                   && shapeB[1] == 1
                   && shapeB[2] == 1;
        }
        else if (shapeA.Length == 4
                 && shapeB.Length == 4)
        {
            return shapeB[0] == 1
                   && (shapeB[1] == shapeA[1] || shapeB[1] == 1)
                   && shapeB[2] == 1
                   && shapeB[3] == 1;
        }
        else if (shapeB.Length == 1)
        {
            return shapeB[0] == 1;
        }
        else if (shapeB.Length == 0)
        {
            return true;
        }
        else if (shapeA.Length == 4)
        {
            if (shapeB.Length == 1)
            {
                return shapeB[0] == 1;
            }
            else if (shapeB.Length == 0)
            {
                return true;
            }
        }

        return false;
    }
}

[RuleGenerator]
public class MulToFakeActivation : BinaryToFakeActivation
{
    public override BinaryOp Op => BinaryOp.Mul;

    public override void ProcessActParam(ActParam2 actParam, bool isCRHS, int i, float v)
    {
        actParam.Ks[0, i] = actParam.Ks[1, i] = v;
    }
}

[RuleGenerator]
public class DivToFakeActivation : BinaryToFakeActivation
{
    public override BinaryOp Op => BinaryOp.Div;

    public override bool CanSupportedConstantLhs => false;

    public override void ProcessActParam(ActParam2 actParam, bool isCRHS, int i, float v)
    {
        Trace.Assert(isCRHS);
        actParam.Ks[0, i] = actParam.Ks[1, i] = 1f / v;
    }
}

[RuleGenerator]
public class AddToFakeActivation : BinaryToFakeActivation
{
    public override BinaryOp Op => BinaryOp.Add;

    public override void ProcessActParam(ActParam2 actParam, bool isCRHS, int i, float v)
    {
        actParam.Bs[0, i] = actParam.Bs[1, i] = v;
    }
}

[RuleGenerator]
public class SubToFakeActivation : BinaryToFakeActivation
{
    public override BinaryOp Op => BinaryOp.Sub;

    public override void ProcessActParam(ActParam2 actParam, bool isCRHS, int i, float v)
    {
        if (isCRHS)
        {
            actParam.Bs[0, i] = actParam.Bs[1, i] = -v;
        }
        else
        {
            actParam.Ks[0, i] = actParam.Ks[1, i] = -1f;
            actParam.Bs[0, i] = actParam.Bs[1, i] = v;
        }
    }
}

[RuleGenerator]
public class MaxToFakeActivation : BinaryToFakeActivation
{
    public override BinaryOp Op => BinaryOp.Max;

    public override void ProcessActParam(ActParam2 actParam, bool isCRHS, int i, float v)
    {
        actParam.Ks[0, i] = 0f;
        actParam.Bs[0, i] = actParam.Xs[0, i] = v;
    }
}

[RuleGenerator]
public class MinToFakeActivation : BinaryToFakeActivation
{
    public override BinaryOp Op => BinaryOp.Min;

    public override void ProcessActParam(ActParam2 actParam, bool isCRHS, int i, float v)
    {
        actParam.Ks[1, i] = 0f;
        actParam.Bs[1, i] = actParam.Xs[0, i] = v;
    }
}

[RuleGenerator]
public partial class GeneralAddSubMulDivToFakeActivation : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } =
        IsRangeOfMarker(
            name: "outputMarker",
            IsBinary(
                    "bn",
                    "output",
                    _ => true,
                    IsRangeOfMarker("inputMarkerL", IsWildcard("lhs") with { TypePattern = HasFixedShape() & HasRank(r => r <= 4, "Only support rank <= 4") }, IsTensorConst("inputRangeL")),
                    IsRangeOfMarker("inputMarkerR", IsWildcard("rhs") with { TypePattern = HasFixedShape() & HasRank(r => r <= 4, "Only support rank <= 4") }, IsTensorConst("inputRangeR"))) with
            { TypePattern = HasDataType(DataTypes.Float32), },
            IsTensorConst("outputRange"));

    public Expr? GetReplace(Binary bn, Marker inputMarkerL, Expr lhs, Tensor<float> inputRangeL, Marker inputMarkerR, Expr rhs, Tensor<float> inputRangeR, Marker outputMarker, Call output, Tensor<float> outputRange)
    {
        if (bn.BinaryOp != BinaryOp.Add && bn.BinaryOp != BinaryOp.Sub && bn.BinaryOp != BinaryOp.Mul)
        {
            return null;
        }

        if (lhs.CheckedDataType != DataTypes.Float32 || output.CheckedDataType != DataTypes.Float32 ||
            rhs.CheckedDataType != DataTypes.Float32)
        {
            return null;
        }

        if (CompileSession.CompileOptions.QuantizeOptions.QuantScheme != string.Empty
            && (inputMarkerL?.MixQuantInfo?.MarkerQuantType == DataTypes.Float32
            || inputMarkerR?.MixQuantInfo?.MarkerQuantType == DataTypes.Float32))
        {
            return null;
        }

        if (System.Math.Abs(inputRangeL[0]) >= 65504
            || System.Math.Abs(inputRangeL[1]) >= 65504
            || System.Math.Abs(inputRangeR[0]) >= 65504
            || System.Math.Abs(inputRangeR[1]) >= 65504
            || System.Math.Abs(outputRange[0]) >= 65504
            || System.Math.Abs(outputRange[1]) >= 65504)
        {
            return null;
        }

        int[] newInAShape = new[] { 1, 1, 1, 1 };
        Array.Copy(lhs.CheckedShape.ToValueArray(), 0, newInAShape, newInAShape.Length - lhs.CheckedShape.Rank, lhs.CheckedShape.Rank);
        int[] newInBShape = new[] { 1, 1, 1, 1 };
        Array.Copy(rhs.CheckedShape.ToValueArray(), 0, newInBShape, newInBShape.Length - rhs.CheckedShape.Rank, rhs.CheckedShape.Rank);
        int[] outBcShape = lhs.CheckedShape.Rank > rhs.CheckedShape.Rank
            ? lhs.CheckedShape.ToValueArray().Select(x => (int)x).ToArray()
            : rhs.CheckedShape.ToValueArray().Select(x => (int)x).ToArray();
        int[] outputSmallShape = lhs.CheckedShape.Rank > rhs.CheckedShape.Rank
            ? rhs.CheckedShape.ToValueArray().Select(x => (int)x).ToArray()
            : lhs.CheckedShape.ToValueArray().Select(x => (int)x).ToArray();
        for (int i = 0; i < outBcShape.Length; i++)
        {
            if (i >= outBcShape.Length - outputSmallShape.Length)
            {
                if (outBcShape[i] <
                    outputSmallShape[i - (outBcShape.Length - outputSmallShape.Length)])
                {
                    outBcShape[i] = outputSmallShape[i - (outBcShape.Length - outputSmallShape.Length)];
                }
            }
        }

        int[] newOutShape = new[] { 1, 1, 1, 1 };
        Array.Copy(outBcShape, 0, newOutShape, newOutShape.Length - outBcShape.Length, outBcShape.Length);

        int channels = newOutShape[1];
        var actParam = new ActParam2(channels);
        GnneActivationType actType;
        if (bn.BinaryOp == BinaryOp.Add || bn.BinaryOp == BinaryOp.Sub)
        {
            actType = GnneActivationType.Add;
        }
        else
        {
            actType = GnneActivationType.Mul;
        }

        var act = new Tensor<float>(actParam.GetAct1Data, new long[] { channels, 7 });
        var neg = RangeOfMarker(Unary(UnaryOp.Neg, inputMarkerR!), new float[] { -inputRangeR[1], -inputRangeR[0] }).With(
            adaQuantInfo: inputMarkerR?.AdaQuantInfo,
            mixQuantInfo: inputMarkerR?.MixQuantInfo);
        return RangeOfMarker(FakeActivation(inputMarkerL!.With(target: lhs, attribute: (Expr)inputRangeL), bn.BinaryOp == BinaryOp.Sub ? neg : inputMarkerR!.With(target: rhs, attribute: (Expr)inputRangeR), act, channels, 0, 0, 0, false, actType, actParam, output.CheckedShape.ToValueArray().Select(x => (int)x).ToArray()), outputRange).With(
            adaQuantInfo: outputMarker.AdaQuantInfo,
            mixQuantInfo: outputMarker.MixQuantInfo);
    }
}
