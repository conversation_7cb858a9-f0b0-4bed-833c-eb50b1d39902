// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Shapes;
using Nncase.PatternMatch;
using static Nncase.IR.F.Math;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.K230;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class RestoreFakeConv2D : RewriteRule<Pattern>
{
    public override Pattern Pattern { get; } =
            IsFakeConv2D(
                target_name: "fakeConv",
                call_name: "call",
                _ => true,
                IsRangeOfMarker("inputMarker", IsWildcard("input"), IsConst("inputRange") with { TypePattern = HasFixedShape() }),
                IsRangeOfMarker("weightsMarker", IsTensorConst("weights"), IsConst("weightsRange") with { TypePattern = HasFixedShape() }),
                IsTensor<PERSON>onst("act"),
                IsFixedPaddings("padding"),
                IsFixedShape("stride"),
                IsFixedShape("dilation"),
                IsFixedDimension("groups"),
                IsTensorConst("padValue"));

    public Expr? GetReplace(FakeConv2D fakeConv, Call call, Expr inputMarker, Expr input, Tensor<float> inputRange, Expr weightsMarker, Tensor weights, Tensor<float> weightsRange, TensorConst act, Paddings padding, int[] stride, int[] dilation, int groups, Tensor<float> padValue)
    {
        var channel = call.CheckedShape[1].FixedValue;
        var ap = fakeConv.ActParam;
        if (ap.FusedClamp.All(x => x.Max == ap.FusedClamp[0].Max && x.Min == ap.FusedClamp[0].Min))
        {
            float[] wConst = weights.ToArray<float>();

            for (int i = 0; i < wConst.Length; i++)
            {
                // 目前只支持线性act的还原
                wConst[i] *= ap.Ks[0, i / (wConst.Length / channel)];
            }

            float[] biasData = new float[channel];
            for (int i = 0; i < biasData.Length; i++)
            {
                biasData[i] = ap.Bs[0, i];
            }

            var bias = Tensor.From(biasData, new[] { channel });
            var newWeights = Tensor.From(wConst, weights.Shape);
            return IR.F.NN.Conv2D(input, newWeights, bias, stride, padding, dilation, PadMode.Constant, groups, new[] { ap.FusedClamp[0].Min, ap.FusedClamp[0].Max });
        }

        return null;
    }
}

[RuleGenerator]
public sealed partial class RestoreFakeActivation : GNNEDIFQuantRule
{
    public override Pattern Pattern { get; } =
        IsFakeActivation(
            "fakeActivation",
            "call",
            _ => true,
            IsRangeOfMarker(IsWildcard("inputA"), IsTensorConst("inputARange")),
            IsRangeOfMarker(IsWildcard("inputB"), IsTensorConst("inputBRange")),
            IsWildcard("act"),
            IsWildcard("outChannels"),
            IsWildcard("inAShiftbits"),
            IsWildcard("inBShiftbits"),
            IsWildcard("outShiftbits"),
            IsWildcard("is16Segment"));

    public Expr? GetReplace(FakeActivation fakeActivation, Call call, Expr inputA, Tensor<float> inputARange, Expr inputB, Tensor<float> inputBRange, Expr act, Expr outChannels, Expr inAShiftbits, Expr inBShiftbits, Expr outShiftbits, Expr is16Segment)
    {
        var binaryOp = fakeActivation.Type == GnneActivationType.Add ? BinaryOp.Add : BinaryOp.Mul;

        // var fused_clamp = new ValueRange<float> {fusedclamps.Min, fusedclamps.Max};
        return Binary(binaryOp, inputA, inputB);
    }
}
