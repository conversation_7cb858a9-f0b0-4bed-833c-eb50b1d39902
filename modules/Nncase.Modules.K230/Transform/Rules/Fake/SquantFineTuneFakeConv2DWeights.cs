﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.PatternMatch;
using Nncase.Quantization;
using Nncase.Targets;
using Nncase.Utilities;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.K230;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class SquantFineTuneFakeConv2DWeights : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } =
        IsFakeConv2D(
            target_name: "fakeConv",
            call_name: "call",
            _ => true,
            IsRangeOfMarker("inputMarker", IsWildcard("input"), IsConst("inputRange") with { TypePattern = HasFixedShape() }),
            IsRangeOfMarker("weightsMarker", IsTensor<PERSON>onst("weights"), <PERSON><PERSON><PERSON><PERSON>("weightsRange") with { TypePattern = HasFixedShape() }),
            IsTensorConst("act"),
            IsTensorConst("padding"),
            IsTensorConst("stride"),
            IsTensorConst("dilation"),
            IsTensorConst("groups"),
            IsTensorConst("padValue"));

    private Expr? GetReplace(IR.K230.FakeConv2D fakeConv, Marker inputMarker, Marker weightsMarker, TensorConst weights, TensorConst weightsRange, Tensor<float> act, Expr padding, Expr stride, Expr dilation, int groups, Expr padValue, RunPassContext options)
    {
        if (!CompileSession.CompileOptions.QuantizeOptions.UseSquant)
        {
            return null;
        }

        if (weightsMarker.MixQuantInfo == null)
        {
            weightsMarker.MixQuantInfo = new K230Target.K230MixQuantInfo();
        }

        float[] weightsValue = ((TensorConst)weights).Value.ToArray<float>();
        var weightsValueTensor = Tensor.From<float>(weightsValue, weights.CheckedShape.ToValueArray());

        // int oc = weights.CheckedShape[0].FixedValue;
        var weightsRangeU8 = weightsMarker.MixQuantInfo!.U8FineTunedWeightsRangesByChannel == null ? weightsRange : weightsMarker.MixQuantInfo!.U8FineTunedWeightsRangesByChannel;
        var weightsRangeI8 = weightsMarker.MixQuantInfo!.I8FineTunedWeightsRangesByChannel == null ? weightsRange : weightsMarker.MixQuantInfo!.I8FineTunedWeightsRangesByChannel;
        var weightsRangeI16 = ((K230Target.K230MixQuantInfo)weightsMarker!.MixQuantInfo!).I16FineTunedWeightsRangesByChannel == null ? weightsRange : ((K230Target.K230MixQuantInfo)weightsMarker.MixQuantInfo!).I16FineTunedWeightsRangesByChannel;
        var weightsRangeU8Tensor = Tensor.From<float>(weightsRangeU8.Value.ToArray<float>(), weightsRange.CheckedShape.ToValueArray());
        var weightsRangeI8Tensor = Tensor.From<float>(weightsRangeI8.Value.ToArray<float>(), weightsRange.CheckedShape.ToValueArray());
        var weightsRangeI16Tensor = Tensor.From<float>(weightsRangeI16!.Value.ToArray<float>(), weightsRange.CheckedShape.ToValueArray());

        var weightsValueU8Squant = QuantAlgorithmUtility.SquantWeights(weightsValueTensor, weightsRangeU8Tensor!, weights.CheckedShape.ToValueArray(), QuantMode.UnsignedMode, 8, true);
        var weightsValueI8Squant = QuantAlgorithmUtility.SquantWeights(weightsValueTensor, weightsRangeI8Tensor!, weights.CheckedShape.ToValueArray(), QuantMode.SignedSymmetricMode, 8, true);
        var weightsValueI16Squant = QuantAlgorithmUtility.SquantWeights(weightsValueTensor, weightsRangeI16Tensor!, weights.CheckedShape.ToValueArray(), QuantMode.SignedSymmetricMode, 16, true);

        weightsMarker.MixQuantInfo!.U8FineTunedWeights = new TensorConst(weightsValueU8Squant);
        weightsMarker.MixQuantInfo!.I8FineTunedWeights = new TensorConst(weightsValueI8Squant);
        ((K230Target.K230MixQuantInfo)weightsMarker.MixQuantInfo!).I16FineTunedWeights = new TensorConst(weightsValueI16Squant);

        weightsMarker.MixQuantInfo!.DoSquant = true;
        var output = FakeConv2D(inputMarker, weightsMarker, act, padding, stride, dilation, groups, padValue, fakeConv.ActParam);
        options.MatchOptions.SuppressPattern(output, Pattern); // only invoke once
        return output;
    }
}
