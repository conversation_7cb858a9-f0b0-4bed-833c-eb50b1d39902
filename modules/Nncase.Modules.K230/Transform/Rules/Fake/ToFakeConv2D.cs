// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.NN;
using Nncase.PatternMatch;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.K230.GNNETypePatternUtility;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.NN;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class ToFakeConv2D : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } =
        IsRangeOfMarker(
            name: "outputMarker",
            IsConv2D(
                "conv",
                "call",
                _ => true,
                IsRangeOfMarker("inputMarker", IsWildcard("input") with { TypePattern = HasFixedShape() }, Is<PERSON>onst("inputRange")),
                IsRangeOfMarker("weightsMarker", IsTensorConst("weights") with { TypePattern = HasFixedShape() }, IsConst("weightsRange")),
                IsTensorConst("bias"),
                IsTensorConst("stride"),
                IsWildcard("padding"),
                IsTensorConst("dilation"),
                IsTensorConst("groups"),
                IsTensorConst("fusedClamp")),
            IsTensorConst("outputRange"));

    private Expr? GetReplace(Call call, Conv2D conv, Expr input, TensorConst weights, TensorConst bias, Expr stride, Expr padding, Expr dilation, int groups, Tensor<float> fusedClamp, Tensor<float> weightsRange, Tensor<float> inputRange, Tensor<float> outputRange, Marker inputMarker, Marker outputMarker, Marker weightsMarker)
    {
        if (System.Math.Abs(inputRange[0]) >= 65504
         || System.Math.Abs(inputRange[1]) >= 65504
         || System.Math.Abs(outputRange[0]) >= 65504
         || System.Math.Abs(outputRange[1]) >= 65504)
        {
            return null;
        }

        // If the weight range is too large, it will no longer be lower to kpu
        if (weightsRange.ToArray().Max() - weightsRange.ToArray().Min() > 255)
        {
            return null;
        }

        var op = CompileSession.CompileOptions;

        // If the target is not int8, uin8, int16, it will not be lower to cpu
        if (op.QuantizeOptions.QuantScheme != string.Empty
        && weightsMarker?.MixQuantInfo?.MarkerQuantType != DataTypes.Int8
        && weightsMarker?.MixQuantInfo?.MarkerQuantType != DataTypes.UInt8
        && weightsMarker?.MixQuantInfo?.MarkerQuantType != DataTypes.Int16)
        {
            return null;
        }

        bool splitWeightsToAct = IsDepthWise(input, weights, groups) && (op.QuantizeOptions.QuantScheme == string.Empty || op.QuantizeOptions.QuantSchemeStrictMode == false);
        var newWeights = (float[])weights.Value.ToArray<float>();
        var actParam = ActParam2.GetFakeConvActParam(weights, bias, splitWeightsToAct, newWeights);
        var c = weights.CheckedShape[0].FixedValue;
        for (int i = 0; i < c; i++)
        {
            actParam.FusedClamp[i].Min = fusedClamp[0];
            actParam.FusedClamp[i].Max = fusedClamp[1];
        }

        var newWeightsMarker = IR.F.Math.RangeOfMarker((Expr)Tensor.From(newWeights, weights.CheckedShape.ToValueArray()), weightsRange).With(
                adaQuantInfo: ((Marker)weightsMarker!).AdaQuantInfo,
                mixQuantInfo: ((Marker)weightsMarker!).MixQuantInfo);
        return IR.F.Math.RangeOfMarker(FakeConv2D(inputMarker, newWeightsMarker, actParam.ToFakeActData(), padding, stride, dilation, groups, 0f, actParam), outputRange).With(
            adaQuantInfo: outputMarker.AdaQuantInfo,
            mixQuantInfo: outputMarker.MixQuantInfo);
    }
}
