﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.Imaging;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Imaging;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class ResizeToFakeActivation : IRewriteRule
{
    public IPattern Pattern { get; }
        = IsResizeImage(
            "resize",
            "call",
            _ => true,
            IsWildcard("input") with { TypePattern = HasFixedShape() },
            IsWildcard("roi"),
            IsTensorConst("newSize"),
            IsWildcard("cubiccoeffa"),
            IsWildcard("excludeOutside"),
            Is<PERSON>ildcard("extrapolationValue")) with
            {
                TypePattern = HasRank(4),
            };

    public bool OnTryMatch(ResizeImage resize, Expr input, TensorConst newSize)
    {
        if (resize.ResizeMode == ImageResizeMode.NearestNeighbor &&
            input.CheckedShape.Rank == 4 &&
            newSize.Value.ToArray<int>()[2] % input.CheckedShape[2].FixedValue == 0 &&
            newSize.Value.ToArray<int>()[3] % input.CheckedShape[3].FixedValue == 0)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    private Expr? GetReplace(Call call, ResizeImage resize, Expr input, TensorConst newSize)
    {
        if (!OnTryMatch(resize, input, newSize))
        {
            return null;
        }

        int channels = (int)call.CheckedShape[1].FixedValue;
        var actParam = new ActParam2(channels, new(0, 1));

        var act = new Tensor<float>(actParam.GetAct1Data, new long[] { channels, 7 });
        return FakeActivation(input, None.Default, act, channels, 0, 0, 0, false, GnneActivationType.Uninitialized, actParam, call.CheckedShape.ToValueArray().Select(x => (int)x).ToArray());
    }
}
