﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.Evaluator;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Math;
using Nncase.PatternMatch;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Math;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class ClampToFakeActivation : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } =
        IsClamp(
            "clamp",
            "call",
            _ => true,
            IsWildcard("input") with { TypePattern = HasFixedShape() & HasRank(r => r > 1 & r <= 4, "not support scalar, and only support rank <= 4") },
            IsTensorConst("min"),
            IsTensorConst("max"));

    public Expr? GetReplace(Clamp clamp, Call call, Expr input, Tensor<float> min, Tensor<float> max)
    {
        if (!TryMatch(min, max))
        {
            return null;
        }

        var newInAShape = new[] { 1, 1, 1, 1 };
        Array.Copy(input.CheckedShape.ToValueArray(), 0, newInAShape, newInAShape.Length - input.CheckedShape.Rank, input.CheckedShape.Rank);

        // GNNEShape newOutShap = new GNNEShape(shape.CheckedShape.ToValueArray());
        int channels = newInAShape[1];
        var actParam = new ActParam2(channels);
        for (int c = 0; c < channels; c++)
        {
            actParam.FusedClamp[c] = new ValueRange<float>(min.ToArray()[c % min.Length], max.ToArray()[c % max.Length]);
        }

        var act = new Tensor<float>(actParam.GetAct1Data, new long[] { channels, 7 });
        return FakeActivation(input, None.Default, act, channels, 0, 0, 0, false, GnneActivationType.Add, actParam, call.CheckedShape.ToValueArray().Select(x => (int)x).ToArray());
    }

    private bool TryMatch(Tensor<float> min, Tensor<float> max)
    {
        if (TypeInference.BroadcastType(new TensorType(DataTypes.Float32, min.Shape), new TensorType(DataTypes.Float32, max.Shape)) is TensorType tt)
        {
            if (tt.Shape.IsScalar)
            {
                return true;
            }

            int[] shape = tt.Shape.ToValueArray().Select(x => (int)x).ToArray();
            return shape.All(x => x == 1) || (shape.Length > 2 && shape.Aggregate((x, y) => x * y) == shape[^3]);
        }

        return false;
    }
}
