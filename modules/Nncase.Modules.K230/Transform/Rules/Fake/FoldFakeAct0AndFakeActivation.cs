﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.Passes.Analysis;
using Nncase.PatternMatch;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.Passes.Rules.GetReplaceHelper;
using static Nncase.PatternMatch.F.K230;
using static Nncase.PatternMatch.Utility;
using static Nncase.Utilities.MetadataUtility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class FoldFakeConv2DAndFakeActivation : RewriteRule<Pattern>, IRewriteRule
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } =
        IsFakeActivation(
            target_name: "fakeAct",
            call_name: "call2",
            _ => true,
            IsRangeOfMarker(
                "range",
                IsFakeConv2D(
                    target_name: "fakeConv",
                    call_name: "call1",
                    _ => true,
                    IsRangeOfMarker("inputMarker", <PERSON><PERSON><PERSON>dcard("input"), <PERSON><PERSON><PERSON><PERSON>("inputRange")),
                    IsRangeOfMarker("weightsMarker", IsTensorConst("weights"), IsConst("weightsRange")),
                    IsTensorConst("act1"),
                    IsTensorConst("padding"),
                    IsTensorConst("stride"),
                    IsTensorConst("dilation"),
                    IsTensorConst("groups"),
                    IsTensorConst("padValue")),
                IsTensorConst("fakeActInputRange")),
            IsNone(),
            IsTensorConst("act2"),
            IsTensorConst("outChannels"),
            IsTensorConst("inAShiftBits"),
            IsTensorConst("inBShiftBits"),
            IsTensorConst("outShiftBits"),
            IsTensorConst("is16Segments"));

    private Expr? GetReplace(Expr fakeConv, Expr input, Expr inputMarker, Expr weights, Expr weightsMarker, TensorConst act1, TensorConst padding, TensorConst stride, TensorConst dilation, TensorConst groups, TensorConst padValue, FakeActivation fakeAct, TensorConst act2, TensorConst outChannels, bool is16Segments, Expr range, Call call1, Call call2, RunPassContext context)
    {
        if (context.Driver is DataflowPass)
        {
            var userAnalysis = context.GetAnalysis<IExprUserAnalysisResult>();
            if (userAnalysis[range].Count() > 1)
            {
                return null;
            }
        }

        if (call1.CheckedShape != call2.CheckedShape)
        {
            return null;
        }

        if (fakeAct.ActParam is ActParam16)
        {
            return null;
        }

        var fakeAct0Param = ((FakeConv2D)fakeConv).ActParam;
        var actParam = FoldAct0WithAct1(fakeAct0Param, is16Segments, outChannels, (ActParam2)fakeAct.ActParam);
        if (actParam == null)
        {
            return null;
        }

        var act = new Tensor<float>(actParam.GetAct0Data, new[] { weights.CheckedShape[0].FixedValue, 7 });
        return FakeConv2D(inputMarker, weightsMarker, act, padding, stride, dilation, groups, padValue, actParam).InheritMetaData(call2);
    }
}

[RuleGenerator]
public sealed partial class FoldFakeConv2DTransposeAndFakeActivation : RewriteRule<Pattern>, IRewriteRule
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } =
        IsFakeActivation(
            target_name: "fakeAct",
            call_name: "call2",
            _ => true,
            IsRangeOfMarker(
                "range",
                IsFakeConv2DTranspose(
                    target_name: "fakeConvTranspose",
                    call_name: "call1",
                    _ => true,
                    IsRangeOfMarker("inputMarker", IsWildcard("input"), IsConst("inputRange")),
                    IsRangeOfMarker("weightsMarker", IsTensorConst("weights"), IsConst("weightsRange")),
                    IsTensorConst("act1"),
                    IsTensorConst("outputShape"),
                    IsTensorConst("padding"),
                    IsTensorConst("outputPadding"),
                    IsTensorConst("stride"),
                    IsTensorConst("dilation"),
                    IsTensorConst("groups"),
                    IsTensorConst("padValue")),
                IsTensorConst("fakeActInputRange")),
            IsNone(),
            IsTensorConst("act2"),
            IsTensorConst("outChannels"),
            IsTensorConst("inAShiftBits"),
            IsTensorConst("inBShiftBits"),
            IsTensorConst("outShiftBits"),
            IsTensorConst("is16Segments"));

    private Expr? GetReplace(Expr fakeConvTranspose, Expr input, Expr inputMarker, Expr weights, Expr weightsMarker, TensorConst act1, TensorConst outputShape, TensorConst padding, TensorConst outputPadding, TensorConst stride, TensorConst dilation, TensorConst groups, TensorConst padValue, FakeActivation fakeAct, TensorConst act2, int outChannels, bool is16Segments, Expr range, Call call1, Call call2, RunPassContext context)
    {
        if (context.Driver is DataflowPass)
        {
            var userAnalysis = context.GetAnalysis<IExprUserAnalysisResult>();
            if (userAnalysis[range].Count() > 1)
            {
                return null;
            }
        }

        if (call1.CheckedShape != call2.CheckedShape)
        {
            return null;
        }

        if (fakeAct.ActParam is ActParam16)
        {
            return null;
        }

        var fakeAct0Param = new ActParam2(outChannels);
        for (int i = 0; i < outChannels; i++)
        {
            fakeAct0Param.Ks[0, i] = act1.Value.ToArray<float>()[i * 7];
            fakeAct0Param.Ks[1, i] = act1.Value.ToArray<float>()[(i * 7) + 1];
            fakeAct0Param.Bs[0, i] = act1.Value.ToArray<float>()[(i * 7) + 2];
            fakeAct0Param.Bs[1, i] = act1.Value.ToArray<float>()[(i * 7) + 3];
            fakeAct0Param.FusedClamp[i].Min = act1.Value.ToArray<float>()[(i * 7) + 4];
            fakeAct0Param.FusedClamp[i].Max = act1.Value.ToArray<float>()[(i * 7) + 5];
            fakeAct0Param.Xs[0, i] = act1.Value.ToArray<float>()[(i * 7) + 6];
        }

        var actParam = FoldAct0WithAct1(fakeAct0Param, is16Segments, outChannels, (ActParam2)fakeAct.ActParam);
        if (actParam == null)
        {
            return null;
        }

        var act = new Tensor<float>(actParam.GetAct0Data, new long[] { outChannels, 7 });
        return FakeConv2DTranspose(inputMarker, weightsMarker, act, outputShape, padding, outputPadding, stride, dilation, groups, padValue, actParam).InheritMetaData(call2);
    }
}

[RuleGenerator]
public sealed partial class FoldFakeMatMulAndFakeActivation : RewriteRule<Pattern>, IRewriteRule
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } =
        IsFakeActivation(
            target_name: "fakeAct",
            call_name: "call2",
            _ => true,
            IsRangeOfMarker(
                "range",
                IsFakeMatMul(
                    target_name: "fakeMatMul",
                    call_name: "call1",
                    _ => true,
                    IsRangeOfMarker("inputMarker", IsWildcard("input"), IsConst("inputRange")),
                    IsRangeOfMarker("weightsMarker", IsTensorConst("weights"), IsConst("weightsRange")),
                    IsTensorConst("act1")),
                IsTensorConst("fakeActInputRange")),
            IsNone(),
            IsTensorConst("act2"),
            IsTensorConst("outChannels"),
            IsTensorConst("inAShiftBits"),
            IsTensorConst("inBShiftBits"),
            IsTensorConst("outShiftBits"),
            IsTensorConst("is16Segments"));

    private Expr? GetReplace(Expr fakeMatMul, Expr input, Expr inputMarker, Expr weights, Expr weightsMarker, TensorConst act1, FakeActivation fakeAct, TensorConst act2, TensorConst outChannels, bool is16Segments, Expr range, Call call1, Call call2, RunPassContext context)
    {
        if (context.Driver is DataflowPass)
        {
            var userAnalysis = context.GetAnalysis<IExprUserAnalysisResult>();
            if (userAnalysis[range].Count() > 1)
            {
                return null;
            }
        }

        if (call1.CheckedShape != call2.CheckedShape)
        {
            return null;
        }

        var fakeAct0Param = new ActParam2((int)act1.CheckedShape[0].FixedValue);
        for (int i = 0; i < outChannels.Value.ToScalar<int>(); i++)
        {
            fakeAct0Param.Ks[0, i] = act1.Value.ToArray<float>()[i * 7];
            fakeAct0Param.Ks[1, i] = act1.Value.ToArray<float>()[(i * 7) + 1];
            fakeAct0Param.Bs[0, i] = act1.Value.ToArray<float>()[(i * 7) + 2];
            fakeAct0Param.Bs[1, i] = act1.Value.ToArray<float>()[(i * 7) + 3];
            fakeAct0Param.FusedClamp[i].Min = act1.Value.ToArray<float>()[(i * 7) + 4];
            fakeAct0Param.FusedClamp[i].Max = act1.Value.ToArray<float>()[(i * 7) + 5];
            fakeAct0Param.Xs[0, i] = act1.Value.ToArray<float>()[(i * 7) + 6];
        }

        var actParam = FoldAct0WithAct1(fakeAct0Param, is16Segments, outChannels, (ActParam2)fakeAct.ActParam);
        if (actParam == null)
        {
            return null;
        }

        var act = new Tensor<float>(actParam.GetAct0Data, new[] { act1.CheckedShape[0].FixedValue, 7 });
        return FakeMatMul(inputMarker, weightsMarker, act, actParam).InheritMetaData(call2);
    }
}
