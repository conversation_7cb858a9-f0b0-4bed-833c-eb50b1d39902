// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.
#if false
using Nncase.IR;
using Nncase.IR.K230.F;
using Nncase.PatternMatch;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Math;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

/// <summary>
/// convert dynamic batch matmul to fake dynamic gnne matmul.
/// </summary>
[RuleGenerator]
public sealed partial class ToFakeDynamicMatmul : IRewriteRule
{
    /// <inheritdoc/>
    public IPattern Pattern { get; } = IsMatMul(null, "oldMatmul", IsWildcard("inputA"), IsWildcard("inputB")) with { TypePattern = HasShape(shape => !shape.IsFixed, "HasDynamicShape") };

    private bool AnyUnknow(Shape shape, int start, int end)
    {
        return Enumerable.Range(0, end < 0 ? shape.Rank + end : end).Any(i => shape[i].IsUnknown);
    }

    private Expr? GetReplace(Call oldMatmul, Expr inputA, Expr inputB)
    {
        var aShape = inputA.CheckedShape;
        var bShape = inputB.CheckedShape;

        // 1. fix channel
        // a : [?...,fix,fix]

        // 2. dynamic channel
        // a : [?...,?,fix]
        // a : [?...,?,?]
        var ap = new ActivationParameter<float>(aShape.SkipLast(1).Select(d => d.IsFixed ? d.FixedValue : 1).Select(x => (int)x).ToArray(), ValueRange<float>.Full);
        Expr act = ap.ToAct0Data<float>();

        // output = IR.K230.F.Tensors.FakeDynamicGNNEMatMul(
        //     IR.F.Math.RangeOfMarker(input_a, IR.F.Math.RangeOf(input_a)),
        //     IR.F.Math.RangeOfMarker(input_b, IR.F.Math.RangeOf(input_b)),
        //     act, input_a.CheckedShape[^2].IsUnknown);
        // return IR.F.Math.RangeOfMarker(output, IR.F.Math.RangeOf(output));
        return Tensors.FakeDynamicGNNEMatMul(inputA, inputB, act, inputA.CheckedShape[^2].IsUnknown);
    }
}
#endif
