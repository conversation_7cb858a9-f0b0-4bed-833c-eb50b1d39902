﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using DryIoc.ImTools;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.Passes.Analysis;
using Nncase.PatternMatch;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.PatternMatch.F.K230;
using static Nncase.PatternMatch.F.Math;
using static Nncase.PatternMatch.Utility;
using static Nncase.Utilities.MetadataUtility;

namespace Nncase.Passes.Rules.K230;

/// <inheritdoc cref="IRewriteRule" />
[RuleGenerator]
public sealed partial class FoldTwoFakeActivation : RewriteRule<Pattern>, IRewriteRule
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } =
        IsFakeActivation(
            target_name: "fakeAct2",
            call_name: "call2",
            _ => true,
            IsRangeOfMarker(
                "range",
                IsFakeActivation(
                        target_name: "fakeAct1",
                        call_name: "call1",
                        _ => true,
                        <PERSON><PERSON><PERSON><PERSON><PERSON>("inputa"),
                        <PERSON><PERSON><PERSON><PERSON><PERSON>("inputb"),
                        Is<PERSON>ensor<PERSON><PERSON>t("act1"),
                        Is<PERSON>ensorConst("outchannels1"),
                        IsTensorConst("inAShiftBits1"),
                        IsTensorConst("inBShiftBits1"),
                        IsTensorConst("outShiftBits1"),
                        IsTensorConst("is16Segments1")),
                IsTensorConst("fakeAct2InputRange")),
            IsNone(),
            IsTensorConst("act2"),
            IsTensorConst("outchannels2"),
            IsTensorConst("inAShiftBits2"),
            IsTensorConst("inBShiftBits2"),
            IsTensorConst("outShiftBits2"),
            IsTensorConst("is16Segments2"));

    private Expr? GetReplace(FakeActivation fakeAct1, Expr inputa, Expr inputb, TensorConst act1, TensorConst outchannels1, FakeActivation fakeAct2, Call call1, Call call2, TensorConst act2, TensorConst outchannels2, bool is16Segments1, bool is16Segments2, Expr range, RunPassContext context)
    {
        if (call1.CheckedShape.Rank < call2.CheckedShape.Rank)
        {
            return null;
        }

        if (context.Driver is DataflowPass)
        {
            var userAnalysis = context.GetAnalysis<IExprUserAnalysisResult>();
            if (userAnalysis[range].Count() > 1)
            {
                return null;
            }
        }

        if (is16Segments1 == true || is16Segments2 == true)
        {
            return null;
        }

        var oc1 = outchannels1.Value.ToScalar<int>();
        var oc2 = outchannels2.Value.ToScalar<int>();
        if (oc1 != oc2)
        {
            return null;
        }

        var actFoldParam = new ActFoldParam(oc1);
        for (int i = 0; i < oc1; i++)
        {
            // case1
            if (act2.Value.Cast<float>()[i, 0] >= fakeAct1.ActParam.FusedClamp[i].Max)
            {
                if (act2.Value.Cast<float>()[i, 1] > 0)
                {
                    var a1Max = double.IsPositiveInfinity(fakeAct1.ActParam.FusedClamp[i].Max) ? fakeAct1.ActParam.FusedClamp[i].Max : (fakeAct1.ActParam.FusedClamp[i].Max * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                    var a1Min = double.IsNegativeInfinity(fakeAct1.ActParam.FusedClamp[i].Min) ? fakeAct1.ActParam.FusedClamp[i].Min : (fakeAct1.ActParam.FusedClamp[i].Min * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                    actFoldParam.FusedClamp[i] = new ValueRange<Half>
                    {
                        Min = a1Min > fakeAct2.ActParam.FusedClamp[i].Min ? (Half)a1Min : (Half)fakeAct2.ActParam.FusedClamp[i].Min,
                        Max = a1Max < fakeAct2.ActParam.FusedClamp[i].Max ? (Half)a1Max : (Half)fakeAct2.ActParam.FusedClamp[i].Max,
                    };
                    if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                    {
                        return null;
                    }
                }
                else if (act2.Value.Cast<float>()[i, 1] < 0)
                {
                    var a1Min = double.IsPositiveInfinity(fakeAct1.ActParam.FusedClamp[i].Max) ? -fakeAct1.ActParam.FusedClamp[i].Max : (fakeAct1.ActParam.FusedClamp[i].Max * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                    var a1Max = double.IsNegativeInfinity(fakeAct1.ActParam.FusedClamp[i].Min) ? -fakeAct1.ActParam.FusedClamp[i].Min : (fakeAct1.ActParam.FusedClamp[i].Min * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                    actFoldParam.FusedClamp[i] = new ValueRange<Half>
                    {
                        Min = a1Min > fakeAct2.ActParam.FusedClamp[i].Min ? (Half)a1Min : (Half)fakeAct2.ActParam.FusedClamp[i].Min,
                        Max = a1Max < fakeAct2.ActParam.FusedClamp[i].Max ? (Half)a1Max : (Half)fakeAct2.ActParam.FusedClamp[i].Max,
                    };
                    if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                    {
                        return null;
                    }
                }
                else
                {
                    actFoldParam.FusedClamp[i] = new ValueRange<Half>
                    {
                        Min = (Half)fakeAct2.ActParam.FusedClamp[i].Min,
                        Max = (Half)fakeAct2.ActParam.FusedClamp[i].Max,
                    };
                }
            }

            // case2
            else if (act2.Value.Cast<float>()[i, 0] <= fakeAct1.ActParam.FusedClamp[i].Min)
            {
                if (act2.Value.Cast<float>()[i, 1] > 0)
                {
                    float a1Max = double.IsPositiveInfinity(fakeAct1.ActParam.FusedClamp[i].Max) ? fakeAct1.ActParam.FusedClamp[i].Max : (fakeAct1.ActParam.FusedClamp[i].Max * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                    float a1Min = double.IsNegativeInfinity(fakeAct1.ActParam.FusedClamp[i].Min) ? fakeAct1.ActParam.FusedClamp[i].Min : (fakeAct1.ActParam.FusedClamp[i].Min * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                    actFoldParam.FusedClamp[i] = new ValueRange<Half>
                    {
                        Min = a1Min > fakeAct2.ActParam.FusedClamp[i].Min ? (Half)a1Min : (Half)fakeAct2.ActParam.FusedClamp[i].Min,
                        Max = a1Max < fakeAct2.ActParam.FusedClamp[i].Max ? (Half)a1Max : (Half)fakeAct2.ActParam.FusedClamp[i].Max,
                    };
                    if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                    {
                        return null;
                    }
                }
                else if (act2.Value.Cast<float>()[i, 1] < 0)
                {
                    float a1Min = double.IsPositiveInfinity(fakeAct1.ActParam.FusedClamp[i].Max) ? -fakeAct1.ActParam.FusedClamp[i].Max : (fakeAct1.ActParam.FusedClamp[i].Max * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                    float a1Max = double.IsNegativeInfinity(fakeAct1.ActParam.FusedClamp[i].Min) ? -fakeAct1.ActParam.FusedClamp[i].Min : (fakeAct1.ActParam.FusedClamp[i].Min * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                    actFoldParam.FusedClamp[i] = new ValueRange<Half>
                    {
                        Min = a1Min > fakeAct2.ActParam.FusedClamp[i].Min ? (Half)a1Min : (Half)fakeAct2.ActParam.FusedClamp[i].Min,
                        Max = a1Max < fakeAct2.ActParam.FusedClamp[i].Max ? (Half)a1Max : (Half)fakeAct2.ActParam.FusedClamp[i].Max,
                    };
                    if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                    {
                        return null;
                    }
                }
                else
                {
                    actFoldParam.FusedClamp[i] = new ValueRange<Half>
                    {
                        Min = (Half)fakeAct2.ActParam.FusedClamp[i].Min,
                        Max = (Half)fakeAct2.ActParam.FusedClamp[i].Max,
                    };
                }
            }

            // case3
            else
            {
                if (act2.Value.Cast<float>()[i, 1] > 0 && act2.Value.Cast<float>()[i, 2] > 0)
                {
                    var a1Max = double.IsPositiveInfinity(fakeAct1.ActParam.FusedClamp[i].Max) ? fakeAct1.ActParam.FusedClamp[i].Max : (fakeAct1.ActParam.FusedClamp[i].Max * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                    var a1Min = double.IsNegativeInfinity(fakeAct1.ActParam.FusedClamp[i].Min) ? fakeAct1.ActParam.FusedClamp[i].Min : (fakeAct1.ActParam.FusedClamp[i].Min * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                    actFoldParam.FusedClamp[i] = new ValueRange<Half>
                    {
                        Min = a1Min > fakeAct2.ActParam.FusedClamp[i].Min ? (Half)a1Min : (Half)fakeAct2.ActParam.FusedClamp[i].Min,
                        Max = a1Max < fakeAct2.ActParam.FusedClamp[i].Max ? (Half)a1Max : (Half)fakeAct2.ActParam.FusedClamp[i].Max,
                    };
                    if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                    {
                        return null;
                    }
                }
                else if (act2.Value.Cast<float>()[i, 1] < 0 && act2.Value.Cast<float>()[i, 2] < 0)
                {
                    var a1Min = double.IsPositiveInfinity(fakeAct1.ActParam.FusedClamp[i].Max) ? -fakeAct1.ActParam.FusedClamp[i].Max : (fakeAct1.ActParam.FusedClamp[i].Max * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                    var a1Max = double.IsNegativeInfinity(fakeAct1.ActParam.FusedClamp[i].Min) ? -fakeAct1.ActParam.FusedClamp[i].Min : (fakeAct1.ActParam.FusedClamp[i].Min * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                    actFoldParam.FusedClamp[i] = new ValueRange<Half>
                    {
                        Min = a1Min > fakeAct2.ActParam.FusedClamp[i].Min ? (Half)a1Min : (Half)fakeAct2.ActParam.FusedClamp[i].Min,
                        Max = a1Max < fakeAct2.ActParam.FusedClamp[i].Max ? (Half)a1Max : (Half)fakeAct2.ActParam.FusedClamp[i].Max,
                    };
                    if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                    {
                        return null;
                    }
                }
                else if (act2.Value.Cast<float>()[i, 1] > 0 && act2.Value.Cast<float>()[i, 2] < 0)
                {
                    var a1Min1 = double.IsPositiveInfinity(fakeAct1.ActParam.FusedClamp[i].Max) ? -fakeAct1.ActParam.FusedClamp[i].Max : (fakeAct1.ActParam.FusedClamp[i].Max * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                    var a1Min2 = double.IsNegativeInfinity(fakeAct1.ActParam.FusedClamp[i].Min) ? fakeAct1.ActParam.FusedClamp[i].Min : (fakeAct1.ActParam.FusedClamp[i].Min * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                    var a1Min = a1Min1 > a1Min2 ? a1Min1 : a1Min2;
                    actFoldParam.FusedClamp[i] = new ValueRange<Half>
                    {
                        Min = a1Min > fakeAct2.ActParam.FusedClamp[i].Min ? (Half)a1Min : (Half)fakeAct2.ActParam.FusedClamp[i].Min,
                        Max = (Half)fakeAct2.ActParam.FusedClamp[i].Max,
                    };
                    if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                    {
                        return null;
                    }
                }
                else if (act2.Value.Cast<float>()[i, 1] < 0 && act2.Value.Cast<float>()[i, 2] > 0)
                {
                    var a1Max1 = double.IsPositiveInfinity(fakeAct1.ActParam.FusedClamp[i].Max) ? fakeAct1.ActParam.FusedClamp[i].Max : (fakeAct1.ActParam.FusedClamp[i].Max * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                    var a1Max2 = double.IsNegativeInfinity(fakeAct1.ActParam.FusedClamp[i].Min) ? -fakeAct1.ActParam.FusedClamp[i].Min : (fakeAct1.ActParam.FusedClamp[i].Min * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                    var a1Max = a1Max1 > a1Max2 ? a1Max2 : a1Max1;
                    actFoldParam.FusedClamp[i] = new ValueRange<Half>
                    {
                        Min = (Half)fakeAct2.ActParam.FusedClamp[i].Min,
                        Max = a1Max < fakeAct2.ActParam.FusedClamp[i].Max ? (Half)a1Max : (Half)fakeAct2.ActParam.FusedClamp[i].Max,
                    };
                    if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                    {
                        return null;
                    }
                }
                else if (act2.Value.Cast<float>()[i, 1] > 0 && act2.Value.Cast<float>()[i, 2] == 0)
                {
                    var a1Min = double.IsNegativeInfinity(fakeAct1.ActParam.FusedClamp[i].Min) ? fakeAct1.ActParam.FusedClamp[i].Min : (fakeAct1.ActParam.FusedClamp[i].Min * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                    actFoldParam.FusedClamp[i] = new ValueRange<Half>
                    {
                        Min = a1Min > fakeAct2.ActParam.FusedClamp[i].Min ? (Half)a1Min : (Half)fakeAct2.ActParam.FusedClamp[i].Min,
                        Max = (Half)fakeAct2.ActParam.FusedClamp[i].Max,
                    };
                    if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                    {
                        return null;
                    }
                }
                else if (act2.Value.Cast<float>()[i, 1] == 0 && act2.Value.Cast<float>()[i, 2] > 0)
                {
                    var a1Max = double.IsPositiveInfinity(fakeAct1.ActParam.FusedClamp[i].Max) ? fakeAct1.ActParam.FusedClamp[i].Max : (fakeAct1.ActParam.FusedClamp[i].Max * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                    actFoldParam.FusedClamp[i] = new ValueRange<Half>
                    {
                        Min = (Half)fakeAct2.ActParam.FusedClamp[i].Min,
                        Max = a1Max < fakeAct2.ActParam.FusedClamp[i].Max ? (Half)a1Max : (Half)fakeAct2.ActParam.FusedClamp[i].Max,
                    };
                    if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                    {
                        return null;
                    }
                }
                else if (act2.Value.Cast<float>()[i, 1] < 0 && act2.Value.Cast<float>()[i, 2] == 0)
                {
                    var a1Max = double.IsNegativeInfinity(fakeAct1.ActParam.FusedClamp[i].Min) ? -fakeAct1.ActParam.FusedClamp[i].Min : (fakeAct1.ActParam.FusedClamp[i].Min * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                    actFoldParam.FusedClamp[i] = new ValueRange<Half>
                    {
                        Min = (Half)fakeAct2.ActParam.FusedClamp[i].Min,
                        Max = a1Max < fakeAct2.ActParam.FusedClamp[i].Max ? (Half)a1Max : (Half)fakeAct2.ActParam.FusedClamp[i].Max,
                    };
                    if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                    {
                        return null;
                    }
                }
                else if (act2.Value.Cast<float>()[i, 1] == 0 && act2.Value.Cast<float>()[i, 2] < 0)
                {
                    var a1Min = double.IsPositiveInfinity(fakeAct1.ActParam.FusedClamp[i].Max) ? -fakeAct1.ActParam.FusedClamp[i].Max : (fakeAct1.ActParam.FusedClamp[i].Max * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                    actFoldParam.FusedClamp[i] = new ValueRange<Half>
                    {
                        Min = a1Min > fakeAct2.ActParam.FusedClamp[i].Min ? (Half)a1Min : (Half)fakeAct2.ActParam.FusedClamp[i].Min,
                        Max = (Half)fakeAct2.ActParam.FusedClamp[i].Max,
                    };
                    if (actFoldParam.FusedClamp[i].Max < actFoldParam.FusedClamp[i].Min)
                    {
                        return null;
                    }
                }
                else if (act2.Value.Cast<float>()[i, 1] == 0 && act2.Value.Cast<float>()[i, 2] == 0)
                {
                    actFoldParam.FusedClamp[i] = new ValueRange<Half>
                    {
                        Min = (Half)fakeAct2.ActParam.FusedClamp[i].Min,
                        Max = (Half)fakeAct2.ActParam.FusedClamp[i].Max,
                    };
                }
            }
        }

        for (int i = 0; i < outchannels1.Value.ToScalar<int>(); i++)
        {
            if (act1.Value.Cast<float>()[i, 1] > 0 && act1.Value.Cast<float>()[i, 2] > 0)
            {
                if (act1.Value.Cast<float>()[i, 0] != 0 && Math.Abs(act2.Value.Cast<float>()[i, 0] - ((act1.Value.Cast<float>()[i, 0] * act1.Value.Cast<float>()[i, 1]) + act1.Value.Cast<float>()[i, 3])) > float.Epsilon)
                {
                    return null;
                }
                else
                {
                    {
                        if (act1.Value.Cast<float>()[i, 3] > act2.Value.Cast<float>()[i, 0] ||
                            act1.Value.Cast<float>()[i, 4] < act2.Value.Cast<float>()[i, 0])
                        {
                            if ((act2.Value.Cast<float>()[i, 1] != act2.Value.Cast<float>()[i, 2] || act2.Value.Cast<float>()[i, 3] != act2.Value.Cast<float>()[i, 4]) &&
                                (act1.Value.Cast<float>()[i, 1] != act1.Value.Cast<float>()[i, 2] || act1.Value.Cast<float>()[i, 3] != act1.Value.Cast<float>()[i, 4]))
                            {
                                return null;
                            }
                        }

                        if (fakeAct1.ActParam.FusedClamp[i].Max < act2.Value.Cast<float>()[i, 0] ||
                            fakeAct1.ActParam.FusedClamp[i].Min > act2.Value.Cast<float>()[i, 0])
                        {
                            return null;
                        }

                        if (act1.Value.Cast<float>()[i, 1] == act1.Value.Cast<float>()[i, 2] && act1.Value.Cast<float>()[i, 3] == act1.Value.Cast<float>()[i, 4] &&
                            (act2.Value.Cast<float>()[i, 1] != act2.Value.Cast<float>()[i, 2] || act2.Value.Cast<float>()[i, 3] != act2.Value.Cast<float>()[i, 4]))
                        {
                            actFoldParam.Ks[0, i] = act1.Value.Cast<float>()[i, 1] * act2.Value.Cast<float>()[i, 1];
                            actFoldParam.Ks[1, i] = act1.Value.Cast<float>()[i, 2] * act2.Value.Cast<float>()[i, 2];
                            actFoldParam.Bs[0, i] = (act1.Value.Cast<float>()[i, 3] * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                            actFoldParam.Bs[1, i] = (act1.Value.Cast<float>()[i, 4] * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                            actFoldParam.Xs[0, i] = (act2.Value.Cast<float>()[i, 0] - act1.Value.Cast<float>()[i, 3]) / act1.Value.Cast<float>()[i, 1];
                        }
                        else
                        {
                            actFoldParam.Ks[0, i] = act1.Value.Cast<float>()[i, 1] * act2.Value.Cast<float>()[i, 1];
                            actFoldParam.Ks[1, i] = act1.Value.Cast<float>()[i, 2] * act2.Value.Cast<float>()[i, 2];
                            actFoldParam.Bs[0, i] = (act1.Value.Cast<float>()[i, 3] * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                            actFoldParam.Bs[1, i] = (act1.Value.Cast<float>()[i, 4] * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                            actFoldParam.Xs[0, i] = act1.Value.Cast<float>()[i, 0];
                        }
                    }
                }
            }
            else if (act1.Value.Cast<float>()[i, 1] < 0 && act1.Value.Cast<float>()[i, 2] < 0)
            {
                {
                    if (act1.Value.Cast<float>()[i, 0] != 0 && Math.Abs(act2.Value.Cast<float>()[i, 0] - ((act1.Value.Cast<float>()[i, 0] * act1.Value.Cast<float>()[i, 1]) + act1.Value.Cast<float>()[i, 3])) > float.Epsilon)
                    {
                        return null;
                    }
                    else
                    {
                        if (act1.Value.Cast<float>()[i, 3] < act2.Value.Cast<float>()[i, 0] ||
                            act1.Value.Cast<float>()[i, 4] > act2.Value.Cast<float>()[i, 0])
                        {
                            if ((act2.Value.Cast<float>()[i, 1] != act2.Value.Cast<float>()[i, 2] || act2.Value.Cast<float>()[i, 3] != act2.Value.Cast<float>()[i, 4]) &&
                                (act1.Value.Cast<float>()[i, 1] != act1.Value.Cast<float>()[i, 2] || act1.Value.Cast<float>()[i, 3] != act1.Value.Cast<float>()[i, 4]))
                            {
                                return null;
                            }
                        }

                        if (fakeAct1.ActParam.FusedClamp[i].Max < act2.Value.Cast<float>()[i, 0] ||
                            fakeAct1.ActParam.FusedClamp[i].Min > act2.Value.Cast<float>()[i, 0])
                        {
                            return null;
                        }

                        if (act1.Value.Cast<float>()[i, 1] == act1.Value.Cast<float>()[i, 2] && act1.Value.Cast<float>()[i, 3] == act1.Value.Cast<float>()[i, 4] &&
                            (act2.Value.Cast<float>()[i, 1] != act2.Value.Cast<float>()[i, 2] || act2.Value.Cast<float>()[i, 3] != act2.Value.Cast<float>()[i, 4]))
                        {
                            actFoldParam.Ks[0, i] = act1.Value.Cast<float>()[i, 1] * act2.Value.Cast<float>()[i, 2];
                            actFoldParam.Ks[1, i] = act1.Value.Cast<float>()[i, 2] * act2.Value.Cast<float>()[i, 1];
                            actFoldParam.Bs[0, i] = (act1.Value.Cast<float>()[i, 3] * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                            actFoldParam.Bs[1, i] = (act1.Value.Cast<float>()[i, 4] * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                            actFoldParam.Xs[0, i] = (act2.Value.Cast<float>()[i, 0] - act1.Value.Cast<float>()[i, 3]) / act1.Value.Cast<float>()[i, 1];
                        }
                        else
                        {
                            actFoldParam.Ks[0, i] = act1.Value.Cast<float>()[i, 1] * act2.Value.Cast<float>()[i, 1];
                            actFoldParam.Ks[1, i] = act1.Value.Cast<float>()[i, 2] * act2.Value.Cast<float>()[i, 2];
                            actFoldParam.Bs[0, i] = (act1.Value.Cast<float>()[i, 3] * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                            actFoldParam.Bs[1, i] = (act1.Value.Cast<float>()[i, 4] * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                            actFoldParam.Xs[0, i] = act1.Value.Cast<float>()[i, 0];
                        }
                    }
                }
            }
            else if (act1.Value.Cast<float>()[i, 1] == 0 && act1.Value.Cast<float>()[i, 2] > 0)
            {
                // below if branch is max-min special case.
                if (act2.Value.Cast<float>()[i, 0] > act1.Value.Cast<float>()[i, 0] && act1.Value.Cast<float>()[i, 1] == 0 && act1.Value.Cast<float>()[i, 2] == 1 &&
                    act1.Value.Cast<float>()[i, 4] == 0 && act2.Value.Cast<float>()[i, 1] == 0 && act2.Value.Cast<float>()[i, 2] == 1 && act2.Value.Cast<float>()[i, 3] == 0)
                {
                    actFoldParam.Ks[0, i] = 1.0f;
                    actFoldParam.Ks[1, i] = 0.0f;
                    actFoldParam.Bs[0, i] = 0.0f;
                    actFoldParam.Bs[1, i] = act2.Value.Cast<float>()[i, 0];
                    actFoldParam.Xs[0, i] = act2.Value.Cast<float>()[i, 0];
                    actFoldParam.FusedClamp[i].Min = (Half)act1.Value.Cast<float>()[i, 0];
                    actFoldParam.FusedClamp[i].Max = (Half)act2.Value.Cast<float>()[i, 0];
                }
                else
                {
                    if (act1.Value.Cast<float>()[i, 3] >= act2.Value.Cast<float>()[i, 0] || act1.Value.Cast<float>()[i, 4] + (act1.Value.Cast<float>()[i, 0] * act1.Value.Cast<float>()[i, 2]) <= act2.Value.Cast<float>()[i, 0])
                    {
                        if (act2.Value.Cast<float>()[i, 1] != act2.Value.Cast<float>()[i, 2] || act2.Value.Cast<float>()[i, 3] != act2.Value.Cast<float>()[i, 4])
                        {
                            return null;
                        }
                    }

                    if (fakeAct1.ActParam.FusedClamp[i].Max < act2.Value.Cast<float>()[i, 0] ||
                        fakeAct1.ActParam.FusedClamp[i].Min > act2.Value.Cast<float>()[i, 0])
                    {
                        return null;
                    }

                    actFoldParam.Ks[0, i] = 0.0f;
                    actFoldParam.Ks[1, i] = act1.Value.Cast<float>()[i, 2] * act2.Value.Cast<float>()[i, 2];
                    actFoldParam.Bs[0, i] = (act1.Value.Cast<float>()[i, 3] * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                    actFoldParam.Bs[1, i] = (act1.Value.Cast<float>()[i, 4] * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                    actFoldParam.Xs[0, i] = act1.Value.Cast<float>()[i, 0];
                }
            }
            else if (act1.Value.Cast<float>()[i, 1] > 0 && act1.Value.Cast<float>()[i, 2] == 0)
            {
                // below if branch is max-min special case.
                if (act2.Value.Cast<float>()[i, 0] < act1.Value.Cast<float>()[i, 0] &&
                    act1.Value.Cast<float>()[i, 1] == 1 && act1.Value.Cast<float>()[i, 2] == 0 &&
                    act1.Value.Cast<float>()[i, 3] == 0 && act2.Value.Cast<float>()[i, 1] == 0 &&
                    act2.Value.Cast<float>()[i, 2] == 1 && act2.Value.Cast<float>()[i, 4] == 0)
                {
                    actFoldParam.Ks[0, i] = 1.0f;
                    actFoldParam.Ks[1, i] = 0.0f;
                    actFoldParam.Bs[0, i] = 0.0f;
                    actFoldParam.Bs[1, i] = act1.Value.Cast<float>()[i, 0];
                    actFoldParam.Xs[0, i] = act1.Value.Cast<float>()[i, 0];
                    actFoldParam.FusedClamp[i].Min = (Half)act2.Value.Cast<float>()[i, 0];
                    actFoldParam.FusedClamp[i].Max = (Half)act1.Value.Cast<float>()[i, 0];
                }
                else
                {
                    if (act1.Value.Cast<float>()[i, 3] + (act1.Value.Cast<float>()[i, 0] * act1.Value.Cast<float>()[i, 1]) >= act2.Value.Cast<float>()[i, 0] || act1.Value.Cast<float>()[i, 4] <= act2.Value.Cast<float>()[i, 0])
                    {
                        if (act2.Value.Cast<float>()[i, 1] != act2.Value.Cast<float>()[i, 2] || act2.Value.Cast<float>()[i, 3] != act2.Value.Cast<float>()[i, 4])
                        {
                            return null;
                        }
                    }

                    if (fakeAct1.ActParam.FusedClamp[i].Max < act2.Value.Cast<float>()[i, 0] || fakeAct1.ActParam.FusedClamp[i].Min > act2.Value.Cast<float>()[i, 0])
                    {
                        return null;
                    }

                    actFoldParam.Ks[0, i] = act1.Value.Cast<float>()[i, 1] * act2.Value.Cast<float>()[i, 1];
                    actFoldParam.Ks[1, i] = 0.0f;
                    actFoldParam.Bs[0, i] = (act1.Value.Cast<float>()[i, 3] * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                    actFoldParam.Bs[1, i] = (act1.Value.Cast<float>()[i, 4] * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                    actFoldParam.Xs[0, i] = act1.Value.Cast<float>()[i, 0];
                }
            }
            else if (act1.Value.Cast<float>()[i, 1] == 0 && act1.Value.Cast<float>()[i, 2] < 0)
            {
                // below if branch is max-min special case.
                if (act2.Value.Cast<float>()[i, 0] > act1.Value.Cast<float>()[i, 0] && act1.Value.Cast<float>()[i, 1] == 0 && act1.Value.Cast<float>()[i, 2] == -1 &&
                    act1.Value.Cast<float>()[i, 4] == 0 && act2.Value.Cast<float>()[i, 1] == -1 && act2.Value.Cast<float>()[i, 2] == 0 && act2.Value.Cast<float>()[i, 3] == 0)
                {
                    actFoldParam.Ks[0, i] = 1.0f;
                    actFoldParam.Ks[1, i] = -1.0f;
                    actFoldParam.Bs[0, i] = act1.Value.Cast<float>()[i, 0];
                    actFoldParam.Bs[1, i] = 0.0f;
                    actFoldParam.Xs[0, i] = act1.Value.Cast<float>()[i, 0];
                    actFoldParam.FusedClamp[i].Min = (Half)(-act2.Value.Cast<float>()[i, 0]);
                    actFoldParam.FusedClamp[i].Max = (Half)(-act1.Value.Cast<float>()[i, 0]);
                }
                else
                {
                    if (act1.Value.Cast<float>()[i, 3] <= act2.Value.Cast<float>()[i, 0] || act1.Value.Cast<float>()[i, 4] - (act1.Value.Cast<float>()[i, 0] * act1.Value.Cast<float>()[i, 2]) >= act2.Value.Cast<float>()[i, 0])
                    {
                        if (act2.Value.Cast<float>()[i, 1] != act2.Value.Cast<float>()[i, 2] || act2.Value.Cast<float>()[i, 3] != act2.Value.Cast<float>()[i, 4])
                        {
                            return null;
                        }
                    }

                    if (fakeAct1.ActParam.FusedClamp[i].Max < act2.Value.Cast<float>()[i, 0] || fakeAct1.ActParam.FusedClamp[i].Min > act2.Value.Cast<float>()[i, 0])
                    {
                        return null;
                    }

                    actFoldParam.Ks[0, i] = 0.0f;
                    actFoldParam.Ks[1, i] = act1.Value.Cast<float>()[i, 2] * act2.Value.Cast<float>()[i, 1];
                    actFoldParam.Bs[0, i] = (act1.Value.Cast<float>()[i, 3] * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                    actFoldParam.Bs[1, i] = (act1.Value.Cast<float>()[i, 4] * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                    actFoldParam.Xs[0, i] = act1.Value.Cast<float>()[i, 0];
                }
            }
            else if (act1.Value.Cast<float>()[i, 1] < 0 && act1.Value.Cast<float>()[i, 2] == 0)
            {
                // below if branch is neg min-neg max special case.
                if (act2.Value.Cast<float>()[i, 0] < act1.Value.Cast<float>()[i, 0] && act1.Value.Cast<float>()[i, 1] == -1 && act1.Value.Cast<float>()[i, 2] == 0 &&
                    act1.Value.Cast<float>()[i, 3] == 0 && act2.Value.Cast<float>()[i, 1] == 0 && act2.Value.Cast<float>()[i, 2] == -1 && act2.Value.Cast<float>()[i, 4] == 0)
                {
                    actFoldParam.Ks[0, i] = 1.0f;
                    actFoldParam.Ks[1, i] = -1.0f;
                    actFoldParam.Bs[0, i] = -act1.Value.Cast<float>()[i, 0];
                    actFoldParam.Bs[1, i] = 0.0f;
                    actFoldParam.Xs[0, i] = act1.Value.Cast<float>()[i, 0];
                    actFoldParam.FusedClamp[i].Min = (Half)(-act1.Value.Cast<float>()[i, 0]);
                    actFoldParam.FusedClamp[i].Max = (Half)(-act2.Value.Cast<float>()[i, 0]);
                }
                else
                {
                    if (act1.Value.Cast<float>()[i, 3] - (act1.Value.Cast<float>()[i, 0] * act1.Value.Cast<float>()[i, 1]) <= act2.Value.Cast<float>()[i, 0] || act1.Value.Cast<float>()[i, 4] >= act2.Value.Cast<float>()[i, 0])
                    {
                        if (act2.Value.Cast<float>()[i, 1] != act2.Value.Cast<float>()[i, 2] || act2.Value.Cast<float>()[i, 3] != act2.Value.Cast<float>()[i, 4])
                        {
                            return null;
                        }
                    }

                    if (fakeAct1.ActParam.FusedClamp[i].Max < act2.Value.Cast<float>()[i, 0] || fakeAct1.ActParam.FusedClamp[i].Min > act2.Value.Cast<float>()[i, 0])
                    {
                        return null;
                    }

                    actFoldParam.Ks[0, i] = act1.Value.Cast<float>()[i, 1] * act2.Value.Cast<float>()[i, 2];
                    actFoldParam.Ks[1, i] = 0.0f;
                    actFoldParam.Bs[0, i] = (act1.Value.Cast<float>()[i, 3] * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                    actFoldParam.Bs[1, i] = (act1.Value.Cast<float>()[i, 4] * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                    actFoldParam.Xs[0, i] = act1.Value.Cast<float>()[i, 0];
                }
            }
            else if (act1.Value.Cast<float>()[i, 1] == 0 && act1.Value.Cast<float>()[i, 2] == 0)
            {
                if (act1.Value.Cast<float>()[i, 3] >= act2.Value.Cast<float>()[i, 0] || act1.Value.Cast<float>()[i, 4] <= act2.Value.Cast<float>()[i, 0])
                {
                    if (act2.Value.Cast<float>()[i, 1] != act2.Value.Cast<float>()[i, 2] || act2.Value.Cast<float>()[i, 3] != act2.Value.Cast<float>()[i, 4])
                    {
                        return null;
                    }
                }

                if (fakeAct1.ActParam.FusedClamp[i].Max <= act2.Value.Cast<float>()[i, 0] || fakeAct1.ActParam.FusedClamp[i].Min >= act2.Value.Cast<float>()[i, 0])
                {
                    return null;
                }

                actFoldParam.Ks[0, i] = 0.0f;
                actFoldParam.Ks[1, i] = 0.0f;
                actFoldParam.Bs[0, i] = (act1.Value.Cast<float>()[i, 3] * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                actFoldParam.Bs[1, i] = (act1.Value.Cast<float>()[i, 4] * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                actFoldParam.Xs[0, i] = act1.Value.Cast<float>()[i, 0];
            }
            else
            {
                if (act2.Value.Cast<float>()[i, 1] != act2.Value.Cast<float>()[i, 2] || act2.Value.Cast<float>()[i, 3] != act2.Value.Cast<float>()[i, 4])
                {
                    return null;
                }

                actFoldParam.Ks[0, i] = act1.Value.Cast<float>()[i, 1] * act2.Value.Cast<float>()[i, 1];
                actFoldParam.Ks[1, i] = act1.Value.Cast<float>()[i, 2] * act2.Value.Cast<float>()[i, 2];
                actFoldParam.Bs[0, i] = (act1.Value.Cast<float>()[i, 3] * act2.Value.Cast<float>()[i, 1]) + act2.Value.Cast<float>()[i, 3];
                actFoldParam.Bs[1, i] = (act1.Value.Cast<float>()[i, 4] * act2.Value.Cast<float>()[i, 2]) + act2.Value.Cast<float>()[i, 4];
                actFoldParam.Xs[0, i] = act1.Value.Cast<float>()[i, 0];
            }
        }

        var actParam = new ActParam2(oc1);

        for (int i = 0; i < outchannels1.Value.ToScalar<int>(); i++)
        {
            actParam.Ks[0, i] = actFoldParam.Ks[0, i];
            actParam.Ks[1, i] = actFoldParam.Ks[1, i];
            actParam.Bs[0, i] = actFoldParam.Bs[0, i];
            actParam.Bs[1, i] = actFoldParam.Bs[1, i];
            actParam.Xs[0, i] = actFoldParam.Xs[0, i];
            actParam.FusedClamp[i].Min = (float)actFoldParam.FusedClamp[i].Min;
            actParam.FusedClamp[i].Max = (float)actFoldParam.FusedClamp[i].Max;
        }

        var act = new Tensor<float>(actParam.GetAct1Data, new long[] { oc1, 7 });
        return FakeActivation(inputa, inputb, act, oc1, 0, 0, 0, false, fakeAct1.Type, actParam, call2.CheckedShape.ToValueArray().Select(x => (int)x).ToArray()).InheritMetaData(call2);
    }
}
