﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using static Nncase.Evaluator.K230Kernels;
using static Nncase.IR.F.Tensors;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.RNN;
using static Nncase.PatternMatch.F.Tensors;
using static Nncase.PatternMatch.Utility;
using LSTM = Nncase.IR.RNN.LSTM;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class ToFakeLSTM : RewriteRule<Pattern>
{
    public override Pattern Pattern { get; }
        = IsWrappedLSTM(
            IsLSTM(
                target_name: "lstm",
                call_name: "call",
                _ => true,
                IsRangeOfMarker("xMarker", IsWildcard("x"), IsConst("xRange")) with { TypePattern = HasFixedShape() },
                IsRangeOfMarker("wMarker", IsTensor<PERSON>onst("w"), IsConst("wRange")) with { TypePattern = HasFixedShape() },
                IsRangeOfMarker("rMarker", IsTensorConst("r"), IsConst("rRange")) with { TypePattern = HasFixedShape() },
                IsTensorConst("b"),
                IsTensorConst(),
                IsRangeOfMarker("initHMarker", IsWildcard("initH"), IsConst("initHRange")) with { TypePattern = HasFixedShape() },
                IsRangeOfMarker("initCMarker", IsWildcard("initC"), IsConst("initCRange")) with { TypePattern = HasFixedShape() },
                IsTensorConst(),
                IsTensorConst(),
                IsTensorConst(),
                IsTensorConst(),
                IsTensorConst(),
                IsTensorConst(),
                IsTensorConst("outputSize")),
            (t, i) => IsRangeOfMarker($"outputMarker_{i}", t, IsWildcard()));

    private Expr? GetReplace(LSTM lstm, Call call, Expr x, TensorConst w, TensorConst r, Tensor<float> b, Expr initH, Expr initC, int outputSize, Marker xMarker, Marker wMarker, Marker rMarker, Marker initHMarker, Marker initCMarker, Tensor<float> xRange, Tensor<float> wRange, Tensor<float> rRange, Tensor<float> initHRange, Tensor<float> initCRange, IMatchResult result)
    {
        // handle activation_parameter
        int channel = (int)(b.Shape[0].FixedValue * b.Shape[1].FixedValue / 2);
        var apXc = new ActParam2(channel, new QuantParam(0, 1));
        var apRc = new ActParam2(channel, new QuantParam(0, 1));

        // get bias data
        int numDirection = (int)((TensorType)((TupleType)call.CheckedType)[0]).Shape[1].FixedValue;
        int biasDataSize = (int)(ComputeSize(b.Shape) / 2 / numDirection);

        // var p = b;
        // b = b.ToArray();
        var vecXc = new List<float>();
        for (int i = 0; i < biasDataSize; i++)
        {
            // vec_xc[i] = b[i];
            vecXc.Add(b.ToArray()[i]);
        }

        if (lstm.Direction == LSTMDirection.Bidirectional)
        {
            for (int i = biasDataSize * 2; i < biasDataSize * 3; i++)
            {
                vecXc.Add(b.ToArray()[i]);
            }
        }

        float[] xcData = new float[vecXc.Count];

        Array.Copy(vecXc.ToArray(), 0, xcData, 0, xcData.Length);

        var vecRc = new List<float>();
        for (int i = biasDataSize; i < biasDataSize * 2; i++)
        {
            vecRc.Add(b.ToArray()[i]);
        }

        if (lstm.Direction == LSTMDirection.Bidirectional)
        {
            for (int i = biasDataSize * 3; i < biasDataSize * 4; i++)
            {
                vecRc.Add(b.ToArray()[i]);
            }
        }

        float[] rcData = new float[vecRc.Count];
        Array.Copy(vecRc.ToArray(), 0, rcData, 0, rcData.Length);

        for (int i = 0; i < xcData.Length; i++)
        {
            apXc.Bs[0, i] = xcData[i];
            apXc.Bs[1, i] = xcData[i];
        }

        for (int i = 0; i < rcData.Length; i++)
        {
            apRc.Bs[0, i] = rcData[i];
            apRc.Bs[1, i] = rcData[i];
        }

        var actParamFt = new ActParam16(1);
        var actParamGt = new ActParam16(1);

        var sigmoid = new ActFun();
        sigmoid.SplitPoint0 = -8f;
        sigmoid.SplitPoint14 = 8f;
        sigmoid.SplitPointCenter = 0f;
        sigmoid.CenterPoint = 7;
        sigmoid.MinParam = new List<float> { 0f, 0f };
        sigmoid.MaxParam = new List<float> { 0f, 1f };
        sigmoid.Func = i => 1f / (MathF.Exp(-i) + 1f);

        var tanh1 = new ActFun();
        tanh1.SplitPoint0 = -4f;
        tanh1.SplitPoint14 = 4f;
        tanh1.SplitPointCenter = 0f;
        tanh1.CenterPoint = 7;
        tanh1.MinParam = new List<float> { 0f, -1f };
        tanh1.MaxParam = new List<float> { 0f, 1f };
        tanh1.Func = i => MathF.Tanh(i);

        SetSegFittingParamSigmoid(actParamFt, sigmoid);
        SetSegFittingParamTanh(actParamGt, tanh1);

        long[] inputShape = x.CheckedShape.ToValueArray();
        long[] xShape = new long[] { 1, 1, 1, 1 };
        Array.Copy(inputShape, 0, xShape, 1, 3);
        var newInput = IR.F.Math.RangeOfMarker(Reshape(x, xShape), xRange).With(adaQuantInfo: xMarker.AdaQuantInfo, mixQuantInfo: xMarker.MixQuantInfo);
        var wShape = new long[] { 1, 1, 1, 1 };
        Array.Copy(w.CheckedShape.ToValueArray(), 0, wShape, 1, 3);
        var newW = IR.F.Math.RangeOfMarker((Expr)Tensor.FromBytes(w.CheckedDataType, w.Value.BytesBuffer.ToArray(), wShape), wRange).With(adaQuantInfo: wMarker.AdaQuantInfo, mixQuantInfo: wMarker.MixQuantInfo);
        var rShape = new long[] { 1, 1, 1, 1 };
        Array.Copy(r.CheckedShape.ToValueArray(), 0, rShape, 1, 3);
        var newR = IR.F.Math.RangeOfMarker((Expr)Tensor.FromBytes(r.CheckedDataType, r.Value.BytesBuffer.ToArray(), rShape), rRange).With(adaQuantInfo: rMarker.AdaQuantInfo, mixQuantInfo: rMarker.MixQuantInfo);
        long[] initHShape = new long[] { 1, 1, 1, 1 };
        Array.Copy(initH.CheckedShape.ToValueArray(), 0, initHShape, 1, 3);
        Expr newInitH = initH switch
        {
            TensorConst { Value: { } t } => IR.F.Math.RangeOfMarker((Expr)Tensor.FromBytes(t.ElementType, t.BytesBuffer.ToArray(), initHShape), initHRange).With(adaQuantInfo: initHMarker.AdaQuantInfo, mixQuantInfo: ((Marker)initHMarker).MixQuantInfo),
            _ => IR.F.Math.RangeOfMarker(Reshape(initH, initHShape), initHRange).With(adaQuantInfo: initHMarker.AdaQuantInfo, mixQuantInfo: initHMarker.MixQuantInfo),
        };

        var initCShape = new long[] { 1, 1, 1, 1 };
        Array.Copy(initC.CheckedShape.ToValueArray(), 0, initCShape, 1, 3);
        Expr newInitC = initC switch
        {
            TensorConst { Value: { } t } => IR.F.Math.RangeOfMarker((Expr)Tensor.FromBytes(t.ElementType, t.BytesBuffer.ToArray(), initCShape), initCRange).With(adaQuantInfo: initCMarker.AdaQuantInfo, mixQuantInfo: ((Marker)initCMarker).MixQuantInfo),
            _ => IR.F.Math.RangeOfMarker(Reshape(initC, initCShape), initCRange).With(adaQuantInfo: initCMarker.AdaQuantInfo, mixQuantInfo: initCMarker.MixQuantInfo),
        };

        // var act_xc = new Tensor<float>(ap_xc.GetAct0Data, new[] { 1, 7 });
        // var act_rc = new Tensor<float>(ap_rc.GetAct0Data, new[] { 1, 7 });
        // var act_ft = new Tensor<float>(act_param_ft.GetAct1Data, new[] { 1, 7 });
        // var act_gt = new Tensor<float>(act_param_gt.GetAct1Data, new[] { 1, 7 });
        // var num = getLstm_direction(lstm.Direction) == GNNETypePatternUtility.lstm_direction.kBidirectional ? 2 : 1;
        var output = FakeLSTM(newInput, newW, new Tensor<float>(apXc.GetAct0Data, new[] { 1, 1, numDirection * w.CheckedShape[1].FixedValue, 7 }), newR, new Tensor<float>(apRc.GetAct0Data, new[] { 1, 1, numDirection * r.CheckedShape[1].FixedValue, 7 }), newInitH, newInitC, new Tensor<float>(actParamFt.GetAct1Data, new long[] { 1, 1, 1, 49 }), new Tensor<float>(actParamGt.GetAct1Data, new long[] { 1, 1, 1, 49 }), false, lstm.Direction, outputSize, apXc, apRc);

        IR.Tuple WrapOutput(Call call1, int outputSize1, Shape[] oldShapes)
        {
            var outputs = Enumerable
                .Range(0, outputSize1)
                .Select(i => GetItem(call1, i)).ToArray();
            var exprs = outputs.Select((item, i) => ((Marker)result[$"outputMarker_{i}"]).With(target: Reshape(item, oldShapes[i]))).ToArray();
            return new IR.Tuple(exprs);
        }

        var oldShapes = ((TupleType)call.CheckedType).Select(s => ((TensorType)s).Shape).ToArray();
        return (Expr)(BaseExpr)WrapOutput(output, outputSize, oldShapes);
    }

    /// <summary>
    /// Set_seg_fitting_param_sigmoid.
    /// </summary>
    private void SetSegFittingParamSigmoid(ActParam16 actParam, ActFun f)
    {
        var splitPoint = actParam.Xs;

        var sigmoidSeg = new[] { -7.0f, -4.5f, -3.5f, -2.7f, -2.1f, -1.6f, -1.0f, 0, 1.0f, 1.6f, 2.1f, 2.7f, 3.5f, 4.5f, 7f };
        for (int i = 0; i < 15; i++)
        {
            splitPoint[i, 0] = sigmoidSeg[i];
        }

        var ksBs = new[] { 0.0005523135475095087, 0.004717946782565874, 0.003582545941984816, 0.024643784893781717, 0.017628076952972527, 0.08920121475552378, 0.04080084671519202, 0.17057512199171598, 0.07504241042393778, 0.2641958959068108, 0.11550039574401527, 0.35039917518496155, 0.16589656476120918, 0.4312276071962273, 0.23123362875892262, 0.4955178069668943, 0.23398496370676491, 0.5031840614393268, 0.17066333504274322, 0.5625762717242175, 0.1197647477013204, 0.6417049229131112, 0.07822321089963047, 0.7281582013897308, 0.04270052410967129, 0.8235195920244173, 0.01849619693381177, 0.9073131477622514, 0.0037644096557241102, 0.9742926548134362, 0.000567715948649905, 0.9951637114353361 };
        for (int i = 0; i < 16; i++)
        {
            actParam.Ks[i, 0] = (float)ksBs[i * 2];
            actParam.Bs[i, 0] = (float)ksBs[(i * 2) + 1];
        }

        actParam.SetFusedClamp(new ValueRange<float>(0, 1));
    }

    private void SetSegFittingParamTanh(ActParam16 actParam, ActFun f)
    {
        float[,] splitPoint = actParam.Xs;
        float[] sigmoidSeg = new[] { -3.1f, -2.28f, -1.76f, -1.438f, -1.122f, -0.82f, -0.51f, 0, 0.47f, 0.81f, 1.125f, 1.432f, 1.77f, 2.28f, 3.1f };
        for (int i = 0; i < 15; i++)
        {
            splitPoint[i, 0] = sigmoidSeg[i];
        }

        var ksBs = new[] { 0.0009063486449397695, -0.995190713545316, 0.019181480050426303, -0.9381162870143267, 0.06880845134681457, -0.8250607603958839, 0.1518869708683811, -0.6772576184059398, 0.27007052179269864, -0.5091301547712312, 0.4374196151711459, -0.3220241963133208, 0.6540450071930701, -0.14398516081895907, 0.9192436825013623, -0.010453854050918476, 0.9412592709759997, 0.005250815043192469, 0.6730960939548211, 0.13033188197303736, 0.43741961517114625, 0.32202419631332024, 0.2700705217926983, 0.5091301547712318, 0.151886970868381, 0.6772576184059396, 0.0688084513468159, 0.8250607603958814, 0.01918148005042708, 0.9381162870143249, 0.0009063486449409908, 0.9951907135453116 };
        for (int i = 0; i < 16; i++)
        {
            actParam.Ks[i, 0] = (float)ksBs[i * 2];
            actParam.Bs[i, 0] = (float)ksBs[(i * 2) + 1];
        }

        actParam.SetFusedClamp(new ValueRange<float>(-1f, 1f));
    }

    private void SetSegFittingParam(ActParam16 actParam, ActFun f)
    {
        var splitPoint = actParam.Xs;
        var channel = 0;
        splitPoint[0, channel] = f.SplitPoint0;
        splitPoint[14, channel] = f.SplitPoint14;
        int centerPoint = f.CenterPoint;
        splitPoint[centerPoint, channel] = f.SplitPointCenter;
        for (int i = 1; i < centerPoint; i++)
        {
            splitPoint[i, channel] =
                ((splitPoint[centerPoint, channel] - splitPoint[0, channel]) / centerPoint * i) +
                splitPoint[0, channel];
        }

        for (int i = centerPoint + 1; i < 15; i++)
        {
            splitPoint[i, channel] =
                ((splitPoint[14, channel] - splitPoint[centerPoint, channel]) / (14 - centerPoint) *
                 (i - centerPoint)) + splitPoint[centerPoint, channel];
        }

        actParam.Ks[0, channel] = f.MinParam[0];
        actParam.Bs[0, channel] = f.MinParam[1];
        actParam.Ks[15, channel] = f.MaxParam[0];
        actParam.Bs[15, channel] = f.MaxParam[1];
        for (int i = 1; i < 15; i++)
        {
            float scale = (f.Func(splitPoint[i, channel]) - f.Func(splitPoint[i - 1, channel])) /
                          (splitPoint[i, channel] - splitPoint[i - 1, channel]);
            float bias = f.Func(splitPoint[i, channel]) - (scale * splitPoint[i, channel]);
            actParam.Ks[i, channel] = scale;
            actParam.Bs[i, channel] = bias;
        }

        actParam.SetFusedClamp(ValueRange<float>.Full);
    }
}

public partial class ActFun
{
    private ActFuncType _func = null!;
    private float _splitPoint0;
    private float _splitPoint14;
    private float _splitPointCenter;
    private int _centerPoint;
    private List<float> _minParam = null!;
    private List<float> _maxParam = null!;

    public delegate float ActFuncType(float x);

    public ActFuncType Func
    {
        get => _func;
        set => _func = value ?? throw new ArgumentNullException(nameof(value));
    }

    public float SplitPoint0
    {
        get => _splitPoint0;
        set => _splitPoint0 = value;
    }

    public float SplitPoint14
    {
        get => _splitPoint14;
        set => _splitPoint14 = value;
    }

    public float SplitPointCenter
    {
        get => _splitPointCenter;
        set => _splitPointCenter = value;
    }

    public int CenterPoint
    {
        get => _centerPoint;
        set => _centerPoint = value;
    }

    public List<float> MinParam
    {
        get => _minParam;
        set => _minParam = value ?? throw new ArgumentNullException(nameof(value));
    }

    public List<float> MaxParam
    {
        get => _maxParam;
        set => _maxParam = value ?? throw new ArgumentNullException(nameof(value));
    }
}
