// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using NetFabric.Hyperlinq;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.NN;
using Nncase.PatternMatch;
using static Nncase.IR.F.NN;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.NN;
using static Nncase.PatternMatch.F.Tensors;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class ReluToFakeActivation : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } =
        IsRelu("relu", "reluCall", _ => true, IsWildcard("input") with { TypePattern = HasFixedShape() });

    public Expr? GetReplace(Relu relu, Call reluCall, Expr input)
    {
        var newInAShape = new[] { 1, 1, 1, 1 };
        Array.Copy(reluCall.CheckedShape.ToValueArray(), 0, newInAShape, newInAShape.Length - reluCall.CheckedShape.Rank, reluCall.CheckedShape.Rank);
        int channels = newInAShape[1];
        var actParam = new ActParam2(channels, new(0, 1));
        for (int i = 0; i < actParam.Ks.GetLength(1); i++)
        {
            actParam.Ks[0, i] = 0;
            actParam.Bs[0, i] = 0;
        }

        var act = new Tensor<float>(actParam.GetAct1Data, new long[] { channels, 7 });
        return FakeActivation(input, None.Default, act, channels, 0, 0, 0, false, GnneActivationType.Uninitialized, actParam, reluCall.CheckedShape.ToValueArray().Select(x => (int)x).ToArray());
    }
}

[RuleGenerator]
public partial class FoldReluConcatWithConv2d : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    // conv   conv   conv          conv   conv   conv
    //   \     |     /               \     |     /
    //    \    |    /                 \    |    /
    //     \   |   /       ---->     relu relu relu
    //      \  |  /                     \  |  /
    //      concat                       \ | /
    //         |                          \|/
    //       relu                        concat
    public override Pattern Pattern { get; } = IsRangeOfMarker(
                                                                "reluMarker",
                                                                IsRelu(
                                                                       "relu",
                                                                       "reluCall",
                                                                       _ => true,
                                                                       IsRangeOfMarker(
                                                                                        "concatMarker",
                                                                                        IsConcat(
                                                                                                "concat",
                                                                                                "catCall",
                                                                                                _ => true,
                                                                                                IsTuple(
                                                                                                        IsVArgsRepeat(
                                                                                                                     "tupleInputs",
                                                                                                                     exprs =>
                                                                                                                        {
                                                                                                                            var patterns = new Pattern[exprs.Length];
                                                                                                                            for (var i = 0; i < patterns.Length; i++)
                                                                                                                            {
                                                                                                                                patterns[i] = IsRangeOfMarker($"convMarker{i}", IsWildcard($"conv2d{i}"), IsTensorConst($"convRange{i}"));
                                                                                                                            }

                                                                                                                            return patterns;
                                                                                                                        }))),
                                                                                        IsTensorConst("concatRange"))),
                                                                IsTensorConst("reluRange"));

    public Expr? GetReplace(Call reluCall, Expr reluMarker, Expr concatMarker, IReadOnlyList<BaseExpr> tupleInputs, Tensor<float> concatRange, IMatchResult matchResult, IR.Tensors.Concat concat, Tensor<float> reluRange)
    {
        bool isOnlyUsedByConv2D = tupleInputs.All(call => ((Call)((Marker)call).Target).Target is Conv2D);
        var convRanges = Enumerable.Range(0, tupleInputs.Count).Select(i => ((TensorConst)matchResult[$"convRange{i}"]).Value.Cast<float>(CastMode.KDefault)).ToArray();
        var convMarkers = Enumerable.Range(0, tupleInputs.Count).Select(i => ((Marker)matchResult[$"convMarker{i}"]).With(attribute: (Expr)Tensor.FromArray(new float[] { 0f, convRanges[i][1] })));
        var newTuple = new IR.Tuple(convMarkers.ToArray());
        var newConcat = IR.F.Tensors.Concat(newTuple, concat.Axis);
        var quantType = ((Marker)reluMarker).MixQuantInfo!.MarkerQuantType;
        if (isOnlyUsedByConv2D && reluCall.Target is Relu && quantType != DataTypes.Int16)
        {
            return IR.F.Math.RangeOfMarker(newConcat, reluRange).With(
                adaQuantInfo: ((Marker)reluMarker!).AdaQuantInfo,
                mixQuantInfo: ((Marker)reluMarker!).MixQuantInfo);
        }
        else
        {
            return null;
        }
    }
}
