// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using static Nncase.Passes.Utility;
using Fx = System.Func<Nncase.IR.BaseExpr, Nncase.IR.BaseExpr>;

namespace Nncase.Passes.Rules.K230;

public abstract class GNNEActLowerRule : GNNEDIFQuantRule
{
    public Fx WithTmpFloat(Fx inputCtor)
    {
        return WithTmpType(inputCtor, DataTypes.Float32);
    }
}
