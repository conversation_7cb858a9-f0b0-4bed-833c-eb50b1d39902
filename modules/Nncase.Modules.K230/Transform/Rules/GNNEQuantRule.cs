// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.Quantization;
using Nncase.Utilities;
using static Nncase.IR.F.Math;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.K230.GNNETypePatternUtility;
using static Nncase.Passes.Rules.GetReplaceHelper;
using static Nncase.Passes.Utility;
using Fx = System.Func<Nncase.IR.BaseExpr, Nncase.IR.BaseExpr>;

namespace Nncase.Passes.Rules.K230;

/// <summary>
/// the gnne quan rule
/// NOTE it will get the matchresult :
/// `input`
/// `output`
/// `input_range`
/// `output_range`.
/// </summary>
public abstract class GNNEQuantRule : QuantRule
{
    /// <summary>
    /// GNNEConv2D.
    /// </summary>
    private bool _isInt16Quant;

    /// <summary>
    /// Gets used for get input info
    /// NOTE becauseof the egraph match result can't get the input by expr it's self, so we have to specific the `input`.
    /// </summary>
    public Expr Input => (Expr)MatchResult!["input"];

    /// <summary>
    /// Gets saving old expr output, used for get output info
    /// e.g. get old OutputDataType for store.
    /// </summary>
    public Expr Output => (Expr)MatchResult!["output"];

    /// <summary>
    /// Gets get the InputRange.
    /// </summary>
    public ValueRange<float> InputRange { get; private set; }

    /// <summary>
    /// Gets get the OutputRange.
    /// </summary>
    public ValueRange<float> OutputRange { get; private set; }

    // public QuantMode qm => QuantType == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;
    public int QuantBits => QuantType == DataTypes.Int16 ? 12 : 8;

    public int WQuantBits => WQuantType == DataTypes.Int16 ? 12 : 8;

    public ValueRange<float> GetValueRange(string name)
    {
        float[] rg = ((TensorConst)MatchResult![name]).Value.ToArray<float>();
        return new ValueRange<float>(rg[0], rg[1]);
    }

    public override void Init()
    {
        InputRange = GetValueRange("inputRange");
        OutputRange = GetValueRange("outputRange");
    }

    /// <summary>
    /// store | store -> deq.
    /// </summary>
    public BaseExpr Store(BaseExpr callResult)
    {
        // if (callResult.CheckedType == null)
        // {
        //     if (!callResult.InferenceType())
        //     {
        //         CompilerServices.DumpIR(callResult, "Store_Falid", Option.DumpDir);
        //         throw new InvalidOperationException("After Replace By QuantRule Shapeinfer Falid");
        //     }
        // }
        // if (ModelQuantMode == ModelQuantMode.UsePTQ)
        // {
        //     //若需要的话后面再进一步完善
        //     var weightsType = isInt16Quant ? DataTypes.Int16 : DataTypes.Int8;
        //     var outQP = QuantUtility.GetQuantParam(OutputRange, QuantBits, QuantMode);
        //     // var stQuantParam = GNNEGetQuantParams(callResult.CheckedShape[1].FixedValue, outQP.Scale, outQP.ZeroPoint);
        //     var st = GNNEStoreQuant(QuantType, callResult);
        //     return st;
        //     // return Dequantize(st, outQP, weightsType);
        // }
        // else
        // {
        return GNNEStore(DataTypes.Int8, (Expr)callResult);

        // }
    }

    /// <summary>
    /// load | quant -> load.
    /// </summary>
    public BaseExpr Load(BaseExpr input)
    {
        if (input.CheckedType == null)
        {
            input.InferenceType();
        }

        _isInt16Quant = false;
        var ifType = _isInt16Quant ? DataTypes.Int16 : DataTypes.Int8;
        if (ModelQuantMode == ModelQuantMode.UsePTQ)
        {
            var quantMode = QuantType == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;
            var inQP = QuantUtility.GetQuantParam(InputRange, QuantBits, quantMode);
            var q = Quantize((Expr)input, inQP, QuantType);
            var deqParamFromQ = GNNEGetDeqParams((int)input.CheckedShape[1].FixedValue, inQP.Scale, inQP.ZeroPoint);

            // return GNNELoadIFDeq(ifType, q, GNNELoadW(ExtDataTypes.DeQuantParam, deqParamFromQ));
            return GNNELoadIFDeq(ifType, q);
        }
        else
        {
            // load输入输出类型只能为u8、i8、i16
            return GNNELoad(ifType, (Expr)input);
        }
    }

    public Expr LoadW(Expr weights, ValueRange<float> weightsRange)
    {
        if (ModelQuantMode == ModelQuantMode.UsePTQ)
        {
            var quantModeW = WQuantType == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;
            var inQP = QuantUtility.GetQuantParam(weightsRange, WQuantBits, quantModeW);
            var quantedWeights = Quantize(weights, inQP, WQuantType);
            return GNNELoadIFDeq(WQuantType, quantedWeights);
        }

        // loadw输入输出类型只能为u8、i8、i16
        return GNNELoadW(DataTypes.Int8, weights);
    }

    /// <summary>
    /// wrap load store for inputCtor.
    /// </summary>
    public Fx WithLoadStore(Fx inputCtor)
    {
        Fx WithLoadStoreImpl(Fx inCtor) => input =>
            Store(inCtor(Load(input)));

        return Apply(WithLoadStoreImpl, inputCtor);
    }

    /// <summary>
    /// wrap LoadStore and temp Reshape to 4dims(GNNEShape)
    /// reshape(GNNEStore(inputCtor(GNNELoad(Reshape(input of inputCtor, ToGNNEShape(inShape))))), oldShape).
    /// </summary>
    /// <param name="inputCtor">see Fx.</param>
    /// <returns>.</returns>
    public Fx WithFullLower(Fx inputCtor)
    {
        return WithTmpGNNEShape(WithLoadStore(inputCtor), Output.CheckedShape.ToValueArray().Select(x => (int)x).ToArray());
    }
}

/// <summary>
/// 单独实现GNNEDIF的QuantRule，应对两输入的情况.
/// </summary>
public abstract class GNNEDIFQuantRule : QuantRule
{
    /// <summary>
    /// Gets used for get input info
    /// NOTE becauseof the egraph match result can't get the input by expr it's self, so we have to specific the `input`.
    /// </summary>
    public Expr InputA => (Expr)MatchResult!["inputA"];

    public Expr InputB => (Expr)MatchResult!["inputB"];

    /// <summary>
    /// Gets saving old expr output, used for get output info
    /// e.g. get old OutputDataType for store.
    /// </summary>
    public Expr Output => (Expr)MatchResult!["output"];

    /// <summary>
    /// Gets get the InputRange.
    /// </summary>
    public ValueRange<float> InputARange { get; private set; }

    public ValueRange<float> InputBRange { get; private set; }

    public int QuantBits => QuantType == DataTypes.Int16 ? 12 : 8;

    public ValueRange<float> GetValueRange(string name)
    {
        if (MatchResult!.GetValueOrDefault(name) is TensorConst tc)
        {
            var rg = tc.Value.ToArray<float>();
            return new ValueRange<float>(rg[0], rg[1]);
        }

        return new ValueRange<float>(default, default);
    }

    public override void Init()
    {
        InputARange = GetValueRange("inputARange");
        InputBRange = GetValueRange("inputBRange");
    }

    /// <summary>
    /// store | store -> deq.
    /// </summary>
    public Expr Store(Expr callResult)
    {
        // if (callResult.CheckedType == null)
        // {
        //     if (!callResult.InferenceType())
        //     {
        //         CompilerServices.DumpIR(callResult, "Store_Falid", Option.DumpDir);
        //         throw new InvalidOperationException("After Replace By QuantRule Shapeinfer Falid");
        //     }
        // }
        // if (ModelQuantMode == ModelQuantMode.UsePTQ)
        // {
        //     //若需要的话后面再进一步完善
        //     var weightsType = isInt16Quant ? DataTypes.Int16 : DataTypes.Int8;
        //     var outQP = QuantUtility.GetQuantParam(OutputRange, QuantBits, QuantMode);
        //     // var stQuantParam = GNNEGetQuantParams(callResult.CheckedShape[1].FixedValue, outQP.Scale, outQP.ZeroPoint);
        //     var st = GNNEStoreQuant(QuantType, callResult);
        //     return st;
        //     // return Dequantize(st, outQP, weightsType);
        // }
        // else
        // {
        return GNNEStore(DataTypes.Int8, callResult);

        // }
    }

    /// <summary>
    /// load | quant -> load.
    /// </summary>
    public Expr Load(Expr input, string name)
    {
        if (input.CheckedType == null)
        {
            input.InferenceType();
        }

        if (ModelQuantMode == ModelQuantMode.UsePTQ)
        {
            var quantMode = QuantType == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;
            var inQP = QuantUtility.GetQuantParam(name == "inputA" ? InputARange : InputBRange, QuantBits, quantMode);
            var q = Quantize(input, inQP, QuantType);

            // var deqParamFromQ = GNNEGetDeqParams(input.CheckedShape[1].FixedValue, inQP.Scale, inQP.ZeroPoint);
            // return GNNELoadIFDeq(ifType, q, GNNELoadW(ExtDataTypes.DeQuantParam, deqParamFromQ));
            return GNNELoadIFDeq(QuantType, q);
        }
        else
        {
            // load输入输出类型只能为u8、i8、i16
            return GNNELoad((PrimType)QuantType, input);
        }
    }
}
