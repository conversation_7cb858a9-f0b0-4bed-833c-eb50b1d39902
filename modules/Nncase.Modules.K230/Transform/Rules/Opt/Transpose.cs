﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.PatternMatch;
using static Nncase.IR.F.Tensors;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Tensors;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

/// <summary>
/// Fold two <see cref="IR.Tensors.Transpose"/>.
/// </summary>
[RuleGenerator]
public sealed partial class FiveDimWithOneBatchTranspose : IRewriteRule
{
    /// <inheritdoc/>
    public IPattern Pattern { get; } = IsTranspose(null, "transpose", IsWildcard("input") with { TypePattern = HasFixedShape() }, IsWildcard("perm") with { TypePattern = HasRank(5) });

    private Expr? GetReplace(Call transpose, Expr input, TensorConst perm)
    {
        if (!OnTryMatch(input, perm))
        {
            return null;
        }

        var bcIn = Reshape(
            transpose,
            new[]
            {
                transpose.CheckedShape[0], transpose.CheckedShape[1], transpose.CheckedShape[2], transpose.CheckedShape[3],
            });
        var newPerm = new int[4];
        for (int i = 0; i < 4; i++)
        {
            newPerm[i] = perm.Value.Cast<int>()[i + 1] - 1;
        }

        var tr = Transpose(bcIn, newPerm);
        var bcOut = IR.F.Tensors.Reshape(tr, transpose.CheckedShape);
        return bcOut;
    }

    private bool OnTryMatch(Expr input, TensorConst perm)
    {
        if (input.CheckedShape.Rank == 5 && input.CheckedShape[0] == 1 && perm.Value.ToArray<int>()[0] == 1)
        {
            return true;
        }

        return false;
    }
}
