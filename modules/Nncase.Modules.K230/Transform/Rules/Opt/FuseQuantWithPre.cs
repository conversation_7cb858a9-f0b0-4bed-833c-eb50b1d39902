﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Math;
using Nncase.Passes.Analysis;
using Nncase.PatternMatch;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.K230.GNNETypePatternUtility;
using static Nncase.Passes.Rules.GetReplaceHelper;
using static Nncase.PatternMatch.F.Math;
using static Nncase.PatternMatch.Utility;
using static Nncase.Utilities.ReplaceUtility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class FuseQuantIntoConv : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } = FuseQuantHelper.MakePattern<GNNEConv2D>("convCall", "conv");

    private Expr? GetReplace(Quantize quant, QuantParam qp, Call st, Expr stStrides, GNNEConv2D conv, IReadOnlyList<BaseExpr> convCallParams, RunPassContext context)
    {
        var userAnalysis = context.GetAnalysis<IExprUserAnalysisResult>();
        if (userAnalysis[st].Count() > 1)
        {
            return null;
        }

        var actParam = FuseQuantHelper.FuseActAndQuant(quant, qp, conv.ActParam);
        return GNNEStore(
          quant.TargetType,
          ReplaceCallParams(
              conv.With(actParam: (ActParam2)actParam, destType: (PrimType)quant.TargetType),
              convCallParams,
              (IR.K230.GNNEConv2D.Act, LoadAct0((ActParam2)actParam))),
          stStrides);
    }
}

[RuleGenerator]
public sealed partial class FuseQuantIntoConvTranspose : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } = FuseQuantHelper.MakePattern<GNNEConv2DTranspose>("convCall", "conv");

    private Expr? GetReplace(Quantize quant, QuantParam qp, Call st, Expr stStrides, GNNEConv2DTranspose conv, IReadOnlyList<BaseExpr> convCallParams, RunPassContext context)
    {
        var userAnalysis = context.GetAnalysis<IExprUserAnalysisResult>();
        if (userAnalysis[st].Count() > 1)
        {
            return null;
        }

        var actParam = FuseQuantHelper.FuseActAndQuant(quant, qp, conv.ActParam);
        return GNNEStore(
            quant.TargetType,
            ReplaceCallParams(
                conv.With(actParam: (ActParam2)actParam, destType: (PrimType)quant.TargetType),
                convCallParams,
                (IR.K230.GNNEConv2DTranspose.Act, LoadAct0((ActParam2)actParam))),
            stStrides);
    }
}

[RuleGenerator]
public sealed partial class FuseQuantIntoPdp0Dw : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } = FuseQuantHelper.MakePattern<GNNEPdp0DW>("dwCall", "dw");

    private Expr? GetReplace(Quantize quant, QuantParam qp, Call st, Expr stStrides, GNNEPdp0DW dw, IReadOnlyList<BaseExpr> dwCallParams, RunPassContext context)
    {
        var userAnalysis = context.GetAnalysis<IExprUserAnalysisResult>();
        if (userAnalysis[st].Count() > 1)
        {
            return null;
        }

        var actParam = FuseQuantHelper.FuseActAndQuant(quant, qp, dw.ActParam);
        return GNNEStore(
          quant.TargetType,
          ReplaceCallParams(
              dw.With(actParam: (ActParam2)actParam, destType: (PrimType)quant.TargetType),
              dwCallParams,
              (IR.K230.GNNEPdp0DW.Act, LoadAct0((ActParam2)actParam))),
          stStrides);
    }
}

[RuleGenerator]
public sealed partial class FuseQuantIntoPdp0Reduce : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } = FuseQuantHelper.MakePattern<GNNEPdp0Reduce>("pdpCall", "pdp");

    private Expr? GetReplace(Quantize quant, QuantParam qp, Call st, Expr stStrides, GNNEPdp0Reduce pdp, IReadOnlyList<BaseExpr> pdpCallParams, RunPassContext context)
    {
        var userAnalysis = context.GetAnalysis<IExprUserAnalysisResult>();
        if (userAnalysis[st].Count() > 1)
        {
            return null;
        }

        var actParam = FuseQuantHelper.FuseActAndQuant(quant, qp, pdp.ActParam);
        return GNNEStore(
          quant.TargetType,
          ReplaceCallParams(
              pdp.With(actParam: (ActParam2)actParam, destType: (PrimType)quant.TargetType),
              pdpCallParams,
              (IR.K230.GNNEPdp0Reduce.Act, LoadAct0((ActParam2)actParam))),
          stStrides);
    }
}

[RuleGenerator]
public sealed partial class FuseQuantIntoPdp1 : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } = FuseQuantHelper.MakePattern<GNNEPdp1>("pdpCall", "pdp");

    private Expr? GetReplace(Quantize quant, QuantParam qp, Call st, Expr stStrides, Call pdpCall, GNNEPdp1 pdp, IReadOnlyList<BaseExpr> pdpCallParams, RunPassContext context)
    {
        var userAnalysis = context.GetAnalysis<IExprUserAnalysisResult>();

        if (userAnalysis[st].Count() > 1)
        {
            return null;
        }

        // var newQp = qp;
        // var outQP = QuantUtility.GetQuantParam(OutputRange, QuantBits, QuantMode);
        // qp.Scale = 1f / qp.Scale;
        var newQp = GNNEGetQuantParams(1, qp.Scale, qp.ZeroPoint);
        return GNNEStore(
          quant.TargetType,
          ReplaceCallParams(
            pdp.With(destType: (PrimType)quant.TargetType),
            pdpCallParams,
            (pdpCall[IR.K230.GNNEPdp1.QuantParams], (Expr)Tensor.FromArray(newQp))),
          stStrides);
    }
}

[RuleGenerator]
public sealed partial class FuseQuantIntoAct1 : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } = FuseQuantHelper.MakePattern<GNNEActivation>("actCall", "act");

    private Expr? GetReplace(Quantize quant, QuantParam qp, Call st, Expr stStrides, GNNEActivation act, IReadOnlyList<BaseExpr> actCallParams, RunPassContext context)
    {
        var userAnalysis = context.GetAnalysis<IExprUserAnalysisResult>();
        if (userAnalysis[st].Count() > 1)
        {
            return null;
        }

        // var newQp = qp;
        // var outQP = QuantUtility.GetQuantParam(OutputRange, QuantBits, QuantMode);
        // qp.Scale = 1f / qp.Scale;
        // var newQp = GNNEGetQuantParams(1, qp.Scale, qp.ZeroPoint);
        var actParam = act.ActParam.N == 2
            ? FuseQuantHelper.FuseActAndQuant(quant, qp, (ActParam2)act.ActParam)
            : FuseQuantHelper.FuseActAndQuant(quant, qp, (ActParam16)act.ActParam);
        return GNNEStore(
          quant.TargetType,
          ReplaceCallParams(
            act.With(actParam: actParam, outputDType: (PrimType)quant.TargetType),
            actCallParams,
            (IR.K230.GNNEActivation.Act, LoadAct1(actParam))),
          stStrides);
    }
}

internal static class FuseQuantHelper
{
    internal static Pattern MakePattern<T>(string callName, string opName)
        where T : Op
        => IsQuantize(
            "quant",
            "call",
            _ => true,
            IsCallWildcard(
                "st",
                IsOp<GNNEStore>("stOp"),
                IsCallWildcard(callName, IsOp<T>(opName), IsWildcard("input")),
                IsWildcard("stStrides")),
            IsWildcard("qp"));

    internal static ActParamBase FuseActAndQuant(Quantize quant, QuantParam qp, ActParam2 actParam)
    {
        var ret = new ActParam2(actParam);
        ret.FusedQuantParam(qp);
        var fusedClamp = FuseQuantHelper.GetFusedClamp(ret, quant.TargetType);
        ret.SetFusedClamp(fusedClamp);
        return ret;
    }

    internal static ActParamBase FuseActAndQuant(Quantize quant, QuantParam qp, ActParam16 actParam)
    {
        var ret = new ActParam16(actParam);
        ret.FusedQuantParam(qp);
        var fusedClamp = FuseQuantHelper.GetFusedClamp(ret, quant.TargetType);
        ret.SetFusedClamp(fusedClamp);
        return ret;
    }

    internal static ValueRange<float> GetFusedClamp(ActParamBase act_param, DataType quantType)
    {
        var clamp = ValueRange<float>.Full;
        if (quantType == DataTypes.UInt8)
        {
            for (var i = 0; i < act_param.FusedClamp.Length; i++)
            {
                clamp.Max = act_param.FusedClamp[i].Max > 255f ? 255f : act_param.FusedClamp[i].Max;
                clamp.Min = act_param.FusedClamp[i].Min < 0f ? 0f : act_param.FusedClamp[i].Min;
            }
        }
        else if (quantType == DataTypes.Int8)
        {
            for (var i = 0; i < act_param.FusedClamp.Length; i++)
            {
                clamp.Max = act_param.FusedClamp[i].Max > 127f ? 127f : act_param.FusedClamp[i].Max;
                clamp.Min = act_param.FusedClamp[i].Min < -127f ? -127f : act_param.FusedClamp[i].Min;
            }
        }
        else if (quantType == DataTypes.Int16)
        {
            for (var i = 0; i < act_param.FusedClamp.Length; i++)
            {
                clamp.Max = act_param.FusedClamp[i].Max > 2047f ? 2047f : act_param.FusedClamp[i].Max;
                clamp.Min = act_param.FusedClamp[i].Min < -2047f ? -2047f : act_param.FusedClamp[i].Min;
            }
        }

        return clamp;
    }
}
