﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.Tensors;
using Nncase.PatternMatch;
using static Nncase.IR.F.Tensors;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Tensors;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class TransposeCastMotion : IRewriteRule
{
    /// <inheritdoc />
    public IPattern Pattern { get; } =
        IsCast("cast", "castCall", _ => true, IsTranspose("tp", "tpCall", _ => true, IsWildcard("input") with { TypePattern = HasDataType(DataTypes.Boolean) }, IsTensorConst("perm")));

    private Expr? GetReplace(Expr input, int[] perm, Cast cast, Call castCall)
    {
        return Transpose(Cast(input, castCall.CheckedDataType, cast.CastMode), perm);
    }
}
