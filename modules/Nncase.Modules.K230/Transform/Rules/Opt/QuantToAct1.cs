﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Math;
using Nncase.PatternMatch;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.TypePatternUtility;
using static Nncase.Passes.Rules.GetReplaceHelper;
using static Nncase.PatternMatch.F.Math;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class QuantToAct1 : IRewriteRule
{
    /// <inheritdoc/>
    public IPattern Pattern { get; } =
        IsQuantize(
            "quant",
            "call",
            _ => true,
            IsWildcard("input") with { TypePattern = HasRank(r => r <= 4, "GNNE not support more than 4D") & HasFixedShape() },
            IsWildcard("qp"));

    private Expr? GetReplace(Call call, Quantize quant, Expr input, QuantParam qp)
    {
        var newInputShape = new[] { 1, 1, 1, 1 };
        Array.Copy(call.CheckedShape.ToValueArray(), 0, newInputShape, newInputShape.Length - call.CheckedShape.ToValueArray().Length, call.CheckedShape.Rank);
        var channels = newInputShape[1];

        var act = new ActParam2(channels);
        act.FusedQuantParam(qp);
        var fusedClamp = FuseQuantHelper.GetFusedClamp(act, quant.TargetType);
        act.SetFusedClamp(fusedClamp);

        return IR.F.Tensors.Reshape(
            GNNEStore(
                quant.TargetType,
                SimpleSingleInputGNNEAct(
                    LoadActIF(IR.F.Tensors.Reshape(input, newInputShape)),
                    act,
                    channels,
                    qp,
                    quant.TargetType,
                    act,
                    newInputShape)),
            call.CheckedShape);
    }
}

[RuleGenerator]
public sealed partial class DeqToAct1 : IRewriteRule
{
    /// <inheritdoc/>
    public IPattern Pattern { get; } =
        IsDequantize(
            "deq",
            "call",
            _ => true,
            IsWildcard("input") with { TypePattern = HasFixedShape() },
            IsWildcard("qp"));

    private Expr? GetReplace(Call call, Expr input, QuantParam qp)
    {
        var newInputShape = new[] { 1, 1, 1, 1 };
        Array.Copy(call.CheckedShape.ToValueArray(), 0, newInputShape, newInputShape.Length - call.CheckedShape.ToValueArray().Length, call.CheckedShape.Rank);
        var channels = newInputShape[1];

        var act = new ActParam2(channels);
        return IR.F.Tensors.Reshape(
            GNNEStore(
                DataTypes.Float32,
                GNNEActivation(
                GNNELoad((PrimType)input.CheckedDataType, IR.F.Tensors.Reshape(input, newInputShape)),
                None.Default,
                GNNELoadW(DataTypes.Float16, act.ToAct1Data()),
                0,
                0,
                0,
                Tensor.FromArray(new[] { new DeQuantizeParam(qp.ZeroPoint, qp.Scale) }),
                Tensor.FromArray(new[] { new DeQuantizeParam(qp.ZeroPoint, qp.Scale) }),
                channels,
                GnneActivationType.Uninitialized,
                false,
                DataTypes.Float16,
                act,
                newInputShape)),
            call.CheckedShape);
    }
}
