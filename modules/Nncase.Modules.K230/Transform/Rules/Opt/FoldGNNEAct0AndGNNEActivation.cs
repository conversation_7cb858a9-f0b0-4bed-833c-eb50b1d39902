﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.Passes.Analysis;
using Nncase.PatternMatch;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.Passes.Rules.GetReplaceHelper;
using static Nncase.PatternMatch.F.K230;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class FoldGNNEPdp0ReduceAndGNNEActivation : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } =
        IsGNNEActivation(target_name: "act", call_name: "call2", _ => true, IsGNNELoad(_ => true, IsGNNEStore("st", "stCall", _ => true, IsGNNEPdp0Reduce(target_name: "pdp0", call_name: "call1", _ => true, <PERSON><PERSON><PERSON><PERSON><PERSON>("input"), IsTensor<PERSON>onst("filter"), IsTensor<PERSON>onst("stride"), IsTensorConst("padding"), IsTensorConst("dequantizeParam"), IsTensorConst("value"), IsTensorConst("shiftBits"), IsTensorConst("countIncludePad"), IsGNNELoadW(_ => true, IsTensorConst("act1"))))), IsNone(), IsGNNELoadW(_ => true, IsTensorConst("act2")), IsTensorConst("inAShiftBits"), IsTensorConst("inBShiftBits"), IsTensorConst("outShiftBits"), IsTensorConst("deqAParams"), IsTensorConst("deqBParams"), IsTensorConst("outChannels"), IsTensorConst("is16Segments"));

    private Expr? GetReplace(Expr pdp0, Expr input, Expr filter, Call call2, TensorConst stride, TensorConst padding, TensorConst dequantizeParam, TensorConst value, TensorConst shiftBits, TensorConst countIncludePad, GNNEActivation act, TensorConst deqAParams, TensorConst outChannels, bool is16Segments, Expr stCall, RunPassContext context)
    {
        var userAnalysis = context.GetAnalysis<IExprUserAnalysisResult>();

        // todo: UsedByResult is null
        if (userAnalysis[stCall].Count() > 1)
        {
            return null;
        }

        var act0Param = ((GNNEPdp0Reduce)pdp0).ActParam;
        var actParam = FoldAct0WithAct1(act0Param, is16Segments, outChannels, (ActParam2)act.ActParam, deqAParams);
        if (actParam == null)
        {
            return null;
        }

        var newAct = GNNELoadW(DataTypes.Float16, actParam.ToAct0Data());
        return GNNEPdp0Reduce(((GNNEPdp0Reduce)pdp0).ReduceOp, (PrimType)call2.CheckedDataType, actParam, input, filter, stride, padding, dequantizeParam, value, shiftBits, countIncludePad, newAct);
    }
}
