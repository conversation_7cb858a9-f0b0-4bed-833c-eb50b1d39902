// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.Tensors;
using Nncase.PatternMatch;
using static Nncase.IR.F.Tensors;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Tensors;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class OptSlice : IRewriteRule
{
    /// <inheritdoc />
    public IPattern Pattern { get; }
        = IsSlice(
            "slice",
            "call",
            _ => true,
            IsWildcard("input") with { TypePattern = HasFixedShape() },
            IsTensorConst("begin"),
            IsTensorConst("ends"),
            IsTensorConst("axes"),
            IsTensorConst("strides"));

    private Expr? GetReplace(IR.Tensors.Slice slice, Call call, Expr input, TensorConst begin, TensorConst ends, TensorConst axes, int[] strides)
    {
        if (strides == new[] { 1, 1, 1, 1 } ||
            (input.CheckedShape[^1].FixedValue %
             strides[strides.Length - 1] != 0 &&
             strides[strides.Length - 1] != 1))
        {
            return null;
        }

        if (strides.Length == 4)
        {
            var a = 0;
            foreach (int v in strides)
            {
                if (v > 1)
                {
                    if (++a == 4)
                    {
                        return null;
                    }
                }
            }
        }

        foreach (var dim in strides)
        {
            if (dim < 0)
            {
                return null;
            }
        }

        var newBegin = new List<int>();
        var newEnd = new List<int>();
        var newStrides = new List<int>();
        var newShape = new List<int>();
        for (int i = 0; i < input.CheckedShape.Rank; i++)
        {
            if (input.CheckedShape[i].FixedValue > 1)
            {
                newBegin.Add(begin.Value.ToArray<int>()[i]);
                newEnd.Add(ends.Value.ToArray<int>()[i]);
                newStrides.Add(strides[i]);
                newShape.Add((int)input.CheckedShape[i].FixedValue);
            }
        }

        if (strides[3] > 1)
        {
            int b = newBegin[^1];
            int s = newStrides[^1];
            int e = newEnd[^1];
            int sh = newShape[^1];
            if (newBegin.Count != 0)
            {
                newBegin.RemoveAt(newBegin.Count - 1);
            }

            if (newStrides.Count != 0)
            {
                newStrides.RemoveAt(newStrides.Count - 1);
            }

            if (newEnd.Count != 0)
            {
                newEnd.RemoveAt(newEnd.Count - 1);
            }

            if (newShape.Count != 0)
            {
                newShape.RemoveAt(newShape.Count - 1);
            }

            newBegin.Add(b / s);
            newBegin.Add(b % s);
            newStrides.Add(1);
            newStrides.Add(1);
            newEnd.Add((e / s) + (e % s > b % s ? 1 : 0));
            newEnd.Add((b % s) + 1);
            newShape.Add(sh / s);
            newShape.Add(s);
        }

        for (int i = newShape.Count; i < 4; i++)
        {
            newBegin.Insert(newBegin[0], 0);
            newEnd.Insert(newEnd[0], 1);
            newStrides.Insert(newStrides[0], 1);
        }

        var bit1 = IR.F.Tensors.Reshape(input, newShape.ToArray<int>());
        var newSlice = IR.F.Tensors.Slice(
            bit1,
            newBegin.ToArray(),
            newEnd.ToArray(),
            new[] { 0, 0, 0, 0 },
            new[] { 1, 1, 1, 1 });
        var midType = call.CheckedDataType == DataTypes.Float32 ? DataTypes.Float16 : call.CheckedDataType;
        var sof = GNNEStore(midType, GNNELoad((PrimType)midType, newSlice));
        return Reshape(sof, call.CheckedShape);
    }
}
