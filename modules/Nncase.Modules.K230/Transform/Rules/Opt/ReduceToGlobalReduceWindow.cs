﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.Math;
using Nncase.PatternMatch;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Math;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class ReduceToGlobalReduceWindow : IRewriteRule
{
    /// <inheritdoc />
    public IPattern Pattern { get; }
        = IsReduce(
            "reduce",
            "call",
            _ => true,
            IsWildcard("input") with { TypePattern = HasFixedShape() },
            IsTensorConst("axis"),
            IsTensorConst("initValue"),
            IsTensorConst());

    private Expr? GetReplace(Reduce reduce, Call call, Expr input, TensorConst axis, TensorConst initValue)
    {
        long[] inShape = input.CheckedShape.ToValueArray();
        int[] axisValue = axis.Value.ToArray<int>();
        if (!TryMatch(call.CheckedShape.ToValueArray(), inShape, axisValue) || reduce.ReduceOp == ReduceOp.Prod)
        {
            return null;
        }

        int[] filter = { (int)inShape[2], (int)inShape[3] };
        int[] stride = { 1, 1 };
        int[] dilation = { 1, 1 };
        int[,] padding = { { 0, 0 }, { 0, 0 } };
        return IR.F.NN.ReduceWindow2D(reduce.ReduceOp, input, initValue, filter, stride, padding, dilation, false, false);
    }

    private bool TryMatch(long[] outShape, long[] inShape, int[] axisValue)
    {
        if (outShape.Length == 4 && inShape.Length == 4 && axisValue.Length == 2 && (axisValue[0] == 2 || axisValue[0] == -2) && (axisValue[1] == 3 || axisValue[1] == -1))
        {
            return true;
        }

        return false;
    }
}

[RuleGenerator]
public sealed partial class ExpandShapeOfGlobalReduceWindow : IRewriteRule
{
    /// <inheritdoc />
    public IPattern Pattern { get; }
        = IsRangeOfMarker(
            "outputMarker",
            IsReduce(
            "reduce",
            "call",
            _ => true,
            IsRangeOfMarker("inputMarker", IsWildcard("input"), IsTensorConst("inputRange")) with { TypePattern = HasFixedShape() },
            IsFixedShape("axis"),
            IsTensorConst("initValue"),
            IsTensorConst()),
            IsTensorConst("outputRange"));

    private Expr? GetReplace(Reduce reduce, Call call, Expr input, int[] axis, TensorConst initValue, Marker inputMarker, Marker outputMarker, Expr outputRange)
    {
        long[] inShape = input.CheckedShape.ToValueArray();
        if (!TryMatch(call.CheckedShape.ToValueArray(), inShape, axis) || reduce.ReduceOp == ReduceOp.Prod)
        {
            return null;
        }

        var newRd = IR.F.Math.RangeOfMarker(IR.F.Tensors.Reduce(reduce.ReduceOp, inputMarker, axis, initValue, true), outputRange).With(
            adaQuantInfo: outputMarker.AdaQuantInfo,
            mixQuantInfo: outputMarker.MixQuantInfo);
        return IR.F.Math.RangeOfMarker(IR.F.Tensors.Reshape(newRd, call.CheckedShape), outputRange).With(
            adaQuantInfo: outputMarker.AdaQuantInfo,
            mixQuantInfo: outputMarker.MixQuantInfo);
    }

    private bool TryMatch(long[] outShape, long[] inShape, int[] axisValue)
    {
        if (outShape.Length == 2 && inShape.Length == 4 && axisValue.Length == 2 && (axisValue[0] == 2 || axisValue[0] == -2) && (axisValue[1] == 3 || axisValue[1] == -1))
        {
            return true;
        }

        return false;
    }
}

[RuleGenerator]
public sealed partial class ReduceWithDim1ToGlobalReduceWindow : IRewriteRule
{
    /// <inheritdoc />
    public IPattern Pattern { get; }
        = IsRangeOfMarker(
            "outputMarker",
            IsReduce(
            "reduce",
            "call",
            _ => true,
            IsRangeOfMarker("inputMarker", IsWildcard("input"), IsTensorConst("inputRange")) with { TypePattern = HasFixedShape() },
            IsTensorConst("axis"),
            IsTensorConst("initValue"),
            IsTensorConst()),
            IsTensorConst("outputRange"));

    private Expr? GetReplace(Reduce reduce, Call call, Expr input, TensorConst axis, TensorConst initValue, Marker inputMarker, Marker outputMarker, Expr inputRange, Expr outputRange)
    {
        long[] inShape = input.CheckedShape.ToValueArray();
        int[] axisValue = axis.Value.ToArray<int>();
        if (!TryMatch(call.CheckedShape.ToValueArray(), inShape, axisValue) || reduce.ReduceOp == ReduceOp.Prod)
        {
            return null;
        }

        var tpIn = IR.F.Math.RangeOfMarker(IR.F.Tensors.Transpose(inputMarker, new[] { 2, 3, 0, 1 }), inputRange).With(
            adaQuantInfo: inputMarker.AdaQuantInfo,
            mixQuantInfo: inputMarker.MixQuantInfo);
        var rw = IR.F.Math.RangeOfMarker(IR.F.NN.ReduceWindow2D(reduce.ReduceOp, tpIn, initValue, new[] { inShape[0], inShape[1] }, new[] { 1, 1 }, new[,] { { 0, 0 }, { 0, 0 } }, new[] { 1, 1 }, false, false), outputRange).With(
            adaQuantInfo: outputMarker.AdaQuantInfo,
            mixQuantInfo: outputMarker.MixQuantInfo);
        return IR.F.Math.RangeOfMarker(IR.F.Tensors.Reshape(rw, call.CheckedShape), outputRange).With(
            adaQuantInfo: outputMarker.AdaQuantInfo,
            mixQuantInfo: outputMarker.MixQuantInfo);
    }

    private bool TryMatch(long[] outShape, long[] inShape, int[] axisValue)
    {
        if (outShape.Length == 4 && inShape.Length == 4 && axisValue.Length == 1 && axisValue[0] == 1 &&
            inShape[0] == 1)
        {
            return true;
        }

        return false;
    }
}

[RuleGenerator]
public sealed partial class ReduceWithDim12ToGlobalReduceWindow : IRewriteRule
{
    public IPattern Pattern { get; }
        = IsRangeOfMarker(
            "outputMarker",
            IsReduce(
            "reduce",
            "call",
            _ => true,
            IsRangeOfMarker("inputMarker", IsWildcard("input"), IsTensorConst("inputRange")) with { TypePattern = HasFixedShape() },
            IsTensorConst("axis"),
            IsTensorConst("initValue"),
            IsTensorConst()),
            IsTensorConst("outputRange"));

    private Expr? GetReplace(Reduce reduce, Call call, Expr input, TensorConst axis, TensorConst initValue, Marker inputMarker, Marker outputMarker, Expr inputRange, Expr outputRange)
    {
        var inShape = input.CheckedShape.ToValueArray();
        var axisValue = axis.Value.ToArray<int>();
        if (!TryMatch(call.CheckedShape.ToValueArray(), inShape, axisValue) || reduce.ReduceOp == ReduceOp.Prod)
        {
            return null;
        }

        var tpIn = IR.F.Math.RangeOfMarker(IR.F.Tensors.Transpose(inputMarker, new[] { 0, 3, 1, 2 }), inputRange).With(
            adaQuantInfo: inputMarker.AdaQuantInfo,
            mixQuantInfo: inputMarker.MixQuantInfo);
        var rw = IR.F.Math.RangeOfMarker(IR.F.NN.ReduceWindow2D(reduce.ReduceOp, tpIn, initValue, new[] { 3, 3 }, new[] { 2, 1 }, new[,] { { 1, 1 }, { 1, 1 } }, new[] { 1, 1 }, false, false), outputRange).With(
            adaQuantInfo: outputMarker.AdaQuantInfo,
            mixQuantInfo: outputMarker.MixQuantInfo);
        return IR.F.Math.RangeOfMarker(IR.F.Tensors.Transpose(rw, new[] { 0, 2, 3, 1 }), outputRange).With(
            adaQuantInfo: outputMarker.AdaQuantInfo,
            mixQuantInfo: outputMarker.MixQuantInfo);
    }

    private bool TryMatch(long[] outShape, long[] inShape, int[] axisValue)
    {
        if (outShape.Length == 4 && inShape.Length == 4 && axisValue.Length == 2 && axisValue[0] == 1 &&
            axisValue[1] == 2)
        {
            return true;
        }

        return false;
    }
}
