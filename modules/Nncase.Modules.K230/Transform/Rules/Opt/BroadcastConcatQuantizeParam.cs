// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using NetFabric.Hyperlinq;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Math;
using Nncase.IR.Tensors;
using Nncase.Passes.Analysis;
using Nncase.Passes.Rules.Neutral;
using Nncase.PatternMatch;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.K230.GNNETypePatternUtility;
using static Nncase.Passes.Rules.GetReplaceHelper;
using static Nncase.PatternMatch.F.Math;
using static Nncase.PatternMatch.F.Tensors;
using static Nncase.PatternMatch.Utility;
using static Nncase.Utilities.ReplaceUtility;
using Tuple = Nncase.IR.Tuple;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class BroadcastConcatRange : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } = IsRangeOfMarker("marker", IsWildcard("input"), IsTensorConst("range"));

    private Expr? GetReplace(Marker marker, Tensor<float> range, Expr input, RunPassContext context)
    {
        var broadThresold = 2.0f;
        var userAnalysis = context.GetAnalysis<IExprUserAnalysisResult>();
        if (userAnalysis[marker].Count(u => u is Tuple) == 1)
        {
            var tuple = userAnalysis[marker].First(u => u is Tuple);
            if (userAnalysis[tuple].Count(u => u is Call { Target: Concat }) == 1)
            {
                var concat = userAnalysis[tuple].First(u => u is Call { Target: Concat }) as Call;
                if (userAnalysis[concat!].Count(u => u is Marker { Name: "RangeOf" }) == 1)
                {
                    var concatMarker = userAnalysis[concat!].First(u => u is Marker { Name: "RangeOf" }) as Marker;
                    var conncatRange = ((TensorConst)concatMarker!.Attribute).Value.Cast<float>();
                    if (!range.Equals(conncatRange) && Math.Abs(new float[] { range[0] / (conncatRange[0] + float.Epsilon), range[1] / (conncatRange[1] + float.Epsilon), conncatRange[0] / (range[0] + float.Epsilon), conncatRange[1] / (range[1] + float.Epsilon) }.Max()) < broadThresold)
                    {
                        return IR.F.Math.RangeOfMarker(input, conncatRange).With(
                            adaQuantInfo: marker.AdaQuantInfo,
                            mixQuantInfo: marker.MixQuantInfo);
                    }
                }
            }
        }

        return null;
    }
}

/// <summary>
/// replace concat output range with union of inputs.
/// </summary>
[RuleGenerator]
public sealed partial class UniteConcatRange : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } = IsRangeOfMarker(
        "marker",
        IsConcat(
            "concat",
            null,
            _ => true,
            IsTuple(IsVArgsRepeat("tupleInputs", exprs =>
            {
                var patterns = new Pattern[exprs.Length];
                for (int i = 0; i < exprs.Length; i++)
                {
                    patterns[i] = IsAlt(IsRangeOfMarker(IsWildcard($"input_{i}"), IsTensorConst($"input_range_{i}")), IsWildcard($"input_{i}"));
                }

                return patterns;
            }))),
        IsTensorConst("range"));

    private Expr? GetReplace(Marker marker, Tensor<float> range, IReadOnlyList<BaseExpr> tupleInputs, Concat concat, IMatchResult result)
    {
        var newRange = range.Clone();
        for (int i = 0; i < tupleInputs.Count; i++)
        {
            var e = tupleInputs[i];
            if (e is Marker)
            {
                var inputRangeValue = ((TensorConst)result[$"input_range_{i}"]).Value.Cast<float>();
                newRange[0] = Math.Min(newRange[0], inputRangeValue[0]);
                newRange[1] = Math.Max(newRange[1], inputRangeValue[1]);
            }
        }

        if (!newRange.Equals(range))
        {
            return IR.F.Math.RangeOfMarker(IR.F.Tensors.Concat(new Tuple(tupleInputs.ToArray()), concat.Axis), newRange).With(
            mixQuantInfo: marker.MixQuantInfo,
            adaQuantInfo: marker.AdaQuantInfo);
        }

        return null;
    }
}
