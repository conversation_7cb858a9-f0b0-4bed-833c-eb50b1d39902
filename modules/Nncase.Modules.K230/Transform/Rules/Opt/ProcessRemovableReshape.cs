﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.Math;
using Nncase.IR.NN;
using Nncase.IR.Shapes;
using Nncase.IR.Tensors;
using Nncase.PatternMatch;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Math;
using static Nncase.PatternMatch.F.Tensors;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class FoldReshapeLeakyReshape : IRewriteRule
{
    /// <inheritdoc />
    public IPattern Pattern { get; }
        = IsReshape("bc2", _ => true, IsBinary("bin2", BinaryOp.Max, IsBinary("bin1", BinaryOp.Mul, IsReshape("bc1", _ => true, IsTensorConst("input") with { TypePattern = HasFixedShape() }), IsTensorConst("rhs") with { TypePattern = HasFixedShape() })));

    public Expr? GetReplace(Reshape bc2, Binary bin2, Binary bin1, Reshape bc1, Expr input, Expr rhs)
    {
        var newBin1 = IR.F.Math.Binary(bin1.BinaryOp, input, rhs);
        var newBin2 = IR.F.Math.Binary(bin2.BinaryOp, bin1, newBin1);
        return newBin2;
    }
}

[RuleGenerator]
public sealed partial class ReshapeLeakyReshapeMotion : IRewriteRule
{
    /// <inheritdoc />
    public IPattern Pattern { get; }
        = IsReshape("bc2", _ => true, IsBinary("bin2", BinaryOp.Max, IsBinary("bin1", BinaryOp.Mul, IsReshape("bc1", _ => true, IsTensorConst("input") with { TypePattern = HasFixedShape() }), IsTensorConst("rhs") with { TypePattern = HasFixedShape() })));

    private Expr? GetReplace(Reshape bc2, Binary bin2, Binary bin1, Reshape bc1, Expr input, Expr rhs)
    {
        var newBin1 = IR.F.Math.Binary(bin1.BinaryOp, input, rhs);
        var newBin2 = IR.F.Math.Binary(bin2.BinaryOp, bin1, newBin1);
        var newBc = IR.F.Tensors.Reshape(newBin2, newBin2.CheckedShape);
        return newBc;
    }
}

[RuleGenerator]
public sealed partial class ReshapeLeakyPadReshapeMotion : IRewriteRule
{
    public IPattern Pattern { get; }
        = IsReshape("bc2", "call", _ => true, PatternMatch.F.NN.IsPad("p", _ => true, IsBinary("bn2", BinaryOp.Max, IsBinary("bn1", BinaryOp.Mul, IsReshape("bc1", _ => true, IsTensorConst("input") with { TypePattern = HasFixedShape() }), IsTensorConst("rhs") with { TypePattern = HasFixedShape() })), IsTensorConst("pads"), IsTensorConst("value")));

    private Expr? GetReplace(Reshape bc2, Call call, Pad p, Binary bn2, Binary bn1, Reshape bc1, Expr rhs, TensorConst pads, TensorConst value, TensorConst input)
    {
        if (call.CheckedShape[1] != p.CheckedShape[0] || call.CheckedShape[2] != p.CheckedShape[1] ||
            call.CheckedShape[3] != p.CheckedShape[2])
        {
            return null;
        }

        var inShape = call.CheckedShape;
        var newBc = IR.F.Tensors.Reshape(bc1, new[] { 1, inShape[0], inShape[1], inShape[2] });
        var padding = pads.Value.Cast<int>();
        var np = Paddings.Zeros(4).ToDimensionArray();
        for (int i = 0; i < 4; i++)
        {
            if (i == 0)
            {
                continue;
            }

            for (int j = 0; j < padding.Length; j++)
            {
                np[i, j] = padding[i - 1, j];
            }
        }

        var newP = IR.F.NN.Pad(newBc, Dimension.ConcatPadding(np), p.PadMode, value);
        return newP;
    }
}

[RuleGenerator]
public sealed partial class ReshapePadReshapeMotion : IRewriteRule
{
    public IPattern Pattern { get; }
        = IsReshape("bc2", "call", _ => true, PatternMatch.F.NN.IsPad("p", _ => true, IsReshape("bc1", _ => true, IsTensorConst("input") with { TypePattern = HasFixedShape() }), IsTensorConst("pads"), IsTensorConst("value")));

    public Expr? GetReplace(Reshape bc2, Call call, Pad p, Reshape bc1, TensorConst pads, TensorConst value, TensorConst input)
    {
        if (call.CheckedShape.Rank != 4 || p.CheckedShape.Rank != 3 || call.CheckedShape[1] != p.CheckedShape[0] ||
            call.CheckedShape[2] != p.CheckedShape[1] ||
            call.CheckedShape[3] != p.CheckedShape[2])
        {
            return null;
        }

        var inShape = call.CheckedShape;
        var newBc = IR.F.Tensors.Reshape(bc1, new[] { 1, inShape[0], inShape[1], inShape[2] });
        var padding = pads.Value.Cast<int>();
        var np = Paddings.Zeros(4).ToDimensionArray();
        for (int i = 0; i < 4; i++)
        {
            if (i == 0)
            {
                continue;
            }

            for (int j = 0; j < padding.Length; j++)
            {
                np[i, j] = padding[i - 1, j];
            }
        }

        var newP = IR.F.NN.Pad(newBc, Dimension.ConcatPadding(np), p.PadMode, value);
        return newP;
    }
}

[RuleGenerator]
public sealed partial class InnodePadReshapeMotion : IRewriteRule
{
    public IPattern Pattern { get; }
        = PatternMatch.F.NN.IsPad("p", "call", _ => true, IsReshape("bc", _ => true, IsTensorConst("input") with { TypePattern = HasFixedShape() }), IsTensorConst("pads"), IsTensorConst("value"));

    private Expr? GetReplace(Pad p, Call call, Reshape bc, TensorConst input, TensorConst pads, TensorConst value)
    {
        if (bc.CheckedShape.Rank != 4 || input.CheckedShape.Rank != 3 ||
            bc.CheckedShape[1] != input.CheckedShape[0] ||
            bc.CheckedShape[2] != input.CheckedShape[1] ||
            bc.CheckedShape[3] != input.CheckedShape[2])
        {
            return null;
        }

        var inShape = call.CheckedShape;
        var newBc = IR.F.Tensors.Reshape(input, new[] { 1, inShape[0], inShape[1], inShape[2] });
        var padding = pads.Value.Cast<int>();
        var np = Paddings.Zeros(4).ToDimensionArray();
        for (int i = 0; i < 4; i++)
        {
            if (i == 0)
            {
                continue;
            }

            for (int j = 0; j < padding.Length; j++)
            {
                np[i, j] = padding[i - 1, j];
            }
        }

        var newP = IR.F.NN.Pad(newBc, Dimension.ConcatPadding(np), p.PadMode, value);
        return newP;
    }
}

[RuleGenerator]
public sealed partial class FoldTransposeReshape : IRewriteRule
{
    public IPattern Pattern { get; }
        = IsTranspose("tp", "call", _ => true, IsReshape("bc", _ => true, IsTensorConst("input") with { TypePattern = HasFixedShape() }), IsTensorConst("perm"));

    private Expr? GetReplace(Transpose tp, Call call, Reshape bc, TensorConst input, int[] perm)
    {
        if (perm != new[] { 0, 2, 3, 1 } || bc.CheckedShape[0] != input.CheckedShape[0] ||
            bc.CheckedShape[1] != input.CheckedShape[2] || bc.CheckedShape[2] != input.CheckedShape[1] ||
            bc.CheckedShape[3] != input.CheckedShape[3])
        {
            return null;
        }

        var newTp = IR.F.Tensors.Transpose(input, new[] { 0, 3, 2, 1 });
        return newTp;
    }
}
