// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.NN;
using Nncase.PatternMatch;
using static Nncase.IR.F.NN;
using static Nncase.IR.F.Tensors;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Math;
using static Nncase.PatternMatch.F.NN;
using static Nncase.PatternMatch.F.Tensors;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class LeakyReluTransposeSwap : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } =
    IsRangeOfMarker(
                    name: "leakyMarker",
                    IsLeakyRelu(
                        "leaky",
                        "leakyCall",
                        _ => true,
                        IsRangeOfMarker(
                            name: "transMark<PERSON>",
                            <PERSON><PERSON>ranspose(
                                    IsWildcard("input"),
                                    IsTensor<PERSON>onst("perm", IsIntegral())),
                            IsTensorConst("transRange")),
                        IsTensorConst("alpha")),
                    IsTensorConst("leakyRange"));

    public Expr? GetReplace(LeakyRelu leaky, Call leakyCall, TensorConst alpha, Expr input, Expr leakyMarker, Tensor<float> leakyRange, Expr transMarker, Tensor<float> transRange, int[] perm)
    {
        if (((Nncase.IR.Marker)input).Target is Call { Target: Nncase.IR.Math.Binary })
        {
            var leakyRelu = LeakyRelu(input, alpha);
            var markerRelu = IR.F.Math.RangeOfMarker(leakyRelu, leakyRange).With(
                            adaQuantInfo: ((Marker)leakyMarker!).AdaQuantInfo,
                            mixQuantInfo: ((Marker)leakyMarker!).MixQuantInfo);
            var transpose = Transpose(markerRelu, (RankedShape)perm);
            var markerTrans = IR.F.Math.RangeOfMarker(transpose, transRange).With(
                            adaQuantInfo: ((Marker)transMarker!).AdaQuantInfo,
                            mixQuantInfo: ((Marker)transMarker!).MixQuantInfo);
            return markerTrans;
        }
        else
        {
            return null;
        }
    }
}
