// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.NN;
using Nncase.PatternMatch;
using static Nncase.IR.F.NN;
using static Nncase.IR.F.Tensors;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.PatternMatch.F.NN;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class LeakyReluReshape : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } =
    IsRangeOfMarker(
                    name: "outputMarker",
                    IsLeakyRelu(
                        "leaky",
                        "leakyCall",
                        _ => true,
                        IsWildcard("input"),
                        IsTensorConst("alpha")),
                    IsTensorConst("outputRange"));

    public Expr? GetReplace(LeakyRelu leaky, Call leakyCall, TensorConst alpha, Expr input, Expr outputMarker, Tensor<float> outputRange)
    {
        // if shape[^1] != 1, Perhaps the optimization results may not be very noticeable.
        // if shape[^2] != 1, It may cause a deadlock.
        if (leakyCall.CheckedShape.Rank < 2 || leakyCall.CheckedShape.ToValueArray()[^1] != 1 || leakyCall.CheckedShape.ToValueArray()[^2] == 1)
        {
            return null;
        }

        var newShape = leakyCall.CheckedShape.ToValueArray();
        newShape[^1] = leakyCall.CheckedShape.ToValueArray()[^2];
        newShape[^2] = leakyCall.CheckedShape.ToValueArray()[^1];
        if (leakyCall.CheckedShape.ProdWithDynamicAsMaxValue() < 65536)
        {
            newShape = leakyCall.CheckedShape.ToValueArray().Select((dim, idx) => idx < leakyCall.CheckedShape.Rank - 1 ? 1L : leakyCall.CheckedShape.Rank).ToArray();
        }

        var reshapeBefore = Reshape(input, newShape);
        var markerBefore = IR.F.Math.RangeOfMarker(reshapeBefore, (Expr)((Nncase.IR.Marker)input).Attribute).With(
            adaQuantInfo: ((Marker)input!).AdaQuantInfo,
            mixQuantInfo: ((Marker)input!).MixQuantInfo);
        var leakyRelu = LeakyRelu(markerBefore, alpha);
        var markerAfter = IR.F.Math.RangeOfMarker(leakyRelu, outputRange).With(
            adaQuantInfo: ((Marker)outputMarker!).AdaQuantInfo,
            mixQuantInfo: ((Marker)outputMarker!).MixQuantInfo);
        var reshapeAfter = IR.F.Math.RangeOfMarker(Reshape(markerAfter, leakyCall.CheckedShape), outputRange).With(
            adaQuantInfo: ((Marker)outputMarker!).AdaQuantInfo,
            mixQuantInfo: ((Marker)outputMarker!).MixQuantInfo);
        return reshapeAfter;
    }
}
