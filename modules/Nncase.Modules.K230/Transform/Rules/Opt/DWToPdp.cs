﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.Passes.Analysis;
using Nncase.PatternMatch;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.PatternMatch.F.K230;
using static Nncase.PatternMatch.Utility;
using GNNEConv2D = Nncase.IR.K230.GNNEConv2D;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class DWToPdp : RewriteRule<Pattern>, IRewriteRule
{
    public override Pattern Pattern { get; } =
        IsGNNEConv2D(
            "dw",
            "dwCall",
            _ => true,
            IsGNNELoad("ld", "ldCall", _ => true, IsGNNEStore("st", "stCall", _ => true, IsGNNEConv2D("conv", "convCall", _ => true, IsGNNELoad("convInput", "convInputCall", _ => true), null, null, null, null, null, null, null, null, null, null, null, null, IsTensorConst("convGroups")))),
            IsGNNELoadW("weights", "weightsCall", _ => true),
            IsWildcard("weightsBias"),
            IsWildcard("weightsBiasQint8"),
            IsWildcard("act"),
            IsWildcard("actQint8"),
            IsWildcard("deqBias"),
            IsWildcard("shiftBits"),
            IsWildcard("shiftBitsQint8"),
            IsWildcard("qint8Qp"),
            IsWildcard("padding"),
            IsWildcard("stride"),
            IsWildcard("dilation"),
            IsTensorConst("groups"),
            IsWildcard("is16Quant"),
            IsWildcard("padValue"),
            IsWildcard("weightsQInt8"));

    private Expr? GetReplace(Call dwCall, GNNEConv2D dw, Call ldCall, Call stCall, Call convCall, Call convInputCall, int convGroups, Call weightsCall, Expr weightsBias, Expr weightsBiasQint8, Expr act, Expr actQint8, Expr deqBias, Expr shiftBits, Expr shiftBitsQint8, Expr qint8Qp, Expr padding, Expr stride, Expr dilation, int groups, Expr is16Quant, Expr padValue, Expr weightsQInt8, RunPassContext context)
    {
        var userAnalysis = context.GetAnalysis<IExprUserAnalysisResult>();

        var callerGroups = groups;
        int callerInputChannels = (int)ldCall.CheckedShape[1].FixedValue;
        int callerOutputChannels = (int)dwCall.CheckedShape[1].FixedValue;
        bool callerIsDepthwise = callerInputChannels == callerOutputChannels && callerOutputChannels == callerGroups && callerGroups != 1;
        int[] callerFilters = weightsCall.CheckedShape.ToValueArray()[2..].Select(x => (int)x).ToArray();

        var calleeGroups = convGroups;
        int calleeInputChannels = (int)convInputCall.CheckedShape[1].FixedValue;
        int calleeOutputChannels = (int)convCall.CheckedShape[1].FixedValue;
        bool calleeIsDepthwise = calleeInputChannels == calleeOutputChannels && calleeOutputChannels == calleeGroups && calleeGroups != 1;

        var inputType = ldCall.CheckedDataType;
        var wType = weightsCall.CheckedDataType;

        if (!(userAnalysis[stCall].Count() > 1 || userAnalysis[ldCall].Count() > 1) && callerIsDepthwise && callerFilters[0] <= 3 && callerFilters[1] <= 3 && !calleeIsDepthwise && (inputType == DataTypes.UInt8 || inputType == DataTypes.Int8) && (wType == DataTypes.UInt8 || wType == DataTypes.Int8))
        {
            int[] weightsShape = dwCall[GNNEConv2D.Weights].CheckedShape.ToValueArray().Select(x => (int)x).ToArray();
            weightsShape[0] = TileUtilities.GetAlignedNum(weightsShape[0], GNNEEnv.PuWidth);
            byte[] oldWeights = ((TensorConst)weightsCall[IR.K230.GNNELoadW.Input]).Value.ToArray<byte>();
            byte[] newWeights = Enumerable.Repeat((byte)0, weightsShape.Aggregate((d0, d1) => d0 * d1)).ToArray();
            Array.Copy(oldWeights, newWeights, oldWeights.Length);
            weightsCall = GNNELoadW((PrimType)wType, Tensor.FromBytes<byte>(newWeights, weightsShape.Select(x => (long)x).ToArray()));

            return GNNEPdp0DW(dw.DestType, ldCall, weightsCall, weightsQInt8, weightsBias, weightsBiasQint8, act, actQint8, deqBias, shiftBits, shiftBitsQint8, qint8Qp, padding, stride, dilation, groups, is16Quant, padValue, dw.ActParam, dw.ActParamQInt8);
        }
        else
        {
            return null;
        }
    }
}
