﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using static Nncase.IR.F.Tensors;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.PatternMatch.Utility;
using GNNEConv2D = Nncase.IR.K230.GNNEConv2D;
using GNNELoadW = Nncase.IR.K230.GNNELoadW;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class Reshape1x1Conv : RewriteRule<Pattern>
{
    public override Pattern Pattern { get; } =
        IsCallWildcard("fusionCall", FusionPattern.IsGNNEFusion<GNNEConv2D>());

    private Expr? GetReplace(Call call, Call ld, Call st, Call fusionCall, IReadOnlyList<BaseExpr> fusionCallParams)
    {
        var input = call[GNNEConv2D.Input];
        var weights = call[GNNEConv2D.Weights];
        var weightsBias = call[GNNEConv2D.WeightsBias];
        var weightsBiasQint8 = call[GNNEConv2D.WeightsBiasQint8];
        var act = call[GNNEConv2D.Act];
        var actQint8 = call[GNNEConv2D.ActQint8];
        var deqBias = call[GNNEConv2D.DeqBias];
        var shiftBits = call[GNNEConv2D.ShiftBits];
        var shiftBitsQint8 = call[GNNEConv2D.ShiftBitsQint8];
        var qint8Qp = call[GNNEConv2D.Qint8Qp];
        var padding = call[GNNEConv2D.Padding];
        var stride = call[GNNEConv2D.Stride];
        var dilation = call[GNNEConv2D.Dilation];
        var groups = call[GNNEConv2D.Groups];
        var is16Quant = call[GNNEConv2D.Is16Quant];
        var padValue = call[GNNEConv2D.PadValue];
        var weightsQInt8 = call[GNNEConv2D.WeightsQInt8];

        var conv = (GNNEConv2D)call.Target;
        var fusionTarget = (Fusion)fusionCall.Target;

        if ((input.CheckedShape[0].FixedValue == 1 || !(input.CheckedShape[2].FixedValue == 1 && input.CheckedShape[3].FixedValue == 1))
            && ((TensorConst)groups).Value.ToScalar<int>() == 1
            && ((TensorConst)stride).Value.ToArray<int>().All(x => x == 1)
            && weights.CheckedShape.ToValueArray()[2..].All(x => x == 1)
            && ((TensorConst)padding).Value.ToArray<int>().All(x => x == 0)
            && (input.CheckedDataType == DataTypes.UInt8 || input.CheckedDataType == DataTypes.Int8)
            && ((input.CheckedShape[1].FixedValue == weights.CheckedShape[1].FixedValue && new[] { 24, 20, 16 }.Any(x => input.CheckedShape[1].FixedValue % x == 0 && input.CheckedShape[1].FixedValue != x))
               || (input.CheckedShape[1].FixedValue != weights.CheckedShape[1].FixedValue))
            && input.CheckedShape[2].FixedValue * input.CheckedShape[3].FixedValue < 65536)
        {
            int[] divisors = { 24, 20, 16, (int)input.CheckedShape[1].FixedValue };
            int cNum = divisors.FirstOrDefault(d => input.CheckedShape[1].FixedValue % d == 0);
            int bytesPerElement = weights.CheckedDataType == DataTypes.Int16 ? 2 : 1;
            int newWeightSize = (int)(weights.CheckedShape[0] * input.CheckedShape[1] * weights.CheckedShape[2] *
                                 weights.CheckedShape[3] * bytesPerElement).FixedValue;
            byte[] oldWeightsConst = ((TensorConst)((Call)weights)[GNNELoadW.Input]).Value.BytesBuffer.ToArray();
            byte[] newWeightsConst = new byte[newWeightSize];
            for (int i = 0; i < newWeightSize; i++)
            {
                newWeightsConst[i] = oldWeightsConst[
                    (i / (input.CheckedShape[1].FixedValue * bytesPerElement) *
                     weights.CheckedShape[1].FixedValue * bytesPerElement) +
                    (i % (input.CheckedShape[1].FixedValue * bytesPerElement))];
            }

            var newWeightsShape = new RankedShape(weights.CheckedShape[0], cNum, input.CheckedShape[1] / cNum, 1);
            var newLw = GNNELoadW((PrimType)weights.CheckedDataType, Tensor.FromBytes(new TensorType(weights.CheckedDataType, newWeightsShape), newWeightsConst));
            var newInputShape = new RankedShape(input.CheckedShape[0], cNum, input.CheckedShape[1] / cNum, input.CheckedShape[2] * input.CheckedShape[3]);
            var bcIn = Reshape((Expr)fusionCallParams[0], newInputShape);
            bcIn.CheckedType = new TensorType(input.CheckedDataType, newInputShape);
            var fusionInput = new Var("input", new TensorType(bcIn.CheckedDataType, bcIn.CheckedShape));
            var newLoad = GNNELoad((PrimType)ld.CheckedDataType, fusionInput);
            var newConv = GNNEConv2D(conv.DestType, newLoad, newLw, (Expr)weightsQInt8, (Expr)weightsBias, (Expr)weightsBiasQint8, (Expr)act, (Expr)actQint8, (Expr)deqBias, (Expr)shiftBits, (Expr)shiftBitsQint8, (Expr)qint8Qp, (Expr)padding, (Expr)stride, (Expr)dilation, (Expr)groups, (Expr)is16Quant, (Expr)padValue, conv.ActParam, conv.ActParamQInt8);
            var newStore = GNNEStore(st.CheckedDataType, newConv);

            var newFusion = new Fusion(fusionTarget.Name, fusionTarget.ModuleKind, newStore, fusionInput);

            return Reshape(new Call(newFusion, bcIn), st.CheckedShape);
        }

        return null;
    }
}
