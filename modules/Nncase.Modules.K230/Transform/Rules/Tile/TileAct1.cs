﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using Nncase.Schedule;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using static Nncase.Passes.Rules.K230.TileUtilities;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class TileAct1 : RewriteRule<Pattern>
{
    private static int _count = -1;

    private DataType? _inputAType;
    private DataType? _inputBType;
    private DataType? _outputType;

    private Call? _act1;
    private Call? _lif1;
    private Call? _lif2;
    private Call? _lact;
    private Call? _sof;

    private GNNEShape? _inputAShape;
    private GNNEShape? _inputBShape;
    private GNNEShape? _outputShape;

    /// <inheritdoc/>
    public override Pattern Pattern { get; } = FusionPattern.IsGNNEActivationFusion();

    private List<GnneAction> BuildSchedule(TiledGlb glb, Call act, Call ld, Call st, TIR.Buffer ddrIf, TIR.Buffer ddrOf, TIR.Buffer ddrAct, TIR.Buffer ddrIf2)
    {
        List<GnneAction> actions = new();
        var gpr = new GprHandler(GNNEEnv.GprNum);
        var ssr = new SsrHandler(GNNEEnv.SsrNum);
        var ccrHandler = new CcrHandler();
        GnneActionUpdater actionUpdater = new(actions, glb, ccrHandler, gpr, ssr);

        // mmu conf
        actionUpdater.UpdateMmuConf();

        // load act for all
        actionUpdater.UpdateLoadAct(_lact!, ddrAct, ItemName.MfuAct1);

        if (act[GNNEActivation.InputB] != None.Default)
        {
            GNNEShape minShape = new(0, 0, 0, 0);
            for (int i = 0; i < 4; i++)
            {
                minShape[i] = Math.Min(_inputAShape![i], _inputBShape![i]);
            }

            GNNEShape scaleA = new(0, 0, 0, 0), scaleB = new(0, 0, 0, 0), scaleOut = new(0, 0, 0, 0);
            for (int i = 0; i < 4; i++)
            {
                scaleA[i] = minShape[i] == 1 ? 1 : _inputAShape![i] / minShape[i];
                scaleB[i] = minShape[i] == 1 ? 1 : _inputBShape![i] / minShape[i];
                scaleOut[i] = minShape[i] == 1 ? 1 : _outputShape![i] / minShape[i];
            }

            var outputBatchSeg = GetSegmentStartEndLength(0, glb.LastOutShape[0], _outputShape![0]);
            foreach (var glbOutputBatch in outputBatchSeg)
            {
                var glbInputBatchA = glbOutputBatch / (scaleOut[0] / scaleA[0]);
                var glbInputBatchB = glbOutputBatch / (scaleOut[0] / scaleB[0]);
                if (_inputAShape![0] == 1)
                {
                    glbInputBatchA = new(..1, new(0, 0));
                }

                if (_inputBShape![0] == 1)
                {
                    glbInputBatchB = new(..1, new(0, 0));
                }

                var outputChanSeg = GetSegmentStartEndLength(0, glb.LastOutShape[1], _outputShape[1]);
                foreach (var glbOutputChannel in outputChanSeg)
                {
                    var glbInputChannelA = glbOutputChannel / (scaleOut[1] / scaleA[1]);
                    var glbInputChannelB = glbOutputChannel / (scaleOut[1] / scaleB[1]);
                    if (_inputAShape[1] == 1)
                    {
                        glbInputChannelA = new(..1, Padding.Zero());
                    }

                    if (_inputBShape[1] == 1)
                    {
                        glbInputChannelB = new(..1, new(0, 0));
                    }

                    var outputRowSeg = GetSegmentStartEndLength(0, glb.LastOutShape[2], _outputShape[2]);
                    foreach (var glbOutputRow in outputRowSeg)
                    {
                        var glbInputRowA = glbOutputRow / (scaleOut[2] / scaleA[2]);
                        var glbInputRowB = glbOutputRow / (scaleOut[2] / scaleB[2]);
                        if (_inputAShape[2] == 1)
                        {
                            glbInputRowA = new(..1, Padding.Zero());
                        }

                        if (_inputBShape[2] == 1)
                        {
                            glbInputRowB = new(..1, new(0, 0));
                        }

                        var outputColSeg = GetSegmentStartEndLength(0, glb.LastOutShape[3], _outputShape[3]);
                        foreach (var glbOutputColumn in outputColSeg)
                        {
                            var glbInputColA = glbOutputColumn / (scaleOut[3] / scaleA[3]);
                            var glbInputColB = glbOutputColumn / (scaleOut[3] / scaleB[3]);
                            if (_inputAShape[3] == 1)
                            {
                                glbInputColA = new Segment1D(..1, Padding.Zero());
                            }

                            if (_inputBShape[3] == 1)
                            {
                                glbInputColB = new Segment1D(..1, Padding.Zero());
                            }

                            SegmentND ofmap = new(glbOutputBatch.Start..glbOutputBatch.End, glbOutputChannel, glbOutputRow, glbOutputColumn);
                            SegmentND ifmapA = new(glbInputBatchA, glbInputChannelA, glbInputRowA, glbInputColA);
                            SegmentND ifmapB = new(glbInputBatchB, glbInputChannelB, glbInputRowB, glbInputColB);

                            int minC = Math.Min(glbInputChannelA.Length, glbInputChannelB.Length);
                            int cPpSplitSize = (int)Math.Ceiling(1.0f * minC / glb.NPingPongSplit);
                            var inChanASplit = GetSegmentStartEndLength(glbInputChannelA.Start, cPpSplitSize * glbInputChannelA.Length / minC, glbInputChannelA.End);
                            var inChanBSplit = GetSegmentStartEndLength(glbInputChannelB.Start, cPpSplitSize * glbInputChannelB.Length / minC, glbInputChannelB.End);
                            var outChanSplit = GetSegmentStartEndLength(glbOutputChannel.Start, cPpSplitSize * glbOutputChannel.Length / minC, glbOutputChannel.End);

                            for (int iPp = 0; iPp < outChanSplit.Count; iPp++)
                            {
                                var cPpSplitA = inChanASplit[iPp];
                                var cPpSplitB = inChanBSplit[iPp];
                                var cPpSplitO = outChanSplit[iPp];

                                // load ifmap
                                SegmentND ifmapPpA = new(ifmapA[0], cPpSplitA, ifmapA[2], ifmapA[3]);
                                SegmentND ifmapPpB = new(ifmapB[0], cPpSplitB, ifmapB[2], ifmapB[3]);
                                SegmentND ofmapPp = new(ofmap[0], cPpSplitO, ofmap[2], ofmap[3]);

                                List<CcrSet> ccrSetIfmapA = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap, iPp)), 1) };
                                actionUpdater.UpdateLoadIf(ifmapPpA, _lif1!, iPp, ddrIf, 0, null!, ItemName.Ifmap, ccrSetIfmapA);
                                List<CcrSet> ccrSetIfmapB = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap2, iPp)), 1) };
                                actionUpdater.UpdateLoadIf(ifmapPpB, _lif2!, iPp, ddrIf2, 0, null!, ItemName.Ifmap2, ccrSetIfmapB);

                                // activation
                                List<CcrSet> ccrSetOfmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ofmap, iPp)), 1) };
                                List<CcrClr> ccrClrIfmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap, iPp))) };
                                ccrClrIfmap.Add(new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap2, iPp))));
                                List<int> src1Stride = new List<int>() { ifmapPpA[1].Length, ifmapPpA[2].Length, ifmapPpA[3].Length };
                                List<int> src2Stride = new List<int>() { ifmapPpB[1].Length, ifmapPpB[2].Length, ifmapPpB[3].Length };
                                actionUpdater.UpdateMfuAct1(ifmapPpA, ifmapPpB, ofmapPp, ACT1_SOURCE_TYPE.l2, ACT1_SOURCE_TYPE.l2, _inputAType!, _inputBType!, _outputType!, _inputAType == DataTypes.Float16 ? new(0, 1) : ((TensorConst)act[GNNEActivation.DeqAParams]).Value.ToScalar<DeQuantizeParam>(), _inputBType! == DataTypes.Float16 ? new(0, 1) : ((TensorConst)act[GNNEActivation.DeqBParams]).Value.ToScalar<DeQuantizeParam>(), ((TensorConst)act[GNNEActivation.InAShiftBits]).Value.ToScalar<int>(), ((TensorConst)act[GNNEActivation.InBShiftBits]).Value.ToScalar<int>(), ((TensorConst)act[GNNEActivation.OutShiftBits]).Value.ToScalar<int>(), ((TensorConst)act[GNNEActivation.Is16Segments]).Value.ToScalar<bool>(), iPp, ccrSetOfmap, ccrClrIfmap, 0, 0, 0, 0, ItemName.Ifmap2, ItemName.Ifmap, ItemName.MfuAct1, ((GNNEActivation)act.Target).Type == GnneActivationType.Mul ? MFU_ACT1_FUNCTION.mul : MFU_ACT1_FUNCTION.add, ItemName.Ofmap, src1Stride, src2Stride);

                                // store
                                List<CcrClr> ccrClrOfmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ofmap, iPp))) };
                                actionUpdater.UpdateStoreT(ofmapPp, _sof!, iPp, ddrOf, 0, null!, null!, ccrClrOfmap);
                            }
                        }
                    }
                }
            }
        }
        else
        {
            GNNEShape scaleA = new(0, 0, 0, 0), scaleOut = new(0, 0, 0, 0);
            for (int i = 0; i < 4; i++)
            {
                scaleA[i] = 1;
                scaleOut[i] = _inputAShape![i] == 1 ? 1 : _outputShape![i] / _inputAShape[i];
            }

            var outputBatchSeg = GetSegmentStartEndLength(0, glb.LastOutShape[0], _outputShape![0]);
            foreach (var glbOutputBatch in outputBatchSeg)
            {
                var glbInputBatchA = glbOutputBatch / (scaleOut[0] / scaleA[0]);
                if (_inputAShape![0] == 1)
                {
                    glbInputBatchA = new Segment1D(0..1, Padding.Zero());
                }

                var outputChanSeg = GetSegmentStartEndLength(0, glb.LastOutShape[1], _outputShape[1]);
                foreach (var glbOutputChannel in outputChanSeg)
                {
                    var glbInputChannelA = glbOutputChannel / (scaleOut[1] / scaleA[1]);
                    if (_inputAShape[1] == 1)
                    {
                        glbInputChannelA = new(0..1, Padding.Zero());
                    }

                    var outputRowSeg = GetSegmentStartEndLength(0, glb.LastOutShape[2], _outputShape[2]);
                    foreach (var glbOutputRow in outputRowSeg)
                    {
                        var glbInputRowA = glbOutputRow / (scaleOut[2] / scaleA[2]);
                        if (_inputAShape[2] == 1)
                        {
                            glbInputRowA = new(0..1, Padding.Zero());
                        }

                        var outputColSeg = GetSegmentStartEndLength(0, glb.LastOutShape[3], _outputShape[3]);
                        foreach (var glbOutputColumn in outputColSeg)
                        {
                            var glbInputColA = glbOutputColumn / (scaleOut[3] / scaleA[3]);
                            if (_inputAShape[3] == 1)
                            {
                                glbInputColA = new(0..1, Padding.Zero());
                            }

                            SegmentND ofmap = new(glbOutputBatch, glbOutputChannel, glbOutputRow, glbOutputColumn);
                            SegmentND ifmap = new(glbInputBatchA, glbInputChannelA, glbInputRowA, glbInputColA);

                            int cPpSplitSize = (int)Math.Ceiling(1.0 * glbInputChannelA.Length / glb.NPingPongSplit);
                            var inChanSplit = GetSegmentStartEndLength(glbInputChannelA.Start, cPpSplitSize, glbInputChannelA.End);

                            for (int iPp = 0; iPp < inChanSplit.Count; iPp++)
                            {
                                var cPpSplit = inChanSplit[iPp];

                                SegmentND ifmapPp = new(ifmap[0], cPpSplit, ifmap[2], ifmap[3]);
                                Segment1D none = new(0..0, Padding.Zero());
                                SegmentND ifmap2Pp = new(none, none, none, none);
                                SegmentND ofmapPp = new(ofmap[0], cPpSplit, ofmap[2], ofmap[3]);

                                // load src1
                                List<CcrSet> ccrSetIfmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap, iPp)), 1) };
                                List<int> stridesD = new() { glb.GlbMap[ItemName.Ifmap].Dimensions[1], glb.GlbMap[ItemName.Ifmap].Dimensions[2], glb.GlbMap[ItemName.Ifmap].Dimensions[3] };
                                actionUpdater.UpdateLoadIf(ifmapPp, _lif1!, iPp, ddrIf, 0, stridesD, ItemName.Ifmap, ccrSetIfmap);

                                // activation
                                List<CcrSet> ccrSetOfmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ofmap, iPp)), 1) };
                                List<CcrClr> ccrClrIfmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap, iPp))) };
                                actionUpdater.UpdateMfuAct1(ifmapPp, ifmap2Pp, ofmapPp, ACT1_SOURCE_TYPE.l2, ACT1_SOURCE_TYPE.l2, _inputAType!, _inputAType!, _outputType!, _inputAType == DataTypes.Float16 ? new(0, 1) : ((TensorConst)act[GNNEActivation.DeqAParams]).Value.ToScalar<DeQuantizeParam>(), _inputBType == DataTypes.Float16 ? new(0, 1) : ((TensorConst)act[GNNEActivation.DeqBParams]).Value.ToScalar<DeQuantizeParam>(), ((TensorConst)act[GNNEActivation.InAShiftBits]).Value.ToScalar<int>(), ((TensorConst)act[GNNEActivation.InBShiftBits]).Value.ToScalar<int>(), ((TensorConst)act[GNNEActivation.OutShiftBits]).Value.ToScalar<int>(), ((TensorConst)act[GNNEActivation.Is16Segments]).Value.ToScalar<bool>(), iPp, ccrSetOfmap, ccrClrIfmap, 0, 0, 0, 0, ItemName.Ifmap2, ItemName.Ifmap, ItemName.MfuAct1, ((GNNEActivation)act.Target).Type == GnneActivationType.Mul ? MFU_ACT1_FUNCTION.mul : MFU_ACT1_FUNCTION.add);

                                // store
                                List<CcrClr> ccr_clr_ofmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ofmap, iPp))) };
                                actionUpdater.UpdateStoreT(ofmapPp, _sof!, iPp, ddrOf, 0, null!, null!, ccr_clr_ofmap);
                            }
                        }
                    }
                }
            }
        }

        Assert(ccrHandler.CcrSanityCheck());
        return actions;
    }

    private TileAct1Glb SearchGlbParameters(Call ld, Call st, Call act)
    {
        if (act[GNNEActivation.InputB] != None.Default)
        {
            GNNEShape minShape = new(0, 0, 0, 0);
            for (int i = 0; i < 4; i++)
            {
                minShape[i] = Math.Min(_inputAShape![i], _inputBShape![i]);
            }

            GNNEShape scaleA = new(0, 0, 0, 0), scaleB = new(0, 0, 0, 0), scaleOut = new(0, 0, 0, 0);
            for (int i = 0; i < 4; i++)
            {
                scaleA[i] = minShape[i] == 1 ? 1 : _inputAShape![i] / minShape[i];
                scaleB[i] = minShape[i] == 1 ? 1 : _inputBShape![i] / minShape[i];
                scaleOut[i] = minShape[i] == 1 ? 1 : _outputShape![i] / minShape[i];
            }

            int nPingPongSplit = GNNEEnv.NPingPongSplit;

            // TODO: pin-pon slpit on other dims
            int n = scaleOut[0];
            int c = Math.Min(scaleOut[1] * nPingPongSplit, _outputShape![1]);
            if (c == 1)
            {
                nPingPongSplit = 1;
            }

            int h = scaleOut[2];
            int w = scaleOut[3];
            GNNEShape o = new(n, c, h, w);
            GNNEShape a = new(0, 0, 0, 0), b = new(0, 0, 0, 0);

            void CalcInputShape()
            {
                for (int i = 0; i < 4; i++)
                {
                    a[i] = Math.Min(o[i] / scaleOut[i] * scaleA[i], _inputAShape![i]);
                    b[i] = Math.Min(o[i] / scaleOut[i] * scaleB[i], _inputBShape![i]);
                }
            }

            CalcInputShape();

            AllocateResult allocation;
            while (o[3] < _outputShape[3])
            {
                o[3] = Math.Min(o[3] + scaleOut[3], _outputShape[3]);
                CalcInputShape();
                allocation = HandleAllocate(o, a, b, nPingPongSplit);

                if (allocation.IsOk)
                {
                    w = o[3];
                }
                else
                {
                    o[3] = w;
                    break;
                }
            }

            while (o[2] < _outputShape[2])
            {
                o[2] = Math.Min(o[2] + (scaleOut[2] * nPingPongSplit), _outputShape[2]);
                CalcInputShape();
                allocation = HandleAllocate(o, a, b, nPingPongSplit);

                if (allocation.IsOk)
                {
                    h = o[2];
                }
                else
                {
                    o[2] = h;
                    break;
                }
            }

            while (o[1] < _outputShape[1])
            {
                o[1] = Math.Min(o[1] + scaleOut[1], _outputShape[1]);
                CalcInputShape();
                allocation = HandleAllocate(o, a, b, nPingPongSplit);

                if (allocation.IsOk)
                {
                    c = o[1];
                }
                else
                {
                    o[1] = c;
                    break;
                }
            }

            while (o[0] < _outputShape[0])
            {
                o[0] = Math.Min(o[0] + scaleOut[0], _outputShape[0]);
                CalcInputShape();
                allocation = HandleAllocate(o, a, b, nPingPongSplit);

                if (allocation.IsOk)
                {
                    n = o[0];
                }
                else
                {
                    o[0] = n;
                    break;
                }
            }

            CalcInputShape();
            allocation = HandleAllocate(o, a, b, nPingPongSplit);
            Assert(allocation.IsOk);

            GNNEShape lastOutShape = new(n, c, h, w);
            return new TileAct1Glb(allocation.GlbMap, allocation.Items, lastOutShape.Dims, GNNEEnv.NPingPongSplit, allocation.GlbMap[ItemName.Ifmap], allocation.GlbMap[ItemName.Ofmap]);
        }
        else
        {
            int n = 1;
            int c = 1;
            int h = 1;
            int w = _inputAShape![3];
            int oh = _outputShape![2] / _inputAShape[2];
            int ow = _outputShape[3];

            int nPingPongSplit = GNNEEnv.NPingPongSplit;

            // try to ping pong with minimal tile
            var allocation = HandleAllocate(n, c, h, w, oh, ow, nPingPongSplit);
            if (!allocation.IsOk)
            {
                nPingPongSplit = 1;
            }

            while (c < _inputAShape[1])
            {
                allocation = HandleAllocate(n, c + 1, h, w, oh, ow, nPingPongSplit);

                if (allocation.IsOk)
                {
                    c += 1;
                }
                else
                {
                    break;
                }
            }

            while (h < _inputAShape[2])
            {
                int nextH = h + 1;
                int nextOh = nextH * (_outputShape[2] / _inputAShape[2]);
                allocation = HandleAllocate(n, c, nextH, w, nextOh, ow, nPingPongSplit);

                if (allocation.IsOk)
                {
                    h += 1;
                    oh = h * (_outputShape[2] / _inputAShape[2]);
                }
                else
                {
                    break;
                }
            }

            while (n < _inputAShape[0])
            {
                allocation = HandleAllocate(n + 1, c, h, w, oh, ow, nPingPongSplit);

                if (allocation.IsOk)
                {
                    n += 1;
                }
                else
                {
                    break;
                }
            }

            allocation = HandleAllocate(n, c, h, w, oh, ow, nPingPongSplit, true);
            Assert(allocation.IsOk);

            GNNEShape lastOutShape = new(n, c, oh, ow);
            return new TileAct1Glb(allocation.GlbMap, allocation.Items, lastOutShape.Dims, GNNEEnv.NPingPongSplit, allocation.GlbMap[ItemName.Ifmap], allocation.GlbMap[ItemName.Ofmap]);
        }
    }

    private AllocateResult HandleAllocate(int n, int c, int h, int w, int e, int f, int nPingPongSplit, bool isFinal = false)
    {
        var bp = new BoxPacker(16);
        Dictionary<ItemName, TensorOnGlb> glbMap = new();

        int cPp = (int)Math.Ceiling(1.0f * c / nPingPongSplit);

        // load src1
        TensorOnGlb ifGlb = new(new[] { n, cPp, h, w }, _inputAType!, 0);
        int ifmapSize = ifGlb.AllocatedBytes * nPingPongSplit;
        ifmapSize = GetAlignedNum(ifmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);

        TensorOnGlb ofGlb = new(new[] { n, cPp, e, f }, _outputType!, 0);
        int ofmapSize = ofGlb.AllocatedBytes * nPingPongSplit;
        ofmapSize = GetAlignedNum(ofmapSize, GNNEEnv.OfmapBankWidth * GNNEEnv.GlbBankWidth);

        // all act to glb
        bool is16Segments = ((TensorConst)_act1![GNNEActivation.Is16Segments]).Value.ToScalar<bool>();
        int chanNum = is16Segments ? 1 : _inputAShape![1];
        int actParamNum = is16Segments ? 49 : GNNEEnv.ActNumPerChan;
        int actSize = SpaceSearcher.GetActSize(chanNum, actParamNum, GetBytesPerElement(_lact!.CheckedDataType));
        actSize = GetAlignedNum(actSize, GNNEEnv.ActBankWidth * GNNEEnv.GlbBankWidth);

        int basementSize = SpaceSearcher.GetBasementSize();
        basementSize = GetAlignedNum(basementSize, GNNEEnv.GlbWidth * GNNEEnv.GlbBankWidth);

        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.GlbWidth, basementSize / GNNEEnv.GlbWidth / GNNEEnv.GlbBankWidth }, ItemName.Basement));
        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.IfmapBankWidth, ifmapSize / GNNEEnv.IfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ifmap));
        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.OfmapBankWidth, ofmapSize / GNNEEnv.OfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ofmap));
        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.ActBankWidth, actSize / GNNEEnv.ActBankWidth / GNNEEnv.GlbBankWidth }, ItemName.MfuAct1));

        var allocation = SpaceSearcher.TryAllocate(bp);

#if DEBUG
        if (isFinal && allocation.IsOk)
        {
            bp.DisplayFinalAllocation();
        }
#endif

        glbMap.Add(ItemName.Ifmap, ifGlb);
        glbMap.Add(ItemName.Ofmap, ofGlb);
        glbMap.Add(ItemName.MfuAct1, new TensorOnGlb(new[] { 0, 0, 0, 0 }, _lact.CheckedDataType, 0));
        if (allocation.IsOk)
        {
            foreach (var m in glbMap)
            {
                m.Value.Mmu = allocation.Items[m.Key];
            }
        }

        AllocateResult ret = new() { IsOk = allocation.IsOk, Items = allocation.Items, GlbMap = glbMap };

        return ret;
    }

    private AllocateResult HandleAllocate(GNNEShape o, GNNEShape a, GNNEShape b, int nPingPongSplit, bool isFinal = false)
    {
        var bp = new BoxPacker(16);
        Dictionary<ItemName, TensorOnGlb> glbMap = new();

        int minC = Math.Min(a[1], b[1]);
        int cPp = (int)Math.Ceiling(1.0f * minC / nPingPongSplit);

        TensorOnGlb ifAGlb = new(new[] { a[0], a[1] / minC * cPp, a[2], a[3] }, _inputAType!, 0);
        int ifmapASize = ifAGlb.AllocatedBytes * nPingPongSplit;
        ifmapASize = GetAlignedNum(ifmapASize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);

        TensorOnGlb ifBGlb = new(new[] { b[0], b[1] / minC * cPp, b[2], b[3] }, _inputBType!, 0);
        int ifmapBSize = ifBGlb.AllocatedBytes * nPingPongSplit;
        ifmapBSize = GetAlignedNum(ifmapBSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);

        TensorOnGlb ofGlb = new(new[] { o[0], o[1] / minC * cPp, o[2], o[3] }, _outputType!, 0);
        int ofmapSize = ofGlb.AllocatedBytes * nPingPongSplit;
        ofmapSize = GetAlignedNum(ofmapSize, GNNEEnv.OfmapBankWidth * GNNEEnv.GlbBankWidth);

        // all act to glb
        bool is16Segments = ((TensorConst)_act1![GNNEActivation.Is16Segments]).Value.ToScalar<bool>();
        int chanNum = is16Segments ? 1 : _inputAShape![1];
        int actParamNum = is16Segments ? 49 : GNNEEnv.ActNumPerChan;
        int actSize = SpaceSearcher.GetActSize(chanNum, actParamNum, GetBytesPerElement(_lact!.CheckedDataType));
        actSize = GetAlignedNum(actSize, GNNEEnv.ActBankWidth * GNNEEnv.GlbBankWidth);

        int basementSize = SpaceSearcher.GetBasementSize();
        basementSize = GetAlignedNum(basementSize, GNNEEnv.GlbWidth * GNNEEnv.GlbBankWidth);

        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.GlbWidth, basementSize / GNNEEnv.GlbWidth / GNNEEnv.GlbBankWidth }, ItemName.Basement));
        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.IfmapBankWidth, ifmapASize / GNNEEnv.IfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ifmap));
        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.IfmapBankWidth, ifmapBSize / GNNEEnv.IfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ifmap2));
        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.OfmapBankWidth, ofmapSize / GNNEEnv.OfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ofmap));
        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.ActBankWidth, actSize / GNNEEnv.ActBankWidth / GNNEEnv.GlbBankWidth }, ItemName.MfuAct1));

        var allocation = SpaceSearcher.TryAllocate(bp);

#if DEBUG
        if (isFinal && allocation.IsOk)
        {
            bp.DisplayFinalAllocation();
        }
#endif

        glbMap.Add(ItemName.Ifmap, ifAGlb);
        glbMap.Add(ItemName.Ifmap2, ifBGlb);
        glbMap.Add(ItemName.Ofmap, ofGlb);
        glbMap.Add(ItemName.MfuAct1, new TensorOnGlb(new[] { 0, 0, 0, 0 }, _lact.CheckedDataType, 0));
        if (allocation.IsOk)
        {
            foreach (var m in glbMap)
            {
                m.Value.Mmu = allocation.Items[m.Key];
            }
        }

        AllocateResult ret = new() { IsOk = allocation.IsOk, Items = allocation.Items, GlbMap = glbMap };

        return ret;
    }

    private AllocateResult HandleAllocate(int n, int c, int h, int w, int e, int f, DataType inType, DataType outType, bool isFinal = false)
    {
        var bp = new BoxPacker(16);
        Dictionary<ItemName, TensorOnGlb> glbMap = new();

        int bytesPerElement = GetBytesPerElement(inType);
        int alignedNum = bytesPerElement == 1 ? 32 : 16;
        int wTemp = GetAlignedNum(w, alignedNum);
        var ifGlb = new TensorOnGlb(new[] { n, c, h, wTemp }, inType, 0);
        int ifmapSize = ifGlb.GlbNByte * GNNEEnv.NPingPongSplit;
        ifmapSize = GetAlignedNum(ifmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);

        alignedNum = GetBytesPerElement(outType) == 1 ? 32 : 16;
        int fTemp = GetAlignedNum(f, alignedNum);
        var ofGlb = new TensorOnGlb(new[] { n, c, e, fTemp }, outType, 0);
        int ofmapSize = ofGlb.GlbNByte * GNNEEnv.NPingPongSplit;
        ofmapSize = GetAlignedNum(ofmapSize, GNNEEnv.OfmapBankWidth * GNNEEnv.GlbBankWidth);

        int basementSize = SpaceSearcher.GetBasementSize();
        basementSize = GetAlignedNum(basementSize, GNNEEnv.GlbWidth * GNNEEnv.GlbBankWidth);

        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.GlbWidth, basementSize / GNNEEnv.GlbWidth / GNNEEnv.GlbBankWidth }, ItemName.Basement));
        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.IfmapBankWidth, ifmapSize / GNNEEnv.IfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ifmap));
        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.OfmapBankWidth, ofmapSize / GNNEEnv.OfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ofmap));

        var allocation = SpaceSearcher.TryAllocate(bp);

#if DEBUG
        if (isFinal && allocation.IsOk)
        {
            bp.DisplayFinalAllocation();
        }
#endif

        if (allocation.IsOk)
        {
            ifGlb.Mmu = allocation.Items[ItemName.Ifmap];
            ofGlb.Mmu = allocation.Items[ItemName.Ofmap];
        }

        glbMap.Add(ItemName.Ifmap, ifGlb);
        glbMap.Add(ItemName.Ofmap, ofGlb);
        allocation.GlbMap = glbMap;
        return allocation;
    }

    private void InitParameters(Call act, Call ld, Call st)
    {
        _act1 = act;

        _inputAType = _act1[GNNEActivation.InputA].CheckedDataType;
        _inputBType = _act1[GNNEActivation.InputB] != None.Default ? _act1[GNNEActivation.InputB].CheckedDataType : DataTypes.Float16;
        _outputType = act.CheckedDataType;

        _lif1 = ld;
        if (_act1[GNNEActivation.InputB] != None.Default)
        {
            _lif2 = (Call)_act1[GNNEActivation.InputB];
        }

        _lact = (Call)_act1[GNNEActivation.Act];
        _sof = st;

        _inputAShape = new((int)_lif1.CheckedShape[0].FixedValue, (int)_lif1.CheckedShape[1].FixedValue, (int)_lif1.CheckedShape[2].FixedValue, (int)_lif1.CheckedShape[3].FixedValue);
        _inputBShape = _act1[GNNEActivation.InputB] != None.Default ? new((int)_lif2!.CheckedShape[0].FixedValue, (int)_lif2.CheckedShape[1].FixedValue, (int)_lif2.CheckedShape[2].FixedValue, (int)_lif2.CheckedShape[3].FixedValue) : _inputAShape;
        _outputShape = new((int)st.CheckedShape[0].FixedValue, (int)st.CheckedShape[1].FixedValue, (int)st.CheckedShape[2].FixedValue, (int)st.CheckedShape[3].FixedValue);
    }

    private PrimFunction GetReplace(Call call, GNNEActivation callOp, Call ld, Call st)
    {
        _count++;

        InitParameters(call, ld, st);
        var tiledGlb = SearchGlbParameters(ld, st, call);
        long[] inShape = ld.Arguments[0].CheckedShape.ToValueArray();
        long[] inShape2 = inShape;
        if (call[GNNEActivation.InputB] != None.Default)
        {
            inShape2 = call[GNNEActivation.InputB].CheckedShape.ToValueArray();
        }

        T.CreateBuffer(new(ld[GNNELoad.Input].CheckedDataType, inShape), MemoryLocation.Input, out var ddrIf);
        var ddrIf2 = ddrIf;
        List<TIR.Buffer> buffers = new() { ddrIf };
        if (call[GNNEActivation.InputB] != None.Default)
        {
            if (((Call)call[GNNEActivation.InputB])[GNNELoad.Input] is not TensorConst)
            {
                T.CreateBuffer(new(((Call)call[GNNEActivation.InputB])[GNNELoad.Input].CheckedDataType, inShape2), MemoryLocation.Input, out ddrIf2);
                buffers.Add(ddrIf2);
            }
            else
            {
                T.AttachBuffer((TensorConst)((Call)call[GNNEActivation.InputB])[GNNELoad.Input], out ddrIf2);
            }
        }

        T.CreateBuffer(new(st.CheckedDataType, st.CheckedShape), MemoryLocation.Output, out var ddrOf);
        buffers.Add(ddrOf);
        T.AttachBuffer((TensorConst)((Call)call[GNNEActivation.Act])[GNNELoadW.Input], out var ddrAct);
        var actions = BuildSchedule(tiledGlb, call, ld, st, ddrIf, ddrOf, ddrAct, ddrIf2);
        var actionConverter = new ActionToInstruct();
        return T.PrimFunc($"TileAct1_{_count}", Runtime.K230.K230RtModule.Kind, buffers.Select(b => new Var(b.CheckedType)).ToArray()).Body(
                actionConverter.Instructions(actions),
                I.END(GP_REGISTER.x0, 0))
            .Build();
    }
}

internal class TileAct1Glb : TiledGlb
{
    private TensorOnGlb _ifGlb;
    private TensorOnGlb _ofGlb;

    public TileAct1Glb(Dictionary<ItemName, TensorOnGlb> glbMap, Dictionary<ItemName, MmuItem> items, int[] lastOutShape, int nPingPongSplit, TensorOnGlb ifGlb, TensorOnGlb ofGlb)
        : base(glbMap, items, lastOutShape, nPingPongSplit)
    {
        _ifGlb = ifGlb;
        _ofGlb = ofGlb;
    }
}
