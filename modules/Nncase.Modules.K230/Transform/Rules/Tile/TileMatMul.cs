﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using Nncase.Runtime.K230;
using Nncase.Schedule;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using static Nncase.Passes.Rules.K230.TileUtilities;
using Buffer = Nncase.TIR.Buffer;
using tensor_ccr_stat = System.Collections.Generic.List<System.Tuple<Nncase.TIR.SegmentND, Nncase.Passes.Rules.K230.TensorStat>>;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class TileMatMul : RewriteRule<Pattern>
{
    private static int _count = -1;

    // record tensor info to control ccr
    private readonly tensor_ccr_stat[] _ofmapRec = new tensor_ccr_stat[2];
    private readonly tensor_ccr_stat[] _g2LIfRec = new tensor_ccr_stat[2];
    private readonly tensor_ccr_stat[] _g2RWRec = new tensor_ccr_stat[2];
    private readonly tensor_ccr_stat[] _l2GOfRec = new tensor_ccr_stat[2];
    private readonly Dictionary<Var, Buffer> _ifBufferMap = new(ReferenceEqualityComparer.Instance);
    private CcrHandler _ccrHandler = new();
    private WeightGroupHandler _weightGroup = null!; // if_first & w_first specialized
    private GprHandler _gpr = new();
    private SsrHandler _ssr = new();

    // private GlbSearchStrategy? _strategy;
    private DataType? _inputType;
    private DataType? _outputType;
    private DataType? _weightType;

    // private DataType? _psumType;
    private Call? _mm;

    private Call? _lif;
    private Call? _lw;
    private Call? _lact;
    private Call? _lwQarg;
    private Call? _sof;

    private GNNEShape? _inputShape;
    private GNNEShape? _outputShape;
    private GNNEShape? _weightsShape;

    // private Padding? _paddingH;
    // private Padding? _paddingW;
    private int _strideH;
    private int _strideW;
    private int _dilationH;
    private int _dilationW;
    private bool _broadcastW;
    private bool _broadcastIf;

    public override Pattern Pattern { get; } = FusionPattern.IsGNNEMatMulFusion();

    private PrimFunction GetReplace(Call call, Call ld, Call st)
    {
        _count++;

        InitParameters(call, ld, st);
        var tiledGlb = SearchGlbParameters();
        long[] inShape = ((Call)_mm![GNNEMatMul.InputB])[GNNELoad.Input].CheckedShape.ToValueArray();
        TIR.Buffer ddrIf, ddrW;
        List<TIR.Buffer> buffers = new();
        if (((Call)_mm[GNNEMatMul.InputB])[GNNELoad.Input] is TensorConst)
        {
            T.AttachBuffer((TensorConst)((Call)call[GNNEMatMul.InputB])[GNNELoad.Input], out ddrIf);
        }
        else
        {
            T.CreateBuffer(new(((Call)_mm[GNNEMatMul.InputB])[GNNELoad.Input].CheckedDataType, inShape), MemoryLocation.Input, out ddrIf);
            buffers.Add(ddrIf);
            _ifBufferMap.Add((Var)((Call)_mm[GNNEMatMul.InputB])[GNNELoad.Input], ddrIf);
        }

        long[] wShape = ((Call)_mm[GNNEMatMul.InputA])[GNNELoad.Input].CheckedShape.ToValueArray();
        if (((Call)_mm[GNNEMatMul.InputA])[GNNELoad.Input] is TensorConst)
        {
            T.AttachBuffer((TensorConst)((Call)call[GNNEMatMul.InputA])[GNNELoad.Input], out ddrW);
        }
        else
        {
            T.CreateBuffer(new(((Call)_mm[GNNEMatMul.InputA])[GNNELoad.Input].CheckedDataType, wShape), MemoryLocation.Input, out ddrW);
            buffers.Add(ddrW);
            _ifBufferMap.Add((Var)((Call)_mm[GNNEMatMul.InputA])[GNNELoad.Input], ddrW);
        }

        if (buffers.Count == 2)
        {
            buffers.Reverse();
        }

        T.CreateBuffer(new(st.CheckedDataType, st.CheckedShape), MemoryLocation.Output, out var ddrOf);
        buffers.Add(ddrOf);
        T.AttachBuffer((TensorConst)((Call)call[GNNEMatMul.InputABias])[GNNELoadW.Input], out var ddrWQarg);
        T.AttachBuffer((TensorConst)((Call)call[GNNEMatMul.Act])[GNNELoadW.Input], out var ddrAct);

        var actions = new List<GnneAction>();

        ItemRecStatusInit();
        BuildSchedule(tiledGlb, call, ld, st, ddrIf, ddrOf, ddrW, ddrWQarg, ddrAct, true, _weightGroup);
        actions = BuildSchedule(tiledGlb, call, ld, st, ddrIf, ddrOf, ddrW, ddrWQarg, ddrAct, false, _weightGroup);
        var actionConverter = new ActionToInstruct();
        return T.PrimFunc($"TileMatMul_{_count}", K230RtModule.Kind, buffers.Select(b => new Var(b.CheckedType)).ToArray()).Body(
            actionConverter.Instructions(actions),
            I.END(GP_REGISTER.x0, 0))
        .Build();
    }

    private List<GnneAction> BuildSchedule(TiledGlb glb, Call conv, Call ld, Call st, TIR.Buffer ddrIf, TIR.Buffer ddrOf, TIR.Buffer ddrW, TIR.Buffer ddrWQarg, TIR.Buffer ddrAct, bool weightGroupOnly, WeightGroupHandler weightGroup)
    {
        List<GnneAction> actions = new();
        if (!weightGroupOnly)
        {
            _gpr = new(GNNEEnv.GprNum);
            _ssr = new(GNNEEnv.SsrNum);
            _ccrHandler = new CcrHandler();
        }

        GnneActionUpdater actionUpdater = new(actions, glb, _ccrHandler, _gpr, _ssr);

        if (!weightGroupOnly)
        {
            UpdateCcrRecStat();
        }

        // mmu conf
        if (!weightGroupOnly)
        {
            actionUpdater.UpdateMmuConf();
        }

        int ofPp = 0;
        int iPp = -1;
        int wPp = -1;
        int ofBufNum = GNNEEnv.NPingPongSplit;

        var outputBatchSeg =
            GetSegmentStartEndLength(0, glb.LastOutShape[0], _outputShape![1]);
        var outputColSeg =
            GetSegmentStartEndLength(0, glb.LastOutShape[3], _outputShape[3]);
        var outChanSeg =
            GetSegmentStartEndLength(0, glb.LastOutShape[1], _outputShape[2]);
        var inChanSeg =
            GetSegmentStartEndLength(0, glb.GlbMap[ItemName.Ifmap].Dimensions[1], _weightsShape![3]);

        // weights bias
        if (!weightGroupOnly)
        {
            actionUpdater.UpdateLoadAct(_lact!, ddrAct);
            if (_weightType == DataTypes.UInt8 || _weightType == DataTypes.Int16)
            {
                for (int n = 0; n < (_broadcastW ? 1 : _outputShape[1]); n++)
                {
                    actionUpdater.UpdateLoadWQargMatmul(_lwQarg!, weightGroup, ddrWQarg, n);
                }
            }
        }

        Segment1D none = new(..0, new(0, 0));
        Segment1D one = new(..1, new(0, 0));
        var pingPongIf = new List<SegmentND> { new(none, none, none, none), new(none, none, none, none) };
        var pingPongW = new List<SegmentND> { new(none, none, none, none), new(none, none, none, none) };
        var glbOutputRow = one;
        var glbInputRow = glbOutputRow;
        foreach (var glbOutputChannel in outChanSeg)
        {
            foreach (var glbOutputBatch in outputBatchSeg)
            {
                foreach (var glbOutputColumn in outputColSeg)
                {
                    {
                        SegmentND ofmap = new(glbOutputBatch, glbOutputChannel, glbOutputRow, glbOutputColumn);
                        SegmentND ofmapSt = new(one, glbOutputBatch, glbOutputChannel, glbOutputColumn);

                        foreach (var glbInputChannel in inChanSeg)
                        {
                            var glbInputColumn = glbOutputColumn;
                            SegmentND weight = new(glbOutputChannel, glbInputChannel, one, one);
                            SegmentND weightLd = new(one, _broadcastW ? one : glbOutputBatch, glbOutputChannel, glbInputChannel);

                            if (pingPongW[0] == weightLd)
                            {
                                wPp = 0;
                            }
                            else if (pingPongW[1] == weightLd)
                            {
                                wPp = 1;
                            }
                            else
                            {
                                wPp = (wPp + 1) % 2;

                                if (!weightGroupOnly)
                                {
                                    if (_g2RWRec[wPp].Count > 0 && _g2RWRec[wPp][0].Item1 == weightLd)
                                    {
                                        int ccrSetNum = _g2RWRec[wPp][0].Item2 is { IsFirstSlice: true, IsLastSlice: true } ? 1 : 2;
                                        List<CcrSet> ccrSetW = new()
                                        {
                                            new(
                                                _ccrHandler.GetCcrItem(
                                                    _ccrHandler.GetName(ItemName.Weight, wPp)),
                                                ccrSetNum),
                                        };
                                        List<int> stridesD = new()
                                        {
                                            weightLd[1].Length,
                                            glbOutputChannel.Length,
                                            GetAlignedNum(
                                                glbInputChannel.Length,
                                                GNNEEnv.WAlignNum),
                                        };
                                        actionUpdater.UpdateLoadIf(weightLd, _lw!, wPp, ddrW, 0, stridesD, ItemName.Weight, ccrSetW);
                                    }
                                }

                                pingPongW[wPp] = weightLd;
                            }

                            SegmentND ifmap = new(_broadcastIf ? one : glbOutputBatch, glbInputChannel, glbInputRow, glbInputColumn);
                            SegmentND ifmapLd = new(one, _broadcastIf ? one : glbOutputBatch, glbInputChannel, glbInputColumn);

                            if (pingPongIf[0] == ifmapLd)
                            {
                                iPp = 0;
                            }
                            else if (pingPongIf[1] == ifmapLd)
                            {
                                iPp = 1;
                            }
                            else
                            {
                                iPp = (iPp + 1) % 2;
                                if (!weightGroupOnly)
                                {
                                    if (_g2LIfRec[iPp].Count > 0 && _g2LIfRec[iPp][0].Item1 == ifmapLd)
                                    {
                                        int ccrSetNum = _g2LIfRec[iPp][0].Item2 is { IsFirstSlice: true, IsLastSlice: true }
                                            ? 1
                                            : 2;
                                        List<CcrSet> ccrSetIfmap = new()
                                        {
                                            new(
                                                _ccrHandler.GetCcrItem(
                                                    _ccrHandler.GetName(ItemName.Ifmap, iPp)),
                                                ccrSetNum),
                                        };
                                        List<int> stridesD = new()
                                        {
                                            glb.GlbMap[ItemName.Ifmap].Dimensions[0],
                                            glb.GlbMap[ItemName.Ifmap].Dimensions[1],
                                            glb.GlbMap[ItemName.Ifmap].Dimensions[3],
                                        };
                                        actionUpdater.UpdateLoadIf(ifmapLd, _lif!, iPp, ddrIf, 0, stridesD, ItemName.Ifmap, ccrSetIfmap);
                                    }
                                }

                                pingPongIf[iPp] = ifmapLd;
                            }

                            var ifmap2 = ofmap;

                            // l1 schedule
                            BuildL1Schedule(actionUpdater, glb, ifmap, weight, ofmap, iPp, ofPp, wPp, weightGroupOnly, weightGroup);
                        }

                        if (!weightGroupOnly)
                        {
                            int ofmapSetCnt = _ofmapRec[ofPp][0].Item2.IsLastSlice ? 0 : 1;
                            _ofmapRec[ofPp].RemoveAt(0);
                            List<CcrSet> ccrSetOfmap = new();
                            if (ofmapSetCnt > 0)
                            {
                                ccrSetOfmap.Add(new(
                                    _ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.OfmapFake, ofPp)), 1));
                            }

                            List<CcrClr> ccrClrOfmap = new()
                            {
                                new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, ofPp))),
                            };
                            List<int> stridesS = new()
                            {
                                glb.GlbMap[ItemName.Ofmap].Dimensions[0],
                                glb.GlbMap[ItemName.Ofmap].Dimensions[1],
                                glb.GlbMap[ItemName.Ofmap].Dimensions[3],
                            };
                            actionUpdater.UpdateStoreT(ofmapSt, _sof!, ofPp, ddrOf, 0, null!, ccrSetOfmap, ccrClrOfmap, ItemName.Ofmap, stridesS);
                        }
                        else
                        {
                            _ofmapRec[ofPp].Add(new(ofmapSt, new TensorStat(false, false)));
                        }

                        ofPp = (ofPp + 1) % ofBufNum;
                    }
                }
            }
        }

        // pe_usage(&pe_usage_param);
        Assert(_ccrHandler.CcrSanityCheck());
        return actions;
    }

    private List<int> L1Search(TiledGlb glb, SegmentND weight, SegmentND psum)
    {
        int c = Math.Min(GNNEEnv.PuHeight, _weightsShape![3]);
        int m = Math.Min(GNNEEnv.PuWidth, _weightsShape[2]);
        int h = 1;
        int w = 1;
        int e = 1;
        int f = 1;
        int r = 1;
        int s = 1;

        int psumPingPangSplit = 1;
        int ifBytesPerElementGlb = GetBytesPerElement(_inputType!);
        bool ok = HandleL1Allocate(h, w, e, f, psumPingPangSplit, ifBytesPerElementGlb);
        if (!ok)
        {
            throw new NotSupportedException("exceeds L1 size");
        }

        bool IncreaseFBy1()
        {
            int nextF = f + 1;
            int nextW = nextF;

            bool ok = HandleL1Allocate(h, nextW, e, nextF, psumPingPangSplit, ifBytesPerElementGlb);
            if (ok)
            {
                f = nextF;
                w = nextW;
            }

            return ok;
        }

        while (f < psum[3].Length && h * w * ifBytesPerElementGlb < GNNEEnv.IfL1Size / GNNEEnv.PuHeight / 2)
        {
            if (!IncreaseFBy1())
            {
                break;
            }
        }

        return new()
        {
            c,
            m,
            e,
            f,
            r,
            s,
        };
    }

    private bool HandleL1Allocate(int h, int w, int e, int f, int psumPingPangSplit, int ifBytesPerElementGlb)
    {
        if (e * f > GNNEEnv.PsumL1ElePerChan / psumPingPangSplit)
        {
            return false;
        }

        if (GNNEEnv.PuHeight * h * w * ifBytesPerElementGlb > GNNEEnv.IfL1Size)
        {
            return false;
        }

        return true;
    }

    private void BuildL1Schedule(GnneActionUpdater actionUpdater, TiledGlb glb, SegmentND ifmap, SegmentND weight, SegmentND psum, int iPp, int ofPp, int wPp, bool weightGroupOnly, WeightGroupHandler weightGroup)
    {
        int l1Pp = 0;
        var l1Tile = L1Search(glb, weight, psum);
        var eSeg = GetSegmentStartEndLength(psum[2].Start, l1Tile[2], psum[2].End);
        var fSeg = GetSegmentStartEndLength(psum[3].Start, l1Tile[3], psum[3].End);
        var nSeg = GetSegmentStartEndLength(psum[0].Start, 1, psum[0].End);
        var rSeg = GetSegmentStartEndLength(weight[2].Start, l1Tile[4], weight[2].End);
        var seg = GetSegmentStartEndLength(weight[3].Start, l1Tile[5], weight[3].End);
        var ret = GetL1McSeg(ifmap, psum, l1Tile[1], l1Tile[0]);
        var mSeg = ret[0];
        var cSeg = ret[1];

        Segment1D one = new(..1, new(0, 0));
        SegmentND weightLd = new(one, _broadcastW ? one : psum[0], weight[0], weight[1]);
        SegmentND ifmapLd = new(one, _broadcastIf ? one : psum[0], weight[1], ifmap[3]);
        SegmentND ofmapSt = new(one, psum[0], psum[1], psum[3]);

        foreach (var m in mSeg)
        {
            foreach (var n in nSeg)
            {
                Segment1D e = one, h = one;
                foreach (var f in fSeg)
                {
                    var w = f;
                    SegmentND l2GOf = new(n, m, e, f);
                    SegmentND r2LPsum = new(n, m, h, w);

                    bool
                        ofmapCalStart =
                            true; // to fix the problem r2l_psum != 0 but l2r_if == 0; which means the psum is calculation by the pad value
                    foreach (var c in cSeg)
                    {
                        var wcInloop = c;
                        var rL2 = one;
                        var sL2 = one;

                        var cInloop = c;
                        var nInloop = n;
                        var mInloop = m;
                        SegmentND ifmapSlice = new(_broadcastIf ? one : nInloop, cInloop, h, w);
                        SegmentND weightSlice = new(m, wcInloop, rL2, sL2);

                        bool dmLoadIfSn = false; // to fix the problem the l2r_if == 0 at the first element of kernal, but l2r_if != 0 at the last first element of kernal;
                        var g2LIf = ShiftInputTensor(ifmapSlice, weightSlice, 1, 1, _strideH, _strideW, _dilationH, _dilationW);
                        if (g2LIf[0].Length > 0 && g2LIf[1].Length > 0 && g2LIf[2].Length > 0 && g2LIf[3].Length > 0)
                        {
                            dmLoadIfSn = true;
                            if (!weightGroupOnly)
                            {
                                int ifClrCnt = _g2LIfRec[iPp][0].Item2.Stat_cnt();
                                _g2LIfRec[iPp].RemoveAt(0);
                                List<CcrClr> g2LIfCcrClr = new();
                                if (ifClrCnt > 0)
                                {
                                    g2LIfCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ifmap, iPp))));
                                }

                                actionUpdater.UpdateG2LIf(g2LIf, ifmap, _lif!, iPp, null!, g2LIfCcrClr, 0, _inputType!, ItemName.Ifmap, false, 0, 0, 0, false, 1, _strideH);
                            }
                            else
                            {
                                _g2LIfRec[iPp].Add(new(ifmapLd, new(false, false)));
                            }
                        }

                        var rInloop = rL2;
                        var sInloop = sL2;
                        SegmentND l2RW = new(mInloop, wcInloop, rInloop, sInloop);
                        var l2RIf = ShiftInputTensor(ifmapSlice, l2RW, 1, 1, _strideH, _strideW, _dilationH, _dilationW);

                        int posW = _weightType == DataTypes.Int16 ? 2 : 1;
                        int posIf = _inputType == DataTypes.Int16 ? 2 : 1;
                        for (int pW = 0; pW < posW; pW++)
                        {
                            for (int pIf = 0; pIf < posIf; pIf++)
                            {
                                if (!weightGroupOnly)
                                {
                                    int wClrCnt = _g2RWRec[wPp][0].Item2.Stat_cnt();
                                    _g2RWRec[wPp].RemoveAt(0);
                                    List<CcrClr> g2RWCcrClr = new();
                                    if (wClrCnt > 0)
                                    {
                                        g2RWCcrClr.Add(new(
                                            _ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Weight, wPp))));
                                    }

                                    actionUpdater.UpdateG2RWMatmul(l2RW, weight, weightGroup, _weightsShape![2], _lw!, wPp, pW, null!, g2RWCcrClr, _broadcastW ? 0 : n.Start - nSeg[0].Start);
                                }
                                else
                                {
                                    if (n.Start == 0)
                                    {
                                        weightGroup.UpdateWeightGroup(l2RW);
                                    }

                                    _g2RWRec[wPp].Add(new(weightLd, new(false, false)));
                                }

                                if (!weightGroupOnly)
                                {
                                    actionUpdater.UpdateL2RIf(l2RIf, g2LIf, _strideH, _strideW, _weightsShape![3], _lif!, 0, pIf, ((TensorConst)_mm![GNNEMatMul.DeqBBias]).Value.ToArray<byte>()[l2RIf[0].Start], _inputType!, false, 1);
                                }

                                bool releaseIf = false;
                                if (pW == posW - 1 && pIf == posIf - 1 && dmLoadIfSn)
                                {
                                    releaseIf = true;
                                    dmLoadIfSn = false;
                                }

                                bool loopStart = false;
                                if (ofmapCalStart && pW == 0 && pIf == 0)
                                {
                                    loopStart = true;
                                    ofmapCalStart = false;
                                }

                                bool loopEnd = wcInloop.End == _weightsShape![3] && sInloop.End == weight[3].End &&
                                               rInloop.End == weight[2].End && pW == posW - 1 && pIf == posIf - 1;

                                var destType = _outputType;
                                var destTarget = ACT0_OUTPUT_DEST.dm;

                                if (!weightGroupOnly)
                                {
                                    int shift = ((TensorConst)_mm![GNNEMatMul.ShiftBits]).Value.ToScalar<int>();

                                    int l2GOfCcrSetNum = 0;
                                    int l2GOfCcrClrNum = 0;
                                    if (loopEnd)
                                    {
                                        var ofCcrStat = _l2GOfRec[ofPp][0];
                                        _l2GOfRec[ofPp].RemoveAt(0);
                                        if (ofCcrStat.Item2.IsFirstSlice && !_ofmapRec[ofPp][0].Item2.IsFirstSlice)
                                        {
                                            l2GOfCcrClrNum = 1;
                                        }

                                        if (ofCcrStat.Item2.IsLastSlice)
                                        {
                                            l2GOfCcrSetNum = 1;
                                        }
                                    }

                                    List<CcrSet> l2GOfCcrSet = new();
                                    {
                                        if (l2GOfCcrSetNum > 0)
                                        {
                                            l2GOfCcrSet.Add(new(
                                                _ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, ofPp)),
                                                1));
                                        }
                                    }

                                    List<CcrClr> l2GOfCcrClr = new();
                                    if (l2GOfCcrClrNum > 0)
                                    {
                                        l2GOfCcrClr.Add(new(
                                            _ccrHandler.GetCcrItem(
                                                _ccrHandler.GetName(ItemName.OfmapFake, ofPp))));
                                    }

                                    int actOffset = n.Start * glb.GlbMap[ItemName.Act].GlbNByte / _outputShape![1];
                                    destTarget = ACT0_OUTPUT_DEST.dm;
                                    actionUpdater.UpdateR2LPsum(shift, r2LPsum, r2LPsum, psum, ofPp, l1Pp, destTarget, releaseIf, Math.Max(pW, pIf), TcuComputeMode.MatMul, loopStart, loopEnd, _strideH, _strideW, _weightsShape[2], _inputType!, _weightType!, destType!, _lact!.CheckedDataType, l2GOfCcrSet, l2GOfCcrClr, actOffset);
                                }
                                else if (loopEnd && destTarget == ACT0_OUTPUT_DEST.dm)
                                {
                                    _l2GOfRec[ofPp].Add(new(ofmapSt, new(false, false)));
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private List<List<Segment1D>> GetL1McSeg(SegmentND ifmap, SegmentND psum, int mInloop, int cInloop)
    {
        List<Segment1D> mSeg = new();
        List<Segment1D> cSeg = new();

        mSeg = GetSegmentStartEndLength(psum[1].Start, mInloop, psum[1].End);
        cSeg = GetSegmentStartEndLength(ifmap[1].Start, cInloop, ifmap[1].End);

        return new() { mSeg, cSeg };
    }

    private void UpdateCcrRecStat()
    {
        // g2l_if_rec status decide
        foreach (var t in _g2LIfRec)
        {
            for (int cnt = 0; cnt < t.Count; cnt++)
            {
                if (cnt == 0)
                {
                    t[cnt].Item2.IsFirstSlice = true;
                }

                if (cnt == t.Count - 1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                }

                if (cnt < t.Count - 1 && t[cnt].Item1 != t[cnt + 1].Item1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                    t[cnt + 1].Item2.IsFirstSlice = true;
                }
            }
        }

        // g2r_w_rec status decide
        foreach (var t in _g2RWRec)
        {
            for (int cnt = 0; cnt < t.Count; cnt++)
            {
                if (cnt == 0)
                {
                    t[cnt].Item2.IsFirstSlice = true;
                }

                if (cnt == t.Count - 1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                }

                if (cnt < t.Count - 1 && t[cnt].Item1 != t[cnt + 1].Item1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                    t[cnt + 1].Item2.IsFirstSlice = true;
                }
            }
        }

        // l2g_of_rec status decide
        foreach (var t in _l2GOfRec)
        {
            for (int cnt = 0; cnt < t.Count; cnt++)
            {
                if (cnt == 0)
                {
                    t[cnt].Item2.IsFirstSlice = true;
                }

                if (cnt == t.Count - 1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                }

                if (cnt < t.Count - 1 && t[cnt].Item1 != t[cnt + 1].Item1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                    t[cnt + 1].Item2.IsFirstSlice = true;
                }
            }
        }

        // ofmap_rec status decide
        foreach (var t in _ofmapRec)
        {
            if (t.Count > 0)
            {
                t[0].Item2.IsFirstSlice = true;
                t[^1].Item2.IsLastSlice = true;
            }
        }
    }

    private void ItemRecStatusInit()
    {
        for (int i = 0; i < _ofmapRec.Length; i++)
        {
            if (_ofmapRec[i] is not null)
            {
                _ofmapRec[i].Clear();
            }
            else
            {
                _ofmapRec[i] = new tensor_ccr_stat();
            }

            if (_g2LIfRec[i] is not null)
            {
                _g2LIfRec[i].Clear();
            }
            else
            {
                _g2LIfRec[i] = new tensor_ccr_stat();
            }

            if (_g2RWRec[i] is not null)
            {
                _g2RWRec[i].Clear();
            }
            else
            {
                _g2RWRec[i] = new tensor_ccr_stat();
            }

            if (_l2GOfRec[i] is not null)
            {
                _l2GOfRec[i].Clear();
            }
            else
            {
                _l2GOfRec[i] = new tensor_ccr_stat();
            }
        }
    }

    private void InitParameters(Call convNode, Call ld, Call st)
    {
        _mm = convNode;

        _lif = (Call)_mm[GNNEMatMul.InputB];
        _lw = (Call)_mm[GNNEMatMul.InputA];
        _lact = (Call)_mm[GNNEMatMul.Act];
        _lwQarg = (Call)_mm[GNNEMatMul.InputABias];
        _sof = st;

        _inputShape = new GNNEShape((int)_lif.CheckedShape[0].FixedValue, (int)_lif.CheckedShape[1].FixedValue, (int)_lif.CheckedShape[2].FixedValue, (int)_lif.CheckedShape[3].FixedValue);
        _outputShape = new GNNEShape((int)_mm.CheckedShape[0].FixedValue, (int)_mm.CheckedShape[1].FixedValue, (int)_mm.CheckedShape[2].FixedValue, (int)_mm.CheckedShape[3].FixedValue);
        _weightsShape = new GNNEShape((int)_lw.CheckedShape[0].FixedValue, (int)_lw.CheckedShape[1].FixedValue, (int)_lw.CheckedShape[2].FixedValue, (int)_lw.CheckedShape[3].FixedValue);

        // _paddingH = null;
        // _paddingW = null;
        _strideH = 1;
        _strideW = 1;
        _dilationH = 1;
        _dilationW = 1;

        _inputType = _lif.CheckedDataType;
        _weightType = _lw.CheckedDataType;
        _weightGroup = new WeightGroupHandler(_weightType, _weightType);
        _outputType = _mm.CheckedDataType;

        _broadcastW = _weightsShape[1] == 1 ? true : false;
        _broadcastIf = _inputShape[1] == 1 ? true : false;
    }

    private TileMatMulGlb SearchGlbParameters()
    {
        int n = 1;
        int c = 1;
        int r = 1;
        int s = 1;

        int e = 1;
        int f = Math.Min(GNNEEnv.PuWidth, _outputShape![3]);

        int h = 1;
        int w = f;

        int m = 1;

        // pre allocate
        AllocateResult allocation;
        while (c < _weightsShape![3])
        {
            allocation = HandleAllocate(n, c + 1, h, w, r, s, m, e, f);
            if (allocation.IsOk)
            {
                c += 1;
            }
            else
            {
                break;
            }
        }

        while (m < GNNEEnv.PuWidth && m < _weightsShape[2])
        {
            allocation = HandleAllocate(n, c, h, w, r, s, m + 1, e, f);
            if (allocation.IsOk)
            {
                m += 1;
            }
            else
            {
                break;
            }
        }

        while (f < _outputShape[3])
        {
            int nextF = f + 1;
            int nextW = nextF;

            allocation = HandleAllocate(n, c, h, nextW, r, s, m, e, nextF);
            if (allocation.IsOk)
            {
                f = nextF;
                w = nextW;
            }
            else
            {
                break;
            }
        }

        while (m < _weightsShape[2])
        {
            allocation = HandleAllocate(n, c, h, w, r, s, m + 1, e, f);
            if (allocation.IsOk)
            {
                m += 1;
            }
            else
            {
                break;
            }
        }

        while (n < _outputShape[1])
        {
            allocation = HandleAllocate(n + 1, c, h, w, r, s, m, e, f);
            if (allocation.IsOk)
            {
                n += 1;
            }
            else
            {
                break;
            }
        }

        // m % GNNEEnv.pu_width == 0
        if (m % GNNEEnv.PuWidth != 0 && m > GNNEEnv.PuWidth)
        {
            m = m / GNNEEnv.PuWidth * GNNEEnv.PuWidth;
        }

        allocation = HandleAllocate(n, c, h, w, r, s, m, e, f, true);
        Assert(allocation.IsOk);

        var lastOutShape = new GNNEShape(n, m, e, f);
        return new TileMatMulGlb(allocation.GlbMap, allocation.Items, lastOutShape.Dims, GNNEEnv.NPingPongSplit, allocation.GlbMap[ItemName.Ifmap], allocation.GlbMap[ItemName.Ofmap]);
    }

    private AllocateResult HandleAllocate(int n, int c, int h, int w, int r, int s, int m, int e, int f, bool isFinal = false)
    {
        var glbUsage = new List<float> { 0, 0 };
        var boxes = GetBoxes(n, c, h, w, r, s, m, e, f, glbUsage);

        var bp = new BoxPacker(16) { Boxes = boxes.Boxes };

        var allocation = SpaceSearcher.TryAllocate(bp);
        allocation.GlbMap = boxes.GlbMap;

#if DEBUG
        if (isFinal && allocation.IsOk)
        {
            bp.DisplayFinalAllocation();
            Console.WriteLine("fusion type: matmul");
            Console.WriteLine($"GLB usage(tensor, mmu): {glbUsage[0]}\t{glbUsage[1]}");
            Console.WriteLine("--------------------------");
        }
#endif

        if (allocation.IsOk)
        {
            foreach (var map in boxes.GlbMap)
            {
                if (allocation.Items.ContainsKey(map.Key))
                {
                    map.Value.Mmu = allocation.Items[map.Key];
                }
            }
        }

        return allocation;
    }

    private AllocateResult GetBoxes(int n, int c, int h, int w, int r, int s, int m, int e, int f, List<float> glbUsage)
    {
        List<BoxOnGlb> boxes = new();
        Dictionary<ItemName, TensorOnGlb> glbMap = new();
        List<float> glbAllocSize = new() { 0, 0 };

        int ifNPingPongSplit = GNNEEnv.NPingPongSplit;
        if (n == _inputShape![0] && c == _inputShape[1] && h == _inputShape[2] && w == _inputShape[3])
        {
            ifNPingPongSplit = 1;
        }

        TensorOnGlb ifGlb = new(new[] { _broadcastIf ? 1 : n, c, h, w }, _inputType!, 0);
        int ifmapSize = ifGlb.AllocatedBytes * ifNPingPongSplit;
        glbAllocSize[0] += ifmapSize;
        ifmapSize = GetAlignedNum(ifmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);
        glbAllocSize[1] += ifmapSize;

        int oc = _broadcastW ? m : m * n;
        TensorOnGlb wGlb = new(new[] { oc, c, r, s }, _weightType!, 0);
        int wSize = SpaceSearcher.GetWeightSize(r, s, c, oc, GetBytesPerElement(_weightType!)) *
                     GNNEEnv.NPingPongSplit;
        wGlb.AllocatedBytes = wSize / GNNEEnv.NPingPongSplit;
        glbAllocSize[0] += wSize;
        wSize = GetAlignedNum(wSize, GNNEEnv.WBankWidth * GNNEEnv.GlbBankWidth);
        glbAllocSize[1] += wSize;

        TensorOnGlb ofGlb = new(new[] { n, m, e, f }, _outputType!, 0);
        int ofmapSize = ofGlb.AllocatedBytes * GNNEEnv.NPingPongSplit;
        glbAllocSize[0] += ofmapSize;
        ofmapSize = GetAlignedNum(ofmapSize, GNNEEnv.OfmapBankWidth * GNNEEnv.GlbBankWidth);
        glbAllocSize[1] += ofmapSize;

        TensorOnGlb actGlb = new(new[] { 1, _outputShape![1], _outputShape[2], GNNEEnv.ActNumPerChan }, _lact!.CheckedDataType, 0);
        int actSize = SpaceSearcher.GetActSize(_outputShape[1] * _outputShape[2], GNNEEnv.ActNumPerChan, GetBytesPerElement(_lact.CheckedDataType));
        glbAllocSize[0] += actSize;
        actSize = GetAlignedNum(actSize, GNNEEnv.ActBankWidth * GNNEEnv.GlbBankWidth);
        glbAllocSize[1] += actSize;

        int basementSize = SpaceSearcher.GetBasementSize();
        basementSize = GetAlignedNum(basementSize, GNNEEnv.GlbWidth * GNNEEnv.GlbBankWidth);

        boxes.Add(new(new[] { GNNEEnv.GlbWidth, basementSize / GNNEEnv.GlbWidth / GNNEEnv.GlbBankWidth }, ItemName.Basement));
        boxes.Add(new(new[] { GNNEEnv.IfmapBankWidth, ifmapSize / GNNEEnv.IfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ifmap));
        boxes.Add(new(new[] { GNNEEnv.WBankWidth, wSize / GNNEEnv.WBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Weight));
        boxes.Add(new(new[] { GNNEEnv.OfmapBankWidth, ofmapSize / GNNEEnv.OfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ofmap));
        boxes.Add(new(new[] { GNNEEnv.ActBankWidth, actSize / GNNEEnv.ActBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Act));

        glbMap.Add(ItemName.Ifmap, ifGlb);
        glbMap.Add(ItemName.Weight, wGlb);
        glbMap.Add(ItemName.Ofmap, ofGlb);
        glbMap.Add(ItemName.Act, actGlb);

        // only uint8 and int16 have bias
        if (_weightType == DataTypes.UInt8 || _weightType == DataTypes.Int16)
        {
            TensorOnGlb w_qarg_glb = new(new[] { 1, 1, _broadcastW ? 1 : _outputShape[1], _weightsShape![2] }, _lwQarg!.CheckedDataType, 0);
            int w_qarg_size = (_broadcastW ? 1 : _outputShape[1]) * SpaceSearcher.GetWQargSize(_weightsShape[2], GetBytesPerElement(_lwQarg.CheckedDataType));
            glbAllocSize[0] += w_qarg_size;
            w_qarg_size = GetAlignedNum(w_qarg_size, GNNEEnv.WQargBankWidth * GNNEEnv.GlbBankWidth);
            glbAllocSize[1] += w_qarg_size;
            boxes.Add(new(new[] { GNNEEnv.WQargBankWidth, w_qarg_size / GNNEEnv.WQargBankWidth / GNNEEnv.GlbBankWidth }, ItemName.WQarg));
            glbMap.Add(ItemName.WQarg, w_qarg_glb);
        }

        glbUsage[0] = glbAllocSize[0] / GNNEEnv.GlbSize;
        glbUsage[1] = glbAllocSize[1] / GNNEEnv.GlbSize;

        AllocateResult ret = new() { Boxes = boxes, GlbMap = glbMap };
        return ret;
    }
}

internal class TileMatMulGlb : TiledGlb
{
    private TensorOnGlb _ifGlb;
    private TensorOnGlb _ofGlb;

    public TileMatMulGlb(Dictionary<ItemName, TensorOnGlb> glbMap, Dictionary<ItemName, MmuItem> items, int[] lastOutShape, int nPingPongSplit, TensorOnGlb ifGlb, TensorOnGlb ofGlb)
        : base(glbMap, items, lastOutShape, nPingPongSplit)
    {
        _ifGlb = ifGlb;
        _ofGlb = ofGlb;
    }
}
