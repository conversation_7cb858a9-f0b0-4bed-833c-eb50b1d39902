﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Reactive;
using System.Runtime.CompilerServices;
using NetFabric.Hyperlinq;
using Nncase.CostModel;
using Nncase.Diagnostics;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Tensors;
using Nncase.Passes.Analysis;
using Nncase.Passes.Mutators;
using Nncase.Passes.Rules.K230;
using Nncase.PatternMatch;
using Nncase.Schedule;
using Nncase.Targets;
using Nncase.TIR;
using Nncase.Utilities;
using static Nncase.PatternMatch.F.Tensors;
using static Nncase.PatternMatch.Utility;

[assembly: InternalsVisibleTo("Nncase.Tests.K230")]

namespace Nncase.Passes.Rules.Tile;

public sealed class K230FusionConvertVisitor : ExprRewriter
{
    private readonly RunPassContext _options;

    public K230FusionConvertVisitor(RunPassContext options)
    {
        _options = options;
    }

    protected override Expr RewriteLeafFusion(Fusion expr)
    {
        return Process(expr);
    }

    private PrimFunction Process(Fusion fusion)
    {
        IRewriteRule rule;
        if (fusion.Name.StartsWith("TileTransposeCase"))
        {
            rule = new TileTranspose();
        }
        else if (fusion.Name.StartsWith("TilePdp1Case"))
        {
            rule = new TilePdp1();
        }
        else if (fusion.Name.StartsWith("TileAct1Case"))
        {
            rule = new TileAct1();
        }
        else if (fusion.Name.StartsWith("TileConv2dCase"))
        {
            var call = ((Call)fusion.Body)[GNNEStore.Input] as Call;
            var inputShape = call![GNNEConv2D.Input].CheckedShape.ToValueArray();
            var outputShape = call.CheckedShape.ToValueArray();
            var weightsShape = call[GNNEConv2D.Weights].CheckedShape.ToValueArray();
            var paddings = ((TensorConst)call[GNNEConv2D.Padding]).Value.ToArray<int>();
            var strideH = ((TensorConst)call[GNNEConv2D.Stride]).Value.ToArray<int>()[0];
            var strideW = ((TensorConst)call[GNNEConv2D.Stride]).Value.ToArray<int>()[1];
            var dilationH = ((TensorConst)call[GNNEConv2D.Dilation]).Value.ToArray<int>()[0];
            var dilationW = ((TensorConst)call[GNNEConv2D.Dilation]).Value.ToArray<int>()[1];
            var groups = ((TensorConst)call[GNNEConv2D.Groups]).Value.ToScalar<int>();

            if (inputShape[0] > 1 &&
                outputShape[2] == 1 && outputShape[3] == 1
                && inputShape[2] == 1 && inputShape[3] == 1
                && weightsShape[2] == 1 && weightsShape[3] == 1
                && strideH == 1 && strideW == 1
                && dilationH == 1 && dilationW == 1
                && paddings.Sum() == 0
                && groups == 1)
            {
                // process conv2d if not transpose_matmul
                rule = new TileTransposeMatmul();
            }
            else
            {
                rule = new TileConv2D();
            }
        }
        else if (fusion.Name.StartsWith("TileConv2dTransposeCase"))
        {
            rule = new TileConv2dTranspose();
        }
        else if (fusion.Name.StartsWith("TilePadCase"))
        {
            rule = new TilePad();
        }
        else if (fusion.Name.StartsWith("TileResizeCase"))
        {
            rule = new TileResize();
        }
        else if (fusion.Name.StartsWith("TileMatMulCase"))
        {
            rule = new TileMatMul();
        }
        else if (fusion.Name.StartsWith("TileLSTMCase"))
        {
            rule = new TileLSTM();
        }
        else if (fusion.Name.StartsWith("TileLoadStoreCase"))
        {
            rule = new TileLoadStore();
        }
        else
        {
            throw new NotImplementedException();
        }

        if (!CompilerServices.TryMatchRoot(fusion, rule.Pattern, out var result))
        {
            throw new NotImplementedException();
        }

        return rule.GetReplace(result, _options) switch
        {
            null => (PrimFunction)new TileConv2D().GetReplace(result, _options)!,
            var ret => (PrimFunction)ret,
        };
    }
}

internal sealed class GNNESameInputFusionMergeRule : SameInputFusionMergeRule
{
    public override string ModuleKind => K230Target.Kind;
}

internal sealed class GNNEReshapeFusionMergeRule : IMergeRewriteRule
{
    private Pattern? _pattern;

    /// <summary>
    /// Gets get ModuleKind.
    /// </summary>
    public string ModuleKind => K230Target.Kind;

    /// <inheritdoc/>
    public IPattern Pattern => _pattern ??= CreatePattern(ModuleKind);

    /// <inheritdoc/>
    public Pattern CreatePattern(string target_module_kind)
    {
        var inputPat = IsVArgsRepeat(() => IsWildcard());
        var callerPattern =
            IsReshape(
                "reshape",
                "caller",
                IsCall(
                $"callee",
                IsFusion($"callee_fusion", target_module_kind, IsWildcard(), IsVArgsRepeat(() => IsWildcard())),
                inputPat),
                IsWildcard("shape"));

        return callerPattern;
    }

    /// <inheritdoc/>
    public Expr? GetReplace(
      Func<BaseExpr, BaseExpr> mergedFusionRewriteCallBack,
      Func<Fusion, HashSet<Fusion>, bool> mergedFusionCheckCallBack,
      Func<HashSet<Fusion>, bool> candidateFusionCheckCallBack,
      Action<HashSet<Fusion>> candidateFusionRecordCallBack,
      IExprUserAnalysisResult usedByReslut,
      IMatchResult result,
      RunPassContext options)
    {
        var callee = (Call)result["callee"];
        var callee_fusion = (Fusion)result["callee_fusion"];
        var shape = (RankedShape)result["shape"];

        if (usedByReslut[callee].Count() > 1)
        {
            return null;
        }

        if (!ProcessFusionMerge(
            mergedFusionRewriteCallBack,
            candidateFusionCheckCallBack,
            shape,
            callee_fusion,
            result,
            out var candidate_fusions,
            out var merged_fusion))
        {
            return null;
        }

        if (mergedFusionCheckCallBack(merged_fusion, candidate_fusions))
        {
            var new_call = new Call(merged_fusion, callee.Arguments);
            return new_call;
        }

        // else
        // {
        //     candidateFusionRecordCallBack(candidate_fusions);
        // }
        return null;
    }

    private bool ProcessFusionMerge(Func<BaseExpr, BaseExpr> mergedFusionRewriteCallBack, Func<HashSet<Fusion>, bool> candidate_fusion_checker, Shape shape, Fusion callee_fusion, IMatchResult result, out HashSet<Fusion> candidate_fusions, out Fusion merged_fusion)
    {
        merged_fusion = null!;
        candidate_fusions = new();

        string new_fusion_name = $"{callee_fusion.Name}" + "_reshape";

        candidate_fusions.Add(callee_fusion);

        // if (!candidate_fusion_checker(candidate_fusions))
        // {
        //     return false;
        // }
        BaseExpr merged_fusion_body = IR.F.Tensors.Reshape((Expr)callee_fusion.Body, shape);

        merged_fusion_body = mergedFusionRewriteCallBack(merged_fusion_body);
        if (!CompilerServices.InferenceType(merged_fusion_body))
        {
            throw new InvalidOperationException("Merged Fusion Type Infer Error!");
        }

        merged_fusion = new Fusion(new_fusion_name, ModuleKind, merged_fusion_body, callee_fusion.Parameters);

        return true;
    }
}

internal sealed class GNNEFusionReshapeMergeRule : IMergeRewriteRule
{
    private Pattern? _pattern;

    /// <summary>
    /// Gets get ModuleKind.
    /// </summary>
    public string ModuleKind => K230Target.Kind;

    /// <inheritdoc/>
    public IPattern Pattern => _pattern ??= CreatePattern(ModuleKind);

    /// <inheritdoc/>
    public Pattern CreatePattern(string target_module_kind)
    {
        var callerPattern = IsCall(
            "caller",
            IsFusion(
                "caller_fusion",
                target_module_kind,
                IsWildcard(),
                IsVArgs(IsWildcard())),
            IsReshape("reshape", "callee", IsWildcard("input"), IsWildcard("shape")));

        return callerPattern;
    }

    /// <inheritdoc/>
    public Expr? GetReplace(
      Func<BaseExpr, BaseExpr> mergedFusionRewriteCallBack,
      Func<Fusion, HashSet<Fusion>, bool> mergedFusionCheckCallBack,
      Func<HashSet<Fusion>, bool> candidateFusionCheckCallBack,
      Action<HashSet<Fusion>> candidateFusionRecordCallBack,
      IExprUserAnalysisResult usedByReslut,
      IMatchResult result,
      RunPassContext options)
    {
        var callee = (Call)result["callee"];
        var caller_fusion = (Fusion)result["caller_fusion"];
        var shape = (RankedShape)result["shape"];

        if (usedByReslut[callee].Count() > 1)
        {
            return null;
        }

        if (!ProcessFusionMerge(
            mergedFusionRewriteCallBack,
            candidateFusionCheckCallBack,
            shape,
            caller_fusion,
            result,
            out var candidate_fusions,
            out var merged_fusion))
        {
            return null;
        }

        if (mergedFusionCheckCallBack(merged_fusion, candidate_fusions))
        {
            var new_call = new Call(merged_fusion, (Expr)result["input"]);
            return new_call;
        }

        // else
        // {
        //     candidateFusionRecordCallBack(candidate_fusions);
        // }
        return null;
    }

    private bool ProcessFusionMerge(Func<BaseExpr, BaseExpr> mergedFusionRewriteCallBack, Func<HashSet<Fusion>, bool> candidate_fusion_checker, Shape shape, Fusion caller_fusion, IMatchResult result, out HashSet<Fusion> candidate_fusions, out Fusion merged_fusion)
    {
        var input = (Expr)result["input"];
        var inputVar = new Var(new TensorType(input.CheckedDataType, input.CheckedShape));

        merged_fusion = null!;
        candidate_fusions = new() { caller_fusion };

        string new_fusion_name = $"{caller_fusion.Name}" + "_reshape";

        var calleeBodyMap = new Dictionary<IVar, BaseExpr> { { caller_fusion.Parameters[0], IR.F.Tensors.Reshape(inputVar, shape) } };

        var merger = new IMergeRewriteRule.FusionMerger(calleeBodyMap, new Dictionary<IVar, IVar> { { inputVar, inputVar } });
        var merged_fusion_body = merger.Clone(caller_fusion.Body, default);

        merged_fusion_body = mergedFusionRewriteCallBack(merged_fusion_body);
        if (!CompilerServices.InferenceType(merged_fusion_body))
        {
            throw new InvalidOperationException("Merged Fusion Type Infer Error!");
        }

        merged_fusion = new Fusion(new_fusion_name, ModuleKind, merged_fusion_body, inputVar);

        return true;
    }
}

internal sealed class GNNEMultiInputFusionMergeRule : MultiInputFusionMergeRule
{
    public override string ModuleKind => K230Target.Kind;
}

internal sealed class GNNEShortCutFusionMergeRuleLeft : ShortCutFusionMergeRuleLeft
{
    public override string ModuleKind => K230Target.Kind;
}

internal sealed class GNNEShortCutFusionMergeRuleRight : ShortCutFusionMergeRuleRight
{
    public override string ModuleKind => K230Target.Kind;
}

internal sealed class GNNEFusionGroupMutator : FusionGroupMutator
{
    private readonly Dictionary<Fusion, IFusionChecker> _fusioncheckerCache;
    private bool _checked;

    // private readonly TileOptions _tileOptions = null!;
    public GNNEFusionGroupMutator(
        Dictionary<Fusion, IFusionChecker> fusioncheckerCache,
        IMergeRewriteRule rule,
        RunPassContext passOptions)
        : base(rule, passOptions)
    {
        _fusioncheckerCache = fusioncheckerCache;
        _checked = false;
    }

    /// <inheritdoc/>
    public override bool MergedFusionCheckCallBack(Fusion mergedFusion, HashSet<Fusion> candidateFusions)
    {
        bool ret = false;
        if (!_checked)
        {
            var checker = new MultiFusionChecker();

            // CompilerServices.DumpDotIR(merged_fusion, "before_merge_check", PassOptions.DumpDir,true); // dump sub function.
            ret = checker.Check(mergedFusion, PassOptions);

            // CompilerServices.DumpDotIR(merged_fusion, "after_merge_check", PassOptions.DumpDir,true); // dump sub function.
            if (ret)
            {
                _checked = true;
                _fusioncheckerCache.Add(mergedFusion, checker);
                foreach (var cand in candidateFusions)
                {
                    // release the merged fusion.
                    _fusioncheckerCache.Remove(cand);
                }
            }
        }

        return ret;
    }

    public override BaseExpr MergedFusionRewriteCallBack(BaseExpr mergedFusionBody)
    {
        using var dumpScope = new DumpScope("MergedFusionClear");
        return CompilerServices.ERewrite(mergedFusionBody, new[] { new FoldStoreLoad() }, new(), new());
    }

    protected override Expr RewriteLeafCall(Call expr)
    {
        return _checked ? expr : base.RewriteLeafCall(expr);
    }
}

/// <summary>
/// merge conv + dw.
/// </summary>
internal sealed class GNNEPreFusionGroupMutator : FusionGroupMutator
{
    // private readonly TileOptions _tileOptions = null!;
    private readonly Dictionary<Fusion, IFusionChecker> _fusioncheckerCache;

    public GNNEPreFusionGroupMutator(
        Dictionary<Fusion, IFusionChecker> fusioncheckerCache,
        IMergeRewriteRule rule,
        RunPassContext passOptions)
        : base(rule, passOptions)
    {
        _fusioncheckerCache = fusioncheckerCache;
    }

    /// <inheritdoc/>
    public override bool MergedFusionCheckCallBack(Fusion mergedFusion, HashSet<Fusion> candidateFusions)
    {
        var checker = new L1FusionChecker();

        // CompilerServices.DumpDotIR(merged_fusion, "before_merge_check", PassOptions.DumpDir,true); // dump sub function.
        bool ret = checker.Check(mergedFusion, PassOptions);

        // CompilerServices.DumpDotIR(merged_fusion, "after_merge_check", PassOptions.DumpDir,true); // dump sub function.
        if (ret)
        {
            _fusioncheckerCache.Add(mergedFusion, checker);
            foreach (var cand in candidateFusions)
            {
                // release the merged fusion.
                _fusioncheckerCache.Remove(cand);
            }
        }

        return ret;
    }

    public override BaseExpr MergedFusionRewriteCallBack(BaseExpr mergedFusionBody)
    {
        using var dumpScope = new DumpScope("MergedFusionClear");
        return CompilerServices.ERewrite(mergedFusionBody, new[] { new FoldStoreLoad() }, new(), new());
    }
}

internal sealed class CheckedConvertMutator : ExprRewriter
{
    private readonly Dictionary<Fusion, BaseFunction> _fusionConertedCache;
    private readonly Dictionary<string, RoofLineInfo> _fusionMacsMap;
    private readonly IDictionary<Fusion, IFusionChecker> _fusionCheckerCache;

    // private readonly TileOptions _tileOptions = null!;
    private readonly RunPassContext _passOptions;

    public CheckedConvertMutator(Dictionary<Fusion, BaseFunction> fusionConvertedCache, Dictionary<string, RoofLineInfo> fusionMacsMap, IDictionary<Fusion, IFusionChecker> fusionchecker_cache, RunPassContext passOptions)
    {
        _fusionConertedCache = fusionConvertedCache;
        _fusionMacsMap = fusionMacsMap;
        _fusionCheckerCache = fusionchecker_cache;
        _passOptions = passOptions;
    }

    /// <inheritdoc/>
    protected override Expr RewriteLeafFusion(Fusion expr)
    {
        if (expr is { ModuleKind: K230Target.Kind } fusion)
        {
            if (!_fusionConertedCache.TryGetValue(fusion, out _))
            {
                PrimFunction primFunc;
                if (_fusionCheckerCache.TryGetValue(fusion, out var checker))
                {
                    if (checker is L1FusionChecker)
                    {
                        var layerGroupChecker = new MultiFusionChecker();
                        var layerGroupRet = layerGroupChecker.Check(fusion, _passOptions);
                        if (layerGroupRet)
                        {
                            _fusionCheckerCache[fusion] = layerGroupChecker;
                            checker = layerGroupChecker;
                        }
                    }

                    primFunc = checker.Convert();
                }
                else
                {
                    var visitor = new K230FusionConvertVisitor(_passOptions);
                    primFunc = (PrimFunction)visitor.Rewrite(fusion.Clone());
                }

                BaseFunction? convertFunc = primFunc;
                _fusionConertedCache.Add(fusion, convertFunc);
                new DDrMacCalcVisitor(_fusionMacsMap, convertFunc.Name).Visit(fusion);
            }
        }

        return expr;
    }

    protected override Expr RewriteLeafCall(Call expr)
    {
        if (expr.Target is Fusion { ModuleKind: K230Target.Kind } fusion)
        {
            var convertFunc = _fusionConertedCache[fusion];
            PrimFunctionWrapper wrapper;
            if (convertFunc is PrimFunction primFunc)
            {
                int paramCount = fusion.Metadata.ParameterSize;

                wrapper = new PrimFunctionWrapper(primFunc, paramCount);
                _fusionConertedCache[fusion] = wrapper;
            }
            else
            {
                wrapper = (PrimFunctionWrapper)convertFunc;
            }

            return expr.With(target: wrapper);
        }

        return expr;
    }
}

internal sealed class RoofLineInfo
{
    public const string Mac = "Mac";
    public const string FLOPs = "FLOPs";
    public const string OnChipMemTraffic = "OnChipMemTraffic";
    public const string OffChipMemTraffic = "OffChipMemTraffic";
    public const string OffChipMemLWTraffic = "OffChipMemLWTraffic";
    public const string OffChipLoadStoreCnt = "OffChipLoadStoreCnt";
    public const string Header = $"{Mac}, {FLOPs}, {OnChipMemTraffic}, {OffChipMemTraffic}, {OffChipMemLWTraffic}, {OffChipLoadStoreCnt}";
    private readonly Dictionary<string, ulong> _opTypeMap = new() { { "FC", 0 }, { "CONV2D", 1 }, { "DWCONV", 2 }, { "GEMM", 3 }, { "Logit", 4 }, { "Attend", 5 } };

    private readonly Dictionary<string, ulong> _dict;

    private readonly List<string> _operators;

    public RoofLineInfo()
    {
        _dict = new() {
            { Mac, 0 },
            { FLOPs, 0 },
            { OnChipMemTraffic, 0 },
            { OffChipMemTraffic, 0 },
            { OffChipMemLWTraffic, 0 },
            { OffChipLoadStoreCnt, 0 },
        };

        _operators = new();
    }

    public List<string> Operators => _operators;

    public ulong this[string name]
    {
        get => _dict[name];
        set => _dict[name] = value;
    }

    public void AddOperator(string name, params (string, ulong)[] values)
    {
        _operators.Add($"{name}\n{string.Join(",", values.Select(p => p.Item1))}\n{string.Join(",", values.Select(p => p.Item2))}");
    }

    public override string ToString()
    {
        return $"{_dict[Mac]}, {_dict[FLOPs]}, {_dict[OnChipMemTraffic]}, {_dict[OffChipMemTraffic]}, {_dict[OffChipMemLWTraffic]}, {_dict[OffChipLoadStoreCnt]}";
    }
}

internal sealed class DDrMacCalcVisitor : ExprVisitor<Unit, Unit>
{
    public DDrMacCalcVisitor(Dictionary<string, RoofLineInfo> fusionMacsMap, string name)
    {
        FusionMacsMap = fusionMacsMap;
        FusionName = name;
    }

    public Fusion EntryFusion => (Fusion)VisitRoot!;

    public string FusionName { get; }

    public IDictionary<string, RoofLineInfo> FusionMacsMap { get; }

    protected override Unit VisitLeafFusion(Fusion fusion)
    {
        return default;
    }

    protected override Unit VisitLeafCall(Call call)
    {
        if (!FusionMacsMap.TryGetValue(FusionName, out var roofLineInfo))
        {
            roofLineInfo = new();
            FusionMacsMap.Add(FusionName, roofLineInfo);
        }

        ulong macCount = 0;
        ulong flopsCount = 0;
        long onchipMem = 0;
        long offchipMem = 0; // load if + store of + load act + load bias
        ulong loadCount = 0;
        ulong storeCount = 0;
        long offchipMemLW = 0; // load weights
        long miniDmOnchipCycles = 3840; // The minimum time consumed by a dm_load and dm_store operation is 1.5e-7s@ fre = 800e6/s @32B/cycle
        long miniDmWOnchipCycles = 538; // The minimum time consumed by a dm_load and dm_store operation is 2.1e-8s@ fre = 800e6/s @32B/cycle
        long miniMFUAct1SetupCycles = 1638400; // The minimum time consumed by a dm_load and dm_store operation is 6.4e-5s@ fre = 800e6/s @32B/cycle datafrom (1,1,1,1)+(1,1,1,1)
        switch (call.Target)
        {
            case GNNELoad:
                {
                    loadCount++;
                    onchipMem = (long)CostUtility.GetMemoryAccess(call.CheckedType);
                    offchipMem = (long)CostUtility.GetMemoryAccess(call[GNNELoad.Input].CheckedType);
                }

                break;
            case GNNELoadW:
                {
                    onchipMem = (long)CostUtility.GetMemoryAccess(call.CheckedType);
                    offchipMemLW = (long)CostUtility.GetMemoryAccess(call[GNNELoadW.Input].CheckedType);
                }

                break;
            case GNNEStore:
                {
                    storeCount++;
                    onchipMem = (long)CostUtility.GetMemoryAccess(call[GNNEStore.Input].CheckedType);
                    offchipMem = (long)CostUtility.GetMemoryAccess(call.CheckedType);
                }

                break;
            case GNNEConv2D:
                {
                    var inputShape = call[GNNEConv2D.Input].CheckedShape.Select(d => (ulong)d.FixedValue).ToArray();
                    var weightShape = call[GNNEConv2D.Weights].CheckedShape.Select(d => (ulong)d.FixedValue).ToArray();
                    var outputShape = call.CheckedShape.Select(d => (ulong)d.FixedValue).ToArray();
                    _ = 0;
                    bool is_depthwise;
                    int groups;
                    {
                        groups = ((TensorConst)call[GNNEConv2D.Groups]).Value.ToScalar<int>();
                        var input_channels = inputShape[1];
                        var output_channels = outputShape[1];
                        is_depthwise = input_channels == output_channels && (int)output_channels == groups && groups != 1;
                    }

                    bool fuseDW = call.Users.Count() == 1 && call.Users.ElementAt(0) is Call { Target: GNNEPdp0DW };
                    bool singleLayerConv2D = call[GNNEConv2D.Input] is Call { Target: GNNELoad } && call.Users.ElementAt(0) is Call { Target: GNNEStore };

                    ulong count;
                    if (!is_depthwise)
                    {
                        count = inputShape[1] * weightShape[2] * weightShape[3] * outputShape[1] * outputShape[2] * outputShape[3];
                    }
                    else
                    {
                        count = (inputShape[1] * weightShape[2] * weightShape[3] * outputShape[2] * outputShape[3]) + (inputShape[1] * outputShape[1] * outputShape[2] * outputShape[3]);
                    }

                    int strideH = ((TensorConst)call[GNNEConv2D.Stride]).Value.ToArray<int>()[0];
                    int strideW = ((TensorConst)call[GNNEConv2D.Stride]).Value.ToArray<int>()[1];
                    int dilationH = ((TensorConst)call[GNNEConv2D.Dilation]).Value.ToArray<int>()[0];
                    int dilationW = ((TensorConst)call[GNNEConv2D.Dilation]).Value.ToArray<int>()[1];
                    int[] paddings = ((TensorConst)call[GNNEConv2D.Padding]).Value.ToArray<int>();
                    Padding paddingH = new(paddings[0], paddings[1]);
                    Padding paddingW = new(paddings[2], paddings[3]);
                    bool conv1x1 = false;
                    if (!is_depthwise
                        && groups == 1
                        && strideH == 1
                        && strideW == 1
                        && weightShape[2] == 1
                        && weightShape[3] == 1
                        && dilationH == 1
                        && dilationW == 1
                        && paddingH.Sum() == 0
                        && paddingW.Sum() == 0
                        && (call[GNNEConv2D.Input].CheckedDataType == DataTypes.Int8 || call[GNNEConv2D.Input].CheckedDataType == DataTypes.UInt8)
                        && outputShape[2] * outputShape[3] < 512)
                    {
                        conv1x1 = true;
                    }

                    // B * OC * IC * OH * OW * KH * KW
                    flopsCount = inputShape[0] * weightShape[0] * weightShape[1] * outputShape[2] * outputShape[3] * weightShape[2] * weightShape[3] * (ulong)call[GNNEConv2D.Input].CheckedDataType.SizeInBytes * (ulong)call[GNNEConv2D.Weights].CheckedDataType.SizeInBytes;
                    macCount = count;
                    onchipMem = (long)CostUtility.GetMemoryAccess(call[GNNEConv2D.Weights].CheckedType, call[GNNEConv2D.Act].CheckedType, call.CheckedType);
                    onchipMem += (long)CostUtility.GetMemoryAccess(call[GNNEConv2D.Input].CheckedType) * (long)Math.Ceiling((double)weightShape[0] / GNNEEnv.PuWidth);
                    if (conv1x1)
                    {
                        if (inputShape[0] > 1 && inputShape[2] == 1 && inputShape[3] == 1)
                        {
                            onchipMem += (long)Math.Ceiling((double)weightShape[0] / GNNEEnv.PuWidth) * (long)Math.Ceiling((double)weightShape[1] / GNNEEnv.PuHeight) * miniDmOnchipCycles;
                        }
                        else
                        {
                            if (fuseDW)
                            {
                                onchipMem += (long)inputShape[0] * (long)Math.Ceiling((double)weightShape[0] / GNNEEnv.PuWidth) * (long)Math.Ceiling((double)inputShape[2] * inputShape[3] / GNNEEnv.PsumL1ElePerChan * 2) * miniDmOnchipCycles;
                            }
                            else
                            {
                                onchipMem += (long)inputShape[0] * (long)Math.Ceiling((double)weightShape[0] / GNNEEnv.PuWidth) * (long)Math.Ceiling((double)inputShape[2] * inputShape[3] / GNNEEnv.PsumL1ElePerChan) * miniDmOnchipCycles;
                            }
                        }
                    }
                    else
                    {
                        if (is_depthwise)
                        {
                            if (fuseDW)
                            {
                                onchipMem += (long)inputShape[0] * (long)Math.Ceiling((double)inputShape[1] / GNNEEnv.PuHeight) * (long)Math.Ceiling((double)inputShape[2] * inputShape[3] / GNNEEnv.PsumL1ElePerChan * 2) * miniDmOnchipCycles;
                            }
                            else
                            {
                                onchipMem += (long)inputShape[0] * (long)Math.Ceiling((double)inputShape[1] / GNNEEnv.PuHeight) * (long)Math.Ceiling((double)inputShape[2] * inputShape[3] / GNNEEnv.PsumL1ElePerChan) * miniDmOnchipCycles;
                            }
                        }
                        else
                        {
                            if (fuseDW)
                            {
                                onchipMem += (long)inputShape[0] * (long)Math.Ceiling((double)inputShape[1] / GNNEEnv.PuHeight) * (long)Math.Ceiling((double)weightShape[0] / GNNEEnv.PuWidth) * (long)Math.Ceiling((double)inputShape[2] * inputShape[3] / GNNEEnv.PsumL1ElePerChan * 2) * miniDmOnchipCycles;
                            }
                            else
                            {
                                var sKernalLimit = weightShape[3] > 31 ? (long)Math.Ceiling((double)TileUtilities.GetAlignedNum((int)weightShape[3], 31) / GNNEEnv.PuKernelSpad * 2) : 1;
                                onchipMem += (long)inputShape[0] * (long)Math.Ceiling((double)inputShape[1] / GNNEEnv.PuHeight) * (long)Math.Ceiling((double)weightShape[0] / GNNEEnv.PuWidth) * sKernalLimit * (long)Math.Ceiling((double)inputShape[2] * inputShape[3] / GNNEEnv.PsumL1ElePerChan) * miniDmOnchipCycles;
                            }
                        }

                        onchipMem *= singleLayerConv2D ? 12 : 1; // Pelalty single-layer Conv2D overlap scenario
                    }

                    onchipMem += (long)Math.Ceiling((double)outputShape[1] / GNNEEnv.PuWidth) * miniDmOnchipCycles;

                    // note 这里ir上多复制了一份, 需要减去.
                    offchipMemLW = -(long)CostUtility.GetMemoryAccess(call[GNNEConv2D.WeightsQInt8].CheckedType, call[GNNEConv2D.WeightsBiasQint8].CheckedType, call[GNNEConv2D.ActQint8].CheckedType);
                    roofLineInfo.AddOperator(nameof(GNNEConv2D), ("B", inputShape[0]), ("OC", weightShape[0]), ("IC", weightShape[1]), ("OH", outputShape[2]), ("OW", outputShape[3]), ("KH", weightShape[2]), ("KW", weightShape[3]));
                }

                break;
            case GNNEConv2DTranspose:
                {
                    var outputShape = call.CheckedShape.Select(d => (ulong)d.FixedValue).ToArray();
                    macCount = (ulong)((ulong)call[GNNEConv2DTranspose.Input].CheckedShape[1].FixedValue * (ulong)call[GNNEConv2DTranspose.Weights].CheckedShape[2].FixedValue * (ulong)call[GNNEConv2DTranspose.Weights].CheckedShape[3].FixedValue * (ulong)call.CheckedShape[1].FixedValue * (ulong)call[GNNEConv2DTranspose.Input].CheckedShape[2].FixedValue * (ulong)call[GNNEConv2DTranspose.Input].CheckedShape[3].FixedValue);

                    var inputShape = call[GNNEConv2DTranspose.Input].CheckedShape.Select(x => (ulong)x.FixedValue).ToArray();
                    var weightShape = call[GNNEConv2DTranspose.Weights].CheckedShape.Select(x => (ulong)x.FixedValue).ToArray();
                    flopsCount = (ulong)inputShape[0] * (ulong)weightShape[0] * (ulong)weightShape[1] * (ulong)inputShape[2] * (ulong)inputShape[3] * (ulong)weightShape[2] * (ulong)weightShape[3] * (ulong)call[GNNEConv2DTranspose.Input].CheckedDataType.SizeInBytes * (ulong)call[GNNEConv2DTranspose.Weights].CheckedDataType.SizeInBytes;

                    onchipMem = (long)CostUtility.GetMemoryAccess(call[GNNEConv2DTranspose.Input].CheckedType, call[GNNEConv2DTranspose.Weights].CheckedType, call[GNNEConv2DTranspose.Act].CheckedType, call.CheckedType);
                    onchipMem += (long)inputShape[0] * (long)Math.Ceiling((double)inputShape[1] / GNNEEnv.PuHeight) * (long)Math.Ceiling((double)weightShape[0] / GNNEEnv.PuWidth) * (long)Math.Ceiling((double)inputShape[2] * inputShape[3] / GNNEEnv.PsumL1ElePerChan) * miniDmOnchipCycles;
                    onchipMem += (long)inputShape[0] * (long)Math.Ceiling((double)outputShape[1] / GNNEEnv.PuWidth) * miniDmOnchipCycles;
                    offchipMemLW = -(long)CostUtility.GetMemoryAccess(call[GNNEConv2DTranspose.WeightsQInt8].CheckedType, call[GNNEConv2DTranspose.WeightsBiasQint8].CheckedType, call[GNNEConv2DTranspose.ActQint8].CheckedType);
                    offchipMemLW += (long)inputShape[0] * (long)Math.Ceiling((double)inputShape[1] / GNNEEnv.PuHeight) * (long)Math.Ceiling((double)weightShape[0] / GNNEEnv.PuWidth) * (long)Math.Ceiling((double)inputShape[2] * inputShape[3] / GNNEEnv.PsumL1ElePerChan) * (long)weightShape[2] * (long)weightShape[3] * miniDmWOnchipCycles;
                    roofLineInfo.AddOperator(nameof(GNNEConv2DTranspose), ("B", inputShape[0]), ("OC", weightShape[0]), ("IC", weightShape[1]), ("OH", outputShape[2]), ("OW", outputShape[3]), ("KH", weightShape[2]), ("KW", weightShape[3]));
                }

                break;
            case GNNEPdp0DW:
                {
                    var outputShape = call.CheckedShape.Select(d => (ulong)d.FixedValue).ToArray();
                    macCount = (ulong)call[GNNEPdp0DW.Input].CheckedShape[1].FixedValue * (ulong)call[GNNEPdp0DW.Weights].CheckedShape[2].FixedValue * (ulong)call[GNNEPdp0DW.Weights].CheckedShape[3].FixedValue * outputShape[2] * outputShape[3];

                    var inputShape = call[GNNEPdp0DW.Input].CheckedShape.Select(x => (ulong)x.FixedValue).ToArray();
                    var weightShape = call[GNNEPdp0DW.Weights].CheckedShape.Select(x => (ulong)x.FixedValue).ToArray();
                    flopsCount = inputShape[0] * weightShape[0] * weightShape[1] * outputShape[2] * outputShape[3] * weightShape[2] * weightShape[3] * (ulong)call[GNNEPdp0DW.Input].CheckedDataType.SizeInBytes * (ulong)call[GNNEPdp0DW.Weights].CheckedDataType.SizeInBytes;

                    onchipMem = (long)CostUtility.GetMemoryAccess(call[GNNEPdp0DW.Input].CheckedType, call[GNNEPdp0DW.Weights].CheckedType, call[GNNEPdp0DW.Act].CheckedType, call.CheckedType);
                    onchipMem += (long)Math.Ceiling((double)inputShape[1] / GNNEEnv.PuHeight) * (long)Math.Ceiling((double)inputShape[2] * inputShape[3] / (GNNEEnv.PsumL1ElePerChan / 2)) * miniDmOnchipCycles;
                    onchipMem += (long)Math.Ceiling((double)outputShape[1] / GNNEEnv.PuHeight) * (long)Math.Ceiling((double)outputShape[2] * outputShape[3] / (GNNEEnv.PsumL1ElePerChan / 2)) * miniDmOnchipCycles;
                    offchipMemLW = -(long)CostUtility.GetMemoryAccess(call[GNNEPdp0DW.WeightsQInt8].CheckedType, call[GNNEPdp0DW.WeightsBiasQint8].CheckedType, call[GNNEPdp0DW.ActQint8].CheckedType);

                    roofLineInfo.AddOperator(nameof(GNNEPdp0DW), ("B", inputShape[0]), ("OC", 1), ("IC", weightShape[0]), ("OH", outputShape[2]), ("OW", outputShape[3]), ("KH", weightShape[2]), ("KW", weightShape[3]));
                }

                break;
            case GNNEMatMul:
                {
                    var size = call.CheckedShape.Aggregate(0UL, (sum, x) => sum * (ulong)x.FixedValue);
                    macCount = (ulong)call[GNNEMatMul.InputA].CheckedShape[3].FixedValue * size;
                    var inputAShape = call[GNNEMatMul.InputA].CheckedShape.Select(x => (ulong)x.FixedValue).ToArray();
                    var inputBShape = call[GNNEMatMul.InputB].CheckedShape.Select(x => (ulong)x.FixedValue).ToArray();
                    var outputShape = call.CheckedShape.Select(x => (ulong)x.FixedValue).ToArray();
                    flopsCount = inputAShape.Aggregate(1UL, (acc, i) => acc * i) * outputShape[^1] * (ulong)call[GNNEMatMul.InputA].CheckedDataType.SizeInBytes * (ulong)call[GNNEMatMul.InputB].CheckedDataType.SizeInBytes;

                    onchipMem = (long)CostUtility.GetMemoryAccess(call[GNNEMatMul.InputA].CheckedType, call[GNNEMatMul.InputB].CheckedType, call.CheckedType);
                    onchipMem += (long)Math.Max(inputAShape[0] * inputAShape[1], inputBShape[0] * inputBShape[1]) * (long)Math.Ceiling((double)inputAShape[3] / GNNEEnv.PuHeight) * (long)Math.Ceiling((double)inputAShape[2] / GNNEEnv.PuWidth) * miniDmOnchipCycles;
                    onchipMem += (long)Math.Ceiling((double)outputShape[1] * outputShape[2] / GNNEEnv.PuWidth) * miniDmOnchipCycles;

                    // (n,k),(k,m)
                    roofLineInfo.AddOperator(nameof(GNNEMatMul), inputAShape.SkipLast(2).Select(x => (" ", (ulong)x)).Concat(new[] { ("N", inputAShape[^2]), ("K", inputAShape[^1]) }).Concat(
                    inputBShape.SkipLast(2).Select(x => (" ", (ulong)x)).Concat(new[] { ("K", inputBShape[^2]), ("M", inputBShape[^1]) })).ToArray());
                }

                break;
            case GNNEPdp1:
                {
                    onchipMem = (long)CostUtility.GetMemoryAccess(call[GNNEPdp1.Input].CheckedType, call.CheckedType);
                    roofLineInfo.AddOperator(nameof(GNNEPdp1), call[GNNEPdp1.Input].CheckedShape.Select(x => (" ", (ulong)x.FixedValue)).ToArray());
                }

                break;

            case GNNEActivation:
                {
                    bool inputBIsUninit = call[GNNEActivation.InputB] == None.Default;
                    bool firstLayer = call[GNNEActivation.InputA] is Call { Target: GNNELoad } || call[GNNEActivation.InputB] is Call { Target: GNNELoad } || call[GNNEActivation.InputA] is Call { Target: Reshape } || call[GNNEActivation.InputB] is Call { Target: Reshape };
                    onchipMem = (long)CostUtility.GetMemoryAccess(call[GNNEActivation.InputA].CheckedType, call[GNNEActivation.InputB].CheckedType, call.CheckedType);
                    onchipMem += (firstLayer ? miniMFUAct1SetupCycles : 0) + (inputBIsUninit ? miniDmOnchipCycles * 2 : miniDmOnchipCycles * 3);
                    roofLineInfo.AddOperator(nameof(GNNEActivation), call[GNNEActivation.InputA].CheckedShape.Select(x => (" ", (ulong)x.FixedValue)).ToArray());
                }

                break;
            case GNNETranspose:
                {
                    onchipMem = (long)CostUtility.GetMemoryAccess(call[GNNETranspose.Input].CheckedType, call.CheckedType);
                    roofLineInfo.AddOperator(nameof(GNNETranspose), call[GNNETranspose.Input].CheckedShape.Select(x => (" ", (ulong)x.FixedValue)).ToArray());
                }

                break;
            case Reshape:
                break;
            case Ai2dResize:
                onchipMem = (long)CostUtility.GetMemoryAccess(call[Ai2dResize.Input].CheckedType, call.CheckedType);
                roofLineInfo.AddOperator(nameof(Ai2dResize), call[Ai2dResize.Input].CheckedShape.Select(x => (" ", (ulong)x.FixedValue)).ToArray());
                break;
            case GNNELSTM:

                var xShape = call[GNNELSTM.Input].CheckedShape.ToValueArray();  // [1, seq len, batch, input_size]
                var wXcShape = call[GNNELSTM.WXc].CheckedShape.ToValueArray(); // [1, direction, 4*hidden, input_size]
                var hShape = call[GNNELSTM.InitialH].CheckedShape.ToValueArray();  // [1, seq len, batch, hidden]
                var wRcShape = call[GNNELSTM.WRc].CheckedShape.ToValueArray(); // [1, direction, 4*hidden, hidden]

                var ifXMem = (long)CostUtility.GetMemoryAccess(call[GNNELSTM.Input].CheckedType) * (long)Math.Ceiling((double)wXcShape[2] / GNNEEnv.PuWidth) * wXcShape[1];
                var wXcMem = (long)CostUtility.GetMemoryAccess(call[GNNELSTM.WXc].CheckedType) * xShape[1] * xShape[2];
                var hMem = (long)CostUtility.GetMemoryAccess(call[GNNELSTM.InitialH].CheckedType) * (long)Math.Ceiling((double)wRcShape[2] / GNNEEnv.PuWidth) * xShape[1];
                var wRcMem = (long)CostUtility.GetMemoryAccess(call[GNNELSTM.WRc].CheckedType) * xShape[1] * xShape[2];
                Nncase.IR.Shape convOutShapeX = new[] { wXcShape[1], wXcShape[2], xShape[1], xShape[2] };
                var convOutTypeX = new TensorType(DataTypes.Float16, convOutShapeX);
                var convOutMemX = (long)CostUtility.GetMemoryAccess(convOutTypeX);
                Nncase.IR.Shape convOutShapeH = new[] { wRcShape[1], wRcShape[2], xShape[1], xShape[2] };
                var convOutTypeH = new TensorType(DataTypes.Float16, convOutShapeH);
                var convOutMemH = (long)CostUtility.GetMemoryAccess(convOutTypeH);

                onchipMem = (long)CostUtility.GetMemoryAccess(call[GNNELSTM.ActXc].CheckedType, call[GNNELSTM.ActRc0].CheckedType, call[GNNELSTM.ActRc1].CheckedType, call[GNNELSTM.InitialC].CheckedType, call.CheckedType);
                onchipMem += ifXMem + wXcMem + hMem + wRcMem + convOutMemX + convOutMemH;
                onchipMem += (long)xShape[1] * xShape[2] * wXcShape[1] * (long)Math.Ceiling((double)wXcShape[2] / GNNEEnv.PuWidth) * miniDmOnchipCycles;
                onchipMem += (long)xShape[1] * xShape[2] * wXcShape[1] * (long)Math.Ceiling((double)wXcShape[2] / GNNEEnv.PuWidth) * miniDmOnchipCycles;
                onchipMem += (long)xShape[1] * xShape[2] * wRcShape[1] * (long)Math.Ceiling((double)wRcShape[2] / GNNEEnv.PuWidth) * miniDmOnchipCycles;
                onchipMem += (long)xShape[1] * xShape[2] * wRcShape[1] * (long)Math.Ceiling((double)wRcShape[2] / GNNEEnv.PuWidth) * miniDmOnchipCycles;
                var (sequence_len, num_directions, batch_size, hidden_size, embbeding_size) = ((UInt128)xShape[1], (UInt128)wXcShape[1], (UInt128)xShape[2], (UInt128)wXcShape[3] / 4, (UInt128)xShape[^1]);

                flopsCount = (ulong)(num_directions * batch_size * sequence_len * (

                    // [1,embbeding_size] @ [embbeding_size, 4 * hidden_size]
                    (4 * hidden_size * embbeding_size) +

                    // [1,hidden_size] @ [hidden_size, 4 * hidden_size]
                    (4 * hidden_size * hidden_size))); // ht = tanh_ct * ot

                roofLineInfo.AddOperator(nameof(GNNELSTM), call[GNNELSTM.Input].CheckedShape.Select(x => (" ", (ulong)x.FixedValue)).ToArray());

                break;
            case GNNEPad:
                onchipMem = (long)CostUtility.GetMemoryAccess(call[GNNEPad.Input].CheckedType, call.CheckedType);
                roofLineInfo.AddOperator(nameof(GNNEPad), call[GNNEPad.Input].CheckedShape.Select(x => (" ", (ulong)x.FixedValue)).ToArray());
                break;
            case GNNEPdp0Reduce:
                onchipMem = (long)CostUtility.GetMemoryAccess(call[GNNEPdp0Reduce.Input].CheckedType, call.CheckedType);
                roofLineInfo.AddOperator(nameof(GNNEPdp0Reduce), call[GNNEPdp0Reduce.Input].CheckedShape.Select(x => (" ", (ulong)x.FixedValue)).ToArray());
                break;
            case GetItem:
                break;
            default:
                throw new ArgumentException($"{call.Target.GetType().Name} is not supported.");
        }

        roofLineInfo[RoofLineInfo.Mac] += macCount;
        roofLineInfo[RoofLineInfo.FLOPs] += flopsCount;
        roofLineInfo[RoofLineInfo.OnChipMemTraffic] = (ulong)((long)roofLineInfo[RoofLineInfo.OnChipMemTraffic] + onchipMem);
        roofLineInfo[RoofLineInfo.OffChipMemTraffic] = (ulong)((long)roofLineInfo[RoofLineInfo.OffChipMemTraffic] + offchipMem);
        roofLineInfo[RoofLineInfo.OffChipMemLWTraffic] = (ulong)((long)roofLineInfo[RoofLineInfo.OffChipMemLWTraffic] + offchipMemLW);
        roofLineInfo[RoofLineInfo.OffChipLoadStoreCnt] += loadCount + storeCount;

        return default;
    }
}
