﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using Nncase.Runtime.K230;
using Nncase.Schedule;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using static Nncase.Passes.Rules.K230.TileUtilities;
using tensor_ccr_stat = System.Collections.Generic.List<System.Tuple<Nncase.TIR.SegmentND, Nncase.Passes.Rules.K230.TensorStat>>;
using Tuple = Nncase.IR.Tuple;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class TileLSTM : RewriteRule<Pattern>
{
    private static int _count = -1;
    private readonly List<WeightGroupHandler> _weightGroups = new(2); // xc and rc
    private CcrHandler _ccrHandler = new();
    private GprHandler _gpr = new();
    private SsrHandler _ssr = new();

    private GNNEShape? _inputShape;
    private GNNEShape? _wXcShape;
    private GNNEShape? _wXcShapeReshape;
    private GNNEShape? _actXcShape;
    private GNNEShape? _wRcShape;
    private GNNEShape? _wRcShapeReshape;
    private GNNEShape? _actRcShape;
    private GNNEShape? _initHShape;
    private GNNEShape? _initCShape;
    private GNNEShape? _outputShape;
    private GNNEShape? _outputHShape;
    private GNNEShape? _outputCShape;

    private Call? _lIf;
    private Call? _lWXc;
    private Call? _lActXc;
    private Call? _lWRc;
    private Call? _lActRc0;
    private Call? _lActRc1;
    private Call? _lInitH;
    private Call? _lInitC;
    private Call? _lSegFt;
    private Call? _lSegGt;
    private Call? _of;
    private Call? _ofH;
    private Call? _ofC;
    private Call? _lwXcQarg;
    private Call? _lwRcQarg;
    private Call? _lwBinAct;
    private Call? _lwBinQAct;

    private DataType? _inputType;
    private DataType? _wXcType;
    private DataType? _actXcType;
    private DataType? _wRcType;
    private DataType? _actRcType;
    private DataType? _initHType;
    private DataType? _initCType;
    private DataType? _outputType;
    private DataType? _outputHType;
    private DataType? _outputCType;

    private Call? _oldLstm;

    public override Pattern Pattern { get; } = PatternMatch.Utility.IsFusion("k230", new GNNELSTMFusion().Pattern);

    private PrimFunction GetReplace(Expr output, Call midCall, IReadOnlyList<BaseExpr> midCallParams)
    {
        _count++;
        var ld = (Call)midCallParams[0];
        var st = (Call)((Tuple)(BaseExpr)output)[0];

        int outputSize = ((TensorConst)midCall[GNNELSTM.OutputSize]).Value.ToScalar<int>();
        var stH = outputSize >= 2 ? (Call)((Tuple)(BaseExpr)output)[1] : null;
        var stC = outputSize >= 3 ? (Call)((Tuple)(BaseExpr)output)[2] : null;
        TIR.Buffer ddrOfH = null!, ddrOfC = null!, ddrLInitH, ddrLInitC;
        List<TIR.Buffer> buffers = new();

        InitParameters(midCall, ld, st, stH!, stC!);
        var tiledGlb = SearchGlbParameters();
        long[] inShape = _oldLstm![GNNELSTM.Input].CheckedShape.ToValueArray();
        T.CreateBuffer(new(ld[GNNELoad.Input].CheckedDataType, inShape), MemoryLocation.Input, out var ddrIf);
        buffers.Add(ddrIf);
        T.AttachBuffer((TensorConst)_lWXc![GNNELoadW.Input], out TIR.Buffer? ddrLWXC);
        T.AttachBuffer((TensorConst)_lWRc![GNNELoadW.Input], out var ddrLWRC);
        T.AttachBuffer((TensorConst)_lwXcQarg![GNNELoadW.Input], out var ddrLWXCQARG);
        T.AttachBuffer((TensorConst)_lwRcQarg![GNNELoadW.Input], out var ddrLWRCQARG);
        T.AttachBuffer((TensorConst)_lActXc![GNNELoadW.Input], out var ddrLACTXC);
        T.AttachBuffer((TensorConst)_lActRc0![GNNELoadW.Input], out var ddrLACTRC0);
        T.AttachBuffer((TensorConst)_lActRc1![GNNELoadW.Input], out var ddrLACTRC1);
        T.AttachBuffer((TensorConst)_lSegFt![GNNELoadW.Input], out var ddrLSEGFT);
        T.AttachBuffer((TensorConst)_lSegGt![GNNELoadW.Input], out var ddrLSEGGT);
        T.AttachBuffer((TensorConst)_lwBinAct![GNNELoadW.Input], out var ddrLWBINACT);
        T.AttachBuffer((TensorConst)_lwBinQAct![GNNELoadW.Input], out var ddrLWBINQACT);
        if (_lInitH![GNNELoad.Input] is TensorConst)
        {
            T.AttachBuffer((TensorConst)_lInitH[GNNELoad.Input], out ddrLInitH);
        }
        else
        {
            T.CreateBuffer(new(_lInitH[GNNELoad.Input].CheckedDataType, _lInitH[GNNELoad.Input].CheckedShape), MemoryLocation.Input, out ddrLInitH);
            buffers.Add(ddrLInitH);
        }

        if (_lInitC![GNNELoad.Input] is TensorConst)
        {
            T.AttachBuffer((TensorConst)_lInitC[GNNELoad.Input], out ddrLInitC);
        }
        else
        {
            T.CreateBuffer(new(_lInitC[GNNELoad.Input].CheckedDataType, _lInitC[GNNELoad.Input].CheckedShape), MemoryLocation.Input, out ddrLInitC);
            buffers.Add(ddrLInitC);
        }

        T.CreateBuffer(new(st.CheckedDataType, st.CheckedShape), MemoryLocation.Output, out var ddrOf);
        buffers.Add(ddrOf);
        if (stH is not null)
        {
            T.CreateBuffer(new(stH.CheckedDataType, stH.CheckedShape), MemoryLocation.Output, out ddrOfH);
            buffers.Add(ddrOfH);
        }

        if (stC is not null)
        {
            T.CreateBuffer(new(stC.CheckedDataType, stC.CheckedShape), MemoryLocation.Output, out ddrOfC);
            buffers.Add(ddrOfC);
        }

        var actions = BuildSchedule(tiledGlb, midCall, ld, st, stH!, stC!, ddrIf, ddrLWXC, ddrLWRC, ddrLWXCQARG, ddrLWRCQARG, ddrLACTXC, ddrLACTRC0, ddrLACTRC1, ddrLSEGFT, ddrLSEGGT, ddrLWBINACT, ddrLWBINQACT, ddrLInitH, ddrLInitC, ddrOf, ddrOfH, ddrOfC);
        {
            var oldWeighst = ((TensorConst)((Call)ddrLWXC.MemSpan.Buffer.Start)[IR.Buffers.AddressOf.Input]).Value.BytesBuffer;
            ArrangeWeights(_wXcType!, _wXcShape!, oldWeighst, _weightGroups[0], _wXcShapeReshape!);
        }

        {
            var oldWeighst = ((TensorConst)((Call)ddrLWRC.MemSpan.Buffer.Start)[IR.Buffers.AddressOf.Input]).Value.BytesBuffer;
            ArrangeWeights(_wRcType!, _wRcShape!, oldWeighst, _weightGroups[1], _wRcShapeReshape!);
        }

        var actionConverter = new ActionToInstruct();
        return T.PrimFunc($"TileLSTM_{_count}", K230RtModule.Kind, buffers.Select(b => new Var(b.CheckedType)).ToArray()).Body(
            actionConverter.Instructions(actions),
            I.END(GP_REGISTER.x0, 0))
        .Build();
    }

    private void ArrangeWeights(DataType weightsType, GNNEShape weightsShape, Span<byte> oldWeights, WeightGroupHandler weightGroup, GNNEShape weightsReshape)
    {
        int bytesPerElement = GetBytesPerElement(weightsType);
        var weightGroupSlice = weightGroup.WeightGroupSlice();
        byte[] newWeights = new byte[oldWeights.Length];

        int start = 0;
        int j = 0;
        for (int d = 0; d < weightsShape[1]; d++)
        {
            int offset = 0;
            foreach (var slice in weightGroupSlice)
            {
                Assert(offset == weightGroup.WeightGroupOffset(slice) * bytesPerElement);
                for (int b = 0; b < bytesPerElement; b++)
                {
                    for (int m = 0; m < slice[0].Length; m++)
                    {
                        for (int r = 0; r < slice[2].Length; r++)
                        {
                            for (int s = 0; s < slice[3].Length; s++)
                            {
                                for (int c = 0; c < slice[1].Length; c++)
                                {
                                    int srcAddr =
                                        (start + ((((((m + slice[0].Start) * weightsReshape[1]) + c + slice[1].Start) *
                                                    weightsReshape[2]) + r + slice[2].Start) * weightsReshape[3]) +
                                         s + slice[3].Start) * bytesPerElement;

                                    newWeights[j++] = oldWeights[srcAddr + b];
                                    offset++;
                                }
                            }
                        }
                    }
                }
            }

            start += offset / bytesPerElement;
        }

        newWeights.CopyTo(oldWeights);
    }

    private TileLSTMGlb SearchGlbParameters()
    {
        int nPingPongSplit = 1;

        var allocation = HandleAllocate(nPingPongSplit, true);
        Assert(allocation.IsOk);

        // use as input shape
        GNNEShape lastOutShape = new(1, 1, 1, _outputShape![3]);

        return new TileLSTMGlb(allocation.GlbMap, allocation.Items, lastOutShape.Dims, nPingPongSplit, allocation.GlbMap[ItemName.Ifmap], allocation.GlbMap[ItemName.Ofmap]);
    }

    private List<GnneAction> BuildSchedule(TiledGlb glb, Call call, Call ld, Call st, Call stH, Call stC, TIR.Buffer ddrIf, TIR.Buffer ddrLWXC, TIR.Buffer ddrLWRC, TIR.Buffer ddrLWXCQARG, TIR.Buffer ddrLWRCQARG, TIR.Buffer ddrLACTXC, TIR.Buffer ddrLACTRC0, TIR.Buffer ddrLACTRC1, TIR.Buffer ddrLSEGFT, TIR.Buffer ddrLSEGGT, TIR.Buffer ddrLWBINACT, TIR.Buffer ddrLWBINQACT, TIR.Buffer ddrLInitH, TIR.Buffer ddrLInitC, TIR.Buffer ddrOf, TIR.Buffer ddrOfH, TIR.Buffer ddrOfC)
    {
        List<GnneAction> actions = new();

        _gpr = new(GNNEEnv.GprNum);
        _ssr = new(GNNEEnv.SsrNum);
        _ccrHandler = new CcrHandler();

        GnneActionUpdater actionUpdater = new(actions, glb, _ccrHandler, _gpr, _ssr);
        actionUpdater.UpdateMmuConf();

        int nPingPongSplit = glb.NPingPongSplit;
        int[] ifLives = { 0 }, hLives = { 0 };

        // 1. get weight_group
        {
            // 1.1 w_xc_x = w_xc * x + act_xc
            SegmentND ifmap = new(new(..1, new(0, 0)), new(.._inputShape![3], new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)));
            SegmentND ofmap = new(new(..1, new(0, 0)), new(..(_outputShape![3] * 4), new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)));
            SegmentND weight = new(ofmap[1], ifmap[1], new(..1, new(0, 0)), new(..1, new(0, 0)));
            BuildL1Schedule(false, actionUpdater, glb, ifmap, weight, ofmap, 0, 8, 0, true, _weightGroups[0], ifLives, ddrIf, ddrLWXC);

            // 1.2 w_rc_h = w_rc_h * h + act_rc
            SegmentND ofH = new(new(..1, new(0, 0)), new(.._outputShape[3], new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)));
            var ofHGlb = GlbTensorIndexShift(ofH, ofH);
            weight = new(ofmap[1], ofH[1], new(..1, new(0, 0)), new(..1, new(0, 0)));
            BuildL1Schedule(true, actionUpdater, glb, ofH, weight, ofmap, 0, 9, 0, true, _weightGroups[1], hLives, ddrIf, ddrLWRC);
        }

        // weights bias
        if (_wXcType == DataTypes.UInt8 || _wXcType == DataTypes.Int16)
        {
            for (int d = 0; d < _outputShape[1]; d++)
            {
                actionUpdater.UpdateLoadWQarg(_lwXcQarg!, _weightGroups[0], ddrLWXCQARG, _outputShape[3] * 4 * d, null!, null!, ItemName.LstmWXcQarg, _outputShape[3] * 4 * d);
            }
        }

        if (_wRcType == DataTypes.UInt8 || _wRcType == DataTypes.Int16)
        {
            for (int d = 0; d < _outputShape[1]; d++)
            {
                actionUpdater.UpdateLoadWQarg(_lwRcQarg!, _weightGroups[1], ddrLWRCQARG, _outputShape[3] * 4 * d, null!, null!, ItemName.LstmWRcQarg, _outputShape[3] * 4 * d);
            }
        }

        // act param
        actionUpdater.UpdateLoadAct(_lActXc!, ddrLACTXC, ItemName.LstmActXc);
        actionUpdater.UpdateLoadAct(_lActRc0!, ddrLACTRC0, ItemName.LstmActRc);
        actionUpdater.UpdateLoadAct(_lActRc1!, ddrLACTRC1, ItemName.LstmActRc, glb.GlbMap[ItemName.LstmActRc].AllocatedBytes);

        // seg_fitting_param
        actionUpdater.UpdateLoadAct(_lSegFt!, ddrLSEGFT, ItemName.LstmSegFittingParamFt);
        actionUpdater.UpdateLoadAct(_lSegGt!, ddrLSEGGT, ItemName.LstmSegFittingParamGt);

        // binary act
        actionUpdater.UpdateLoadAct(_lwBinAct!, ddrLWBINACT, ItemName.LstmBinAct);

        // quantized binary act
        actionUpdater.UpdateLoadAct(_lwBinQAct!, ddrLWBINQACT, ItemName.LstmBinQAct);

        int iPp = 0;
        List<int> segLenLoop = new();
        for (int l = 0; l < _inputShape[1]; l++)
        {
            segLenLoop.Add(l);
        }

        if (((GNNELSTM)_oldLstm!.Target).Direction == LSTMDirection.Reverse)
        {
            segLenLoop.Reverse();
        }

        for (int d = 0; d < _outputShape[1]; d++)
        {
            if (d == 1)
            {
                segLenLoop.Reverse();
            }

            for (int b = 0; b < _inputShape[2]; b++)
            {
                // for (var &l : seg_len_loop)
                for (int iter = 0; iter != segLenLoop.Count; iter++)
                {
                    int l = segLenLoop[iter];
                    int nextL = iter != segLenLoop.Count - 1 ? segLenLoop[iter + 1] : 0;

                    if (l == segLenLoop[0])
                    {
                        // load ifmap
                        SegmentND ifmapLd = new(new(..1, new(0, 0)), new(l..(l + 1), new(0, 0)), new Segment1D(b..(b + 1), new(0, 0)), new(.._inputShape[3], new(0, 0)));
                        List<CcrSet> ifmapCcrset = new()
                        {
                            new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ifmap, iPp)), ifLives[0]),
                        };
                        List<int> stridesD = new() { ifmapLd[1].Length, ifmapLd[2].Length, ifmapLd[3].Length };
                        List<CcrClr> ifmapFakeCcrclr = new();
                        if (l != segLenLoop[0])
                        {
                            _ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.IfmapFake, iPp));
                        }

                        actionUpdater.UpdateLoadIf(ifmapLd, _lIf!, iPp, ddrIf, 0, stridesD, ItemName.Ifmap, ifmapCcrset, ifmapFakeCcrclr);
                    }

                    // load init_h/init_c
                    SegmentND ofHLd = new(new(..1, new(0, 0)), new(d..(d + 1), new(0, 0)), new(b..(b + 1), new(0, 0)), new(.._outputShape[3], new(0, 0)));
                    SegmentND ofCLd = new(new(..1, new(0, 0)), new(d..(d + 1), new(0, 0)), new(b..(b + 1), new(0, 0)), new(.._outputShape[3], new(0, 0)));
                    List<CcrSet> initHCcrset = new()
                    {
                        new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.LstmOfH)), 0),
                    };
                    List<CcrSet> init_c_ccrset = new()
                    {
                        new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.LstmOfC)), 0),
                    };
                    if (l == segLenLoop[0])
                    {
                        initHCcrset[0].Value = hLives[0];
                        init_c_ccrset[0].Value = 1;
                        List<int> stridesD = new() { ofHLd[1].Length, ofHLd[2].Length, ofHLd[3].Length };
                        actionUpdater.UpdateLoadIf(ofHLd, _lInitH!, 0, ddrLInitH, 0, stridesD, ItemName.LstmOfH, initHCcrset);
                        stridesD = new() { ofCLd[1].Length, ofCLd[2].Length, ofCLd[3].Length };
                        actionUpdater.UpdateLoadIf(ofCLd, _lInitC!, 0, ddrLInitC, 0, stridesD, ItemName.LstmOfC, init_c_ccrset);
                    }

                    // w_xc_x = w_xc * x + act_xc
                    SegmentND ifmap = new(new(..1, new(0, 0)), new(.._inputShape[3], new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)));
                    SegmentND ofmap = new(new(..1, new(0, 0)), new(..(_outputShape[3] * 4), new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)));
                    SegmentND weight = new(ofmap[1], ifmap[1], new(..1, new(0, 0)), new(..1, new(0, 0)));
                    BuildL1Schedule(false, actionUpdater, glb, ifmap, weight, ofmap, iPp, 8, 0, false, _weightGroups[0], ifLives, ddrIf, ddrLWXC, l == segLenLoop[0], d, l == segLenLoop[^1], nextL, b);

                    // w_rc_h = w_rc_h * h + act_rc
                    SegmentND ofH = new(new(..1, new(0, 0)), new(.._outputShape[3], new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)));
                    SegmentND ofC = new(new(..1, new(0, 0)), new(.._outputShape[3], new(0, 0)), new(d..(d + 1), new(0, 0)), new(b..(b + 1), new(0, 0)));
                    var ofHGlb = GlbTensorIndexShift(ofH, ofH);
                    var ofCGlb = GlbTensorIndexShift(ofC, ofC);
                    weight = new(ofmap[1], ofH[1], new(..1, new(0, 0)), new(..1, new(0, 0)));
                    BuildL1Schedule(true, actionUpdater, glb, ofH, weight, ofmap, 0, 9, 0, false, _weightGroups[1], hLives, ddrIf, ddrLWRC, l == segLenLoop[0], d);

                    // g = w_xc_x + w_xc_x
                    {
                        List<CcrSet> ccrSetOfmap = new()
                        {
                            new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 10)), 4),
                        };
                        List<CcrClr> ccrClrIfmap = new()
                        {
                            new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 8))),
                        };
                        ccrClrIfmap.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 9))));
                        int offsetS1 = 0;
                        int offsetS2 = glb.GlbMap[ItemName.Ofmap].AllocatedBytes * 1;
                        int offsetD = 0;
                        int offsetAct1 = 0;
                        SegmentND ofmapReshape = new(new(..1, new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)), new(ofmap[1].Start..ofmap[1].End, new(0, 0)));
                        List<int> src1Stride = new() { ofmapReshape[1].Length, ofmapReshape[2].Length, ofmapReshape[3].Length };
                        List<int> src2Stride = new() { ofmapReshape[1].Length, ofmapReshape[2].Length, ofmapReshape[3].Length };
                        List<int> dstStride = new() { ofmapReshape[1].Length, ofmapReshape[2].Length, ofmapReshape[3].Length };
                        actionUpdater.UpdateMfuAct1(ofmapReshape, ofmapReshape, ofmapReshape, ACT1_SOURCE_TYPE.l2, ACT1_SOURCE_TYPE.l2, DataTypes.Float16, DataTypes.Float16, DataTypes.Float16, new(0, 1), new(0, 1), 0, 0, 0, false, 0, ccrSetOfmap, ccrClrIfmap, offsetS1, offsetS2, offsetD, offsetAct1, ItemName.Ofmap, ItemName.Ofmap, ItemName.LstmBinAct, MFU_ACT1_FUNCTION.add, ItemName.Ofmap, src1Stride, src2Stride, dstStride, false);
                    }

                    // ft = sigmoid(g[2])
                    {
                        List<CcrSet> ccrSetOfmap = new()
                        {
                            new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 6)), 1),
                        };
                        List<CcrClr> ccrClrIfmap = new()
                        {
                            new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 10))),
                        };
                        SegmentND mnMapIfmap = new(ofmap);
                        mnMapIfmap[1] = new((_outputShape[3] * 2)..(_outputShape[3] * 3), new(0, 0));
                        Segment1D none = new(..0, new(0, 0));
                        SegmentND ifmap2Pp = new(none, none, none, none);
                        SegmentND mnMapOfmap = new(ofH);

                        SegmentND mnMapIfmapReshape = new(new(..1, new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)), new(mnMapIfmap[1].Start..mnMapIfmap[1].End, new(0, 0)));
                        SegmentND mnMapOfmapReshape = new(new(..1, new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)), new(mnMapOfmap[1].Start..mnMapOfmap[1].End, new(0, 0)));

                        TensorOnGlb srcGlb =
                            new(new[] { ofmap[0].Length, ofmap[1].Length, ofmap[2].Length, ofmap[3].Length }, DataTypes.Float16, 0);
                        int offsetS1 = srcGlb.GetAddr(mnMapIfmap[0].Start, mnMapIfmap[1].Start, mnMapIfmap[2].Start, mnMapIfmap[3].Start, 0, 1);
                        int offsetS2 = 0;
                        int offsetD = (glb.GlbMap[ItemName.Ofmap].AllocatedBytes * 1) +
                                       (glb.GlbMap[ItemName.Ofmap].GlbNByte / 4 * 2);
                        int offsetAct1 = 0;
                        List<int> src1Stride = new() { mnMapIfmapReshape[1].Length, mnMapIfmapReshape[2].Length, mnMapIfmapReshape[3].Length };
                        List<int> src2Stride = new() { ifmap2Pp[1].Length, ifmap2Pp[2].Length, ifmap2Pp[3].Length };
                        List<int> dstStride = new()
                        {
                            mnMapOfmapReshape[1].Length, mnMapOfmapReshape[2].Length, mnMapOfmapReshape[3].Length,
                        };
                        actionUpdater.UpdateMfuAct1(mnMapIfmapReshape, ifmap2Pp, mnMapOfmapReshape, ACT1_SOURCE_TYPE.l2, ACT1_SOURCE_TYPE.l2, DataTypes.Float16, DataTypes.Float16, DataTypes.Float16, new(0, 1), new(0, 1), 0, 0, 0, true, 0, ccrSetOfmap, ccrClrIfmap, offsetS1, offsetS2, offsetD, offsetAct1, ItemName.Ofmap, ItemName.Ofmap, ItemName.LstmSegFittingParamFt, MFU_ACT1_FUNCTION.add, ItemName.Ofmap, src1Stride, src2Stride, dstStride);
                    }

                    // ct = init_c * f
                    {
                        List<CcrClr> mnMapCcrclr = new() { new(init_c_ccrset[0].Ccr) };
                        mnMapCcrclr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 6))));
                        List<CcrSet> ofmapCcrset = new()
                        {
                            new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 2)), 1),
                        };

                        int offsetS1 = 0;
                        int offsetS2 = (glb.GlbMap[ItemName.Ofmap].AllocatedBytes * 1) +
                                        (glb.GlbMap[ItemName.Ofmap].GlbNByte / 4 * 2);
                        int offsetD = glb.GlbMap[ItemName.Ofmap].GlbNByte / 4 * 2;
                        int offsetAct1 = 0;
                        SegmentND ofCGlbReshape = new(new(..1, new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)), new(ofCGlb[1].Start..ofCGlb[1].End, new(0, 0)));
                        List<int> src1Stride = new() { ofCGlbReshape[1].Length, ofCGlbReshape[2].Length, ofCGlbReshape[3].Length };
                        List<int> src2Stride = new() { ofCGlbReshape[1].Length, ofCGlbReshape[2].Length, ofCGlbReshape[3].Length };
                        List<int> dstStride = new() { ofCGlbReshape[1].Length, ofCGlbReshape[2].Length, ofCGlbReshape[3].Length };
                        actionUpdater.UpdateMfuAct1(ofCGlbReshape, ofCGlbReshape, ofCGlbReshape, ACT1_SOURCE_TYPE.l2, ACT1_SOURCE_TYPE.l2, _initCType!, DataTypes.Float16, DataTypes.Float16, new(0, 1), new(0, 1), 0, 0, 0, false, 0, ofmapCcrset, mnMapCcrclr, offsetS1, offsetS2, offsetD, offsetAct1, ItemName.Ofmap, ItemName.LstmOfC, ItemName.LstmBinAct, MFU_ACT1_FUNCTION.mul, ItemName.Ofmap, src1Stride, src2Stride, dstStride, false);
                    }

                    // it = sigmoid(g[0])
                    {
                        List<CcrSet> ccrSetOfmap = new()
                        {
                            new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 4)), 1),
                        };
                        List<CcrClr> ccrClrIfmap = new()
                        {
                            new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 10))),
                        };
                        SegmentND mnMapIfmap = new(ofmap);
                        mnMapIfmap[1] = new((_outputShape[3] * 0)..(_outputShape[3] * 1), new(0, 0));
                        Segment1D none = new(..0, new(0, 0));
                        SegmentND ifmap2Pp = new(none, none, none, none);
                        SegmentND mnMapOfmap = new(ofH);
                        SegmentND mnMapIfmapReshape = new(new(..1, new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)), new(mnMapIfmap[1].Start..mnMapIfmap[1].End, new(0, 0)));
                        SegmentND mnMapOfmapReshape = new(new(..1, new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)), new(mnMapOfmap[1].Start..mnMapOfmap[1].End, new(0, 0)));

                        TensorOnGlb srcGlb =
                            new(new[] { ofmap[0].Length, ofmap[1].Length, ofmap[2].Length, ofmap[3].Length }, DataTypes.Float16, 0);
                        int offsetS1 = srcGlb.GetAddr(mnMapIfmap[0].Start, mnMapIfmap[1].Start, mnMapIfmap[2].Start, mnMapIfmap[3].Start, 0, 1);
                        int offsetS2 = 0;
                        int offsetD = (glb.GlbMap[ItemName.Ofmap].AllocatedBytes * 1) +
                                       (glb.GlbMap[ItemName.Ofmap].GlbNByte / 4 * 0);
                        int offsetAct1 = 0;
                        List<int> src1Stride = new() { mnMapIfmapReshape[1].Length, mnMapIfmapReshape[2].Length, mnMapIfmapReshape[3].Length };
                        List<int> src2Stride = new() { ifmap2Pp[1].Length, ifmap2Pp[2].Length, ifmap2Pp[3].Length };
                        List<int> dstStride = new()
                        {
                            mnMapOfmapReshape[1].Length, mnMapOfmapReshape[2].Length, mnMapOfmapReshape[3].Length,
                        };
                        actionUpdater.UpdateMfuAct1(mnMapIfmapReshape, ifmap2Pp, mnMapOfmapReshape, ACT1_SOURCE_TYPE.l2, ACT1_SOURCE_TYPE.l2, DataTypes.Float16, DataTypes.Float16, DataTypes.Float16, new(0, 1), new(0, 1), 0, 0, 0, true, 0, ccrSetOfmap, ccrClrIfmap, offsetS1, offsetS2, offsetD, offsetAct1, ItemName.Ofmap, ItemName.Ofmap, ItemName.LstmSegFittingParamFt, MFU_ACT1_FUNCTION.add, ItemName.Ofmap, src1Stride, src2Stride, dstStride);
                    }

                    // c_t = tanh(g[3])
                    {
                        List<CcrSet> ccrSetOfmap = new()
                        {
                            new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 7)), 1),
                        };
                        List<CcrClr> ccrClrIfmap = new()
                        {
                            new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 10))),
                        };
                        SegmentND mnMapIfmap = new(ofmap);
                        mnMapIfmap[1] = new((_outputShape[3] * 3)..(_outputShape[3] * 4), new(0, 0));
                        Segment1D none = new(..0, new(0, 0));
                        SegmentND ifmap2Pp = new(none, none, none, none);
                        SegmentND mnMapOfmap = new(ofH);
                        SegmentND mnMapIfmapReshape = new(new(..1, new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)), new(mnMapIfmap[1].Start..mnMapIfmap[1].End, new(0, 0)));
                        SegmentND mnMapOfmapReshape = new(new(..1, new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)), new(mnMapOfmap[1].Start..mnMapOfmap[1].End, new(0, 0)));

                        TensorOnGlb srcGlb =
                            new(
                                new[] { ofmap[0].Length, ofmap[1].Length, ofmap[2].Length, ofmap[3].Length },
                                DataTypes.Float16,
                                0);
                        int offsetS1 = srcGlb.GetAddr(mnMapIfmap[0].Start, mnMapIfmap[1].Start, mnMapIfmap[2].Start, mnMapIfmap[3].Start, 0, 1);
                        int offsetS2 = 0;
                        int offsetD = (glb.GlbMap[ItemName.Ofmap].AllocatedBytes * 1) +
                                       (glb.GlbMap[ItemName.Ofmap].GlbNByte / 4 * 3);
                        int offsetAct1 = 0;
                        List<int> src1Stride = new() { mnMapIfmapReshape[1].Length, mnMapIfmapReshape[2].Length, mnMapIfmapReshape[3].Length };
                        List<int> src2Stride = new() { ifmap2Pp[1].Length, ifmap2Pp[2].Length, ifmap2Pp[3].Length };
                        List<int> dstStride = new()
                        {
                            mnMapOfmapReshape[1].Length, mnMapOfmapReshape[2].Length, mnMapOfmapReshape[3].Length,
                        };
                        actionUpdater.UpdateMfuAct1(mnMapIfmapReshape, ifmap2Pp, mnMapOfmapReshape, ACT1_SOURCE_TYPE.l2, ACT1_SOURCE_TYPE.l2, DataTypes.Float16, DataTypes.Float16, DataTypes.Float16, new(0, 1), new(0, 1), 0, 0, 0, true, 0, ccrSetOfmap, ccrClrIfmap, offsetS1, offsetS2, offsetD, offsetAct1, ItemName.Ofmap, ItemName.Ofmap, ItemName.LstmSegFittingParamGt, MFU_ACT1_FUNCTION.add, ItemName.Ofmap, src1Stride, src2Stride, dstStride);
                    }

                    // c_t_it = it * c_t
                    {
                        List<CcrClr> mnMapCcrclr = new()
                        {
                            new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 4))),
                        };
                        mnMapCcrclr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 7))));
                        List<CcrSet> ofmapCcrset = new()
                        {
                            new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 0)), 1),
                        };

                        int offsetS1 = (glb.GlbMap[ItemName.Ofmap].AllocatedBytes * 1) +
                                        (glb.GlbMap[ItemName.Ofmap].GlbNByte / 4 * 0);
                        int offsetS2 = (glb.GlbMap[ItemName.Ofmap].AllocatedBytes * 1) +
                                        (glb.GlbMap[ItemName.Ofmap].GlbNByte / 4 * 3);
                        int offsetD = glb.GlbMap[ItemName.Ofmap].AllocatedBytes * 0;
                        int offsetAct1 = 0;
                        SegmentND ofCGlbReshape = new(new(..1, new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)), new(ofCGlb[1].Start..ofCGlb[1].End, new(0, 0)));
                        List<int> src1Stride = new() { ofCGlbReshape[1].Length, ofCGlbReshape[2].Length, ofCGlbReshape[3].Length };
                        List<int> src2Stride = new() { ofCGlbReshape[1].Length, ofCGlbReshape[2].Length, ofCGlbReshape[3].Length };
                        List<int> dstStride = new() { ofCGlbReshape[1].Length, ofCGlbReshape[2].Length, ofCGlbReshape[3].Length };
                        actionUpdater.UpdateMfuAct1(ofCGlbReshape, ofCGlbReshape, ofCGlbReshape, ACT1_SOURCE_TYPE.l2, ACT1_SOURCE_TYPE.l2, DataTypes.Float16, DataTypes.Float16, DataTypes.Float16, new(0, 1), new(0, 1), 0, 0, 0, false, 0, ofmapCcrset, mnMapCcrclr, offsetS1, offsetS2, offsetD, offsetAct1, ItemName.Ofmap, ItemName.Ofmap, ItemName.LstmBinAct, MFU_ACT1_FUNCTION.mul, ItemName.Ofmap, src1Stride, src2Stride, dstStride, false);
                    }

                    // ct = ct + c_t_it
                    {
                        List<CcrClr> mnMapCcrclr = new()
                        {
                            new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 2))),
                        };
                        mnMapCcrclr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 0))));
                        List<CcrSet> ofmapCcrset = new()
                        {
                            new(init_c_ccrset[0].Ccr, l == segLenLoop[^1] && _ofC is null ? 1 : 2),
                        };

                        int offsetS1 = glb.GlbMap[ItemName.Ofmap].GlbNByte / 4 * 2;
                        int offsetS2 = glb.GlbMap[ItemName.Ofmap].GlbNByte / 4 * 0;
                        int offsetD = 0;
                        int offsetAct1 = 0;
                        SegmentND ofCGlbReshape = new(new(..1, new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)), new(ofCGlb[1].Start..ofCGlb[1].End, new(0, 0)));
                        List<int> src1Stride = new() { ofCGlbReshape[1].Length, ofCGlbReshape[2].Length, ofCGlbReshape[3].Length };
                        List<int> src2Stride = new() { ofCGlbReshape[1].Length, ofCGlbReshape[2].Length, ofCGlbReshape[3].Length };
                        List<int> dstStride = new() { ofCGlbReshape[1].Length, ofCGlbReshape[2].Length, ofCGlbReshape[3].Length };
                        actionUpdater.UpdateMfuAct1(ofCGlbReshape, ofCGlbReshape, ofCGlbReshape, ACT1_SOURCE_TYPE.l2, ACT1_SOURCE_TYPE.l2, DataTypes.Float16, DataTypes.Float16, DataTypes.Float16, new(0, 1), new(0, 1), 0, 0, 0, false, 0, ofmapCcrset, mnMapCcrclr, offsetS1, offsetS2, offsetD, offsetAct1, ItemName.Ofmap, ItemName.Ofmap, ItemName.LstmBinAct, MFU_ACT1_FUNCTION.add, ItemName.LstmOfC, src1Stride, src2Stride, dstStride, false);
                    }

                    // ot = sigmoid(g[1])
                    {
                        List<CcrSet> ccrSetOfmap = new()
                        {
                            new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 5)), 1),
                        };
                        List<CcrClr> ccrClrIfmap = new()
                        {
                            new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 10))),
                        };
                        SegmentND mnMapIfmap = new(ofmap);
                        mnMapIfmap[1] = new((_outputShape[3] * 1)..(_outputShape[3] * 2), new(0, 0));
                        Segment1D none = new(..0, new(0, 0));
                        SegmentND ifmap2Pp = new(none, none, none, none);
                        SegmentND mnMapOfmap = new(ofH);
                        SegmentND mnMapIfmapReshape = new(new(..1, new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)), new(mnMapIfmap[1].Start..mnMapIfmap[1].End, new(0, 0)));
                        SegmentND mnMapOfmapReshape = new(new(..1, new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)), new(mnMapOfmap[1].Start..mnMapOfmap[1].End, new(0, 0)));

                        TensorOnGlb srcGlb = new(new[] { ofmap[0].Length, ofmap[1].Length, ofmap[2].Length, ofmap[3].Length }, DataTypes.Float16, 0);
                        int offsetS1 = srcGlb.GetAddr(mnMapIfmap[0].Start, mnMapIfmap[1].Start, mnMapIfmap[2].Start, mnMapIfmap[3].Start, 0, 1);
                        int offsetS2 = 0;
                        int offsetD = (glb.GlbMap[ItemName.Ofmap].AllocatedBytes * 1) + (glb.GlbMap[ItemName.Ofmap].GlbNByte / 4 * 1);
                        int offsetAct1 = 0;
                        List<int> src1Stride = new() { mnMapIfmapReshape[1].Length, mnMapIfmapReshape[2].Length, mnMapIfmapReshape[3].Length };
                        List<int> src2Stride = new() { ifmap2Pp[1].Length, ifmap2Pp[2].Length, ifmap2Pp[3].Length };
                        List<int> dstStride = new()
                        {
                            mnMapOfmapReshape[1].Length, mnMapOfmapReshape[2].Length, mnMapOfmapReshape[3].Length,
                        };
                        actionUpdater.UpdateMfuAct1(mnMapIfmapReshape, ifmap2Pp, mnMapOfmapReshape, ACT1_SOURCE_TYPE.l2, ACT1_SOURCE_TYPE.l2, DataTypes.Float16, DataTypes.Float16, DataTypes.Float16, new(0, 1), new(0, 1), 0, 0, 0, true, 0, ccrSetOfmap, ccrClrIfmap, offsetS1, offsetS2, offsetD, offsetAct1, ItemName.Ofmap, ItemName.Ofmap, ItemName.LstmSegFittingParamFt, MFU_ACT1_FUNCTION.add, ItemName.Ofmap, src1Stride, src2Stride, dstStride);
                    }

                    // tanh_ct = tanh(ct_o)
                    {
                        List<CcrSet> ccrSetOfmap = new()
                        {
                            new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 3)), 1),
                        };
                        List<CcrClr> ccrClrIfmap = new() { new(init_c_ccrset[0].Ccr) };
                        SegmentND mnMapIfmap = new(ofHGlb);
                        SegmentND mnMapOfmap = new(ofHGlb);
                        Segment1D none = new(..0, new(0, 0));
                        SegmentND ifmap2Pp = new(none, none, none, none);
                        SegmentND mnMapIfmapReshape = new(new(..1, new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)), new(mnMapIfmap[1].Start..mnMapIfmap[1].End, new(0, 0)));
                        SegmentND mnMapOfmapReshape = new(new(..1, new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)), new(mnMapOfmap[1].Start..mnMapOfmap[1].End, new(0, 0)));

                        int offsetS1 = 0;
                        int offsetS2 = 0;
                        int offsetD = glb.GlbMap[ItemName.Ofmap].GlbNByte / 4 * 3;
                        int offsetAct1 = 0;
                        List<int> src1Stride = new()
                        {
                            mnMapIfmapReshape[1].Length, mnMapIfmapReshape[2].Length, mnMapIfmapReshape[3].Length,
                        };
                        List<int> src2Stride = new() { ifmap2Pp[1].Length, ifmap2Pp[2].Length, ifmap2Pp[3].Length };
                        List<int> dstStride = new()
                        {
                            mnMapOfmapReshape[1].Length, mnMapOfmapReshape[2].Length, mnMapOfmapReshape[3].Length,
                        };
                        actionUpdater.UpdateMfuAct1(mnMapIfmapReshape, ifmap2Pp, mnMapOfmapReshape, ACT1_SOURCE_TYPE.l2, ACT1_SOURCE_TYPE.l2, DataTypes.Float16, DataTypes.Float16, DataTypes.Float16, new(0, 1), new(0, 1), 0, 0, 0, true, 0, ccrSetOfmap, ccrClrIfmap, offsetS1, offsetS2, offsetD, offsetAct1, ItemName.Ofmap, ItemName.LstmOfC, ItemName.LstmSegFittingParamGt, MFU_ACT1_FUNCTION.add, ItemName.Ofmap, src1Stride, src2Stride, dstStride);
                    }

                    // ht = ot * tanh_ct
                    {
                        int hCcrVal = hLives[0] + 1;
                        if (l == segLenLoop[^1])
                        {
                            hCcrVal = _ofH is not null ? 2 : 1;
                        }

                        List<CcrClr> mnMapCcrclr = new()
                        {
                            new CcrClr(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 5))),
                        };
                        mnMapCcrclr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, 3))));
                        List<CcrSet> ofmapCcrset = new() { new(initHCcrset[0].Ccr, hCcrVal) };

                        int offsetS1 = (glb.GlbMap[ItemName.Ofmap].AllocatedBytes * 1) +
                                        (glb.GlbMap[ItemName.Ofmap].GlbNByte / 4 * 1);
                        int offsetS2 = glb.GlbMap[ItemName.Ofmap].GlbNByte / 4 * 3;
                        int offsetD = 0;
                        int offsetAct1 = 0;
                        SegmentND ofHGlbReshape = new(new(..1, new(0, 0)), new(..1, new(0, 0)), new(..1, new(0, 0)), new(ofHGlb[1].Start..ofHGlb[1].End, new(0, 0)));
                        List<int> src1Stride = new() { ofHGlbReshape[1].Length, ofHGlbReshape[2].Length, ofHGlbReshape[3].Length };
                        List<int> src2Stride = new() { ofHGlbReshape[1].Length, ofHGlbReshape[2].Length, ofHGlbReshape[3].Length };
                        List<int> dstStride = new() { ofHGlbReshape[1].Length, ofHGlbReshape[2].Length, ofHGlbReshape[3].Length };
                        actionUpdater.UpdateMfuAct1(ofHGlbReshape, ofHGlbReshape, ofHGlbReshape, ACT1_SOURCE_TYPE.l2, ACT1_SOURCE_TYPE.l2, DataTypes.Float16, DataTypes.Float16, _outputType!, new(0, 1), new(0, 1), 0, 0, 0, false, 0, ofmapCcrset, mnMapCcrclr, offsetS1, offsetS2, offsetD, offsetAct1, ItemName.Ofmap, ItemName.Ofmap, ItemName.LstmBinQAct, MFU_ACT1_FUNCTION.mul, ItemName.LstmOfH, src1Stride, src2Stride, dstStride, false);
                    }

                    // store ht
                    {
                        SegmentND ofmapSt = new(new(l..(l + 1), new(0, 0)), new(d..(d + 1), new(0, 0)), new Segment1D(b..(b + 1), new Padding(0, 0)), new Segment1D(.._outputShape[3], new Padding(0, 0)));
                        List<CcrClr> ccrClrOfmap = new() { new(initHCcrset[0].Ccr) };
                        actionUpdater.UpdateStoreT(ofmapSt, _of!, 0, ddrOf, 0, null!, null!, ccrClrOfmap, ItemName.LstmOfH, new() { 1, 1, _outputShape[3] });
                    }

                    // store last ht and ct
                    if (l == segLenLoop[^1])
                    {
                        SegmentND ofmapSt = new(new(..1, new(0, 0)), new(d..(d + 1), new(0, 0)), new(b..(b + 1), new(0, 0)), new(.._outputShape[3], new(0, 0)));
                        if (_ofH is not null)
                        {
                            List<CcrClr> ccrClrOfmap = new() { new(initHCcrset[0].Ccr) };
                            actionUpdater.UpdateStoreT(ofmapSt, _ofH, 0, ddrOfH, 0, null!, null!, ccrClrOfmap, ItemName.LstmOfH, new() { 1, 1, _outputShape[3] });
                        }

                        if (_ofC is not null)
                        {
                            List<CcrClr> ccrClrOfmap = new() { new(init_c_ccrset[0].Ccr) };
                            actionUpdater.UpdateStoreT(ofmapSt, _ofC, 0, ddrOfC, 0, null!, null!, ccrClrOfmap, ItemName.LstmOfC, new() { 1, 1, _outputShape[3] });
                        }
                    }

                    iPp = (iPp + 1) % nPingPongSplit;
                }
            }
        }

        Assert(_ccrHandler.CcrSanityCheck());

        return actions;
    }

    private void BuildL1Schedule(bool isInitH, GnneActionUpdater actionUpdater, TiledGlb glb, SegmentND ifmap, SegmentND weight, SegmentND psum, int iPp, int ofPp, int wPp, bool weightGroupOnly, WeightGroupHandler weightGroup, int[] ifLives, TIR.Buffer ddrIf, TIR.Buffer ddrW, bool isFirstL = false, int d = 0, bool isLastL = false, int l = 0, int b = 0)
    {
        int l1Pp = 0;
        bool reshaped = ReshapeIfmapWeightsOfmap(isInitH, ref ifmap, ref psum, ref weight);
        var l1Tile = L1Search(weight, psum, isInitH ? (isFirstL ? _initHType! : _outputType!) : _inputType!);
        var eSeg = GetSegmentStartEndLength(psum[2].Start, l1Tile[2], psum[2].End);
        var fSeg = GetSegmentStartEndLength(psum[3].Start, l1Tile[3], psum[3].End);
        var nSeg = GetSegmentStartEndLength(psum[0].Start, 1, psum[0].End);
        var rSeg = GetSegmentStartEndLength(weight[2].Start, l1Tile[4], weight[2].End);
        var seg = GetSegmentStartEndLength(weight[3].Start, l1Tile[5], weight[3].End);
        var ret = GetL1McSeg(ifmap, psum, l1Tile[1], l1Tile[0]);
        var mSeg = ret[0];
        var cSeg = ret[1];

        int rLen = l1Tile[4];
        int len = Math.Min(GNNEEnv.PuKernelSpad / 2, l1Tile[5]);

        foreach (var m in mSeg)
        {
            foreach (var n in nSeg)
            {
                foreach (var e in eSeg)
                {
                    Padding paddingH = new(0, 0);
                    var hConvOut = e;
                    var h = GetInputRowSegment(hConvOut.Start, hConvOut.Length, ifmap[2].Length, weight[2].Length, 1, 1, paddingH);
                    foreach (var f in fSeg)
                    {
                        Padding paddingW = new(0, 0);
                        var wConvOut = f;
                        var w = GetInputRowSegment(wConvOut.Start, wConvOut.Length, ifmap[3].Length, weight[3].Length, 1, 1, paddingW);

                        SegmentND l2GOf = new(n, m, e, f);
                        SegmentND r2LPsum = new(n, m, hConvOut, wConvOut);

                        bool ofmapCalStart = true;
                        foreach (var c in cSeg)
                        {
                            var wcInloop = c;

                            foreach (var rL2 in rSeg)
                            {
                                foreach (var l2 in seg)
                                {
                                    var cInloop = c;
                                    var rInloopSeg =
                                        GetSegmentStartEndLength(rL2.Start, rLen, rL2.End);
                                    var inloopSeg =
                                        GetSegmentStartEndLength(l2.Start, len, l2.End);
                                    var nInloop = n;
                                    var mInloop = m;
                                    SegmentND ifmapSlice = new(nInloop, cInloop, h, w);
                                    SegmentND weightSlice = new(m, wcInloop, rL2, l2);

                                    bool dmLoadIfSn = false;
                                    SegmentND g2LIf = new(ifmapSlice);
                                    if (g2LIf[0].Length > 0 && g2LIf[1].Length > 0 && g2LIf[2].Length > 0 &&
                                        g2LIf[3].Length > 0)
                                    {
                                        dmLoadIfSn = true;
                                        if (!weightGroupOnly)
                                        {
                                            int ifCcrClrNum = 0;
                                            List<CcrSet> g2LIfCcrSet = new();
                                            if ((m == mSeg[^1] && IsLastSlice(g2LIf, ifmap)) ||
                                                (m == mSeg[0] && IsFirstSlice(g2LIf, ifmap)))
                                            {
                                                ifCcrClrNum = 1;
                                                if (m == mSeg[^1] && IsLastSlice(g2LIf, ifmap) &&
                                                    !isLastL && !isInitH)
                                                {
                                                    g2LIfCcrSet.Add(new(
                                                        _ccrHandler.GetCcrItem(
                                                            _ccrHandler.GetName(ItemName.IfmapFake, iPp)),
                                                        1));
                                                }
                                            }

                                            int ifCcr =
                                                _ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ifmap, iPp));
                                            if (isInitH)
                                            {
                                                ifCcr = _ccrHandler.GetCcrItem(
                                                    _ccrHandler.GetName(ItemName.LstmOfH));
                                            }

                                            List<CcrClr> g2LIfCcrClr = new();
                                            if (ifCcrClrNum > 0)
                                            {
                                                g2LIfCcrClr.Add(new(ifCcr));
                                            }

                                            int strideNReshape = ifmap[1].Length;
                                            int strideCReshape = ifmap[2].Length;
                                            int strideHReshape = ifmap[3].Length;
                                            actionUpdater.UpdateG2LIf(g2LIf, ifmap, isInitH && isFirstL ? _lInitH! : _lIf!, iPp, g2LIfCcrSet, g2LIfCcrClr, 0, (isInitH ? (isFirstL ? _initHType : _outputType) : _inputType)!, isInitH ? ItemName.LstmOfH : ItemName.Ifmap, reshaped, strideNReshape, strideCReshape, strideHReshape);

                                            if (m == mSeg[^1] && IsLastSlice(g2LIf, ifmap) &&
                                                !isLastL && !isInitH)
                                            {
                                                // load ifmap
                                                SegmentND ifmapLd = new(new(..1, new(0, 0)), new(l..(l + 1), new(0, 0)), new(b..(b + 1), new(0, 0)), new(.._inputShape![3], new(0, 0)));
                                                List<CcrSet> ifmapCcrset = new()
                                                {
                                                    new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ifmap, iPp)), ifLives[0]),
                                                };
                                                List<int> stridesD = new()
                                                {
                                                    ifmapLd[1].Length, ifmapLd[2].Length, ifmapLd[3].Length,
                                                };
                                                List<CcrClr> ifmapFakeCcrclr = new()
                                                {
                                                    new(_ccrHandler.GetCcrItem(
                                                        _ccrHandler.GetName(ItemName.IfmapFake, iPp))),
                                                };
                                                actionUpdater.UpdateLoadIf(ifmapLd, _lIf!, iPp, ddrIf, 0, stridesD, ItemName.Ifmap, ifmapCcrset, ifmapFakeCcrclr);
                                            }
                                        }
                                        else
                                        {
                                            ifLives[0]++;
                                        }
                                    }

                                    foreach (var rInloop in rInloopSeg)
                                    {
                                        foreach (var inloop in inloopSeg)
                                        {
                                            SegmentND l2RW = new(mInloop, wcInloop, rInloop, inloop);
                                            SegmentND l2RIf = new(ifmapSlice);

                                            int posW = isInitH
                                                ? _wRcType == DataTypes.Int16 ? 2 : 1
                                                : _wXcType == DataTypes.Int16 ? 2 : 1;
                                            int posIf = isInitH
                                                ? (isFirstL ? (_initHType == DataTypes.Int16 ? 2 : 1) : (_outputType == DataTypes.Int16 ? 2 : 1))
                                                : _inputType == DataTypes.Int16 ? 2 : 1;
                                            for (int pW = 0; pW < posW; pW++)
                                            {
                                                for (int pIf = 0; pIf < posIf; pIf++)
                                                {
                                                    if (!weightGroupOnly)
                                                    {
                                                        List<CcrSet> g2RWCcrSet = new();
                                                        List<CcrClr> g2RWCcrClr = new();
                                                        if (b == 0 && isFirstL && pIf == 0)
                                                        {
                                                            // 2.1 load w_xc
                                                            if (!isInitH)
                                                            {
                                                                g2RWCcrSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.LstmWXc)), 1));
                                                                g2RWCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.LstmWXc))));
                                                                actionUpdater.UpdateLoadW(l2RW, _lWXc!, weightGroup, 0, ddrW, g2RWCcrSet, null!, glb.GlbMap[ItemName.LstmWXc].AllocatedBytes * d, false, weightGroup.CurrentOffset() * d * GetBytesPerElement(_wXcType!), ItemName.LstmWXc, pW);
                                                            }

                                                            // 2.1 load w_rc
                                                            else
                                                            {
                                                                g2RWCcrSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.LstmWRc)), 1));
                                                                g2RWCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.LstmWRc))));
                                                                actionUpdater.UpdateLoadW(l2RW, _lWRc!, weightGroup, 0, ddrW, g2RWCcrSet, null!, glb.GlbMap[ItemName.LstmWRc].AllocatedBytes * d, false, weightGroup.CurrentOffset() * d * GetBytesPerElement(_wRcType!), ItemName.LstmWRc, pW);
                                                            }
                                                        }

                                                        int wOffset = isInitH
                                                            ? glb.GlbMap[ItemName.LstmWRc].AllocatedBytes * d
                                                            : glb.GlbMap[ItemName.LstmWXc].AllocatedBytes * d;
                                                        actionUpdater.UpdateG2RW(l2RW, weightGroup, weight[0].Length, (isInitH ? _lWRc : _lWXc)!, wPp, pW, null!, g2RWCcrClr, wOffset, _outputShape![3] * 4 * d, isInitH ? ItemName.LstmWRc : ItemName.LstmWXc, isInitH ? ItemName.LstmWRcQarg : ItemName.LstmWXcQarg);
                                                    }
                                                    else
                                                    {
                                                        weightGroup.UpdateWeightGroup(l2RW);
                                                    }

                                                    if (!weightGroupOnly)
                                                    {
                                                        int ifDeqBias = ((TensorConst)_oldLstm![GNNELSTM.IfDeqBias])
                                                            .Value.ToScalar<int>();
                                                        if (isInitH)
                                                        {
                                                            ifDeqBias = isFirstL ? ((TensorConst)_oldLstm![GNNELSTM.HDeqBias0]).Value.ToScalar<int>() : ((TensorConst)_oldLstm![GNNELSTM.HDeqBias1]).Value.ToScalar<int>();
                                                        }

                                                        actionUpdater.UpdateL2RIf(l2RIf, g2LIf, 1, 1, weight[1].Length, (isInitH && isFirstL ? _lInitH : _lIf)!, 0, pIf, ifDeqBias);
                                                    }

                                                    bool releaseIf = false;
                                                    if (inloop == inloopSeg[^1] && rInloop == rInloopSeg[^1] &&
                                                        pW == posW - 1 && pIf == posIf - 1 && dmLoadIfSn)
                                                    {
                                                        releaseIf = true;
                                                        dmLoadIfSn = false;
                                                    }

                                                    bool loopStart = false;
                                                    if (ofmapCalStart && pW == 0 && pIf == 0)
                                                    {
                                                        loopStart = true;
                                                        ofmapCalStart = false;
                                                    }

                                                    bool loopEnd = wcInloop.End == weight[1].Length &&
                                                                    inloop.End == weight[3].End &&
                                                                    rInloop.End == weight[2].End && pW == posW - 1 &&
                                                                    pIf == posIf - 1;

                                                    DataType destType = DataTypes.Float16;
                                                    var destTarget = ACT0_OUTPUT_DEST.dm;
                                                    if (!weightGroupOnly)
                                                    {
                                                        int shift = ((TensorConst)_oldLstm![GNNELSTM.XcShiftBits]).Value
                                                            .ToScalar<int>();
                                                        if (isInitH)
                                                        {
                                                            if (isFirstL)
                                                            {
                                                                shift = ((TensorConst)_oldLstm[GNNELSTM.RcShiftBits0])
                                                                    .Value.ToScalar<int>();
                                                            }
                                                            else
                                                            {
                                                                shift = ((TensorConst)_oldLstm[GNNELSTM.RcShiftBits1])
                                                                    .Value.ToScalar<int>();
                                                            }
                                                        }

                                                        int l2GOfCcrSetNum = 0;
                                                        if (loopEnd && m.End == weight[0].End && destTarget == ACT0_OUTPUT_DEST.dm)
                                                        {
                                                            l2GOfCcrSetNum = 1;
                                                        }

                                                        List<CcrSet> l2GOfCcrSet = new();
                                                        if (l2GOfCcrSetNum > 0)
                                                        {
                                                            l2GOfCcrSet.Add(new(
                                                                _ccrHandler.GetCcrItem(
                                                                    _ccrHandler.GetName(ItemName.Ofmap, ofPp)),
                                                                1));
                                                        }

                                                        List<CcrClr> l2GOfCcrClr = new();

                                                        int actAddr = 0;
                                                        if (!isInitH)
                                                        {
                                                            actAddr = glb.GlbMap[ItemName.LstmActXc].GlbNByte / 2 * d;
                                                        }

                                                        if (isInitH)
                                                        {
                                                            actAddr = glb.GlbMap[ItemName.LstmActRc].GlbNByte / 2 * d;
                                                            if (!isFirstL)
                                                            {
                                                                actAddr += glb.GlbMap[ItemName.LstmActRc].AllocatedBytes;
                                                            }
                                                        }

                                                        actionUpdater.UpdateR2LPsum(shift, r2LPsum, r2LPsum, psum, 0, l1Pp, destTarget, releaseIf, Math.Max(pW, pIf), TcuComputeMode.NormalConv2d, loopStart, loopEnd, 1, 1, weight[0].Length, _inputType!, (isInitH ? _wRcType : _wXcType)!, destType, isInitH ? _lActRc0!.CheckedDataType : _lActXc!.CheckedDataType, l2GOfCcrSet, l2GOfCcrClr, actAddr, glb.GlbMap[ItemName.Ofmap].AllocatedBytes * (isInitH ? 1 : 0), null!, isInitH ? ItemName.LstmActRc : ItemName.LstmActXc);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // only clear ifmap ccr at 1st and last pass
        ifLives[0] = Math.Min(ifLives[0], 2);
    }

    private List<List<Segment1D>> GetL1McSeg(SegmentND ifmap, SegmentND psum, int mInloop, int cInloop)
    {
        var mSeg = GetSegmentStartEndLength(psum[1].Start, mInloop, psum[1].End);
        var cSeg = GetSegmentStartEndLength(ifmap[1].Start, cInloop, ifmap[1].End);

        return new() { mSeg, cSeg };
    }

    private List<int> L1Search(SegmentND weight, SegmentND psum, DataType type)
    {
        int c = Math.Min(GNNEEnv.PuHeight, weight[1].Length);
        int m = Math.Min(GNNEEnv.PuWidth, weight[0].Length);
        int h = weight[2].Length;
        int w = weight[3].Length;
        int e = 1;
        int f = 1;
        int r = weight[2].Length;
        int s = weight[3].Length;

        int hConvOut = 1;
        int wConvOut = 1;

        int psumPingPangSplit = 1;
        int ifBytesPerElementGlb = GetBytesPerElement(type);
        bool ok = HandleL1Allocate(h, w, Math.Max(e, hConvOut), Math.Max(f, wConvOut), psumPingPangSplit, ifBytesPerElementGlb);
        if (!ok)
        {
            throw new NotSupportedException("exceeds L1 size");
        }

        return new()
        {
            c,
            m,
            e,
            f,
            r,
            s,
        };
    }

    private bool HandleL1Allocate(int h, int w, int e, int f, int psumPingPangSplit, int ifBytesPerElementGlb)
    {
        if (e * f > GNNEEnv.PsumL1ElePerChan / psumPingPangSplit)
        {
            return false;
        }

        if (GNNEEnv.PuHeight * h * w * ifBytesPerElementGlb > GNNEEnv.IfL1Size)
        {
            return false;
        }

        return true;
    }

    private bool ReshapeIfmapWeightsOfmap(bool isInitH, ref SegmentND ifmap, ref SegmentND ofmap, ref SegmentND weights)
    {
        bool ret;
        if (ifmap[2].Length == 1 && ifmap[3].Length == 1
                                 && weights[2].Length == 1 && weights[3].Length == 1
                                 && (weights[1].Length % 24 == 0 || weights[1].Length % 20 == 0 ||
                                     weights[1].Length % 16 == 0))
        {
            int c = weights[1].Length % 24 == 0 ? 24 : weights[1].Length % 20 == 0 ? 20 : 16;
            int rs = weights[1].Length / c;
            uint s = 4U;
            if (rs <= s)
            {
                s = (uint)rs;
            }
            else
            {
                while (s > 0)
                {
                    if (rs % s == 0)
                    {
                        break;
                    }

                    s--;
                }
            }

            long oldR = rs / s;
            uint r = 31U;
            if (oldR <= r)
            {
                r = (uint)oldR;
            }
            else
            {
                while (r > 0)
                {
                    if (oldR % r == 0)
                    {
                        break;
                    }

                    r--;
                }
            }

            c = (int)((int)oldR / r * c);

            ifmap = new(ifmap[0], new(..c, new(0, 0)), new(..(int)r, new(0, 0)), new(..(int)s, new(0, 0)));
            weights = new(weights[0], new(..c, new(0, 0)), new(..(int)r, new(0, 0)), new(..(int)s, new(0, 0)));
            ofmap = new(ofmap[0], ofmap[1], ofmap[2], ofmap[3]);

            if (!isInitH)
            {
                _wXcShapeReshape = new GNNEShape(weights[0].Length, c, (int)r, (int)s);
            }
            else
            {
                _wRcShapeReshape = new GNNEShape(weights[0].Length, c, (int)r, (int)s);
            }

            ret = true;
        }
        else
        {
            if (!isInitH)
            {
                _wXcShapeReshape = new GNNEShape(weights[0].Length, weights[1].Length, weights[2].Length, weights[3].Length);
            }
            else
            {
                _wRcShapeReshape = new GNNEShape(weights[0].Length, weights[1].Length, weights[2].Length, weights[3].Length);
            }

            ret = false;
        }

        return ret;
    }

    private GNNEShape ComputeRS(GNNEShape inShape, GNNEShape wShape)
    {
        if (inShape[2] == 1 && inShape[3] == 1
                                 && wShape[2] == 1 && wShape[3] == 1
                                 && (wShape[1] % 24 == 0 || wShape[1] % 20 == 0 ||
                                     wShape[1] % 16 == 0))
        {
            int c = wShape[1] % 24 == 0 ? 24 : wShape[1] % 20 == 0 ? 20 : 16;
            int rs = wShape[1] / c;
            int s = 4;
            if (rs <= s)
            {
                s = rs;
            }
            else
            {
                while (s > 0)
                {
                    if (rs % s == 0)
                    {
                        break;
                    }

                    s--;
                }
            }

            int oldR = rs / s;
            int r = 31;
            if (oldR <= r)
            {
                r = oldR;
            }
            else
            {
                while (r > 0)
                {
                    if (oldR % r == 0)
                    {
                        break;
                    }

                    r--;
                }
            }

            c = oldR / r * c;

            return new GNNEShape(wShape[0], c, r, s);
        }

        return wShape;
    }

    private AllocateResult HandleAllocate(int nPingPongSplit, bool isFinal = false)
    {
        List<BoxOnGlb> boxes = new();
        Dictionary<ItemName, TensorOnGlb> glbMap = new();

        var newInshape = new GNNEShape(1, _inputShape![3], 1, 1);
        var newWxcShape = new GNNEShape(_wXcShape![2], _wXcShape[3], 1, 1);
        var newWrcShape = new GNNEShape(_wRcShape![2], _wRcShape[3], 1, 1);
        newWxcShape = ComputeRS(newInshape!, newWxcShape);
        newWrcShape = ComputeRS(newInshape!, newWrcShape!);

        TensorOnGlb ifGlb = new(new[] { 1, _inputShape![3], 1, 1 }, _inputType!, 0);
        int ifGlbGlbNByte = ifGlb.GlbNByte * nPingPongSplit;
        ifGlbGlbNByte = GetAlignedNum(ifGlbGlbNByte, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);

        TensorOnGlb wXcGlb = new(new[] { newWxcShape[2], newWxcShape[3], newWxcShape![1], newWxcShape[0] }, _wXcType!, 0);
        int wXcSize =
            SpaceSearcher.GetWeightSize(newWxcShape[2], newWxcShape[3], newWxcShape[1], newWxcShape[0], GetBytesPerElement(_wXcType!)) * _wXcShape[1];
        wXcGlb.AllocatedBytes = wXcSize / _wXcShape[1];
        wXcSize = GetAlignedNum(wXcSize, GNNEEnv.WBankWidth * GNNEEnv.GlbBankWidth);

        TensorOnGlb actXcGlb = new(new[] { 1, 1, _actXcShape![2], _actXcShape[3] }, _actXcType!, 0);
        int actXcSize = actXcGlb.GlbNByte * _actXcShape[1];
        actXcSize = GetAlignedNum(actXcSize, GNNEEnv.ActBankWidth * GNNEEnv.GlbBankWidth);

        TensorOnGlb wRcGlb = new(new[] { newWrcShape[2], newWrcShape[3], newWrcShape![1], newWrcShape[0] }, _wRcType!, 0);
        int wRcSize = SpaceSearcher.GetWeightSize(newWrcShape[2], newWrcShape[3], newWrcShape[1], newWrcShape[0], GetBytesPerElement(_wRcType!)) * _wRcShape[1];
        wRcGlb.AllocatedBytes = wRcSize / _wRcShape[1];
        wRcSize = GetAlignedNum(wRcSize, GNNEEnv.WBankWidth * GNNEEnv.GlbBankWidth);

        TensorOnGlb actRcGlb = new(new[] { 1, 1, _actRcShape![2], _actRcShape[3] }, _actRcType!, 0);
        int actRcSize = actRcGlb.GlbNByte * _actRcShape[1];
        actRcSize = GetAlignedNum(actRcSize, GNNEEnv.ActBankWidth * GNNEEnv.GlbBankWidth);
        actRcGlb.AllocatedBytes = actRcSize;
        actRcSize *= 2;

        TensorOnGlb hGlb = new(new[] { 1, _initHShape![3], 1, 1 }, _outputType!, 0);
        int hSize = hGlb.GlbNByte;
        hSize = GetAlignedNum(hSize, GNNEEnv.OfmapBankWidth * GNNEEnv.GlbBankWidth);

        TensorOnGlb cGlb = new(new[] { 1, _initCShape![3], 1, 1 }, _outputCType!, 0);
        int cSize = cGlb.GlbNByte;
        cSize = GetAlignedNum(cSize, GNNEEnv.OfmapBankWidth * GNNEEnv.GlbBankWidth);

        TensorOnGlb ofGlb = new(new[] { 1, _outputShape![3] * 4, 1, 1 }, DataTypes.Float16, 0);
        int ofmapSize = ofGlb.GlbNByte;
        ofmapSize = GetAlignedNum(ofmapSize, GNNEEnv.OfmapBankWidth * GNNEEnv.GlbBankWidth);
        ofGlb.AllocatedBytes = ofmapSize;
        ofmapSize *= 2;

        TensorOnGlb segFittingParamGlbFt = new(new[] { 1, 1, 1, 49 }, DataTypes.Float16, 0);
        int segFittingParamSize = segFittingParamGlbFt.GlbNByte;
        segFittingParamSize = GetAlignedNum(segFittingParamSize, 8 * GNNEEnv.GlbBankWidth);
        TensorOnGlb segFittingParamGlbGt = new(new[] { 1, 1, 1, 49 }, DataTypes.Float16, 0);

        TensorOnGlb binActGlb = new(new[] { 1, 1, _outputShape[3], 7 }, DataTypes.BFloat16, 0);
        int binActSize = binActGlb.GlbNByte;
        binActSize = GetAlignedNum(binActSize, GNNEEnv.GlbWidth * GNNEEnv.GlbBankWidth);

        TensorOnGlb binActQGlb = new(new[] { 1, 1, _outputShape[3], 7 }, DataTypes.Float16, 0);
        int binActQSize = binActQGlb.GlbNByte;
        binActQSize = GetAlignedNum(binActQSize, GNNEEnv.GlbWidth * GNNEEnv.GlbBankWidth);

        int basementSize = SpaceSearcher.GetBasementSize();
        basementSize = GetAlignedNum(basementSize, GNNEEnv.GlbWidth * GNNEEnv.GlbBankWidth);

        boxes.Add(new(
            new[] { GNNEEnv.GlbWidth, basementSize / GNNEEnv.GlbWidth / GNNEEnv.GlbBankWidth },
            ItemName.Basement));
        boxes.Add(new(
            new[] { GNNEEnv.IfmapBankWidth, ifGlbGlbNByte / GNNEEnv.IfmapBankWidth / GNNEEnv.GlbBankWidth },
            ItemName.Ifmap));
        boxes.Add(new(
            new[] { GNNEEnv.WBankWidth, wXcSize / GNNEEnv.WBankWidth / GNNEEnv.GlbBankWidth },
            ItemName.LstmWXc));
        boxes.Add(new(
            new[] { GNNEEnv.ActBankWidth, actXcSize / GNNEEnv.ActBankWidth / GNNEEnv.GlbBankWidth },
            ItemName.LstmActXc));
        boxes.Add(new(
            new[] { GNNEEnv.WBankWidth, wRcSize / GNNEEnv.WBankWidth / GNNEEnv.GlbBankWidth },
            ItemName.LstmWRc));
        boxes.Add(new(
            new[] { GNNEEnv.ActBankWidth, actRcSize / GNNEEnv.ActBankWidth / GNNEEnv.GlbBankWidth },
            ItemName.LstmActRc));
        boxes.Add(new(
            new[] { GNNEEnv.OfmapBankWidth, hSize / GNNEEnv.OfmapBankWidth / GNNEEnv.GlbBankWidth },
            ItemName.LstmOfH));
        boxes.Add(new(
            new[] { GNNEEnv.OfmapBankWidth, cSize / GNNEEnv.OfmapBankWidth / GNNEEnv.GlbBankWidth },
            ItemName.LstmOfC));
        boxes.Add(new(
            new[] { GNNEEnv.OfmapBankWidth, ofmapSize / GNNEEnv.OfmapBankWidth / GNNEEnv.GlbBankWidth },
            ItemName.Ofmap));
        boxes.Add(new(
            new[]
            {
                GNNEEnv.OfmapBankWidth, segFittingParamSize / GNNEEnv.OfmapBankWidth / GNNEEnv.GlbBankWidth,
            },
            ItemName.LstmSegFittingParamFt));
        boxes.Add(new(
            new[]
            {
                GNNEEnv.OfmapBankWidth, segFittingParamSize / GNNEEnv.OfmapBankWidth / GNNEEnv.GlbBankWidth,
            },
            ItemName.LstmSegFittingParamGt));
        boxes.Add(new(
            new[] { GNNEEnv.GlbWidth, binActSize / GNNEEnv.GlbWidth / GNNEEnv.GlbBankWidth },
            ItemName.LstmBinAct));
        boxes.Add(new(
            new[] { GNNEEnv.GlbWidth, binActQSize / GNNEEnv.GlbWidth / GNNEEnv.GlbBankWidth },
            ItemName.LstmBinQAct));

        glbMap.Add(ItemName.Ifmap, ifGlb);
        glbMap.Add(ItemName.LstmWXc, wXcGlb);
        glbMap.Add(ItemName.LstmActXc, actXcGlb);
        glbMap.Add(ItemName.LstmWRc, wRcGlb);
        glbMap.Add(ItemName.LstmActRc, actRcGlb);
        glbMap.Add(ItemName.LstmOfH, hGlb);
        glbMap.Add(ItemName.LstmOfC, cGlb);
        glbMap.Add(ItemName.Ofmap, ofGlb);
        glbMap.Add(ItemName.LstmSegFittingParamFt, segFittingParamGlbFt);
        glbMap.Add(ItemName.LstmSegFittingParamGt, segFittingParamGlbGt);
        glbMap.Add(ItemName.LstmBinAct, binActGlb);
        glbMap.Add(ItemName.LstmBinQAct, binActQGlb);

        // only uint8 and int16 have bias
        if (_wXcType == DataTypes.UInt8 || _wXcType == DataTypes.Int16)
        {
            TensorOnGlb w_xc_qarg = new(new[] { 0, 0, 0, 0 }, _wXcType, 0);
            int w_xc_qarg_size = SpaceSearcher.
                GetWQargSize(
                    _wXcShape[1] * _wXcShape[2],
                    GetBytesPerElement(_lwXcQarg![GNNELoadW.Input].CheckedDataType));
            w_xc_qarg_size =
                GetAlignedNum(w_xc_qarg_size, GNNEEnv.WQargBankWidth * GNNEEnv.GlbBankWidth);
            boxes.Add(new(
                new[] { GNNEEnv.WQargBankWidth, w_xc_qarg_size / GNNEEnv.WQargBankWidth / GNNEEnv.GlbBankWidth },
                ItemName.LstmWXcQarg));
            glbMap.Add(ItemName.LstmWXcQarg, w_xc_qarg);
        }

        if (_wRcType == DataTypes.UInt8 || _wRcType == DataTypes.Int16)
        {
            TensorOnGlb w_rc_qarg = new(new[] { 0, 0, 0, 0 }, _wRcType, 0);
            int w_rc_qarg_size = SpaceSearcher.GetWQargSize(
                _wRcShape[1] * _wRcShape[2],
                GetBytesPerElement(_lwRcQarg![GNNELoadW.Input].CheckedDataType));
            w_rc_qarg_size =
                GetAlignedNum(w_rc_qarg_size, GNNEEnv.WQargBankWidth * GNNEEnv.GlbBankWidth);
            boxes.Add(new(
                new[] { GNNEEnv.WQargBankWidth, w_rc_qarg_size / GNNEEnv.WQargBankWidth / GNNEEnv.GlbBankWidth },
                ItemName.LstmWRcQarg));
            glbMap.Add(ItemName.LstmWRcQarg, w_rc_qarg);
        }

        var bp = new BoxPacker(16) { Boxes = boxes };
        var allocation = SpaceSearcher.TryAllocate(bp);

        if (allocation.IsOk)
        {
            foreach (var map in glbMap)
            {
                if (allocation.Items.ContainsKey(map.Key))
                {
                    map.Value.Mmu = allocation.Items[map.Key];
                }
            }
        }

        AllocateResult ret = new();
        ret.IsOk = allocation.IsOk;
        ret.Items = allocation.Items;
        ret.GlbMap = glbMap;
        return ret;
    }

    private void InitParameters(Call lstmNode, Call ld, Call st, Call stH, Call stC)
    {
        _oldLstm = lstmNode;
        _of = st;
        _ofH = stH;
        _ofC = stC;

        _inputShape = new GNNEShape(
            (int)_oldLstm[GNNELSTM.Input].CheckedShape[0].FixedValue,
            (int)_oldLstm[GNNELSTM.Input].CheckedShape[1].FixedValue,
            (int)_oldLstm[GNNELSTM.Input].CheckedShape[2].FixedValue,
            (int)_oldLstm[GNNELSTM.Input].CheckedShape[3].FixedValue);
        _wXcShape = new GNNEShape(
            (int)_oldLstm[GNNELSTM.WXc].CheckedShape[0].FixedValue,
            (int)_oldLstm[GNNELSTM.WXc].CheckedShape[1].FixedValue,
            (int)_oldLstm[GNNELSTM.WXc].CheckedShape[2].FixedValue,
            (int)_oldLstm[GNNELSTM.WXc].CheckedShape[3].FixedValue);
        _wXcShapeReshape = new GNNEShape(
            (int)_oldLstm[GNNELSTM.WXc].CheckedShape[0].FixedValue,
            (int)_oldLstm[GNNELSTM.WXc].CheckedShape[1].FixedValue,
            (int)_oldLstm[GNNELSTM.WXc].CheckedShape[2].FixedValue,
            (int)_oldLstm[GNNELSTM.WXc].CheckedShape[3].FixedValue);
        _actXcShape = new GNNEShape(
            (int)_oldLstm[GNNELSTM.ActXc].CheckedShape[0].FixedValue,
            (int)_oldLstm[GNNELSTM.ActXc].CheckedShape[1].FixedValue,
            (int)_oldLstm[GNNELSTM.ActXc].CheckedShape[2].FixedValue,
            (int)_oldLstm[GNNELSTM.ActXc].CheckedShape[3].FixedValue);
        _wRcShape = new GNNEShape(
            (int)_oldLstm[GNNELSTM.WRc].CheckedShape[0].FixedValue,
            (int)_oldLstm[GNNELSTM.WRc].CheckedShape[1].FixedValue,
            (int)_oldLstm[GNNELSTM.WRc].CheckedShape[2].FixedValue,
            (int)_oldLstm[GNNELSTM.WRc].CheckedShape[3].FixedValue);
        _wRcShapeReshape = new GNNEShape(
            (int)_oldLstm[GNNELSTM.WRc].CheckedShape[0].FixedValue,
            (int)_oldLstm[GNNELSTM.WRc].CheckedShape[1].FixedValue,
            (int)_oldLstm[GNNELSTM.WRc].CheckedShape[2].FixedValue,
            (int)_oldLstm[GNNELSTM.WRc].CheckedShape[3].FixedValue);
        _actRcShape = new GNNEShape(
            (int)_oldLstm[GNNELSTM.ActRc0].CheckedShape[0].FixedValue,
            (int)_oldLstm[GNNELSTM.ActRc0].CheckedShape[1].FixedValue,
            (int)_oldLstm[GNNELSTM.ActRc0].CheckedShape[2].FixedValue,
            (int)_oldLstm[GNNELSTM.ActRc0].CheckedShape[3].FixedValue);
        _initHShape = new GNNEShape(
            (int)_oldLstm[GNNELSTM.InitialH].CheckedShape[0].FixedValue,
            (int)_oldLstm[GNNELSTM.InitialH].CheckedShape[1].FixedValue,
            (int)_oldLstm[GNNELSTM.InitialH].CheckedShape[2].FixedValue,
            (int)_oldLstm[GNNELSTM.InitialH].CheckedShape[3].FixedValue);
        _initCShape = new GNNEShape(
            (int)_oldLstm[GNNELSTM.InitialC].CheckedShape[0].FixedValue,
            (int)_oldLstm[GNNELSTM.InitialC].CheckedShape[1].FixedValue,
            (int)_oldLstm[GNNELSTM.InitialC].CheckedShape[2].FixedValue,
            (int)_oldLstm[GNNELSTM.InitialC].CheckedShape[3].FixedValue);
        _outputShape = new GNNEShape(
            (int)st.CheckedShape[0].FixedValue,
            (int)st.CheckedShape[1].FixedValue,
            (int)st.CheckedShape[2].FixedValue,
            (int)st.CheckedShape[3].FixedValue);
        _outputHShape = _ofH is not null ? new GNNEShape((int)stH.CheckedShape[0].FixedValue, (int)stH.CheckedShape[1].FixedValue, (int)stH.CheckedShape[2].FixedValue, (int)stH.CheckedShape[3].FixedValue) : new GNNEShape(_initHShape.N, _initHShape.C, _initHShape.H, _initHShape.W);

        _outputCShape = _ofC is not null ? new GNNEShape((int)stC.CheckedShape[0].FixedValue, (int)stC.CheckedShape[1].FixedValue, (int)stC.CheckedShape[2].FixedValue, (int)stC.CheckedShape[3].FixedValue) : new GNNEShape(_initCShape.N, _initCShape.C, _initCShape.H, _initCShape.W);

        _lIf = ld;
        _lWXc = (Call)_oldLstm[GNNELSTM.WXc];
        _lActXc = (Call)_oldLstm[GNNELSTM.ActXc];
        _lWRc = (Call)_oldLstm[GNNELSTM.WRc];
        _lActRc0 = (Call)_oldLstm[GNNELSTM.ActRc0];
        _lActRc1 = (Call)_oldLstm[GNNELSTM.ActRc1];
        _lInitH = (Call)_oldLstm[GNNELSTM.InitialH];
        _lInitC = (Call)_oldLstm[GNNELSTM.InitialC];
        _lSegFt = (Call)_oldLstm[GNNELSTM.SegFittingParamFt];
        _lSegGt = (Call)_oldLstm[GNNELSTM.SegFittingParamGt];
        _lwXcQarg = (Call)_oldLstm[GNNELSTM.WXcQarg];
        _lwRcQarg = (Call)_oldLstm[GNNELSTM.WRcQarg];
        _lwBinAct = (Call)_oldLstm[GNNELSTM.ActBin];
        _lwBinQAct = (Call)_oldLstm[GNNELSTM.ActBinQ];

        _of = st;
        _ofH = stH;
        _ofC = stC;

        _inputType = _oldLstm[GNNELSTM.Input].CheckedDataType;
        _wXcType = _oldLstm[GNNELSTM.WXc].CheckedDataType;
        _actXcType = _oldLstm[GNNELSTM.ActXc].CheckedDataType;
        _wRcType = _oldLstm[GNNELSTM.WRc].CheckedDataType;
        _actRcType = _oldLstm[GNNELSTM.ActRc0].CheckedDataType;
        _initHType = _oldLstm[GNNELSTM.InitialH].CheckedDataType;
        _initCType = _oldLstm[GNNELSTM.InitialC].CheckedDataType;
        _outputType = ((TensorType)((TupleType)_oldLstm.CheckedType)[0]).DType;
        _outputHType = _ofH is not null ? _ofH[GNNEStore.Input].CheckedDataType : _outputType;

        _outputCType = _ofC is not null ? _ofC[GNNEStore.Input].CheckedDataType : DataTypes.Float16;

        _weightGroups.Add(new WeightGroupHandler(_wXcType, _wXcType));
        _weightGroups.Add(new WeightGroupHandler(_wRcType, _wRcType));
    }
}

internal class TileLSTMGlb : TiledGlb
{
    private TensorOnGlb _ifGlb;
    private TensorOnGlb _ofGlb;

    public TileLSTMGlb(Dictionary<ItemName, TensorOnGlb> glbMap, Dictionary<ItemName, MmuItem> items, int[] lastOutShape, int nPingPongSplit, TensorOnGlb ifGlb, TensorOnGlb ofGlb)
        : base(glbMap, items, lastOutShape, nPingPongSplit)
    {
        _ifGlb = ifGlb;
        _ofGlb = ofGlb;
    }
}
