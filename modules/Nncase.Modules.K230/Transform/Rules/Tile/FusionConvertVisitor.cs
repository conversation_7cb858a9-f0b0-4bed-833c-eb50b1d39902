﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Tensors;
using Nncase.Passes.Rules.K230;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using MathF = Nncase.IR.F.Math;

namespace Nncase.Passes.Rules.Tile;

internal sealed class FusionConvertVisitor
{
    private readonly TileOptions _tileOptions;
    private Fusion? _currentFusion;
    private int _ofBufNum = 2;
    private bool _containNotSupportedOp;

    private RunPassContext _passOptions = null!;

    public FusionConvertVisitor(RunPassContext passOptions, TileOptions tileOptions)
    {
        PassOptions = passOptions;
        _tileOptions = tileOptions;
    }

    public RunPassContext PassOptions
    {
        get => _passOptions;
        set => _passOptions = value;
    }

    public Fusion CurrentFusion => _currentFusion!;

    public bool TryL1Fuse(Fusion fusion)
    {
        if (_currentFusion is null)
        {
            _currentFusion = fusion;
        }
        else
        {
            throw new InvalidOperationException("Can't Visit More Than One Fusion!");
        }

        // 0. get compute sequence and node_info
        List<NodeInfo> nodesInfo = new();
        Visit(fusion.Body, nodesInfo);

        if (_containNotSupportedOp)
        {
            return false;
        }

        if (nodesInfo.Count == 4
            && (nodesInfo[1].Op is { Target: GNNEPdp0DW } || nodesInfo[1].Op is { Target: GNNEPdp0Reduce })
            && nodesInfo[2].Op is { Target: GNNEConv2D })
        {
            return true;
        }

        return nodesInfo.Count == 4
               && nodesInfo[1].Op is { Target: GNNEActivation }
               && nodesInfo[2].Op is { Target: GNNEConv2D }
               && ((((GNNEActivation)nodesInfo[1].Op.Target).InputFromL1[0] && ReferenceEquals(nodesInfo[1].Op[GNNEActivation.InputA], nodesInfo[2].Op))
                   || (((GNNEActivation)nodesInfo[1].Op.Target).InputFromL1[1] && ReferenceEquals(nodesInfo[1].Op[GNNEActivation.InputB], nodesInfo[2].Op)));
    }

    public AllocateResult TryL2Fuse(Fusion fusion, FusionType fusionType)
    {
        if (_currentFusion is null)
        {
            _currentFusion = fusion;
        }
        else
        {
            throw new InvalidOperationException("Can't Visit More Than One Fusion!");
        }

        // 0. get compute sequence and node_info
        List<NodeInfo> nodesInfo = new();
        _containNotSupportedOp = false;
        Visit(fusion.Body, nodesInfo);
        if (_containNotSupportedOp)
        {
            return new AllocateResult { IsOk = false };
        }

        List<NodeInfo> reducedNodeInfo = new();
        for (var i = 0; i < nodesInfo.Count; i++)
        {
            var ni = nodesInfo[i].Op;
            if (ni is { Target: Reshape }
                && (ni[Reshape.Input] is not Call { Target: GNNEStore } || nodesInfo[i].Children.Count > 0)
                && ni[Reshape.Input] is not Var)
            {
                return new AllocateResult { IsOk = false };
            }

            if (ni is not { Target: Reshape })
            {
                reducedNodeInfo.Add(nodesInfo[i]);
            }
        }

        nodesInfo = reducedNodeInfo;

        // process pattern like
        //     load
        //    /    \
        //   /      conv
        //   \     /
        //    act
        //     |
        foreach (var ni in nodesInfo.Where(ni => ni.Op is { Target: GNNEActivation }))
        {
            if (ni.Op[GNNEActivation.InputA] == nodesInfo[^1].Op && !nodesInfo[^1].Children.Contains(ni))
            {
                nodesInfo[^1].Children.Add(ni);
            }

            if (ni.Op[GNNEActivation.InputB] != None.Default && ni.Op[GNNEActivation.InputB] == nodesInfo[^1].Op && !nodesInfo[^1].Children.Contains(ni))
            {
                nodesInfo[^1].Children.Add(ni);
            }
        }

        UpdateAlignType(nodesInfo);
        _ofBufNum = nodesInfo.Exists(ni => ni.Children.Count > 1) ? 3 : nodesInfo.Count(ni => ni.Op is { Target: GNNELoad }) > 1 ? 3 : 2;

        // 1. init the shape candidates
        List<int[]> shapeCandidates = new();
        long[] outputShape = fusion.Body.CheckedShape.ToValueArray();
        if (fusion.Body is Call { Target: Reshape } reshape)
        {
            outputShape = reshape[Reshape.Input].CheckedShape.ToValueArray();
        }

        int e = Math.Min(GNNEEnv.MultiLayerTilingH, (int)outputShape[2]);
        int f = Math.Min(GNNEEnv.MultiLayerTilingW, (int)outputShape[3]);
        if (fusionType == FusionType.L2IfFullWPp)
        {
            e = (int)outputShape[2];
            f = (int)outputShape[3];
        }

        var allocation = new AllocateResult { IsOk = false };
        if ((e == 0 || f == 0) && (fusionType == FusionType.L2IfSplitWPp || fusionType == FusionType.L2IfSplitWFull))
        {
            // TBD: Just for temp:If the final shape is smaller than 0.9 of the glb size, it will not be included in the current slice's fusion. Consider starting a new fusion.
            double sliceTheshold = Math.Floor(0.9 * GNNEEnv.GlbSize);
            int lastSize = (int)(outputShape[0] * outputShape[1] * outputShape[2] * outputShape[3] * _ofBufNum * TileUtilities.GetBytesPerElement(nodesInfo[0].Op[GNNEStore.Input].CheckedDataType));
            if (lastSize < sliceTheshold)
            {
                return allocation;
            }

            int splitH = 1;
            int splitW = 1;
            while (splitH < outputShape[2] || splitW < outputShape[3])
            {
                if ((splitH <= splitW && splitH < outputShape[2]) || splitW == outputShape[3])
                {
                    splitH++;
                }
                else
                {
                    splitW++;
                }

                e = (int)Math.Ceiling(1.0f * outputShape[2] / splitH);
                f = (int)Math.Ceiling(1.0f * outputShape[3] / splitW);
                shapeCandidates.Add(new[] { (int)outputShape[0], (int)outputShape[1], e, f });
            }
        }
        else
        {
            shapeCandidates.Add(new[] { (int)outputShape[0], (int)outputShape[1], e, f });
        }

        for (int idx = 0; idx < shapeCandidates.Count && idx < 3; idx++)
        {
            int[] lastShape = shapeCandidates[idx];
            List<BoxOnGlb> boxes = new();

            GlbItemSize maxSizes = new();
            maxSizes.BasementSize = TileUtilities.GetAlignedNum(SpaceSearcher.GetBasementSize(), GNNEEnv.GlbWidth * GNNEEnv.GlbBankWidth);
            var currNodesInfo = nodesInfo;
            Visit(fusionType, lastShape, maxSizes, nodesInfo);

            boxes.Add(new BoxOnGlb(new[] { GNNEEnv.GlbWidth, maxSizes.BasementSize / GNNEEnv.GlbWidth / GNNEEnv.GlbBankWidth }, ItemName.Basement));
            boxes.Add(new BoxOnGlb(new[] { GNNEEnv.OfmapBankWidth, maxSizes.MaxOfmapSize / GNNEEnv.OfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ofmap));
            boxes.Add(new BoxOnGlb(new[] { GNNEEnv.WBankWidth, maxSizes.MaxWeightSize / GNNEEnv.WBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Weight));
            boxes.Add(new BoxOnGlb(new[] { GNNEEnv.ActBankWidth, maxSizes.MaxActSize / GNNEEnv.ActBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Act));
            if (maxSizes.MaxDwWeightSize > 0)
            {
                boxes.Add(new BoxOnGlb(new[] { GNNEEnv.WBankWidth, maxSizes.MaxDwWeightSize / GNNEEnv.WBankWidth / GNNEEnv.GlbBankWidth }, ItemName.DwWeight));
            }

            if (maxSizes.MaxDwActSize > 0)
            {
                boxes.Add(new BoxOnGlb(new[] { GNNEEnv.ActBankWidth, maxSizes.MaxDwActSize / GNNEEnv.ActBankWidth / GNNEEnv.GlbBankWidth }, ItemName.DwAct1));
            }

            if (maxSizes.MaxWeightQargSize > 0)
            {
                boxes.Add(new BoxOnGlb(new[] { GNNEEnv.WQargBankWidth, maxSizes.MaxWeightQargSize / GNNEEnv.WQargBankWidth / GNNEEnv.GlbBankWidth }, ItemName.WQarg));
            }

            if (maxSizes.MaxDwWeightQargSize > 0)
            {
                boxes.Add(new BoxOnGlb(new[] { GNNEEnv.WQargBankWidth, maxSizes.MaxDwWeightQargSize / GNNEEnv.WQargBankWidth / GNNEEnv.GlbBankWidth }, ItemName.DwQarg));
            }

            if (maxSizes.MaxPdp0ActSize > 0)
            {
                boxes.Add(new BoxOnGlb(new[] { GNNEEnv.ActBankWidth, maxSizes.MaxPdp0ActSize / GNNEEnv.ActBankWidth / GNNEEnv.GlbBankWidth }, ItemName.PdpAct1));
            }

            if (maxSizes.MaxIfmap2Size > 0)
            {
                boxes.Add(new BoxOnGlb(new[] { GNNEEnv.IfmapBankWidth, maxSizes.MaxIfmap2Size / GNNEEnv.IfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ifmap2));
            }

            if (maxSizes.MaxAct1Size > 0)
            {
                boxes.Add(new BoxOnGlb(new[] { GNNEEnv.ActBankWidth, maxSizes.MaxAct1Size / GNNEEnv.ActBankWidth / GNNEEnv.GlbBankWidth }, ItemName.MfuAct1));
            }

            if (maxSizes.MaxWeightPreloadSize > 0)
            {
                boxes.Add(new BoxOnGlb(new[] { GNNEEnv.WBankWidth, maxSizes.MaxWeightPreloadSize / GNNEEnv.WBankWidth / GNNEEnv.GlbBankWidth }, ItemName.WeightPreload));
            }

            var bp = new BoxPacker(16);
            bp.Boxes = boxes;
            allocation = SpaceSearcher.TryAllocate(bp);
            allocation.LastOutShape = shapeCandidates[idx];
            int weightSize = fusionType == FusionType.L2IfFullWPp || fusionType == FusionType.L2IfSplitWPp ? maxSizes.MaxWeightSize : 0;
            allocation.BufferSize = new() { { ItemName.Ofmap, maxSizes.MaxOfmapSize / _ofBufNum }, { ItemName.Weight, weightSize } };
            allocation.NodeInfo = currNodesInfo;

            if (fusionType == FusionType.L2IfSplitWPp || fusionType == FusionType.L2IfSplitWFull)
            {
                if (maxSizes.MaxWeightSize > GNNEEnv.GlbSize)
                {
                    allocation.IsOk = false;
                    break;
                }

                if (allocation.IsOk)
                {
                    break;
                }
            }
        }

        if (allocation.IsOk)
        {
            GetFusionInfo(fusion, allocation);
        }

        return allocation;
    }

    public void Visit(BaseExpr expr, List<NodeInfo> nodeInfos)
    {
        if (expr is Call)
        {
            var call = (expr as Call)!;
            switch (call.Target)
            {
                case GNNELoad:
                    LowerGNNELoad(call, nodeInfos);
                    break;
                case GNNEStore:
                    LowerGnneStore(call, nodeInfos);
                    break;
                case GNNEConv2D:
                    LowerGNNEConv2D(call, nodeInfos);
                    break;
                case GNNEPdp0DW:
                    LowerGNNEPdp0Dw(call, nodeInfos);
                    break;
                case GNNEPdp0Reduce:
                    LowerGnnePdp0Reduce(call, nodeInfos);
                    break;
                case IR.Buffers.Uninitialized:
                    LowerUninitialized(call, nodeInfos);
                    break;
                case GNNEPdp1:
                    LowerGnnePdp1(call, nodeInfos);
                    break;
                case GNNETranspose:
                    LowerGnneTranspose(call, nodeInfos);
                    break;
                case GNNEActivation:
                    if (call[GNNEActivation.InputB] is Call { Target: GNNELoad } ldB && ldB[GNNELoad.Input] is Call { Target: Reshape })
                    {
                        _containNotSupportedOp = true;
                    }
                    else if (call[GNNEActivation.InputB] != None.Default
                             && call[GNNEActivation.InputB] is not Call { Target: GNNELoad }
                             && call[GNNEActivation.InputA] is Call { Target: GNNELoad } ldA
                             && ldA[GNNELoad.Input] is Call { Target: Reshape })
                    {
                        _containNotSupportedOp = true;
                    }
                    else
                    {
                        LowerGnneActivation(call, nodeInfos);
                    }

                    break;
                case Reshape:
                    LowerReshape(call, nodeInfos);
                    break;
                default:
                    _containNotSupportedOp = true;
                    break;

                    // throw new NotImplementedException("Not implemented Op in L2 fuse");
            }
        }
        else
        {
            _containNotSupportedOp = true;
        }
    }

    public void Visit(FusionType fusionType, int[] tileOutShape, GlbItemSize maxSizes, List<NodeInfo> nodesInfo)
    {
        nodesInfo[0].OutShape = tileOutShape;
        foreach (var ni in nodesInfo)
        {
            var call = ni.Op;
            switch (call.Target)
            {
                case GNNELoad op:
                    LowerGnneLoad(call, op, fusionType, nodesInfo, maxSizes, ni);
                    break;
                case GNNEStore op:
                    LowerGnneStore(call, op, fusionType, nodesInfo, maxSizes, ni);
                    break;
                case GNNEConv2D op:
                    LowerGnneConv2D(call, op, fusionType, nodesInfo, maxSizes, ni);
                    break;
                case GNNEPdp0DW op:
                    LowerGnnePdp0Dw(call, op, fusionType, nodesInfo, maxSizes, ni);
                    break;
                case GNNEPdp0Reduce op:
                    LowerGnnePdp0Reduce(call, op, fusionType, nodesInfo, maxSizes, ni);
                    break;
                case IR.Buffers.Uninitialized op:
                    LowerUninitialized(call, op, fusionType, nodesInfo, maxSizes, ni);
                    break;
                case GNNEPdp1 op:
                    LowerGnnePdp1(call, op, fusionType, nodesInfo, maxSizes, ni);
                    break;
                case GNNETranspose op:
                    LowerGnneTranspose(call, op, fusionType, nodesInfo, maxSizes, ni);
                    break;
                case GNNEActivation op:
                    LowerGnneActivation(call, op, fusionType, nodesInfo, maxSizes, ni);
                    break;
                default:
                    throw new NotImplementedException("Not implemented Op in L2 fuse");
            }
        }
    }

    public List<NodeInfo> GetFusionInfo(Fusion fusion, AllocateResult allocation)
    {
        List<NodeInfo> ret = new();
        var l2Buffers = _ofBufNum == 2 ? new[] { 0, 1 }.ToList() : new[] { 0, 1, 2 }.ToList();
        var lifetime = new Dictionary<Call, int>();

        int currentBufIndex;

        // TODO: 处理act1 L1 fuse的情况
        var nodeInfo = new List<NodeInfo>();
        nodeInfo.AddRange(allocation.NodeInfo);
        nodeInfo.Reverse();
        foreach (var ni in nodeInfo)
        {
            if (ni.Op is { Target: GNNEPdp0DW } || ni.Op is { Target: GNNEPdp0Reduce } || (ni.Op is { Target: GNNEActivation } && ni.Op[GNNEActivation.InputB] == None.Default && ((GNNEActivation)ni.Op.Target).InputFromL1[0]))
            {
                var preNi = nodeInfo.Find(nI => nI.Op == ni.Op.Arguments[0]);
                ni.Nb.OfBufferIndex = preNi!.Nb.OfBufferIndex;
                ni.Nb.OutputsSize = ni.Children.Count;
                ni.Nb.OfmapOffset = preNi.Nb.OfmapOffset;
                ret.Find(nI => nI.Op == preNi.Op)!.Nb.AlignType = ni.Nb.AlignType;
                lifetime.Add(ni.Op, ni.Nb.OutputsSize);
            }
            else if (ni.Op is { Target: GNNEActivation } && ni.Op[GNNEActivation.InputB] != None.Default && ((GNNEActivation)ni.Op.Target).InputFromL1.Any(x => x))
            {
                var index = ni.Op switch
                {
                    { Target: GNNEActivation } when ((GNNEActivation)ni.Op.Target).InputFromL1[1] => 1,
                    _ => 0,
                };
                var preNi = nodeInfo.Find(nI => nI.Op == ni.Op.Arguments[index]);
                ni.Nb.OfBufferIndex = preNi!.Nb.OfBufferIndex;
                ni.Nb.OutputsSize = ni.Children.Count;
                ni.Nb.OfmapOffset = preNi.Nb.OfmapOffset;
                ret.Find(nI => nI.Op == preNi.Op)!.Nb.AlignType = ni.Nb.AlignType;
                lifetime.Add(ni.Op, ni.Nb.OutputsSize);

                if (lifetime.ContainsKey((Call)ni.Op.Arguments[1 - index]))
                {
                    lifetime[(Call)ni.Op.Arguments[1 - index]]--;
                    if (lifetime[(Call)ni.Op.Arguments[1 - index]] == 0)
                    {
                        l2Buffers.Add(ret.Find(nI => nI.Op == (Call)ni.Op.Arguments[1 - index])!.Nb.OfBufferIndex);
                    }
                }
            }
            else
            {
                if (l2Buffers.Count == 0)
                {
                    allocation.IsOk = false;
                    return new();
                }

                currentBufIndex = l2Buffers[0];
                l2Buffers.RemoveAt(0);
                ni.Nb.OfBufferIndex = currentBufIndex;

                // update parent's outputs_size
                ni.Nb.OutputsSize = ni.Children.Count;
                ni.Nb.OfmapOffset = currentBufIndex * allocation.BufferSize[ItemName.Ofmap];
                lifetime.Add(ni.Op, ni.Nb.OutputsSize);

                if (ni.Op is not { Target: GNNELoad } && lifetime.ContainsKey((Call)ni.Op.Arguments[0]))
                {
                    lifetime[(Call)ni.Op.Arguments[0]]--;
                    if (lifetime[(Call)ni.Op.Arguments[0]] == 0)
                    {
                        l2Buffers.Add(ret.Find(nI => nI.Op == (Call)ni.Op.Arguments[0])!.Nb.OfBufferIndex);
                    }
                }

                if (ni.Op is { Target: GNNEActivation } && ni.Op[GNNEActivation.InputB] != None.Default && lifetime.ContainsKey((Call)ni.Op.Arguments[1]))
                {
                    lifetime[(Call)ni.Op.Arguments[1]]--;
                    if (lifetime[(Call)ni.Op.Arguments[1]] == 0)
                    {
                        l2Buffers.Add(ret.Find(nI => nI.Op == (Call)ni.Op.Arguments[1])!.Nb.OfBufferIndex);
                    }
                }
            }

            ret.Add(ni);
        }

        return ret;
    }

    public PrimFunction BuildSchedule(FusionType fusionType, FusionInfo fusionInfo)
    {
        var layerGroup = new TileLayerGroup();
        return layerGroup.BuildSchedule(fusionInfo);
    }

    /// <summary>
    /// convert to the final prim func.
    /// </summary>
    public PrimFunction VisitToPrimFunc(Fusion fusion)
    {
        return T.PrimFunc("nop", Runtime.K230.K230RtModule.Kind).Body().Build();
    }

    // TODO: act1做L1 fuse时可以在H上对齐
    private void UpdateAlignType(List<NodeInfo> nodeInfos)
    {
        foreach (var ni in nodeInfos)
        {
            if (ni.Children.Exists(subNi => subNi.Op is { Target: GNNEActivation }))
            {
                foreach (var child in from child in ni.Children where child.Op is { Target: GNNEActivation } && child.Op[GNNEActivation.InputB] != None.Default && ((GNNEActivation)child.Op.Target).InputFromL1.Any(x => x) let index = ((GNNEActivation)child.Op.Target).InputFromL1[0] ? 1 : 0 where ReferenceEquals((Call)child.Op.Arguments[index], ni.Op) select child)
                {
                    if (ni.Op.CheckedShape[2] == 1)
                    {
                        ni.Nb.AlignType = AlignedType.FAligned;
                    }
                    else
                    {
                        ni.Nb.AlignType = AlignedType.EAligned;
                    }
                }
            }

            if (ni.Op is { Target: GNNEPdp1 } || ni.Op is { Target: GNNETranspose })
            {
                ni.Nb.AlignType = AlignedType.FAligned;
            }

            if (ni.Children.Exists(subNi => subNi.Op is { Target: GNNEPdp1 } || subNi.Op is { Target: GNNETranspose }))
            {
                ni.Nb.AlignType = AlignedType.FAligned;
            }
        }
    }

    private void UpdateNodeInfo(List<NodeInfo> nodeInfos, Call call, NodeInfo currNode = null!)
    {
        int idx = nodeInfos.FindIndex(ni => ni.Op == call);
        if (idx >= 0)
        {
            var ni = nodeInfos[idx];
            nodeInfos.RemoveAt(idx);
            if (currNode != null && !ni.Children.Contains(currNode))
            {
                ni.Children.Add(currNode);
            }

            nodeInfos.Add(ni);
        }
        else
        {
            nodeInfos.Add(new(call, currNode != null ? new() { currNode } : new()));
        }
    }

    private void LowerGNNELoad(Call call, List<NodeInfo> nodeInfos)
    {
        if (call[GNNELoad.Input] is not TensorConst)
        {
            UpdateNodeInfo(nodeInfos, call);
            if (call[GNNELoad.Input] is not Var)
            {
                UpdateNodeInfo(nodeInfos, (call[GNNELoad.Input] as Call)!, nodeInfos[^1]);
                Visit(call[GNNELoad.Input], nodeInfos);
            }
        }
    }

    private void LowerGnneStore(Call call, List<NodeInfo> nodeInfos)
    {
        UpdateNodeInfo(nodeInfos, call);
        UpdateNodeInfo(nodeInfos, (call[GNNEStore.Input] as Call)!, nodeInfos[^1]);
        Visit(call[GNNEStore.Input], nodeInfos);
    }

    private void LowerGNNEConv2D(Call call, List<NodeInfo> nodeInfos)
    {
        var inputShape = call[GNNEConv2D.Input].CheckedShape.ToValueArray();
        var outputShape = call.CheckedShape.ToValueArray();
        var weightsShape = call[GNNEConv2D.Weights].CheckedShape.ToValueArray();
        var paddings = ((TensorConst)call[GNNEConv2D.Padding]).Value.ToArray<int>();
        var strideH = ((TensorConst)call[GNNEConv2D.Stride]).Value.ToArray<int>()[0];
        var strideW = ((TensorConst)call[GNNEConv2D.Stride]).Value.ToArray<int>()[1];
        var dilationH = ((TensorConst)call[GNNEConv2D.Dilation]).Value.ToArray<int>()[0];
        var dilationW = ((TensorConst)call[GNNEConv2D.Dilation]).Value.ToArray<int>()[1];
        var groups = ((TensorConst)call[GNNEConv2D.Groups]).Value.ToScalar<int>();

        if (inputShape[0] > 1 &&
            outputShape[2] == 1 && outputShape[3] == 1
            && inputShape[2] == 1 && inputShape[3] == 1
            && weightsShape[2] == 1 && weightsShape[3] == 1
            && strideH == 1 && strideW == 1
            && dilationH == 1 && dilationW == 1
            && paddings.Sum() == 0
            && groups == 1)
        {
            _containNotSupportedOp = true;
        }

        UpdateNodeInfo(nodeInfos, call);
        UpdateNodeInfo(nodeInfos, (call[GNNEConv2D.Input] as Call)!, nodeInfos[^1]);
        Visit(call[GNNEConv2D.Input], nodeInfos);
    }

    private void LowerGNNEPdp0Dw(Call call, List<NodeInfo> nodeInfos)
    {
        UpdateNodeInfo(nodeInfos, call);
        UpdateNodeInfo(nodeInfos, (call[GNNEPdp0DW.Input] as Call)!, nodeInfos[^1]);
        Visit(call[GNNEPdp0DW.Input], nodeInfos);
    }

    private void LowerUninitialized(Call call, List<NodeInfo> nodeInfos)
    {
        // do nothing
    }

    private void LowerGnnePdp1(Call call, List<NodeInfo> nodeInfos)
    {
        UpdateNodeInfo(nodeInfos, call);
        UpdateNodeInfo(nodeInfos, (call[GNNEPdp1.Input] as Call)!, nodeInfos[^1]);
        Visit(call[GNNEPdp1.Input], nodeInfos);
    }

    private void LowerGnnePdp0Reduce(Call call, List<NodeInfo> nodeInfos)
    {
        UpdateNodeInfo(nodeInfos, call);
        UpdateNodeInfo(nodeInfos, (call[GNNEPdp0Reduce.Input] as Call)!, nodeInfos[^1]);
        Visit(call[GNNEPdp0Reduce.Input], nodeInfos);
    }

    private void LowerGnneTranspose(Call call, List<NodeInfo> nodeInfos)
    {
        UpdateNodeInfo(nodeInfos, call);
        UpdateNodeInfo(nodeInfos, (call[GNNETranspose.Input] as Call)!, nodeInfos[^1]);
        Visit(call[GNNETranspose.Input], nodeInfos);
    }

    private void LowerGnneActivation(Call call, List<NodeInfo> nodeInfos)
    {
        UpdateNodeInfo(nodeInfos, call);
        var currNode = nodeInfos[^1];

        bool inputBFirst = false;
        if (call[GNNEActivation.InputB] != None.Default &&
            call[GNNEActivation.InputB] is Call { Target: GNNEConv2D } &&
            ((GNNEActivation)call.Target).InputFromL1[1])
        {
            inputBFirst = true;
            UpdateNodeInfo(nodeInfos, (call[GNNEActivation.InputB] as Call)!, currNode);
            Visit(call[GNNEActivation.InputB], nodeInfos);
        }

        if (call[GNNEActivation.InputB] == None.Default ||
            call[GNNEActivation.InputA] is not Call { Target: GNNELoad } ||
            ((((Call)call[GNNEActivation.InputA])[GNNELoad.Input] is Var || ((Call)call[GNNEActivation.InputA])[GNNELoad.Input] is Call { Target: Reshape }) &&
             call[GNNEActivation.InputB] is Call { Target: GNNELoad }))
        {
            UpdateNodeInfo(nodeInfos, (call[GNNEActivation.InputA] as Call)!, currNode);
            Visit(call[GNNEActivation.InputA], nodeInfos);
        }

        if (!inputBFirst)
        {
            if (call[GNNEActivation.InputB] != None.Default &&
                (call[GNNEActivation.InputB] is not Call { Target: GNNELoad } ||
                 (((Call)call[GNNEActivation.InputB])[GNNELoad.Input] is Var &&
                 call[GNNEActivation.InputA] is Call { Target: GNNELoad } &&
                 ((Call)call[GNNEActivation.InputA])[GNNELoad.Input] is TensorConst)))
            {
                UpdateNodeInfo(nodeInfos, (call[GNNEActivation.InputB] as Call)!, currNode);
                Visit(call[GNNEActivation.InputB], nodeInfos);
            }
        }
    }

    private void LowerReshape(Call call, List<NodeInfo> nodeInfos)
    {
        UpdateNodeInfo(nodeInfos, call);
        if (call[Reshape.Input] is not Var)
        {
            UpdateNodeInfo(nodeInfos, (call[Reshape.Input] as Call)!, nodeInfos[^1]);
            Visit(call[Reshape.Input], nodeInfos);
        }
    }

    private void LowerGnneLoad(Call call, GNNELoad op, FusionType fusionType, List<NodeInfo> nodesInfo, GlbItemSize maxSizes, NodeInfo currNode)
    {
        var tileOutShape = currNode.OutShape;
        if (call[GNNELoad.Input] is not TensorConst)
        {
            int[] currentOutputShape = tileOutShape;
            int hAligned = currentOutputShape[2];
            int wAligned = currentOutputShape[3];
            int alignedNum = TileUtilities.GetBytesPerElement(call.CheckedDataType) == 1 ? 32 : 16;
            if (currNode.Nb.AlignType == AlignedType.EAligned)
            {
                hAligned = TileUtilities.GetAlignedNum(hAligned, alignedNum);
            }

            if (currNode.Nb.AlignType == AlignedType.FAligned)
            {
                wAligned = TileUtilities.GetAlignedNum(wAligned, alignedNum);
            }

            TensorOnGlb ofGlb = new(new[] { currentOutputShape[0], currentOutputShape[1], hAligned, wAligned }, call.CheckedDataType, 0);
            int ofmapSize = ofGlb.AllocatedBytes;
            ofmapSize = TileUtilities.GetAlignedNum(ofmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);
            ofmapSize *= _ofBufNum;
            if (ofmapSize > maxSizes.MaxOfmapSize)
            {
                maxSizes.MaxOfmapSize = ofmapSize;
            }
        }

        nodesInfo.Where(node => node.Op == call[GNNELoad.Input]).ToList().ForEach(node => node.OutShape = tileOutShape);
    }

    private void LowerGnneStore(Call call, GNNEStore op, FusionType fusionType, List<NodeInfo> nodesInfo, GlbItemSize maxSizes, NodeInfo currNode)
    {
        var tileOutShape = currNode.OutShape;
        int[] currentOutputShape = tileOutShape;
        int h = currentOutputShape[2];
        int w = currentOutputShape[3];
        TensorOnGlb ofGlb = new(new[] { currentOutputShape[0], currentOutputShape[1], h, w }, call[GNNEStore.Input].CheckedDataType, 0);
        int ofmapSize = ofGlb.AllocatedBytes;
        ofmapSize = TileUtilities.GetAlignedNum(ofmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);
        ofmapSize *= _ofBufNum;
        if (ofmapSize > maxSizes.MaxOfmapSize)
        {
            maxSizes.MaxOfmapSize = ofmapSize;
        }

        nodesInfo.Where(node => node.Op == call[GNNEStore.Input]).ToList().ForEach(node => node.OutShape = tileOutShape);
    }

    private void LowerGnnePdp1(Call call, GNNEPdp1 op, FusionType fusionType, List<NodeInfo> nodesInfo, GlbItemSize maxSizes, NodeInfo currNode)
    {
        var tileOutShape = currNode.OutShape;
        long[] inputShape = call[GNNEPdp1.Input].CheckedShape.ToValueArray();
        long[] outputShape = call.CheckedShape.ToValueArray();
        int[] paddings = ((TensorConst)call[GNNEPdp1.Padding]).Value.ToArray<int>();
        int[] paddingH = { paddings[0], paddings[1] };
        int[] paddingW = { paddings[2], paddings[3] };
        int strideH = ((TensorConst)call[GNNEPdp1.Stride]).Value.ToArray<int>()[0];
        int strideW = ((TensorConst)call[GNNEPdp1.Stride]).Value.ToArray<int>()[1];
        int dilationH = 1;
        int dilationW = 1;
        int kernelH = ((TensorConst)call[GNNEPdp1.Filter]).Value.ToArray<int>()[0];
        int kernelW = ((TensorConst)call[GNNEPdp1.Filter]).Value.ToArray<int>()[1];
        var inputType = call[GNNEPdp1.Input].CheckedDataType;
        var outputType = call.CheckedDataType;

        int r = kernelH;
        int s = kernelW;

        bool isGlobalPdp = inputShape[2] == kernelH && inputShape[3] == kernelW
                                                        && (inputShape[2] > 16 || inputShape[3] > 64 || inputShape[2] * inputShape[3] > 256);
        if (fusionType != FusionType.L2IfFullWPp && isGlobalPdp)
        {
            maxSizes.MaxOfmapSize = GNNEEnv.GlbSize;
        }

        int[] currentOutputShape = tileOutShape;
        int e = currentOutputShape[2];
        if (isGlobalPdp)
        {
            int rTmp = kernelH > 16 ? 16 : kernelH;
            e = (int)Math.Ceiling(1.0f * inputShape[2] / rTmp);
        }

        int f = currentOutputShape[3];
        int alignedNum = TileUtilities.GetBytesPerElement(isGlobalPdp ? DataTypes.Float16 : outputType) == 1 ? 32 : 16;
        int fTemp = TileUtilities.GetAlignedNum(f, alignedNum);
        TensorOnGlb ofGlb = new(new[] { currentOutputShape[0], currentOutputShape[1], e, fTemp }, isGlobalPdp ? DataTypes.Float16 : outputType, 0);
        int ofmapSize = ofGlb.AllocatedBytes;
        ofmapSize = TileUtilities.GetAlignedNum(ofmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);
        ofmapSize *= _ofBufNum;

        int h = SpaceSearcher.GetInputHeight(e, (int)inputShape[2], r, (int)outputShape[2], strideH, dilationH, new Padding(paddingH[0], paddingH[1]));
        int w = SpaceSearcher.GetInputHeight(f, (int)inputShape[3], s, (int)outputShape[3], strideW, dilationW, new Padding(paddingW[0], paddingW[1]));
        if (fusionType == FusionType.L2IfFullWPp)
        {
            (h, w) = (Math.Min(h, (int)inputShape[2]), Math.Min(w, (int)inputShape[3]));
        }

        int[] currentInputShape = { (int)inputShape[0], (int)inputShape[1], h, w };

        if (ofmapSize > maxSizes.MaxOfmapSize)
        {
            maxSizes.MaxOfmapSize = ofmapSize;
        }

        nodesInfo.Where(node => node.Op == call[GNNEPdp1.Input]).ToList().ForEach(node => node.OutShape = (node.OutShape.Length == 0 || currentInputShape.Aggregate((a, b) => a * b) > node.OutShape.Aggregate((a, b) => a * b)) ? currentInputShape : node.OutShape);
    }

    private void LowerGnnePdp0Reduce(Call call, GNNEPdp0Reduce op, FusionType fusionType, List<NodeInfo> nodesInfo, GlbItemSize maxSizes, NodeInfo currNode)
    {
        var tileOutShape = currNode.OutShape;
        long[] inputShape = call[GNNEPdp0Reduce.Input].CheckedShape.ToValueArray();
        long[] outputShape = call.CheckedShape.ToValueArray();
        int[] paddings = ((TensorConst)call[GNNEPdp0Reduce.Padding]).Value.ToArray<int>();
        int[] paddingH = new[] { paddings[0], paddings[1] };
        int[] paddingW = new[] { paddings[2], paddings[3] };
        int strideH = ((TensorConst)call[GNNEPdp0Reduce.Stride]).Value.ToArray<int>()[0];
        int strideW = ((TensorConst)call[GNNEPdp0Reduce.Stride]).Value.ToArray<int>()[1];
        int dilationH = 1;
        int dilationW = 1;
        int kernelH = ((TensorConst)call[GNNEPdp0Reduce.Filter]).Value.ToArray<int>()[0];
        int kernelW = ((TensorConst)call[GNNEPdp0Reduce.Filter]).Value.ToArray<int>()[1];
        var inputType = call[GNNEPdp0Reduce.Input].CheckedDataType;
        var outputType = call.CheckedDataType;

        int m = (int)outputShape[1];
        int r = kernelH;
        int s = kernelW;
        int oc = m;

        int[] currentOutputShape = tileOutShape;
        int alignedNum = TileUtilities.GetBytesPerElement(outputType) == 1 ? 32 : 16;
        int hAligned = currentOutputShape[2];
        if (currNode.Nb.AlignType == AlignedType.EAligned)
        {
            hAligned = TileUtilities.GetAlignedNum(hAligned, alignedNum);
        }

        int wAligned = currentOutputShape[3];
        if (currNode.Nb.AlignType == AlignedType.FAligned)
        {
            wAligned = TileUtilities.GetAlignedNum(wAligned, alignedNum);
        }

        TensorOnGlb ofGlb = new(new[] { currentOutputShape[0], currentOutputShape[1], hAligned, wAligned }, outputType, 0);
        int ofmapSize = ofGlb.AllocatedBytes;
        ofmapSize = TileUtilities.GetAlignedNum(ofmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);
        ofmapSize *= _ofBufNum;

        int h = SpaceSearcher.GetInputHeight(currentOutputShape[2], (int)inputShape[2], r, (int)outputShape[2], strideH, dilationH, new Padding(paddingH[0], paddingH[1]));
        int w = SpaceSearcher.GetInputHeight(currentOutputShape[3], (int)inputShape[3], s, (int)outputShape[3], strideW, dilationW, new Padding(paddingW[0], paddingW[1]));
        if (fusionType == FusionType.L2IfFullWPp)
        {
            (h, w) = (Math.Min(h, (int)inputShape[2]), Math.Min(w, (int)inputShape[3]));
        }

        int[] currentInputShape = { (int)inputShape[0], (int)inputShape[1], h, w };

        if (ofmapSize > maxSizes.MaxOfmapSize)
        {
            maxSizes.MaxOfmapSize = ofmapSize;
        }

        var nb = currNode.Nb;
        nb.Pdp0ActOffset = maxSizes.MaxPdp0ActSize;
        int actSize = TileUtilities.GetAlignedNum(
            SpaceSearcher.GetActSize(oc, GNNEEnv.ActNumPerChan, 2),
            GNNEEnv.ActBankWidth * GNNEEnv.GlbBankWidth);
        maxSizes.MaxPdp0ActSize += actSize;

        currNode.Nb = nb;
        nodesInfo.Where(node => node.Op == call[GNNEPdp0Reduce.Input]).ToList().ForEach(node => node.OutShape = (node.OutShape.Length == 0 || currentInputShape.Aggregate((a, b) => a * b) > node.OutShape.Aggregate((a, b) => a * b)) ? currentInputShape : node.OutShape);
    }

    private void LowerGnneConv2D(Call call, GNNEConv2D op, FusionType fusionType, List<NodeInfo> nodesInfo, GlbItemSize maxSizes, NodeInfo currNode)
    {
        var tileOutShape = currNode.OutShape;
        long[] inputShape = call[GNNEConv2D.Input].CheckedShape.ToValueArray();
        long[] outputShape = call.CheckedShape.ToValueArray();
        long[] weightsShape = call[GNNEConv2D.Weights].CheckedShape.ToValueArray();
        int[] paddings = ((TensorConst)call[GNNEConv2D.Padding]).Value.ToArray<int>();
        int[] paddingH = new[] { paddings[0], paddings[1] };
        int[] paddingW = new[] { paddings[2], paddings[3] };
        int strideH = ((TensorConst)call[GNNEConv2D.Stride]).Value.ToArray<int>()[0];
        int strideW = ((TensorConst)call[GNNEConv2D.Stride]).Value.ToArray<int>()[1];
        int dilationH = ((TensorConst)call[GNNEConv2D.Dilation]).Value.ToArray<int>()[0];
        int dilationW = ((TensorConst)call[GNNEConv2D.Dilation]).Value.ToArray<int>()[1];
        int groups = ((TensorConst)call[GNNEConv2D.Groups]).Value.ToScalar<int>();

        var outputType = call.CheckedDataType;
        var weightType = call[GNNEConv2D.Weights].CheckedDataType;

        int m = (int)weightsShape[0];
        int c = (int)weightsShape[1];
        int r = (int)weightsShape[2];
        int s = (int)weightsShape[3];
        int oc = m;

        int[] currentOutputShape = tileOutShape;
        int alignedNum = TileUtilities.GetBytesPerElement(outputType) == 1 ? 32 : 16;
        int hAligned = currentOutputShape[2];
        if (currNode.Nb.AlignType == AlignedType.EAligned)
        {
            hAligned = TileUtilities.GetAlignedNum(hAligned, alignedNum);
        }

        int wAligned = currentOutputShape[3];
        if (currNode.Nb.AlignType == AlignedType.FAligned)
        {
            wAligned = TileUtilities.GetAlignedNum(wAligned, alignedNum);
        }

        TensorOnGlb ofGlb = new(new[] { currentOutputShape[0], currentOutputShape[1], hAligned, wAligned }, outputType, 0);
        int ofmapSize = ofGlb.AllocatedBytes;
        ofmapSize = TileUtilities.GetAlignedNum(ofmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);
        ofmapSize *= _ofBufNum;

        int h = SpaceSearcher.GetInputHeight(currentOutputShape[2], (int)inputShape[2], r, (int)outputShape[2], strideH, dilationH, new Padding(0, 0));
        bool hasattrH2C = weightsShape[1] * weightsShape[2] <= GNNEEnv.PuHeight && weightsShape[2] != 1;
        if (hasattrH2C)
        {
            h += paddingH.Sum();
        }

        int w = SpaceSearcher.GetInputHeight(currentOutputShape[3], (int)inputShape[3], s, (int)outputShape[3], strideW, dilationW, new Padding(0, 0));
        if (fusionType == FusionType.L2IfFullWPp)
        {
            (h, w) = (Math.Min(h, (int)inputShape[2]), Math.Min(w, (int)inputShape[3]));
        }

        int[] currentInputShape = { (int)inputShape[0], groups == 1 ? (int)weightsShape[1] : (int)inputShape[1], h, w };

        var nb = currNode.Nb;
        if (!(currNode.Children.Count == 1
        && currNode.Children[0].Op is { Target: GNNEActivation }
        && ((((GNNEActivation)currNode.Children[0].Op.Target).InputFromL1[0] && currNode.Children[0].Op[GNNEActivation.InputA] == call)
        || (((GNNEActivation)currNode.Children[0].Op.Target).InputFromL1[1] && currNode.Children[0].Op[GNNEActivation.InputB] == call)))
        && !(currNode.Children[0].Op is { Target: GNNEPdp0DW })
        && !(currNode.Children[0].Op is { Target: GNNEPdp0Reduce })
        && ofmapSize > maxSizes.MaxOfmapSize)
        {
            maxSizes.MaxOfmapSize = ofmapSize;
        }

        TensorOnGlb wGlb = new(new[] { r, s, c, m }, weightType, 0);
        if (fusionType == FusionType.L2IfFullWPp || fusionType == FusionType.L2IfSplitWPp)
        {
            int wSize = TileUtilities.GetAlignedNum(SpaceSearcher.GetWeightSize(r, s, c, m, TileUtilities.GetBytesPerElement(weightType)), GNNEEnv.WBankWidth * GNNEEnv.GlbBankWidth);

            if (wSize > maxSizes.MaxWeightSize)
            {
                maxSizes.MaxWeightSize = wSize;
            }
        }
        else
        {
            nb.WeightOffset = maxSizes.MaxWeightSize;
            int wSize = TileUtilities.GetAlignedNum(SpaceSearcher.GetWeightSize(r, s, c, m, TileUtilities.GetBytesPerElement(weightType)), GNNEEnv.WBankWidth * GNNEEnv.GlbBankWidth);
            maxSizes.MaxWeightSize += wSize;
        }

        nb.ActOffset = maxSizes.MaxActSize;
        int actSize = TileUtilities.GetAlignedNum(SpaceSearcher.GetActSize(oc, GNNEEnv.ActNumPerChan, 2), GNNEEnv.ActBankWidth * GNNEEnv.GlbBankWidth);
        maxSizes.MaxActSize += actSize;
        if (weightType == DataTypes.UInt8 || weightType == DataTypes.Int16)
        {
            nb.WeightQargOffset = maxSizes.MaxWeightQargSize;
            int wQargSize = TileUtilities.GetAlignedNum(
                SpaceSearcher.GetWQargSize(
                    (int)Math.Ceiling(oc * 1.0 / GNNEEnv.PuHeight) * GNNEEnv.PuWidth, 1),
                GNNEEnv.WQargBankWidth * GNNEEnv.GlbBankWidth);
            maxSizes.MaxWeightQargSize += wQargSize;
        }

        currNode.Nb = nb;
        nodesInfo.Where(node => node.Op == call[GNNEConv2D.Input]).ToList().ForEach(node => node.OutShape = (node.OutShape.Length == 0 || currentInputShape.Aggregate((a, b) => a * b) > node.OutShape.Aggregate((a, b) => a * b)) ? currentInputShape : node.OutShape);
    }

    private void LowerGnnePdp0Dw(Call call, GNNEPdp0DW op, FusionType fusionType, List<NodeInfo> nodesInfo, GlbItemSize maxSizes, NodeInfo currNode)
    {
        var tileOutShape = currNode.OutShape;
        long[] inputShape = call[GNNEPdp0DW.Input].CheckedShape.ToValueArray();
        long[] outputShape = call.CheckedShape.ToValueArray();
        long[] weightsShape = call[GNNEPdp0DW.Weights].CheckedShape.ToValueArray();
        int[] paddings = ((TensorConst)call[GNNEPdp0DW.Padding]).Value.ToArray<int>();
        int[] paddingH = { paddings[0], paddings[1] };
        int[] paddingW = { paddings[2], paddings[3] };
        int strideH = ((TensorConst)call[GNNEPdp0DW.Stride]).Value.ToArray<int>()[0];
        int strideW = ((TensorConst)call[GNNEPdp0DW.Stride]).Value.ToArray<int>()[1];
        int dilationH = ((TensorConst)call[GNNEPdp0DW.Dilation]).Value.ToArray<int>()[0];
        int dilationW = ((TensorConst)call[GNNEPdp0DW.Dilation]).Value.ToArray<int>()[1];
        int groups = ((TensorConst)call[GNNEPdp0DW.Groups]).Value.ToScalar<int>();

        var outputType = call.CheckedDataType;
        var weightType = call[GNNEPdp0DW.Weights].CheckedDataType;

        weightsShape[0] /= inputShape[1];
        weightsShape[1] = TileUtilities.GetAlignedNum((int)inputShape[1], GNNEEnv.PuWidth);

        int m = (int)weightsShape[0];
        int c = (int)weightsShape[1];
        int r = (int)weightsShape[2];
        int s = (int)weightsShape[3];
        int oc = c * m;

        int[] currentOutputShape = tileOutShape;
        int alignedNum = TileUtilities.GetBytesPerElement(outputType) == 1 ? 32 : 16;
        int hAligned = currentOutputShape[2];
        if (currNode.Nb.AlignType == AlignedType.EAligned)
        {
            hAligned = TileUtilities.GetAlignedNum(hAligned, alignedNum);
        }

        int wAligned = currentOutputShape[3];
        if (currNode.Nb.AlignType == AlignedType.FAligned)
        {
            wAligned = TileUtilities.GetAlignedNum(wAligned, alignedNum);
        }

        TensorOnGlb ofGlb = new(new[] { currentOutputShape[0], currentOutputShape[1], hAligned, wAligned }, outputType, 0);
        int ofmapSize = ofGlb.AllocatedBytes;
        ofmapSize = TileUtilities.GetAlignedNum(ofmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);
        ofmapSize *= _ofBufNum;

        int h = SpaceSearcher.GetInputHeight(currentOutputShape[2], (int)inputShape[2], r, (int)outputShape[2], strideH, dilationH, new Padding(0, 0));
        bool hasattrH2C = weightsShape[1] * weightsShape[2] <= GNNEEnv.PuHeight && weightsShape[2] != 1;
        if (hasattrH2C)
        {
            h += paddingH.Sum();
        }

        int w = SpaceSearcher.GetInputHeight(currentOutputShape[3], (int)inputShape[3], s, (int)outputShape[3], strideW, dilationW, new Padding(0, 0));
        if (fusionType == FusionType.L2IfFullWPp)
        {
            (h, w) = (Math.Min(h, (int)inputShape[2]), Math.Min(w, (int)inputShape[3]));
        }

        int[] currentInputShape = { (int)inputShape[0], groups == 1 ? (int)weightsShape[1] : (int)inputShape[1], h, w };

        var nb = currNode.Nb;
        if (ofmapSize > maxSizes.MaxOfmapSize)
        {
            maxSizes.MaxOfmapSize = ofmapSize;
        }

        TensorOnGlb wGlb = new(new[] { r, s, c, m }, weightType, 0);
        nb.DwWeightOffset = maxSizes.MaxDwWeightSize;
        int wSize = TileUtilities.GetAlignedNum(SpaceSearcher.GetWeightSize(r, s, TileUtilities.GetAlignedNum(c, GNNEEnv.PuWidth), m, TileUtilities.GetBytesPerElement(weightType)), GNNEEnv.WBankWidth * GNNEEnv.GlbBankWidth);
        maxSizes.MaxDwWeightSize += wSize;

        nb.DwActOffset = maxSizes.MaxDwActSize;
        int actSize = TileUtilities.GetAlignedNum(SpaceSearcher.GetActSize(oc, GNNEEnv.ActNumPerChan, 2), GNNEEnv.ActBankWidth * GNNEEnv.GlbBankWidth);
        maxSizes.MaxDwActSize += actSize;
        if (weightType == DataTypes.UInt8 || weightType == DataTypes.Int16)
        {
            nb.DwWeightQargOffset = maxSizes.MaxDwWeightQargSize;
            int wQargSize = TileUtilities.GetAlignedNum(SpaceSearcher.GetWQargSize(oc, 1), GNNEEnv.WQargBankWidth * GNNEEnv.GlbBankWidth);
            maxSizes.MaxDwWeightQargSize += wQargSize;
        }

        currNode.Nb = nb;
        nodesInfo.Where(node => node.Op == call[GNNEPdp0DW.Input]).ToList().ForEach(node => node.OutShape = (node.OutShape.Length == 0 || currentInputShape.Aggregate((a, b) => a * b) > node.OutShape.Aggregate((a, b) => a * b)) ? currentInputShape : node.OutShape);
    }

    private void LowerGnneTranspose(Call call, GNNETranspose op, FusionType fusionType, List<NodeInfo> nodesInfo, GlbItemSize maxSizes, NodeInfo currNode)
    {
        var tileOutShape = currNode.OutShape;
        if (fusionType != FusionType.L2IfFullWPp)
        {
            maxSizes.MaxOfmapSize = GNNEEnv.GlbSize;
        }

        var inputType = call[GNNETranspose.Input].CheckedDataType;
        var outputType = call.CheckedDataType;

        int[] currentOutputShape = tileOutShape;
        int f = currentOutputShape[3];
        int alignedNum = TileUtilities.GetBytesPerElement(outputType) == 1 ? 32 : 16;
        int fTemp = TileUtilities.GetAlignedNum(f, alignedNum);
        TensorOnGlb ofGlb = new(new[] { currentOutputShape[0], currentOutputShape[1], currentOutputShape[2], fTemp }, outputType, 0);
        int ofmapSize = ofGlb.AllocatedBytes;
        ofmapSize = TileUtilities.GetAlignedNum(ofmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);
        ofmapSize *= _ofBufNum;

        var perm = op.Perm;

        int[] GetInputShapes(int d0, int d1, int d2, int d3, MFU_TRANS_PERMUTE permute)
        {
            return permute switch
            {
                MFU_TRANS_PERMUTE.NCHW => new[] { d0, d1, d2, d3 },
                MFU_TRANS_PERMUTE.NCWH => new[] { d0, d1, d3, d2 },
                MFU_TRANS_PERMUTE.NHCW => new[] { d0, d2, d1, d3 },
                MFU_TRANS_PERMUTE.NHWC => new[] { d0, d3, d1, d2 },
                MFU_TRANS_PERMUTE.NWCH => new[] { d0, d2, d3, d1 },
                MFU_TRANS_PERMUTE.NWHC => new[] { d0, d3, d2, d1 },
                MFU_TRANS_PERMUTE.CNHW => new[] { d1, d0, d2, d3 },
                MFU_TRANS_PERMUTE.CNWH => new[] { d1, d0, d3, d2 },
                MFU_TRANS_PERMUTE.CHNW => new[] { d2, d0, d1, d3 },
                MFU_TRANS_PERMUTE.CHWN => new[] { d3, d0, d1, d2 },
                MFU_TRANS_PERMUTE.CWNH => new[] { d2, d0, d3, d1 },
                MFU_TRANS_PERMUTE.CWHN => new[] { d3, d0, d2, d1 },
                MFU_TRANS_PERMUTE.HNCW => new[] { d1, d2, d0, d3 },
                MFU_TRANS_PERMUTE.HNWC => new[] { d1, d3, d0, d2 },
                MFU_TRANS_PERMUTE.HCNW => new[] { d2, d1, d0, d3 },
                MFU_TRANS_PERMUTE.HCWN => new[] { d3, d1, d0, d2 },
                MFU_TRANS_PERMUTE.HWNC => new[] { d2, d3, d0, d1 },
                MFU_TRANS_PERMUTE.HWCN => new[] { d3, d2, d0, d1 },
                MFU_TRANS_PERMUTE.WNCH => new[] { d1, d2, d3, d0 },
                MFU_TRANS_PERMUTE.WNHC => new[] { d1, d3, d2, d0 },
                MFU_TRANS_PERMUTE.WCNH => new[] { d2, d1, d3, d0 },
                MFU_TRANS_PERMUTE.WCHN => new[] { d3, d1, d2, d0 },
                MFU_TRANS_PERMUTE.WHNC => new[] { d2, d3, d1, d0 },
                MFU_TRANS_PERMUTE.WHCN => new[] { d3, d2, d1, d0 },
                _ => new[] { d0, d1, d2, d3 },
            };
        }

        int[] inputShape = GetInputShapes(currentOutputShape[0], currentOutputShape[1], currentOutputShape[2], currentOutputShape[3], perm);
        int[] currentInputShape = { inputShape[0], inputShape[1], inputShape[2], inputShape[3] };

        if (ofmapSize > maxSizes.MaxOfmapSize)
        {
            maxSizes.MaxOfmapSize = ofmapSize;
        }

        nodesInfo.Where(node => node.Op == call[GNNETranspose.Input]).ToList().ForEach(node => node.OutShape = (node.OutShape.Length == 0 || currentInputShape.Aggregate((a, b) => a * b) > node.OutShape.Aggregate((a, b) => a * b)) ? currentInputShape : node.OutShape);
    }

    private void LowerUninitialized(Call call, IR.Buffers.Uninitialized op, FusionType fusionType, List<NodeInfo> nodesInfo, GlbItemSize max_sizes, NodeInfo currNode)
    {
        var tileOutShape = currNode.OutShape;
    }

    private void LowerGnneActivation(Call call, GNNEActivation op, FusionType fusionType, List<NodeInfo> nodesInfo, GlbItemSize maxSizes, NodeInfo currNode)
    {
        var tileOutShape = currNode.OutShape;
        bool inputBIsUninit = call[GNNEActivation.InputB] == None.Default;
        long[] inputAShape = call[GNNEActivation.InputA].CheckedShape.ToValueArray();
        long[] inputBShape = inputAShape;
        if (!inputBIsUninit)
        {
            inputBShape = call[GNNEActivation.InputB].CheckedShape.ToValueArray();
        }

        long[] outputShape = call.CheckedShape.ToValueArray();

        if ((fusionType == FusionType.L2IfSplitWPp || fusionType == FusionType.L2IfSplitWFull)
            && (!inputAShape.SequenceEqual(outputShape) || !inputBShape.SequenceEqual(outputShape)))
        {
            maxSizes.MaxOfmapSize = GNNEEnv.GlbSize;
        }

        var inputAType = call[GNNEActivation.InputA].CheckedDataType;
        var inputBType = inputAType;
        if (!inputBIsUninit)
        {
            inputBType = call[GNNEActivation.InputB].CheckedDataType;
        }

        var outputType = call.CheckedDataType;

        int oc = (int)outputShape[1];

        int[] currentOutputShape = tileOutShape;
        int alignedNum = TileUtilities.GetBytesPerElement(outputType) == 1 ? 32 : 16;
        int hAligned = currentOutputShape[2];
        if (currNode.Nb.AlignType == AlignedType.EAligned)
        {
            hAligned = TileUtilities.GetAlignedNum(hAligned, alignedNum);
        }

        int wAligned = currentOutputShape[3];
        if (currNode.Nb.AlignType == AlignedType.FAligned)
        {
            wAligned = TileUtilities.GetAlignedNum(wAligned, alignedNum);
        }

        TensorOnGlb ofGlb = new(new[] { currentOutputShape[0], currentOutputShape[1], hAligned, wAligned }, outputType, 0);
        int ofmapSize = ofGlb.AllocatedBytes;
        ofmapSize = TileUtilities.GetAlignedNum(ofmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);
        ofmapSize *= _ofBufNum;

        int h = currentOutputShape[2];
        int w = currentOutputShape[3];
        int[] currentInputShape = { (int)outputShape[0], (int)outputShape[1], h, w };

        var nb = currNode.Nb;
        if (ofmapSize > maxSizes.MaxOfmapSize)
        {
            maxSizes.MaxOfmapSize = ofmapSize;
        }

        nb.Ifmap2Offset = maxSizes.MaxIfmap2Size;
        int ifmapSize = 0;
        if (!inputBIsUninit && call[GNNEActivation.InputA] is Call { Target: GNNELoad })
        {
            if (nodesInfo.Find(ni => ni.Op == call[GNNEActivation.InputA]) is null)
            {
                h = (int)inputAShape[2];
                w = (int)inputAShape[3];
                TensorOnGlb ifGlb2 = new(new[] { (int)inputAShape[0], (int)inputAShape[1], TileUtilities.GetAlignedNum(h, 32), w }, inputAType, 0);
                ifmapSize = ifGlb2.AllocatedBytes;
                ifmapSize = TileUtilities.GetAlignedNum(ifmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);
            }
        }

        if (!inputBIsUninit && call[GNNEActivation.InputB] is Call { Target: GNNELoad })
        {
            if (nodesInfo.Find(ni => ni.Op == call[GNNEActivation.InputB]) is null)
            {
                h = (int)inputBShape[2];
                w = (int)inputBShape[3];
                TensorOnGlb ifGlb2 = new(new[] { (int)inputBShape[0], (int)inputBShape[1], TileUtilities.GetAlignedNum(h, 32), w }, inputBType, 0);
                ifmapSize = ifGlb2.AllocatedBytes;
                ifmapSize = TileUtilities.GetAlignedNum(ifmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);
            }
        }

        maxSizes.MaxIfmap2Size += ifmapSize;

        nb.Act1Offset = maxSizes.MaxAct1Size;
        bool is16Segments = ((TensorConst)call[GNNEActivation.Is16Segments]).Value.ToScalar<bool>();
        int chanNum = is16Segments ? 1 : oc;
        int actParamNum = is16Segments ? 49 : GNNEEnv.ActNumPerChan;
        int actSize = TileUtilities.GetAlignedNum(SpaceSearcher.GetActSize(chanNum, actParamNum, 2), GNNEEnv.ActBankWidth * GNNEEnv.GlbBankWidth);
        maxSizes.MaxAct1Size += actSize;
        currNode.Nb = nb;

        // if (inputBIsUninit || (call[GNNEActivation.InputB] as Call)[GNNELoad.Input] is TensorConst)
        // {
        //     // // Visit previous node
        //     // Visit(call[GNNEActivation.InputA], fusionType, current_input_shape, max_sizes, nodes_info);
        // }
        // else if ((call[GNNEActivation.InputA] as Call)[GNNELoad.Input] is TensorConst)
        // {
        //     // Visit(call[GNNEActivation.InputB], fusionType, current_input_shape, max_sizes, nodes_info);
        // }
        // else
        {
            if (fusionType != FusionType.L2IfFullWPp && !inputAShape.SequenceEqual(inputBShape))
            {
                maxSizes.MaxOfmapSize = GNNEEnv.GlbSize;
            }

            {
                // if (fusionType == fusion_type.l2_if_full_w_pp)
                //     current_input_shape = input_a_shape;
                // Visit(call[GNNEActivation.InputA], fusionType, current_input_shape, max_sizes, nodes_info);
            }

            {
                // if (fusionType == fusion_type.l2_if_full_w_pp)
                //     current_input_shape = input_b_shape;
                // Visit(call[GNNEActivation.InputB], fusionType, current_input_shape, max_sizes, nodes_info);
            }
        }

        nodesInfo.Where(node => node.Op == call[GNNEActivation.InputA]).ToList().ForEach(node => node.OutShape = (node.OutShape.Length == 0 || currentInputShape.Aggregate((a, b) => a * b) > node.OutShape.Aggregate((a, b) => a * b)) ? currentInputShape : node.OutShape);
        if (!inputBIsUninit)
        {
            nodesInfo.Where(node => node.Op == call[GNNEActivation.InputB]).ToList().ForEach(node => node.OutShape = (node.OutShape.Length == 0 || currentInputShape.Aggregate((a, b) => a * b) > node.OutShape.Aggregate((a, b) => a * b)) ? currentInputShape : node.OutShape);
        }
    }

    public record GlbItemSize
    {
        public int BasementSize { get; set; }

        public int MaxOfmapSize { get; set; }

        public int MaxWeightSize { get; set; }

        public int MaxActSize { get; set; }

        public int MaxWeightQargSize { get; set; }

        public int MaxDwWeightSize { get; set; }

        public int MaxDwActSize { get; set; }

        public int MaxDwWeightQargSize { get; set; }

        public int MaxIfmap2Size { get; set; }

        public int MaxAct1Size { get; set; }

        public int MaxPdp0ActSize { get; set; }

        public int MaxWeightPreloadSize { get; set; }

        public int SumSize => BasementSize + MaxOfmapSize + MaxWeightSize + MaxActSize + MaxWeightQargSize + MaxDwWeightSize + MaxDwActSize + MaxDwWeightQargSize + MaxIfmap2Size + MaxAct1Size + MaxPdp0ActSize + MaxWeightPreloadSize;
    }
}
