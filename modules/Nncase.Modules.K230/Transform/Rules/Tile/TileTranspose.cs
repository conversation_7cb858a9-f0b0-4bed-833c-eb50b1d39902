﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using Nncase.Runtime.K230;
using Nncase.Schedule;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using static Nncase.Passes.Rules.K230.TileUtilities;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class TileTranspose : RewriteRule<Pattern>
{
    private static int _count = -1;

    /// <inheritdoc/>
    public override Pattern Pattern { get; } = FusionPattern.IsGNNETransposeFusion();

    private PrimFunction GetReplace(Call call, GNNETranspose callOp, Call ld, Call st)
    {
        _count++;
        var perm = callOp.Perm;
        var tiledGlb = SearchGlbParameters(ld, st, perm);
        long[] inShape = ld.Arguments[0].CheckedShape.ToValueArray();
        <PERSON><PERSON>CreateBuffer(new(ld[GNNELoad.Input].CheckedDataType, inShape), MemoryLocation.Input, out var ddrIf);
        T.CreateBuffer(new(st.CheckedDataType, st.CheckedShape), MemoryLocation.Output, out var ddrOf);
        var actions = BuildSchedule(tiledGlb, callOp, ld, st, ddrIf, ddrOf);
        var actionConverter = new ActionToInstruct();
        return T.PrimFunc($"TileTranspose_{_count}", K230RtModule.Kind, new Var(ddrIf.CheckedType), new Var(ddrOf.CheckedType)).Body(
            actionConverter.Instructions(actions),
            I.END(GP_REGISTER.x0, 0))
        .Build();
    }

    private List<GnneAction> BuildSchedule(TiledGlb glb, GNNETranspose transpose, Call ld, Call st, TIR.Buffer ddrIf, TIR.Buffer ddrOf)
    {
        var perm = transpose.Perm;

        List<GnneAction> actions = new();
        var gpr = new GprHandler(GNNEEnv.GprNum);
        var ssr = new SsrHandler(GNNEEnv.SsrNum);
        var ccrHandler = new CcrHandler();
        GnneActionUpdater actionUpdater = new(actions, glb, ccrHandler, gpr, ssr);

        // mmu conf
        actionUpdater.UpdateMmuConf();

        int[] lastOutShape = glb.LastOutShape;
        var inputShape = ld.CheckedShape;
        var seq =
            from glbInputBatch in SegmentBy(0, lastOutShape[0], (int)inputShape[0].FixedValue)
            from glbInputChannel in SegmentBy(0, lastOutShape[1], (int)inputShape[1].FixedValue)
            from glbInputRow in SegmentBy(0, lastOutShape[2], (int)inputShape[2].FixedValue)
            from glbInputColumn in SegmentBy(0, lastOutShape[3], (int)inputShape[3].FixedValue)
            select new SegmentND(glbInputBatch, glbInputChannel, glbInputRow, glbInputColumn);

        int iPp = 0;
        foreach (var ifmap in seq)
        {
            var ofmap = new SegmentND(GNNETypePatternUtility.ApplyPerm1(perm, ifmap.ToArray()));

            // load ifmap
            List<CcrSet> ccrSetIfmap = new();
            ccrSetIfmap.Add(new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap, iPp)), 1));
            var ifmapPp = ifmap;
            var stridesD = new[] { glb.GlbMap[ItemName.Ifmap].Dimensions[1], glb.GlbMap[ItemName.Ifmap].Dimensions[2], glb.GlbMap[ItemName.Ifmap].Dimensions[3] }.ToList();
            actionUpdater.UpdateLoadIf(ifmapPp, ld, iPp, ddrIf, 0, stridesD, ItemName.Ifmap, ccrSetIfmap);

            // transpose
            List<CcrSet> ccrSetOfmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ofmap, iPp)), 1) };
            List<CcrClr> ccrClrIfmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap, iPp))) };
            var ofmapPp = ofmap;
            actionUpdater.UpdateMfuTranspose(ifmapPp, ofmapPp, ld.CheckedDataType, perm, iPp, ccrSetOfmap, ccrClrIfmap);

            // store
            List<CcrClr> ccrClrOfmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ofmap, iPp))) };
            actionUpdater.UpdateStoreT(ofmapPp, st, iPp, ddrOf, 0, null!, null!, ccrClrOfmap);

            iPp = (iPp + 1) % 2;
        }

        Assert(ccrHandler.CcrSanityCheck());

        return actions;
    }

    private TileTransposeGlb SearchGlbParameters(Call ld, Call st, MFU_TRANS_PERMUTE perm)
    {
        var input = ld.Arguments[0];
        var inShape = new GNNEShape(input.CheckedShape.ToValueArray().Select(x => (int)x).ToArray());
        var lastOutputShape = new GNNEShape(1, 1, 1, 1);

        for (int i = 3; i >= 0; i--)
        {
            int i1 = i;
            lastOutputShape[i] = SearchAxis(
                lastOutputShape, inShape, i, newV => HandleAllocate(ld, st, perm, lastOutputShape.WithIndex(i1, newV)));
        }

        var allocation = HandleAllocate(ld, st, perm, lastOutputShape, true);
        Assert(allocation.IsOk);

        return new TileTransposeGlb(allocation.GlbMap, allocation.Items, lastOutputShape.Dims, GNNEEnv.NPingPongSplit, allocation.GlbMap[ItemName.Ifmap], allocation.GlbMap[ItemName.Ofmap]);
    }

    private AllocateResult HandleAllocate(Call ld, Call st, MFU_TRANS_PERMUTE perm, GNNEShape shape, bool isFinal = false)
    {
        var bp = new BoxPacker(16);
        Dictionary<ItemName, TensorOnGlb> glbMap = new();

        var outShape = new GNNEShape(GNNETypePatternUtility.ApplyPerm1(perm, shape.Dims));

        int bytesPerElement = GetBytesPerElement(ld.CheckedDataType);
        int alignedNum = bytesPerElement == 1 ? 32 : 16;
        int wTemp = GetAlignedNum(shape[3], alignedNum);
        var ifGlb = new TensorOnGlb(new[] { shape[0], shape[1], shape[2], wTemp }, ld.CheckedDataType, 0);
        int ifmapSize = ifGlb.GlbNByte * GNNEEnv.NPingPongSplit;
        ifmapSize = GetAlignedNum(ifmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);

        int owTemp = GetAlignedNum(outShape[3], alignedNum);
        var ofGlb = new TensorOnGlb(new[] { outShape[0], outShape[1], outShape[2], owTemp }, st.CheckedDataType, 0);
        int ofmapSize = ofGlb.GlbNByte * GNNEEnv.NPingPongSplit;
        ofmapSize = GetAlignedNum(ofmapSize, GNNEEnv.OfmapBankWidth * GNNEEnv.GlbBankWidth);

        int basementSize = SpaceSearcher.GetBasementSize();
        basementSize = GetAlignedNum(basementSize, GNNEEnv.GlbWidth * GNNEEnv.GlbBankWidth);

        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.GlbWidth, basementSize / GNNEEnv.GlbWidth / GNNEEnv.GlbBankWidth }, ItemName.Basement));
        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.IfmapBankWidth, ifmapSize / GNNEEnv.IfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ifmap));
        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.OfmapBankWidth, ofmapSize / GNNEEnv.OfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ofmap));

        var allocation = SpaceSearcher.TryAllocate(bp);

#if DEBUG
        if (isFinal && allocation.IsOk)
        {
            bp.DisplayFinalAllocation();
        }
#endif

        if (allocation.IsOk)
        {
            ifGlb.Mmu = allocation.Items[ItemName.Ifmap];
            ofGlb.Mmu = allocation.Items[ItemName.Ofmap];
        }

        glbMap.Add(ItemName.Ifmap, ifGlb);
        glbMap.Add(ItemName.Ofmap, ofGlb);
        allocation.GlbMap = glbMap;
        return allocation;
    }
}

internal class TileTransposeGlb : TiledGlb
{
    private TensorOnGlb _ifGlb;
    private TensorOnGlb _ofGlb;

    public TileTransposeGlb(Dictionary<ItemName, TensorOnGlb> glbMap, Dictionary<ItemName, MmuItem> items, int[] lastOutShape, int nPingPongSplit, TensorOnGlb ifGlb, TensorOnGlb ofGlb)
        : base(glbMap, items, lastOutShape, nPingPongSplit)
    {
        _ifGlb = ifGlb;
        _ofGlb = ofGlb;
    }
}
