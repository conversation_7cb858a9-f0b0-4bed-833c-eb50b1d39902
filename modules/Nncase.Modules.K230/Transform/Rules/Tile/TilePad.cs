﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using Nncase.Runtime.K230;
using Nncase.Schedule;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using static Nncase.Passes.Rules.K230.TileUtilities;
using tensor_ccr_stat = System.Collections.Generic.List<System.Tuple<Nncase.TIR.SegmentND, Nncase.Passes.Rules.K230.TensorStat>>;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class TilePad : RewriteRule<Pattern>
{
    private static int _count = -1;
    private CcrHandler? _ccrHandler = new();
    private GprHandler? _gpr = new();
    private SsrHandler? _ssr = new();

    private DataType? _inputType;
    private DataType? _outputType;
    private Call? _pad;
    private Call? _lif;
    private Call? _sof;
    private GNNEShape? _inputShape;
    private GNNEShape? _outputShape;

    public override Pattern Pattern { get; } = FusionPattern.IsGNNEPadFusion();

    private PrimFunction GetReplace(Call call, Call ld, Call st)
    {
        _count++;
        long[] inShape = ld[GNNELoad.Input].CheckedShape.ToValueArray();
        T.CreateBuffer(new(ld[GNNELoad.Input].CheckedDataType, inShape), MemoryLocation.Input, out var ddrIf);
        T.CreateBuffer(new(st.CheckedDataType, st.CheckedShape), MemoryLocation.Output, out var ddrOf);
        InitParameters(call, ld, st);
        TiledGlb glb = SearchGlbParameters();
        var actions = BuildSchedule(call, glb, ddrIf, ddrOf);
        var actionConverter = new ActionToInstruct();
        return T.PrimFunc($"TilePad_{_count}", K230RtModule.Kind, new Var(ddrIf.CheckedType), new Var(ddrOf.CheckedType)).Body(
            actionConverter.Instructions(actions),
            I.END(GP_REGISTER.x0, 0))
        .Build();
    }

    private void InitParameters(Call padNode, Call ld, Call st)
    {
        _pad = padNode;
        _lif = ld;
        _sof = st;

        _inputType = _lif.CheckedDataType;
        _outputType = _pad.CheckedDataType;

        _inputShape = new GNNEShape((int)_lif.CheckedShape[0].FixedValue, (int)_lif.CheckedShape[1].FixedValue, (int)_lif.CheckedShape[2].FixedValue, (int)_lif.CheckedShape[3].FixedValue);
        _outputShape = new GNNEShape((int)_pad.CheckedShape[0].FixedValue, (int)_pad.CheckedShape[1].FixedValue, (int)_pad.CheckedShape[2].FixedValue, (int)_pad.CheckedShape[3].FixedValue);
    }

    private TileConv2DGlb SearchGlbParameters()
    {
        int n = 1;
        if (GNNEEnv.BatchInference)
        {
            n = Math.Min(4, _inputShape![0]);
        }

        int m = 1;
        int oh = 1;
        int ow = _outputShape![3];

        AllocateResult allocation;
        while (oh < _outputShape[2])
        {
            allocation = HandleAllocate(n, m, oh + 1, ow);
            if (allocation.IsOk)
            {
                oh += 1;
            }
            else
            {
                break;
            }
        }

        while (m < _outputShape[1])
        {
            allocation = HandleAllocate(n, m + 1, oh, ow);
            if (allocation.IsOk)
            {
                m += 1;
            }
            else
            {
                break;
            }
        }

        allocation = HandleAllocate(n, m, oh, ow, true);
        Assert(allocation.IsOk);

        GNNEShape lastOutShape = new(n, m, oh, ow);
        var glb = new TileConv2DGlb(allocation.GlbMap, allocation.Items, lastOutShape.Dims, GNNEEnv.NPingPongSplit, allocation.GlbMap[ItemName.Ifmap], allocation.GlbMap[ItemName.Ofmap]);

        return glb;
    }

    private AllocateResult HandleAllocate(int n, int m, int e, int f, bool isFinal = false)
    {
        List<BoxOnGlb> boxes = new();
        Dictionary<ItemName, TensorOnGlb> glbMap = new();

        int bytesPerElement = GetBytesPerElement(_inputType!);
        int mPp = (int)Math.Ceiling(1.0 * m / GNNEEnv.NPingPongSplit);

        TensorOnGlb ofGlb = new(new[] { n, mPp, e, f }, _inputType!, 0);
        int ofmapSize = ofGlb.AllocatedBytes;
        ofmapSize = GetAlignedNum(ofmapSize, GNNEEnv.OfmapBankWidth * GNNEEnv.GlbBankWidth);
        ofGlb.AllocatedBytes = ofmapSize;
        ofmapSize *= GNNEEnv.NPingPongSplit;
        var ifGlb = ofGlb;

        int basementSize = SpaceSearcher.GetBasementSize();
        basementSize = GetAlignedNum(basementSize, GNNEEnv.GlbWidth * GNNEEnv.GlbBankWidth);

        boxes.Add(new BoxOnGlb(
            new[] { GNNEEnv.GlbWidth, basementSize / GNNEEnv.GlbWidth / GNNEEnv.GlbBankWidth },
            ItemName.Basement));
        boxes.Add(new BoxOnGlb(
            new[] { GNNEEnv.OfmapBankWidth, ofmapSize / GNNEEnv.OfmapBankWidth / GNNEEnv.GlbBankWidth },
            ItemName.Ofmap));

        var bp = new BoxPacker(16);
        bp.Boxes = boxes;
        var allocation = SpaceSearcher.TryAllocate(bp);

#if DEBUG
        if (isFinal && allocation.IsOk)
        {
            bp.DisplayFinalAllocation();
        }
#endif

        if (allocation.IsOk)
        {
            ifGlb.Mmu = allocation.Items[ItemName.Ofmap];
            ofGlb.Mmu = allocation.Items[ItemName.Ofmap];
        }

        glbMap.Add(ItemName.Ifmap, ifGlb);
        glbMap.Add(ItemName.Ofmap, ofGlb);

        AllocateResult ret = new() { IsOk = allocation.IsOk, Items = allocation.Items, GlbMap = glbMap };

        return ret;
    }

    private List<GnneAction> BuildSchedule(Call pad, TiledGlb glb, TIR.Buffer ddrIf, TIR.Buffer ddrOf)
    {
        int[] paddings = ((TensorConst)pad[GNNEPad.Pads]).Value.ToArray<int>();
        var paddingN = new Padding(paddings[0], paddings[1]);
        var paddingC = new Padding(paddings[2], paddings[3]);
        var paddingH = new Padding(paddings[4], paddings[5]);
        var paddingW = new Padding(paddings[6], paddings[7]);
        var l2Datatype = pad.CheckedDataType;
        int padValue = l2Datatype switch
        {
            { } type when type == DataTypes.UInt8 => ((TensorConst)pad[GNNEPad.Value]).Value.ToScalar<byte>(),
            { } type when type == DataTypes.Int8 => ((TensorConst)pad[GNNEPad.Value]).Value.ToScalar<sbyte>(),
            { } type when type == DataTypes.Int16 => ((TensorConst)pad[GNNEPad.Value]).Value.ToScalar<short>(),
            { } type when type == DataTypes.Float16 => BitConverter.ToInt16(
                BitConverter.GetBytes(((TensorConst)pad[GNNEPad.Value]).Value.ToScalar<Half>()), 0),
            _ => throw new ArgumentOutOfRangeException(l2Datatype.GetDisplayName()),
        };

        List<GnneAction> actions = new();
        var gpr = new GprHandler(GNNEEnv.GprNum);
        var ssr = new SsrHandler(GNNEEnv.SsrNum);
        var ccrHandler = new CcrHandler();
        GnneActionUpdater actionUpdater = new(actions, glb, ccrHandler, gpr, ssr);

        // mmu conf
        actionUpdater.UpdateMmuConf();

        var outputBatchSeg = GetSegmentStartEndLength(0, glb.LastOutShape[0], _outputShape![0]);
        foreach (var glbOutputBatch in outputBatchSeg)
        {
            var glbInputBatch = GetInputRowSegment(glbOutputBatch.Start, glbOutputBatch.Length, _inputShape![0], 1, 1, 1, paddingN);
            var outputChanSeg = GetSegmentStartEndLength(0, glb.LastOutShape[1], _outputShape[1]);
            foreach (var glbOutputChannel in outputChanSeg)
            {
                var glbInputChannel = GetInputRowSegment(glbOutputChannel.Start, glbOutputChannel.Length, _inputShape[1], 1, 1, 1, paddingC);
                var outputRowSeg = GetSegmentStartEndLength(0, glb.LastOutShape[2], _outputShape[2]);
                foreach (var glbOutputRow in outputRowSeg)
                {
                    var glbInputRow = GetInputRowSegment(glbOutputRow.Start, glbOutputRow.Length, _inputShape[2], 1, 1, 1, paddingH);
                    var outputColSeg = GetSegmentStartEndLength(0, glb.LastOutShape[3], _outputShape[3]);
                    foreach (var glbOutputColumn in outputColSeg)
                    {
                        var glbInputColumn = GetInputRowSegment(glbOutputColumn.Start, glbOutputColumn.Length, _inputShape[3], 1, 1, 1, paddingW);
                        SegmentND ofmap = new(glbOutputBatch, glbOutputChannel, glbOutputRow, glbOutputColumn);
                        SegmentND ifmap = new(glbInputBatch, glbInputChannel, glbInputRow, glbInputColumn);

                        int mPpSplitSize = (int)Math.Ceiling((float)glbOutputChannel.Length / GNNEEnv.NPingPongSplit);
                        var outChanSplit = GetSegmentStartEndLength(glbOutputChannel.Start, mPpSplitSize, glbOutputChannel.End);

                        for (int iPp = 0; iPp < outChanSplit.Count; iPp++)
                        {
                            var mPpSplit = outChanSplit[iPp];
                            var cPpSplit = GetInputRowSegment(mPpSplit.Start, mPpSplit.Length, _inputShape[1], 1, 1, 1, paddingC);

                            SegmentND ifmapPp = new(ifmap[0], cPpSplit, ifmap[2], ifmap[3]);
                            SegmentND ofmapPp = new(ofmap[0], mPpSplit, ofmap[2], ofmap[3]);

                            // memset
                            actionUpdater.UpdateMfuMemset(ofmapPp, ofmapPp, padValue, _inputType!, glb.GlbMap[ItemName.Ofmap].Mmu.Id, iPp);

                            // load ifmap to cover pad_value
                            List<CcrSet> ccrSetIfmap = new()
                            {
                                new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ofmap, iPp)), 1),
                            };
                            int offset = (glbInputBatch.Padding.Before * ofmapPp[1].Length * ofmapPp[2].Length * ofmapPp[3].Length) + (cPpSplit.Padding.Before * ofmapPp[2].Length * ofmapPp[3].Length) + (glbInputRow.Padding.Before * ofmapPp[3].Length) + glbInputColumn.Padding.Before;
                            offset *= GetBytesPerElement(type: _inputType!);
                            actionUpdater.UpdateLoadIf(ifmapPp, _lif!, iPp, ddrIf, offset, new() { ofmap[1].Length, ofmap[2].Length, ofmap[3].Length }, ItemName.Ofmap, ccrSetIfmap);

                            // store
                            List<CcrClr> ccr_clr_ofmap = new()
                            {
                                new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ofmap, iPp))),
                            };
                            actionUpdater.UpdateStoreT(ofmapPp, _sof!, iPp, ddrOf, 0, null!, null!, ccr_clr_ofmap);
                        }
                    }
                }
            }
        }

        Assert(ccrHandler.CcrSanityCheck());

        return actions;
    }
}

internal class TilePadGlb : TiledGlb
{
    private TensorOnGlb _ifGlb;
    private TensorOnGlb _ofGlb;

    public TilePadGlb(Dictionary<ItemName, TensorOnGlb> glbMap, Dictionary<ItemName, MmuItem> items, int[] lastOutShape, int nPingPongSplit, TensorOnGlb ifGlb, TensorOnGlb ofGlb)
        : base(glbMap, items, lastOutShape, nPingPongSplit)
    {
        _ifGlb = ifGlb;
        _ofGlb = ofGlb;
    }
}
