﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using NetFabric.Hyperlinq;
using Nncase.IR;
using Nncase.IR.Buffers;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using Nncase.Schedule;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using static Nncase.Passes.Rules.K230.TileUtilities;
using static Nncase.PatternMatch.Utility;
using Buffer = Nncase.TIR.Buffer;
using tensor_ccr_stat = System.Collections.Generic.List<System.Tuple<Nncase.TIR.SegmentND, Nncase.Passes.Rules.K230.TensorStat>>;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class TileConv2D : RewriteRule<Pattern>
{
    private static int _count = -1;

    // record tensor info to control ccr
    private readonly tensor_ccr_stat[] _weightRec = new tensor_ccr_stat[2];
    private readonly tensor_ccr_stat[] _ofmapRec = new tensor_ccr_stat[2];
    private readonly tensor_ccr_stat[] _g2LIfRec = new tensor_ccr_stat[2];
    private readonly tensor_ccr_stat[] _g2RWRec = new tensor_ccr_stat[2];
    private readonly tensor_ccr_stat[] _l2GOfRec = new tensor_ccr_stat[2];
    private readonly tensor_ccr_stat[] _l2RIf2Rec = new tensor_ccr_stat[2];
    private readonly tensor_ccr_stat[] _g2RWSliceRec = new tensor_ccr_stat[2];

    private readonly Dictionary<IVar, Buffer> _ifBufferMap = new(ReferenceEqualityComparer.Instance);

    private readonly List<WeightGroupHandler> _weightGroups = new(2); // if_first & w_first specialized

    private CcrHandler _ccrHandler = new();
    private GprHandler _gpr = new();
    private SsrHandler _ssr = new();

    private GlbSearchStrategy _strategy;
    private DataType? _inputType;
    private DataType? _outputType;
    private DataType? _weightType;

    // private DataType? _psumType;
    private Call? _conv;
    private Call? _pool;
    private Call? _dw;
    private Call? _act1;

    private int _icPerGroup;
    private int _ocPerGroup;
    private int _groupPerPass;

    private Call? _lif;
    private Call? _lif2;
    private Call? _lw;
    private Call? _lact;
    private Call? _lwQarg;
    private Call? _sof;

    private bool _weightPreloadEn;
    private bool _h2C;

    private GNNEShape? _inputShape;
    private GNNEShape? _outputShape;
    private GNNEShape? _convOutputShape;
    private GNNEShape? _weightsShape;
    private Padding? _paddingH;
    private Padding? _paddingW;
    private int _strideH;
    private int _strideW;
    private int _dilationH;
    private int _dilationW;
    private int _groups;

    private int _fusedKernelH;
    private int _fusedKernelW;
    private Padding? _fusedPaddingH;
    private Padding? _fusedPaddingW;
    private int _fusedStrideH;
    private int _fusedStrideW;
    private int _fusedDilationH;
    private int _fusedDilationW;

    private Tuple<int, int>? _weightSplitPattern;

    /// <inheritdoc/>
    public override OrPattern Pattern { get; } = IsAlt(
        FusionPattern.IsL1Pdp0DwFusion(),
        FusionPattern.IsL1Pdp0ReduceFusion(),
        FusionPattern.IsL1Act1Fusion(),
        FusionPattern.IsGNNEConvFusion());

    private PrimFunction GetReplace(Call call, Call ld, Call st, Fusion fusion)
    {
        _count++;

        InitParameters(call, ld, st);
        var tiledGlb = SearchGlbParameters();
        long[] inShape = ld.Arguments[0].CheckedShape.ToValueArray();
        T.CreateBuffer(new(ld[GNNELoad.Input].CheckedDataType, inShape), MemoryLocation.Input, out var ddrIf);
        List<TIR.Buffer> buffers = new() { ddrIf };
        _ifBufferMap.Add((Var)ld[GNNELoad.Input], ddrIf);
        T.CreateBuffer(new(st.CheckedDataType, st.CheckedShape), MemoryLocation.Output, out var ddrOf);

        byte[] oldWeights = ((TensorConst)((Call)call[GNNEConv2D.Weights])[GNNELoadW.Input]).Value.BytesBuffer.ToArray();
        byte[] newWeights = new byte[oldWeights.Length];
        Array.Copy(oldWeights, newWeights, newWeights.Length);
        T.AttachBuffer(Const.FromTensor(Tensor.FromBytes(DataTypes.UInt8, newWeights.ToArray(), new long[] { newWeights.Length })), out var ddrW);

        T.AttachBuffer((TensorConst)((Call)call[GNNEConv2D.WeightsBias])[GNNELoadW.Input], out var ddrWQarg);
        T.AttachBuffer((TensorConst)((Call)call[GNNEConv2D.Act])[GNNELoadW.Input], out var ddrAct);
        TIR.Buffer? ddrDW = null, ddrDWQarg = null, ddrDWAct = null, ddrAct1 = null, ddrPdpAct = null, ddrIf2 = null;
        if (_dw is not null)
        {
            byte[] oldDw = ((TensorConst)((Call)_dw[GNNEPdp0DW.Weights])[GNNELoadW.Input]).Value.BytesBuffer.ToArray();
            byte[] newDw = new byte[oldDw.Length];
            Array.Copy(oldDw, newDw, newDw.Length);
            T.AttachBuffer(Const.FromTensor(Tensor.FromBytes(DataTypes.UInt8, newDw.ToArray(), new long[] { newDw.Length })), out ddrDW);

            T.AttachBuffer((TensorConst)((Call)_dw[GNNEPdp0DW.WeightsBias])[GNNELoadW.Input], out ddrDWQarg);
            T.AttachBuffer((TensorConst)((Call)_dw[GNNEPdp0DW.Act])[GNNELoadW.Input], out ddrDWAct);
        }

        if (_pool is not null)
        {
            T.AttachBuffer((TensorConst)((Call)_pool[GNNEPdp0Reduce.Act])[GNNELoadW.Input], out ddrPdpAct);
        }

        if (_act1 is not null)
        {
            T.AttachBuffer((TensorConst)((Call)_act1[GNNEActivation.Act])[GNNELoadW.Input], out ddrAct1);
            if (_lif2 is not null)
            {
                if (_lif2[GNNELoad.Input] is not TensorConst)
                {
                    ddrIf2 = ddrIf;
                    if (_lif2[GNNELoad.Input] != _lif![GNNELoad.Input])
                    {
                        T.CreateBuffer(new(_lif2[GNNELoad.Input].CheckedDataType, _lif2.CheckedShape), MemoryLocation.Input, out ddrIf2);
                        buffers.Add(ddrIf2);
                        _ifBufferMap.Add((Var)_lif2[GNNELoad.Input], ddrIf2);
                    }
                }
                else
                {
                    T.AttachBuffer((TensorConst)_lif2[GNNELoad.Input], out ddrIf2);
                }
            }
        }

        buffers = fusion.Parameters.AsValueEnumerable().Select(v => _ifBufferMap[v]).ToList();

        buffers.Add(ddrOf);

        ItemRecStatusInit();
        BuildIfFirstSchedule(tiledGlb, call, ld, st, ddrIf, ddrOf, ddrW, ddrWQarg, ddrAct, ddrDW!, ddrDWQarg!, ddrDWAct!, ddrAct1!, ddrPdpAct!, ddrIf2!, true, _weightGroups[(int)ScheduleStrategy.IfFirstSchedule]);
        var actionsIfFirst = BuildIfFirstSchedule(tiledGlb, call, ld, st, ddrIf, ddrOf, ddrW, ddrWQarg, ddrAct, ddrDW!, ddrDWQarg!, ddrDWAct!, ddrAct1!, ddrPdpAct!, ddrIf2!, false, _weightGroups[(int)ScheduleStrategy.IfFirstSchedule]);

        ItemRecStatusInit();
        BuildWFirstSchedule(tiledGlb, call, ld, st, ddrIf, ddrOf, ddrW, ddrWQarg, ddrAct, ddrDW!, ddrDWQarg!, ddrDWAct!, ddrAct1!, ddrPdpAct!, ddrIf2!, true, _weightGroups[(int)ScheduleStrategy.WFirstSchedule]);
        var actionsWFirst = BuildWFirstSchedule(tiledGlb, call, ld, st, ddrIf, ddrOf, ddrW, ddrWQarg, ddrAct, ddrDW!, ddrDWQarg!, ddrDWAct!, ddrAct1!, ddrPdpAct!, ddrIf2!, false, _weightGroups[(int)ScheduleStrategy.WFirstSchedule]);

        var scheduleStrategy = DetermineScheduleStrategy(actionsIfFirst, actionsWFirst);
        var actions = scheduleStrategy == ScheduleStrategy.IfFirstSchedule ? actionsIfFirst : actionsWFirst;

        var oldWeighst = ddrW.Const().Value.BytesBuffer;
        ArrangeWeights(_weightType!, _weightsShape!, oldWeighst, _weightGroups[(int)scheduleStrategy]);
        if (_dw is not null)
        {
            ArrangeDwWeights(_dw[GNNEPdp0DW.Weights].CheckedDataType, _dw[GNNEPdp0DW.Weights].CheckedShape.ToValueArray(), ddrDW!.Const().Value.BytesBuffer, _weightGroups[(int)scheduleStrategy]);
        }

        var actionConverter = new ActionToInstruct();
        return T.PrimFunc($"TileConv2d_{_count}", Runtime.K230.K230RtModule.Kind, buffers.Select(b => new Var(b.CheckedType)).ToArray()).Body(
            actionConverter.Instructions(actions),
            I.END(GP_REGISTER.x0, 0))
        .Build();
    }

    private List<GnneAction> BuildIfFirstSchedule(TiledGlb glb, Call convCall, Call ld, Call st, TIR.Buffer ddrIf, TIR.Buffer ddrOf, TIR.Buffer ddrW, TIR.Buffer ddrWQarg, TIR.Buffer ddrAct, TIR.Buffer ddrDW, TIR.Buffer ddrDWQarg, TIR.Buffer ddrDWAct, TIR.Buffer ddrAct1, TIR.Buffer ddrPdpAct, TIR.Buffer ddrIf2, bool weightGroupOnly, WeightGroupHandler weightGroup)
    {
        List<GnneAction> actions = new();
        {
            _gpr = new(GNNEEnv.GprNum);
            _ssr = new(GNNEEnv.SsrNum);
            _ccrHandler = new CcrHandler();
        }

        GnneActionUpdater actionUpdater = new(actions, glb, _ccrHandler, _gpr, _ssr);

        // ccr set/clr record
        if (!weightGroupOnly)
        {
            UpdateCcrRecStat();
        }

        // mmu conf
        if (!weightGroupOnly)
        {
            actionUpdater.UpdateMmuConf();
        }

        int ofPp = 0;
        int iPp = -1;
        int wPp = -1;
        int ofBufNum = GNNEEnv.NPingPongSplit;

        var outputBatchSeg = GetSegmentStartEndLength(0, glb.LastOutShape[0], _outputShape![0]);
        var outputRowSeg = GetSegmentStartEndLength(0, glb.LastOutShape[2], _outputShape[2]);
        var outputColSeg = GetSegmentStartEndLength(0, glb.LastOutShape[3], _outputShape[3]);
        List<Segment1D> outChanSeg = new();
        List<Segment1D> inChanSeg = new();
        if (glb.LastOutShape[1] >= _ocPerGroup)
        {
            outChanSeg = GetSegmentStartEndLength(0, glb.LastOutShape[1], _outputShape[1]);
            inChanSeg = GetSegmentStartEndLength(0, glb.GlbMap[ItemName.Ifmap].Dimensions[1], _inputShape![1]);
        }
        else
        {
            for (int g = 0; g < _groups; g++)
            {
                var outputChanSegTmp = GetSegmentStartEndLength(g * _ocPerGroup, glb.LastOutShape[1], (g + 1) * _ocPerGroup);
                var inputChanSegTmp = GetSegmentStartEndLength(g * _icPerGroup, glb.GlbMap[ItemName.Ifmap].Dimensions[1], (g + 1) * _icPerGroup);
                outChanSeg.AddRange(outputChanSegTmp);
                inChanSeg.AddRange(inputChanSegTmp);
            }
        }

        if (!weightGroupOnly)
        {
            // weights bias
            if (_weightType == DataTypes.UInt8 || _weightType == DataTypes.Int16)
            {
                actionUpdater.UpdateLoadWQarg(_lwQarg!, weightGroup, ddrWQarg);
            }

            // act param
            actionUpdater.UpdateLoadAct(_lact!, ddrAct);

            if (_dw is not null)
            {
                // load dw weights
                // load dw w qarg
                actionUpdater.UpdateLoadDw(_dw, weightGroup, ddrDW);
                if (_dw[GNNEPdp0DW.Weights].CheckedDataType == DataTypes.UInt8)
                {
                    actionUpdater.UpdateLoadDwQarg(_dw, weightGroup, ddrDWQarg);
                }
            }

            if (_act1 is not null)
            {
                // load act1
                actionUpdater.UpdateLoadAct((Call)_act1[GNNEActivation.Act], ddrAct1, ItemName.MfuAct1);
            }

            // load act0
            if (_dw is not null)
            {
                actionUpdater.UpdateLoadAct((Call)_dw[GNNEPdp0DW.Act], ddrDWAct, ItemName.DwAct1);
            }
            else if (_pool is not null)
            {
                actionUpdater.UpdateLoadAct((Call)_pool[GNNEPdp0Reduce.Act], ddrPdpAct, ItemName.PdpAct1);
            }
        }

        Segment1D none = new(0..0, new(0, 0));
        var pingPongIf = new List<SegmentND> { new(none, none, none, none), new(none, none, none, none) };
        var pingPongW = new List<SegmentND> { new(none, none, none, none), new(none, none, none, none) };
        int l1Pp = 0;
        _weightSplitPattern = new(0, 0);
        foreach (var glbOutputBatch in outputBatchSeg)
        {
            foreach (var glbOutputRow in outputRowSeg)
            {
                var convGlbOutputRow = GetInputRowSegment(glbOutputRow.Start, glbOutputRow.Length, _convOutputShape![2], _fusedKernelH, _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
                var glbInputRow = GetInputRowSegment(convGlbOutputRow.Start, convGlbOutputRow.Length, _inputShape![2], _weightsShape![2], _strideH, _dilationH, _paddingH!);
                foreach (var glbOutputColumn in outputColSeg)
                {
                    var convGlbOutputCol = GetInputColumnSegment(glbOutputColumn.Start, glbOutputColumn.Length, _convOutputShape[3], _fusedKernelW, _fusedStrideW, _fusedDilationW, _fusedPaddingW!);
                    var glbInputColumn = GetInputColumnSegment(convGlbOutputCol.Start, convGlbOutputCol.Length, _inputShape[3], _weightsShape[3], _strideW, _dilationW, _paddingW!);
                    foreach (var glbOutputChannel in outChanSeg)
                    {
                        SegmentND ofmap = new(glbOutputBatch, glbOutputChannel, glbOutputRow, glbOutputColumn);
                        int cs0 = glbOutputChannel.Start / _ocPerGroup * _icPerGroup;
                        int ce0 = ((glbOutputChannel.End - 1) / _ocPerGroup * _icPerGroup) + _icPerGroup;

                        foreach (var glbInputChannel in inChanSeg)
                        {
                            if (glbInputChannel.Start < cs0 || glbInputChannel.End > ce0)
                            {
                                continue;
                            }

                            int cs = glbInputChannel.Start % _icPerGroup;
                            int ce = Math.Min(cs + glbInputChannel.Length, _icPerGroup);
                            SegmentND weight = new(glbOutputChannel, new(cs..ce, new(0, 0)), new(0.._weightsShape[2], new(0, 0)), new(0.._weightsShape[3], new(0, 0)));

                            if (pingPongW[0] == weight)
                            {
                                wPp = 0;
                            }
                            else if (pingPongW[1] == weight)
                            {
                                wPp = 1;
                            }
                            else
                            {
                                wPp = (wPp + 1) % 2;

                                weightGroup.Current_aligned_offset_init();
                                if (weightGroupOnly)
                                {
                                    _weightRec[wPp].Add(new(weight, new(false, false)));
                                }

                                pingPongW[wPp] = weight;
                            }

                            SegmentND ifmap = new(glbOutputBatch, glbInputChannel, glbInputRow, glbInputColumn);

                            if (pingPongIf[0] == ifmap)
                            {
                                iPp = 0;
                            }
                            else if (pingPongIf[1] == ifmap)
                            {
                                iPp = 1;
                            }
                            else
                            {
                                iPp = (iPp + 1) % 2;
                                if (!weightGroupOnly)
                                {
                                    if (_g2LIfRec[iPp].Count > 0 && _g2LIfRec[iPp][0].Item1 == ifmap)
                                    {
                                        int ccrSetNum = _g2LIfRec[iPp][0].Item2 is { IsFirstSlice: true, IsLastSlice: true } ? 1 : 2;
                                        List<CcrSet> ccrSetIfmap = new() { new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ifmap, iPp)), ccrSetNum) };
                                        List<int> stridesD = new() { glb.GlbMap[ItemName.Ifmap].Dimensions[1], glb.GlbMap[ItemName.Ifmap].Dimensions[2], glb.GlbMap[ItemName.Ifmap].Dimensions[3] };
                                        actionUpdater.UpdateLoadIf(ifmap, _lif!, iPp, ddrIf, 0, stridesD, ItemName.Ifmap, ccrSetIfmap, null!, _h2C, ((TensorConst)convCall[GNNEConv2D.DeqBias]).Value.ToScalar<int>());
                                    }
                                }

                                pingPongIf[iPp] = ifmap;
                            }

                            var ifmap2 = ofmap;
                            if (!weightGroupOnly)
                            {
                                bool isDepthwise = _inputShape[1] == _outputShape[1] && _outputShape[1] == _groups && _groups != 1;
                                if (_act1 is not null && _act1[GNNEActivation.InputB] != None.Default && (glbInputChannel.End == _inputShape[1] || isDepthwise))
                                {
                                    int ccrSetNum = _l2RIf2Rec[ofPp][0].Item2 is { IsFirstSlice: true, IsLastSlice: true } ? 1 : 2;
                                    List<CcrSet> ccrSetIfmap2 = new() { new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ifmap2, ofPp)), ccrSetNum) };
                                    List<int> stridesD = new() { glb.GlbMap[ItemName.Ifmap2].Dimensions[1], glb.GlbMap[ItemName.Ifmap2].Dimensions[2], glb.GlbMap[ItemName.Ifmap2].Dimensions[3] };
                                    actionUpdater.UpdateLoadIf(ifmap2, _lif2!, ofPp, ddrIf2, 0, stridesD, ItemName.Ifmap2, ccrSetIfmap2);
                                }
                            }

                            // l1 schedule
                            BuildL1Schedule(actionUpdater, glb, ifmap, weight, ofmap, iPp, ofPp, wPp, ifmap2, weightGroupOnly, weightGroup, ref l1Pp, ddrW);
                        }

                        if (!weightGroupOnly)
                        {
                            int ofmapSetCnt = _ofmapRec[ofPp][0].Item2.IsLastSlice ? 0 : 1;
                            _ofmapRec[ofPp].RemoveAt(0);
                            List<CcrSet> ccrSetOfmap = new();
                            if (ofmapSetCnt > 0)
                            {
                                ccrSetOfmap.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.OfmapFake, ofPp)), 1));
                            }

                            List<CcrClr> ccrClrOfmap = new() { new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, ofPp))) };
                            actionUpdater.UpdateStoreT(ofmap, _sof!, ofPp, ddrOf, 0, null!, ccrSetOfmap, ccrClrOfmap);
                        }
                        else
                        {
                            _ofmapRec[ofPp].Add(new(ofmap, new(false, false)));
                        }

                        ofPp = (ofPp + 1) % ofBufNum;
                    }
                }
            }
        }

        // TODO: remove preload_w since it is only for test
        // if (weight_preload_en && weight_group_only)
        // {
        //     assert(weight_pp_count <= 2);
        //     gpr_ = new(GNNEEnv.gpr_num);
        //     ssr_ = new(GNNEEnv.ssr_num);
        //     ccr_handler_ = new ccr_handler();
        //     action_updater.update_mmu_conf();
        //     int pos_w = weight_type == DataTypes.Int16 ? 2 : 1;
        //     for (var p_w = 0; p_w < pos_w; p_w++)
        //     {
        //         if (ping_pong_w[0] != new SegmentND(none, none, none, none))
        //         {
        //             action_updater.update_preload_w(ping_pong_w[0], lw, weight_group, 0, 0, item_name.weight, h2c, p_w);
        //         }
        //
        //         if (ping_pong_w[1] != new SegmentND(none, none, none, none))
        //         {
        //             action_updater.update_preload_w(ping_pong_w[1], lw, weight_group, 1, 0, item_name.weight, h2c, p_w);
        //         }
        //     }
        // }

        // pe_usage(&pe_usage_param);
        Assert(_ccrHandler.CcrSanityCheck());

        return actions;
    }

    private List<GnneAction> BuildWFirstSchedule(TiledGlb glb, Call convCall, Call ld, Call st, TIR.Buffer ddrIf, TIR.Buffer ddrOf, TIR.Buffer ddrW, TIR.Buffer ddrWQarg, TIR.Buffer ddrAct, TIR.Buffer ddrDW, TIR.Buffer ddrDWQarg, TIR.Buffer ddrDWAct, TIR.Buffer ddrAct1, TIR.Buffer ddrPdpAct, TIR.Buffer ddrIf2, bool weightGroupOnly, WeightGroupHandler weightGroup)
    {
        List<GnneAction> actions = new();
        {
            _gpr = new(GNNEEnv.GprNum);
            _ssr = new(GNNEEnv.SsrNum);
            _ccrHandler = new CcrHandler();
        }

        GnneActionUpdater actionUpdater = new(actions, glb, _ccrHandler, _gpr, _ssr);

        // ccr set/clr record
        if (!weightGroupOnly)
        {
            UpdateCcrRecStat();
        }

        // mmu conf
        if (!weightGroupOnly)
        {
            actionUpdater.UpdateMmuConf();
        }

        int ofPp = 0;
        int iPp = -1;
        int wPp = -1;
        int ofBufNum = GNNEEnv.NPingPongSplit;

        var outputBatchSeg = GetSegmentStartEndLength(0, glb.LastOutShape[0], _outputShape![0]);
        var outputRowSeg = GetSegmentStartEndLength(0, glb.LastOutShape[2], _outputShape[2]);
        var outputColSeg = GetSegmentStartEndLength(0, glb.LastOutShape[3], _outputShape[3]);
        List<Segment1D> outChanSeg = new();
        List<Segment1D> inChanSeg = new();
        if (glb.LastOutShape[1] >= _ocPerGroup)
        {
            outChanSeg = GetSegmentStartEndLength(0, glb.LastOutShape[1], _outputShape[1]);
            inChanSeg = GetSegmentStartEndLength(0, glb.GlbMap[ItemName.Ifmap].Dimensions[1], _inputShape![1]);
        }
        else
        {
            for (int g = 0; g < _groups; g++)
            {
                var outputChanSegTmp = GetSegmentStartEndLength(g * _ocPerGroup, glb.LastOutShape[1], (g + 1) * _ocPerGroup);
                var inputChanSegTmp = GetSegmentStartEndLength(g * _icPerGroup, glb.GlbMap[ItemName.Ifmap].Dimensions[1], (g + 1) * _icPerGroup);
                outChanSeg.AddRange(outputChanSegTmp);
                inChanSeg.AddRange(inputChanSegTmp);
            }
        }

        if (!weightGroupOnly)
        {
            // weights bias
            if (_weightType == DataTypes.UInt8 || _weightType == DataTypes.Int16)
            {
                actionUpdater.UpdateLoadWQarg(_lwQarg!, weightGroup, ddrWQarg);
            }

            // act param
            actionUpdater.UpdateLoadAct(_lact!, ddrAct);

            if (_dw is not null)
            {
                // load dw weights
                // load dw w qarg
                actionUpdater.UpdateLoadDw(_dw, weightGroup, ddrDW);
                if (_dw[GNNEPdp0DW.Weights].CheckedDataType == DataTypes.UInt8)
                {
                    actionUpdater.UpdateLoadDwQarg(_dw, weightGroup, ddrDWQarg);
                }
            }

            if (_act1 is not null)
            {
                // load act1
                actionUpdater.UpdateLoadAct((Call)_act1[GNNEActivation.Act], ddrAct1, ItemName.MfuAct1);
            }

            // load act0
            if (_dw is not null)
            {
                actionUpdater.UpdateLoadAct((Call)_dw[GNNEPdp0DW.Act], ddrDWAct, ItemName.DwAct1);
            }
            else if (_pool is not null)
            {
                actionUpdater.UpdateLoadAct((Call)_pool[GNNEPdp0Reduce.Act], ddrPdpAct, ItemName.PdpAct1);
            }
        }

        Segment1D none = new(0..0, new(0, 0));
        var pingPongIf = new List<SegmentND> { new(none, none, none, none), new(none, none, none, none) };
        var pingPongW = new List<SegmentND> { new(none, none, none, none), new(none, none, none, none) };
        int l1Pp = 0;
        _weightSplitPattern = new(0, 0);
        foreach (var glbOutputChannel in outChanSeg)
        {
            int cs0 = glbOutputChannel.Start / _ocPerGroup * _icPerGroup;
            int ce0 = ((glbOutputChannel.End - 1) / _ocPerGroup * _icPerGroup) + _icPerGroup;
            foreach (var glbOutputBatch in outputBatchSeg)
            {
                foreach (var glbOutputRow in outputRowSeg)
                {
                    var convGlbOutputRow = GetInputRowSegment(glbOutputRow.Start, glbOutputRow.Length, _convOutputShape![2], _fusedKernelH, _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
                    var glbInputRow = GetInputRowSegment(convGlbOutputRow.Start, convGlbOutputRow.Length, _inputShape![2], _weightsShape![2], _strideH, _dilationH, _paddingH!);
                    foreach (var glbOutputColumn in outputColSeg)
                    {
                        var convGlbOutputCol = GetInputColumnSegment(glbOutputColumn.Start, glbOutputColumn.Length, _convOutputShape[3], _fusedKernelW, _fusedStrideW, _fusedDilationW, _fusedPaddingW!);
                        var glbInputColumn = GetInputColumnSegment(convGlbOutputCol.Start, convGlbOutputCol.Length, _inputShape[3], _weightsShape[3], _strideW, _dilationW, _paddingW!);
                        SegmentND ofmap = new(glbOutputBatch, glbOutputChannel, glbOutputRow, glbOutputColumn);

                        foreach (var glbInputChannel in inChanSeg)
                        {
                            if (glbInputChannel.Start < cs0 || glbInputChannel.End > ce0)
                            {
                                continue;
                            }

                            int cs = glbInputChannel.Start % _icPerGroup;
                            int ce = Math.Min(cs + glbInputChannel.Length, _icPerGroup);
                            SegmentND weight = new(glbOutputChannel, new(cs..ce, new(0, 0)), new(0.._weightsShape[2], new(0, 0)), new(0.._weightsShape[3], new(0, 0)));

                            if (pingPongW[0] == weight)
                            {
                                wPp = 0;
                            }
                            else if (pingPongW[1] == weight)
                            {
                                wPp = 1;
                            }
                            else
                            {
                                wPp = (wPp + 1) % 2;

                                weightGroup.Current_aligned_offset_init();
                                if (weightGroupOnly)
                                {
                                    _weightRec[wPp].Add(new(weight, new(false, false)));
                                }

                                pingPongW[wPp] = weight;
                            }

                            SegmentND ifmap = new(glbOutputBatch, glbInputChannel, glbInputRow, glbInputColumn);

                            if (pingPongIf[0] == ifmap)
                            {
                                iPp = 0;
                            }
                            else if (pingPongIf[1] == ifmap)
                            {
                                iPp = 1;
                            }
                            else
                            {
                                iPp = (iPp + 1) % 2;
                                if (!weightGroupOnly)
                                {
                                    if (_g2LIfRec[iPp].Count > 0 && _g2LIfRec[iPp][0].Item1 == ifmap)
                                    {
                                        int ccrSetNum = _g2LIfRec[iPp][0].Item2 is { IsFirstSlice: true, IsLastSlice: true } ? 1 : 2;
                                        List<CcrSet> ccrSetIfmap = new() { new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ifmap, iPp)), ccrSetNum) };
                                        List<int> stridesD = new() { glb.GlbMap[ItemName.Ifmap].Dimensions[1], glb.GlbMap[ItemName.Ifmap].Dimensions[2], glb.GlbMap[ItemName.Ifmap].Dimensions[3] };
                                        actionUpdater.UpdateLoadIf(ifmap, _lif!, iPp, ddrIf, 0, stridesD, ItemName.Ifmap, ccrSetIfmap, null!, _h2C, ((TensorConst)convCall[GNNEConv2D.DeqBias]).Value.ToScalar<int>());
                                    }
                                }

                                pingPongIf[iPp] = ifmap;
                            }

                            var ifmap2 = ofmap;
                            if (!weightGroupOnly)
                            {
                                bool isDepthwise = _inputShape[1] == _outputShape[1] && _outputShape[1] == _groups && _groups != 1;
                                if (_act1 is not null && (_act1[GNNEActivation.InputB] != Nncase.IR.None.Default) && (glbInputChannel.End == _inputShape[1] || isDepthwise))
                                {
                                    int ccrSetNum = _l2RIf2Rec[ofPp][0].Item2 is { IsFirstSlice: true, IsLastSlice: true } ? 1 : 2;
                                    List<CcrSet> ccrSetIfmap2 = new() { new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ifmap2, ofPp)), ccrSetNum) };
                                    List<int> stridesD = new() { glb.GlbMap[ItemName.Ifmap2].Dimensions[1], glb.GlbMap[ItemName.Ifmap2].Dimensions[2], glb.GlbMap[ItemName.Ifmap2].Dimensions[3] };
                                    actionUpdater.UpdateLoadIf(ifmap2, _lif2!, ofPp, ddrIf2, 0, stridesD, ItemName.Ifmap2, ccrSetIfmap2, null!);
                                }
                            }

                            // l1 schedule
                            BuildL1Schedule(actionUpdater, glb, ifmap, weight, ofmap, iPp, ofPp, wPp, ifmap2, weightGroupOnly, weightGroup, ref l1Pp, ddrW);
                        }

                        if (!weightGroupOnly)
                        {
                            int ofmapSetCnt = _ofmapRec[ofPp][0].Item2.IsLastSlice ? 0 : 1;
                            _ofmapRec[ofPp].RemoveAt(0);
                            List<CcrSet> ccrSetOfmap = new();
                            if (ofmapSetCnt > 0)
                            {
                                ccrSetOfmap.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.OfmapFake, ofPp)), 1));
                            }

                            List<CcrClr> ccrClrOfmap = new() { new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, ofPp))) };
                            actionUpdater.UpdateStoreT(ofmap, _sof!, ofPp, ddrOf, 0, null!, ccrSetOfmap, ccrClrOfmap);
                        }
                        else
                        {
                            _ofmapRec[ofPp].Add(new(ofmap, new(false, false)));
                        }

                        ofPp = (ofPp + 1) % ofBufNum;
                    }
                }
            }
        }

        // TODO: remove preload_w since it is only for test
        // if (weight_preload_en && weight_group_only)
        // {
        //     assert(weight_pp_count <= 2);
        //     gpr_ = new(GNNEEnv.gpr_num);
        //     ssr_ = new(GNNEEnv.ssr_num);
        //     ccr_handler_ = new ccr_handler();
        //     action_updater.update_mmu_conf();
        //     int pos_w = weight_type == DataTypes.Int16 ? 2 : 1;
        //     for (var p_w = 0; p_w < pos_w; p_w++)
        //     {
        //         if (ping_pong_w[0] != new SegmentND(none, none, none, none))
        //         {
        //             action_updater.update_preload_w(ping_pong_w[0], lw, weight_group, 0, 0, item_name.weight, h2c, p_w);
        //         }
        //
        //         if (ping_pong_w[1] != new SegmentND(none, none, none, none))
        //         {
        //             action_updater.update_preload_w(ping_pong_w[1], lw, weight_group, 1, 0, item_name.weight, h2c, p_w);
        //         }
        //     }
        // }

        // pe_usage(&pe_usage_param);
        Assert(_ccrHandler.CcrSanityCheck());

        return actions;
    }

    private TileConv2DGlb SearchGlbParameters()
    {
        int n = 1;
        int c = 1;
        int r = _weightsShape![2];
        int s = _weightsShape[3];

        int e = 1;
        int f = Math.Min(GNNEEnv.PuWidth, _outputShape![3]);
        if (f < GNNEEnv.PuWidth)
        {
            e = Math.Min((int)Math.Ceiling(1.0 * GNNEEnv.PuWidth / f), _outputShape[2]);
        }

        int hConvOut = SpaceSearcher.GetInputHeight(e, _convOutputShape![2], _fusedKernelH, _outputShape[2], _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
        int h = SpaceSearcher.GetInputHeight(hConvOut, _inputShape![2], r, _convOutputShape[2], _strideH, _dilationH, _paddingH!);

        int wConvOut = SpaceSearcher.GetInputHeight(f, _convOutputShape[3], _fusedKernelW, _outputShape[3], _fusedStrideW, _fusedDilationW, _fusedPaddingW!);
        int w = SpaceSearcher.GetInputHeight(wConvOut, _inputShape[3], s, _convOutputShape[3], _strideW, _dilationW, _paddingW!);

        int m = 1;

        // pre allocate
        AllocateResult allocation;
        while (c < _groupPerPass * _icPerGroup)
        {
            allocation = HandleAllocate(n, c + 1, h, w, r, s, m, e, f);
            if (allocation.IsOk)
            {
                c += 1;
            }
            else
            {
                break;
            }
        }

        while (m < GNNEEnv.PuWidth && m < _groupPerPass * _ocPerGroup)
        {
            allocation = HandleAllocate(n, c, h, w, r, s, m + 1, e, f);
            if (allocation.IsOk)
            {
                m += 1;
            }
            else
            {
                break;
            }
        }

        // two strategies for now
        if (_strategy == GlbSearchStrategy.IfFirst)
        {
            if (_groupPerPass == 1)
            {
                while (m < _outputShape[1])
                {
                    if (r == _weightsShape[2] && s == _weightsShape[3] && c >= _weightsShape[1] && m >= _outputShape[1] / 2)
                    {
                        break;
                    }

                    int nextM = m + 1;
                    int nextC = c;
                    if (m >= _ocPerGroup)
                    {
                        nextM = m + _ocPerGroup;
                        nextC = c + _icPerGroup;
                    }

                    allocation = HandleAllocate(n, nextC, h, w, r, s, nextM, e, f);
                    if (allocation.IsOk)
                    {
                        m = nextM;
                        c = nextC;
                    }
                    else
                    {
                        break;
                    }
                }
            }
            else
            {
                while (m < _outputShape[1])
                {
                    if (r == _weightsShape[2] && s == _weightsShape[3] && c >= _weightsShape[1] && m >= _weightsShape[0] / 2)
                    {
                        break;
                    }

                    int nextM = Math.Min(m + (_groupPerPass * _ocPerGroup), _outputShape[1]);
                    int nextC = Math.Min(c + (_groupPerPass * _icPerGroup), _inputShape[1]);
                    allocation = HandleAllocate(n, nextC, h, w, r, s, nextM, e, f);
                    if (allocation.IsOk)
                    {
                        m = nextM;
                        c = nextC;
                    }
                    else
                    {
                        break;
                    }
                }
            }

            while (e < _outputShape[2])
            {
                int nextE = e + 1;
                int nextHConvOut = SpaceSearcher.GetInputHeight(nextE, _convOutputShape[2], _fusedKernelH, _outputShape[2], _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
                int nextH = SpaceSearcher.GetInputHeight(nextHConvOut, _inputShape[2], r, _convOutputShape[2], _strideH, _dilationH, _paddingH!);

                allocation = HandleAllocate(n, c, nextH, w, r, s, m, nextE, f);
                if (allocation.IsOk)
                {
                    e = nextE;
                    h = nextH;
                }
                else
                {
                    break;
                }
            }

            while (f < _outputShape[3])
            {
                int nextF = f + 1;
                int nextWConvOut = SpaceSearcher.GetInputHeight(nextF, _convOutputShape[3], _fusedKernelW, _outputShape[3], _fusedStrideW, _fusedDilationW, _fusedPaddingW!);
                int nextW = SpaceSearcher.GetInputHeight(nextWConvOut, _inputShape[3], s, _convOutputShape[3], _strideW, _dilationW, _paddingW!);

                allocation = HandleAllocate(n, c, h, nextW, r, s, m, e, nextF);
                if (allocation.IsOk)
                {
                    f = nextF;
                    w = nextW;
                }
                else
                {
                    break;
                }
            }

            while (n < _outputShape[0])
            {
                allocation = HandleAllocate(n + 1, c, h, w, r, s, m, e, f);
                if (allocation.IsOk)
                {
                    n += 1;
                }
                else
                {
                    break;
                }
            }
        }
        else if (_strategy == GlbSearchStrategy.WFirst)
        {
            while (e < _outputShape[2])
            {
                int nextE = e + 1;
                int nextHConvOut = SpaceSearcher.GetInputHeight(nextE, _convOutputShape[2], _fusedKernelH, _outputShape[2], _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
                int nextH = SpaceSearcher.GetInputHeight(nextHConvOut, _inputShape[2], r, _convOutputShape[2], _strideH, _dilationH, _paddingH!);

                allocation = HandleAllocate(n, c, nextH, w, r, s, m, nextE, f);
                if (allocation.IsOk)
                {
                    e = nextE;
                    h = nextH;
                }
                else
                {
                    break;
                }
            }

            while (f < _outputShape[3])
            {
                int nextF = f + 1;
                int nextWConvOut = SpaceSearcher.GetInputHeight(nextF, _convOutputShape[3], _fusedKernelW, _outputShape[3], _fusedStrideW, _fusedDilationW, _fusedPaddingW!);
                int nextW = SpaceSearcher.GetInputHeight(nextWConvOut, _inputShape[3], s, _convOutputShape[3], _strideW, _dilationW, _paddingW!);

                allocation = HandleAllocate(n, c, h, nextW, r, s, m, e, nextF);
                if (allocation.IsOk)
                {
                    f = nextF;
                    w = nextW;
                }
                else
                {
                    break;
                }
            }

            while (n < _outputShape[0])
            {
                if (c == _inputShape[1] && h == _inputShape[2] && w == _inputShape[3] && n >= _outputShape[0] / 2)
                {
                    break;
                }

                allocation = HandleAllocate(n + 1, c, h, w, r, s, m, e, f);
                if (allocation.IsOk)
                {
                    n += 1;
                }
                else
                {
                    break;
                }
            }

            if (_groupPerPass == 1)
            {
                while (m < _outputShape[1])
                {
                    int nextM = m + 1;
                    int nextC = c;
                    if (m >= _ocPerGroup)
                    {
                        nextM = m + _ocPerGroup;
                        nextC = c + _icPerGroup;
                    }

                    allocation = HandleAllocate(n, nextC, h, w, r, s, nextM, e, f);
                    if (allocation.IsOk)
                    {
                        m = nextM;
                        c = nextC;
                    }
                    else
                    {
                        break;
                    }
                }
            }
            else
            {
                while (m < _outputShape[1])
                {
                    int nextM = Math.Min(m + (_groupPerPass * _ocPerGroup), _outputShape[1]);
                    int nextC = Math.Min(c + (_groupPerPass * _icPerGroup), _inputShape[1]);
                    allocation = HandleAllocate(n, nextC, h, w, r, s, nextM, e, f);
                    if (allocation.IsOk)
                    {
                        m = nextM;
                        c = nextC;
                    }
                    else
                    {
                        break;
                    }
                }
            }
        }

        // m % GNNEEnv.pu_width() == 0
        if (m % GNNEEnv.PuWidth != 0 && m > GNNEEnv.PuWidth && m <= _ocPerGroup)
        {
            m = m / GNNEEnv.PuWidth * GNNEEnv.PuWidth;
        }

        allocation = HandleAllocate(n, c, h, w, r, s, m, e, f, true);
        Assert(allocation.IsOk);

        var lastOutShape = new GNNEShape(n, m, e, f);
        return new TileConv2DGlb(allocation.GlbMap, allocation.Items, lastOutShape.Dims, GNNEEnv.NPingPongSplit, allocation.GlbMap[ItemName.Ifmap], allocation.GlbMap[ItemName.Ofmap]);
    }

    private AllocateResult HandleAllocate(int n, int c, int h, int w, int r, int s, int m, int e, int f, bool isFinal = false)
    {
        var glbUsage = new List<float>() { 0, 0 };
        var boxes = GetBoxes(n, c, h, w, r, s, m, e, f, glbUsage);

        var bp = new BoxPacker(16) { Boxes = boxes.Boxes };

        var allocation = SpaceSearcher.TryAllocate(bp);
        allocation.GlbMap = boxes.GlbMap;

#if DEBUG
        if (isFinal && allocation.IsOk)
        {
            bp.DisplayFinalAllocation();
            Console.WriteLine("fusion type: conv2d/conv2d_pdp/conv2d_dw/conv2d_act1");
            Console.WriteLine($"GLB usage(tensor, mmu): {glbUsage[0]}\t{glbUsage[1]}");
            Console.WriteLine("--------------------------");
        }
#endif

        if (!allocation.IsOk)
        {
            return allocation;
        }

        foreach (var map in boxes.GlbMap.Where(map => allocation.Items.ContainsKey(map.Key)))
        {
            map.Value.Mmu = allocation.Items[map.Key];
        }

        return allocation;
    }

    private List<int> L1Search(TiledGlb glb, SegmentND weight, SegmentND psum)
    {
        bool StripeSearch()
        {
            bool ret = weight[3].Length == 1 && weight[2].Length == psum[2].Length && _strideH == 1 && _strideW == 1 && (((_pool is not null || _dw is not null || _act1 is not null) && psum[3].Length < 512) || psum[3].Length < 1024) && !(glb.GlbMap[ItemName.Ifmap].DType == DataTypes.Int16);
            return ret;
        }

        int c = Math.Min(GNNEEnv.PuHeight, _groupPerPass * _icPerGroup);
        int m = Math.Min(GNNEEnv.PuWidth, _groupPerPass * _ocPerGroup);
        int h = 1;
        int w = 1;
        int e = 1;
        int f = StripeSearch() ? psum[3].Length : 1;
        int fStep = StripeSearch() ? psum[3].Length : 1;
        int r = _weightSplitPattern!.Item1 == 0 || _dilationH > 3 ? 1 : _weightSplitPattern.Item1;
        int s = _weightSplitPattern.Item2 == 0 || _dilationW > 3 ? 1 : _weightSplitPattern.Item2;

        int hConvOut = SpaceSearcher.GetInputHeight(e, _convOutputShape![2], _fusedKernelH, _outputShape![2], _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
        h = SpaceSearcher.GetInputHeight(hConvOut, _inputShape![2] + _paddingH!.Sum(), r, _convOutputShape[2], _strideH, _dilationH, new(0, 0));
        int wConvOut = SpaceSearcher.GetInputHeight(f, _convOutputShape[3], _fusedKernelW, _outputShape[3], _fusedStrideW, _fusedDilationW, _fusedPaddingW!);
        w = SpaceSearcher.GetInputHeight(wConvOut, _inputShape[3] + _paddingW!.Sum(), s, _convOutputShape[3], _strideW, _dilationW, new(0, 0));

        int psumPingPangSplit = 1;
        if (_pool is not null || _dw is not null || _act1 is not null)
        {
            psumPingPangSplit = 2;
        }

        int ifBytesPerElementGlb = GetBytesPerElement(_inputType!);
        bool ok = HandleL1Allocate(h, w, Math.Max(e, hConvOut), Math.Max(f, wConvOut), psumPingPangSplit, ifBytesPerElementGlb);
        if (!ok)
        {
            throw new NotSupportedException("exceeds L1 size");
        }

        bool IncreaseEBy1()
        {
            int nextE = e + 1;
            int nextHConvOut = SpaceSearcher.GetInputHeight(nextE, _convOutputShape[2], _fusedKernelH, _outputShape[2], _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
            int nextH = SpaceSearcher.GetInputHeight(nextHConvOut, _inputShape[2] + _paddingH.Sum(), r, _convOutputShape[2], _strideH, _dilationH, new(0, 0));

            bool ok = HandleL1Allocate(nextH, w, Math.Max(nextE, nextHConvOut), Math.Max(f, wConvOut), psumPingPangSplit, ifBytesPerElementGlb);
            if (!ok)
            {
                return ok;
            }

            e = nextE;
            h = nextH;
            hConvOut = nextHConvOut;

            return ok;
        }

        bool IncreaseFBy1()
        {
            int nextF = f + fStep;
            int nextWConvOut = SpaceSearcher.GetInputHeight(nextF, _convOutputShape[3], _fusedKernelW, _outputShape[3], _fusedStrideW, _fusedDilationW, _fusedPaddingW!);
            int nextW = SpaceSearcher.GetInputHeight(nextWConvOut, _inputShape[3] + _paddingW.Sum(), s, _convOutputShape[3], _strideW, _dilationW, new(0, 0));

            bool ok = HandleL1Allocate(h, nextW, Math.Max(e, hConvOut), Math.Max(nextF, nextWConvOut), psumPingPangSplit, ifBytesPerElementGlb);
            if (!ok)
            {
                return ok;
            }

            f = nextF;
            w = nextW;
            wConvOut = nextWConvOut;

            return ok;
        }

        bool IncreaseRBy1()
        {
            int nextR = r + 1;
            int nextHConvOut = SpaceSearcher.GetInputHeight(e, _convOutputShape[2], _fusedKernelH, _outputShape[2], _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
            int nextH = SpaceSearcher.GetInputHeight(nextHConvOut, _inputShape[2] + _paddingH.Sum(), nextR, _convOutputShape[2], _strideH, _dilationH, new(0, 0));

            bool ok = HandleL1Allocate(nextH, w, Math.Max(e, nextHConvOut), Math.Max(f, wConvOut), psumPingPangSplit, ifBytesPerElementGlb);
            if (ok)
            {
                r = nextR;
                h = nextH;
                hConvOut = nextHConvOut;
            }

            return ok;
        }

        bool IncreaseSBy1()
        {
            int nextS = s + 1;
            int nextWConvOut = SpaceSearcher.GetInputHeight(f, _convOutputShape[3], _fusedKernelW, _outputShape[3], _fusedStrideW, _fusedDilationW, _fusedPaddingW!);
            int nextW = SpaceSearcher.GetInputHeight(nextWConvOut, _inputShape[3] + _paddingW.Sum(), nextS, _convOutputShape[3], _strideW, _dilationW, new(0, 0));

            bool ok = HandleL1Allocate(h, nextW, Math.Max(e, hConvOut), Math.Max(f, nextWConvOut), psumPingPangSplit, ifBytesPerElementGlb);
            if (ok)
            {
                s = nextS;
                w = nextW;
                wConvOut = nextWConvOut;
            }

            return ok;
        }

        // increase e&f, but reserve some space for the increase of r&s
        while (f < psum[3].Length && e < psum[2].Length && h * w * ifBytesPerElementGlb < GNNEEnv.IfL1Size / GNNEEnv.PuHeight / 2)
        {
            if (f <= e)
            {
                if (!IncreaseFBy1())
                {
                    break;
                }
            }
            else
            {
                if (!IncreaseEBy1())
                {
                    break;
                }
            }
        }

        while (f < psum[3].Length && h * w * ifBytesPerElementGlb < GNNEEnv.IfL1SizePerChan / 2)
        {
            if (!IncreaseFBy1())
            {
                break;
            }
        }

        while (e < psum[2].Length && h * w * ifBytesPerElementGlb < GNNEEnv.IfL1SizePerChan / 2)
        {
            if (!IncreaseEBy1())
            {
                break;
            }
        }

        if (_weightSplitPattern.Equals(new Tuple<int, int>(0, 0)))
        {
            // increase r&s to avoid the cutting of the kernel is too small
            while (s < weight[3].Length && r < weight[2].Length && s < 8 && r < 31 && _dilationW <= 3 && _dilationH <= 3)
            {
                if (s <= r)
                {
                    if (!IncreaseSBy1())
                    {
                        break;
                    }
                }
                else
                {
                    if (!IncreaseRBy1())
                    {
                        break;
                    }
                }
            }

            while (s < weight[3].Length && s < 8 && _dilationW <= 3)
            {
                if (!IncreaseSBy1())
                {
                    break;
                }
            }

            while (r < weight[2].Length && r < 31 && _dilationH <= 3)
            {
                if (!IncreaseRBy1())
                {
                    break;
                }
            }
        }

        // increase e&f as large as possible
        while (f < psum[3].Length && e < psum[2].Length)
        {
            if (f <= e)
            {
                if (!IncreaseFBy1())
                {
                    break;
                }
            }
            else
            {
                if (!IncreaseEBy1())
                {
                    break;
                }
            }
        }

        while (f < psum[3].Length)
        {
            if (!IncreaseFBy1())
            {
                break;
            }
        }

        while (e < psum[2].Length)
        {
            if (!IncreaseEBy1())
            {
                break;
            }
        }

        if (_dilationW > 3)
        {
            r = 1;
        }

        if (_dilationH > 3)
        {
            s = 1;
        }

        _weightSplitPattern = new(r, s);

        return new()
        {
            c,
            m,
            e,
            f,
            r,
            s,
        };
    }

    private bool HandleL1Allocate(int h, int w, int e, int f, int psumPingPangSplit, int ifBytesPerElementGlb)
    {
        if (e * f > GNNEEnv.PsumL1ElePerChan / psumPingPangSplit)
        {
            return false;
        }

        if (GNNEEnv.PuHeight * h * w * ifBytesPerElementGlb > GNNEEnv.IfL1Size)
        {
            return false;
        }

        return true;
    }

    private void BuildL1Schedule(GnneActionUpdater actionUpdater, TiledGlb glb, SegmentND ifmap, SegmentND weight, SegmentND psum, int iPp, int ofPp, int wPp, SegmentND ifmap2, bool weightGroupOnly, WeightGroupHandler weightGroup, ref int l1Pp, TIR.Buffer ddrW)
    {
        var l1Tile = L1Search(glb, weight, psum);
        var eSeg = GetSegmentStartEndLength(psum[2].Start, l1Tile[2], psum[2].End);
        var fSeg = GetSegmentStartEndLength(psum[3].Start, l1Tile[3], psum[3].End);
        var nSeg = GetSegmentStartEndLength(psum[0].Start, 1, psum[0].End);
        var rSeg = GetSegmentStartEndLength(weight[2].Start, l1Tile[4], weight[2].End);
        var seg = GetSegmentStartEndLength(weight[3].Start, l1Tile[5], weight[3].End);
        var ret = GetL1McSeg(ifmap, psum, l1Tile[1], l1Tile[0]);
        var mSeg = ret[0];
        var cSeg = ret[1];

        int rLen = l1Tile[4];
        int len = Math.Min(GNNEEnv.PuKernelSpad / 2, l1Tile[5]);
        if (_dilationH > 1)
        {
            rLen = 1;
        }

        if (_dilationW > 1)
        {
            len = 1;
        }

        foreach (var m in mSeg)
        {
            foreach (var n in nSeg)
            {
                foreach (var e in eSeg)
                {
                    var hConvOut = GetInputRowSegment(e.Start, e.Length, _convOutputShape![2], _fusedKernelH, _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
                    var h = GetInputRowSegment(hConvOut.Start, hConvOut.Length, _inputShape![2], _weightsShape![2], _strideH, _dilationH, _paddingH!);
                    foreach (var f in fSeg)
                    {
                        var wConvOut = GetInputColumnSegment(f.Start, f.Length, _convOutputShape[3], _fusedKernelW, _fusedStrideW, _fusedDilationW, _fusedPaddingW!);
                        var w = GetInputColumnSegment(wConvOut.Start, wConvOut.Length, _inputShape[3], _weightsShape[3], _strideW, _dilationW, _paddingW!);

                        SegmentND l2GOf = new(n, m, e, f);
                        SegmentND r2LPsum = new(n, m, hConvOut, wConvOut);

                        int cs0 = m.Start / _ocPerGroup * _icPerGroup;
                        int ce0 = ((m.End - 1) / _ocPerGroup * _icPerGroup) + _icPerGroup;
                        Segment1D wc_inloop = new(0..0, new(0, 0));

                        bool isDepthwise = _inputShape[1] == _outputShape![1] && _outputShape[1] == _groups && _groups != 1;
                        bool ofmapCalStart = cSeg[0].Start == 0 || isDepthwise;
                        foreach (var c in cSeg)
                        {
                            if (c.Start < cs0 || c.End > ce0)
                            {
                                continue;
                            }

                            int cs = c.Start % _icPerGroup;
                            int ce = Math.Min(cs + c.Length, _icPerGroup);
                            wc_inloop = new(cs..ce, new(0, 0));

                            foreach (var rL2 in rSeg)
                            {
                                foreach (var l2 in seg)
                                {
                                    var cInloop = c;
                                    var rInloopSeg = GetSegmentStartEndLength(rL2.Start, rLen, rL2.End);
                                    var inloopSeg = GetSegmentStartEndLength(l2.Start, len, l2.End);
                                    var nInloop = n;
                                    var mInloop = m;
                                    SegmentND ifmapSlice = new(nInloop, cInloop, h, w);
                                    SegmentND weightSlice = new(m, wc_inloop, rL2, l2);

                                    bool dmLoadIfSn = false;
                                    var g2LIf = ShiftInputTensor(ifmapSlice, weightSlice, _weightsShape[2], _weightsShape[3], _strideH, _strideW, _dilationH, _dilationW);
                                    if (g2LIf[0].Length > 0 && g2LIf[1].Length > 0 && g2LIf[2].Length > 0 && g2LIf[3].Length > 0)
                                    {
                                        dmLoadIfSn = true;
                                        if (!weightGroupOnly)
                                        {
                                            int ifClrCnt = _g2LIfRec[iPp][0].Item2.Stat_cnt();
                                            _g2LIfRec[iPp].RemoveAt(0);
                                            List<CcrClr> g2LIfCcrClr = new();
                                            if (ifClrCnt > 0)
                                            {
                                                g2LIfCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ifmap, iPp))));
                                            }

                                            actionUpdater.UpdateG2LIf(g2LIf, ifmap, _lif!, iPp, null!, g2LIfCcrClr, 0, DataTypes.UInt8, ItemName.Ifmap, false, 0, 0, 0, _h2C, _weightsShape[2], _strideH);
                                        }
                                        else
                                        {
                                            _g2LIfRec[iPp].Add(new(ifmap, new(false, false)));
                                        }
                                    }

                                    foreach (var rInloop in rInloopSeg)
                                    {
                                        foreach (var inloop in inloopSeg)
                                        {
                                            SegmentND l2RW = new(mInloop, wc_inloop, rInloop, inloop);
                                            var l2RIf = ShiftInputTensor(ifmapSlice, l2RW, _weightsShape[2], _weightsShape[3], _strideH, _strideW, _dilationH, _dilationW);

                                            int posW = _weightType == DataTypes.Int16 ? 2 : 1;
                                            int posIf = _inputType == DataTypes.Int16 ? 2 : 1;
                                            for (int pW = 0; pW < posW; pW++)
                                            {
                                                for (int pIf = 0; pIf < posIf; pIf++)
                                                {
                                                    if (!weightGroupOnly)
                                                    {
                                                        _weightPreloadEn = false;
                                                        if (!_weightPreloadEn)
                                                        {
                                                            var weightRecStat = _weightRec[wPp][0];
                                                            var g2RWRecStat = _g2RWRec[wPp][0];
                                                            _g2RWRec[wPp].RemoveAt(0);
                                                            if (g2RWRecStat.Item2.IsLastSlice)
                                                            {
                                                                _weightRec[wPp].RemoveAt(0);
                                                            }

                                                            var g2RWSliceRecStat = _g2RWSliceRec[wPp][0];
                                                            _g2RWSliceRec[wPp].RemoveAt(0);
                                                            Assert(l2RW == g2RWSliceRecStat.Item1 && g2RWRecStat.Item1 == weightRecStat.Item1);
                                                            if (g2RWSliceRecStat.Item2.IsFirstSlice)
                                                            {
                                                                int wClrCnt = !weightRecStat.Item2.IsFirstSlice && g2RWRecStat.Item2.IsFirstSlice ? 1 : 0;
                                                                List<CcrClr> ccrClrW = new();
                                                                if (wClrCnt > 0)
                                                                {
                                                                    ccrClrW.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.WeightFake, wPp))));
                                                                }

                                                                List<CcrSet> ccrSetW = new() { new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Weight, (wPp << 1) + (g2RWSliceRecStat.Item2.SliceIdx & 0x1))), 1) };
                                                                actionUpdater.UpdateLoadW(l2RW, _lw!, weightGroup, wPp, ddrW, ccrSetW, ccrClrW, 0, _h2C, 0, ItemName.Weight, pW);
                                                            }

                                                            int g2RWCcrSetNum = 0;
                                                            if (g2RWRecStat.Item2.IsLastSlice &&
                                                                _weightRec[wPp].Count > 0)
                                                            {
                                                                g2RWCcrSetNum = 1;
                                                            }

                                                            List<CcrSet> g2RWCcrSet = new();
                                                            if (g2RWCcrSetNum > 0)
                                                            {
                                                                g2RWCcrSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.WeightFake, wPp)), 1));
                                                            }

                                                            List<CcrClr> g2RWCcrClr = new();
                                                            if (g2RWSliceRecStat.Item2.IsFirstSlice)
                                                            {
                                                                g2RWCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Weight, (wPp << 1) + (g2RWSliceRecStat.Item2.SliceIdx & 0x1)))));
                                                            }

                                                            actionUpdater.UpdateG2RW(l2RW, weightGroup, _ocPerGroup, _lw!, wPp, pW, g2RWCcrSet, g2RWCcrClr, 0, 0, ItemName.Weight, ItemName.WQarg, _h2C);
                                                        }
                                                        else
                                                        {
                                                            actionUpdater.UpdateG2RW(l2RW, weightGroup, _ocPerGroup, _lw!, wPp, pW, null!, null!, 0, 0, ItemName.Weight, ItemName.WQarg, _h2C);
                                                        }
                                                    }
                                                    else
                                                    {
                                                        weightGroup.UpdateWeightGroup(l2RW);
                                                        if (!_weightPreloadEn)
                                                        {
                                                            _g2RWRec[wPp].Add(new(weight, new(false, false)));
                                                            _g2RWSliceRec[wPp].Add(new(l2RW, new(false, false, -1)));
                                                        }
                                                    }

                                                    if (!weightGroupOnly)
                                                    {
                                                        actionUpdater.UpdateL2RIf(l2RIf, g2LIf, _strideH, _strideW, _icPerGroup, _lif!, 0, pIf, ((TensorConst)_conv![GNNEConv2D.DeqBias]).Value.ToScalar<int>(), DataTypes.UInt8, _h2C, _weightsShape[2]);
                                                    }

                                                    bool releaseIf = false;
                                                    if (inloop == inloopSeg[^1] && rInloop == rInloopSeg[^1] && pW == posW - 1 && pIf == posIf - 1 && dmLoadIfSn)
                                                    {
                                                        releaseIf = true;
                                                        dmLoadIfSn = false;
                                                    }

                                                    bool loopStart = false;
                                                    if (ofmapCalStart && pW == 0 && pIf == 0)
                                                    {
                                                        loopStart = true;
                                                        ofmapCalStart = false;
                                                    }

                                                    bool loopEnd = wc_inloop.End == _weightsShape[1] && inloop.End == weight[3].End && rInloop.End == weight[2].End && pW == posW - 1 && pIf == posIf - 1;

                                                    var destType = _outputType!;
                                                    var destTarget = ACT0_OUTPUT_DEST.dm;
                                                    if (_pool is not null || _dw is not null || _act1 is not null)
                                                    {
                                                        destTarget = ACT0_OUTPUT_DEST.psum;
                                                        destType = _conv!.CheckedDataType;
                                                    }

                                                    if (!weightGroupOnly)
                                                    {
                                                        int shift = ((TensorConst)_conv![GNNEConv2D.ShiftBits]).Value.ToScalar<int>();

                                                        int l2GOfCcrSetNum = 0;
                                                        int l2GOfCcrClrNum = 0;
                                                        if (loopEnd && destTarget == ACT0_OUTPUT_DEST.dm)
                                                        {
                                                            var ofCcrStat = _l2GOfRec[ofPp][0];
                                                            _l2GOfRec[ofPp].RemoveAt(0);
                                                            if (ofCcrStat.Item2.IsFirstSlice &&
                                                                !_ofmapRec[ofPp][0].Item2.IsFirstSlice)
                                                            {
                                                                l2GOfCcrClrNum = 1;
                                                            }

                                                            if (ofCcrStat.Item2.IsLastSlice)
                                                            {
                                                                l2GOfCcrSetNum = 1;
                                                            }
                                                        }

                                                        List<CcrSet> l2GOfCcrSet = new();
                                                        if (l2GOfCcrSetNum > 0)
                                                        {
                                                            l2GOfCcrSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, ofPp)), 1));
                                                        }

                                                        List<CcrClr> l2GOfCcrClr = new();
                                                        if (l2GOfCcrClrNum > 0)
                                                        {
                                                            l2GOfCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.OfmapFake, ofPp))));
                                                        }

                                                        actionUpdater.UpdateR2LPsum(shift, r2LPsum, r2LPsum, psum, ofPp, l1Pp, destTarget, releaseIf, Math.Max(pW, pIf), TcuComputeMode.NormalConv2d, loopStart, loopEnd, _strideH, _strideW, _ocPerGroup, _inputType!, _weightType!, destType, _lact!.CheckedDataType, l2GOfCcrSet, l2GOfCcrClr);
                                                    }
                                                    else if (loopEnd && destTarget == ACT0_OUTPUT_DEST.dm)
                                                    {
                                                        _l2GOfRec[ofPp].Add(new(psum, new(false, false)));
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        if (!weightGroupOnly)
                        {
                            if ((_pool is null && _dw is null && _act1 is null) || wc_inloop.End != _weightsShape[1])
                            {
                                continue;
                            }

                            SegmentND l2RDw = new();
                            Segment1D none = new(0..0, new(0, 0));
                            SegmentND l2RIf2 = new(none, none, none, none);
                            List<CcrClr> l2RIf2CcrClr = new();
                            if (_act1 is not null && _act1[GNNEActivation.InputB] != None.Default)
                            {
                                l2RIf2 = r2LPsum;
                                int if2ClrCnt = _l2RIf2Rec[ofPp][0].Item2.Stat_cnt();
                                _l2RIf2Rec[ofPp].RemoveAt(0);
                                for (int i = 0; i < if2ClrCnt; i++)
                                {
                                    l2RIf2CcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ifmap2, ofPp))));
                                }
                            }

                            if (_dw is not null)
                            {
                                long[] dwFilter = _dw[GNNEPdp0DW.Weights].CheckedShape.ToValueArray()[2..];
                                l2RDw = new(new(0..1, new(0, 0)), m, new(0..(int)dwFilter[0], new(0, 0)), new(0..(int)dwFilter[1], new(0, 0)));
                            }

                            int l2GOfCcrSetNum = 0;
                            int l2GOfCcrClrNum = 0;
                            var ofCcrStat = _l2GOfRec[ofPp][0];
                            _l2GOfRec[ofPp].RemoveAt(0);
                            if (ofCcrStat.Item2.IsFirstSlice && !_ofmapRec[ofPp][0].Item2.IsFirstSlice)
                            {
                                l2GOfCcrClrNum = 1;
                            }

                            if (ofCcrStat.Item2.IsLastSlice)
                            {
                                l2GOfCcrSetNum = 1;
                            }

                            List<CcrSet> l2GOfCcrSet = new();
                            if (l2GOfCcrSetNum > 0)
                            {
                                l2GOfCcrSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, ofPp)), 1));
                            }

                            List<CcrClr> l2GOfCcrClr = new();
                            if (l2GOfCcrClrNum > 0)
                            {
                                l2GOfCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.OfmapFake, ofPp))));
                            }

                            actionUpdater.UpdateL2RPsum(_dw!, _pool!, _act1!, l2RIf2, ifmap2, l2RDw, r2LPsum, l2GOf, psum, ofPp, l1Pp, ACT0_OUTPUT_DEST.dm, TcuComputeMode.NormalConv2d, _ocPerGroup, weightGroup, l2RIf2CcrClr, l2GOfCcrSet, l2GOfCcrClr);

                            l1Pp = (l1Pp + 1) % 2;
                        }
                        else
                        {
                            if (_act1 is not null && wc_inloop.End == _weightsShape[1] &&
                                _act1[GNNEActivation.InputB] != None.Default)
                            {
                                _l2RIf2Rec[ofPp].Add(new(ifmap2, new(false, false)));
                            }

                            if ((_pool is not null || _dw is not null || _act1 is not null) &&
                                wc_inloop.End == _weightsShape[1])
                            {
                                _l2GOfRec[ofPp].Add(new(psum, new(false, false)));
                            }
                        }
                    }
                }
            }
        }
    }

    private List<List<Segment1D>> GetL1McSeg(SegmentND ifmap, SegmentND psum, int mInloop, int cInloop)
    {
        int group = Math.Max(ifmap[1].Length / _icPerGroup, 1);
        List<Segment1D> mSeg = new();
        List<Segment1D> cSeg = new();
        if (group >= 2 && _groupPerPass < 2)
        {
            for (int i = 0; i < group; i++)
            {
                var mSegTmp = GetSegmentStartEndLength(psum[1].Start + (i * _ocPerGroup), mInloop, psum[1].Start + ((i + 1) * _ocPerGroup));
                var cSegTmp = GetSegmentStartEndLength(ifmap[1].Start + (i * _icPerGroup), cInloop, ifmap[1].Start + ((i + 1) * _icPerGroup));
                mSeg.AddRange(mSegTmp);
                cSeg.AddRange(cSegTmp);
            }
        }
        else
        {
            mSeg = GetSegmentStartEndLength(psum[1].Start, mInloop, psum[1].End);
            cSeg = GetSegmentStartEndLength(ifmap[1].Start, cInloop, ifmap[1].End);
        }

        return new() { mSeg, cSeg };
    }

    private AllocateResult GetBoxes(int n, int c, int h, int w, int r, int s, int m, int e, int f, List<float> glbUsage)
    {
        List<BoxOnGlb> boxes = new();
        Dictionary<ItemName, TensorOnGlb> glbMap = new();
        List<float> glbAllocSize = new() { 0, 0 };

        int ifNPingPongSplit = GNNEEnv.NPingPongSplit;
        if (n == _inputShape![0] && c == _inputShape[1] && h == _inputShape[2] && w == _inputShape[3])
        {
            ifNPingPongSplit = 1;
        }

        if (_h2C)
        {
            h += _paddingH!.Sum();
        }

        TensorOnGlb ifGlb = new(new[] { n, c, h, w }, _inputType!, 0);
        int ifmapSize = ifGlb.AllocatedBytes * ifNPingPongSplit;
        glbAllocSize[0] += ifmapSize;
        ifmapSize = GetAlignedNum(ifmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);
        glbAllocSize[1] += ifmapSize;

        int wC = c;
        if (wC > _icPerGroup)
        {
            wC = _icPerGroup;
        }

        TensorOnGlb wGlb = new(new[] { m, wC, r, s }, _weightType!, 0);
        int wSize = SpaceSearcher.GetWeightSize(r, s, wC, m, GetBytesPerElement(_weightType!)) * GNNEEnv.NPingPongSplit;
        wGlb.AllocatedBytes = wSize / GNNEEnv.NPingPongSplit;
        glbAllocSize[0] += wSize;
        wSize = GetAlignedNum(wSize, GNNEEnv.WBankWidth * GNNEEnv.GlbBankWidth);
        glbAllocSize[1] += wSize;

        TensorOnGlb ofGlb = new(new[] { n, m, e, f }, _outputType!, 0);
        int ofmapSize = ofGlb.AllocatedBytes * GNNEEnv.NPingPongSplit;
        glbAllocSize[0] += ofmapSize;
        ofmapSize = GetAlignedNum(ofmapSize, GNNEEnv.OfmapBankWidth * GNNEEnv.GlbBankWidth);
        glbAllocSize[1] += ofmapSize;

        TensorOnGlb actGlb = new(new[] { 1, 1, _outputShape![1], GNNEEnv.ActNumPerChan }, _lact!.CheckedDataType, 0);
        int actSize = SpaceSearcher.GetActSize(_outputShape[1], GNNEEnv.ActNumPerChan, GetBytesPerElement(_lact.CheckedDataType));
        glbAllocSize[0] += actSize;
        actSize = GetAlignedNum(actSize, GNNEEnv.ActBankWidth * GNNEEnv.GlbBankWidth);
        glbAllocSize[1] += actSize;

        int basementSize = SpaceSearcher.GetBasementSize();
        basementSize = GetAlignedNum(basementSize, GNNEEnv.GlbWidth * GNNEEnv.GlbBankWidth);

        boxes.Add(new(new[] { GNNEEnv.GlbWidth, basementSize / GNNEEnv.GlbWidth / GNNEEnv.GlbBankWidth }, ItemName.Basement));
        boxes.Add(new(new[] { GNNEEnv.IfmapBankWidth, ifmapSize / GNNEEnv.IfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ifmap));
        boxes.Add(new(new[] { GNNEEnv.WBankWidth, wSize / GNNEEnv.WBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Weight));
        boxes.Add(new(new[] { GNNEEnv.OfmapBankWidth, ofmapSize / GNNEEnv.OfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ofmap));
        boxes.Add(new(new[] { GNNEEnv.ActBankWidth, actSize / GNNEEnv.ActBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Act));

        glbMap.Add(ItemName.Ifmap, ifGlb);
        glbMap.Add(ItemName.Weight, wGlb);
        glbMap.Add(ItemName.Ofmap, ofGlb);
        glbMap.Add(ItemName.Act, actGlb);

        // only uint8 and int16 have bias
        if (_weightType == DataTypes.UInt8 || _weightType == DataTypes.Int16)
        {
            TensorOnGlb w_qarg_glb = new(new[] { 1, 1, 1, _convOutputShape![1] }, _lwQarg!.CheckedDataType, 0);
            int w_qarg_size = SpaceSearcher.GetWQargSize(_convOutputShape[1], GetBytesPerElement(_lwQarg.CheckedDataType));

            bool is_depthwise = _inputShape[1] == _outputShape[1] && _outputShape[1] == _groups && _groups != 1;
            if (is_depthwise)
            {
                w_qarg_size = SpaceSearcher.GetWQargSize((int)Math.Ceiling(1.0 * _convOutputShape[1] / GNNEEnv.PuHeight) * GNNEEnv.PuWidth, GetBytesPerElement(_lwQarg.CheckedDataType));
            }

            glbAllocSize[0] += w_qarg_size;
            w_qarg_size = GetAlignedNum(w_qarg_size, GNNEEnv.WQargBankWidth * GNNEEnv.GlbBankWidth);
            glbAllocSize[1] += w_qarg_size;
            boxes.Add(new(new[] { GNNEEnv.WQargBankWidth, w_qarg_size / GNNEEnv.WQargBankWidth / GNNEEnv.GlbBankWidth }, ItemName.WQarg));
            glbMap.Add(ItemName.WQarg, w_qarg_glb);
        }

        if (_dw is not null)
        {
            int r1 = _fusedKernelH;
            int s1 = _fusedKernelW;
            int m1 = _outputShape[1];
            var dwWeightType = _dw[GNNEPdp0DW.Weights].CheckedDataType;

            TensorOnGlb dwGlb = new(new[] { m1, 1, r1, s1 }, dwWeightType, 0);
            int dwWSize = SpaceSearcher.GetWeightSize(r1, s1, GetAlignedNum(m1, GNNEEnv.PuWidth), 1, GetBytesPerElement(dwWeightType));
            glbAllocSize[0] += dwWSize;
            dwWSize = GetAlignedNum(dwWSize, GNNEEnv.WBankWidth * GNNEEnv.GlbBankWidth);
            glbAllocSize[1] += dwWSize;
            boxes.Add(new(new[] { GNNEEnv.WBankWidth, dwWSize / GNNEEnv.WBankWidth / GNNEEnv.GlbBankWidth }, ItemName.DwWeight));
            glbMap.Add(ItemName.DwWeight, dwGlb);

            TensorOnGlb dwActGlb = new(new[] { 1, 1, m1, GNNEEnv.ActNumPerChan }, _dw[GNNEPdp0DW.Act].CheckedDataType, 0);
            int dwActSize = SpaceSearcher.GetActSize(m1, GNNEEnv.ActNumPerChan, GetBytesPerElement(_dw[GNNEPdp0DW.Act].CheckedDataType));
            glbAllocSize[0] += dwActSize;
            dwActSize = GetAlignedNum(dwActSize, GNNEEnv.ActBankWidth * GNNEEnv.GlbBankWidth);
            glbAllocSize[1] += dwActSize;
            boxes.Add(new(new[] { GNNEEnv.ActBankWidth, dwActSize / GNNEEnv.ActBankWidth / GNNEEnv.GlbBankWidth }, ItemName.DwAct1));
            glbMap.Add(ItemName.DwAct1, dwActGlb);

            if (dwWeightType == DataTypes.UInt8 || dwWeightType == DataTypes.Int16)
            {
                TensorOnGlb dwQargGlb = new(new[] { 1, 1, 1, m1 }, dwWeightType, 0);
                int dwWQargSize = SpaceSearcher.GetWQargSize(m1, GetBytesPerElement(dwWeightType));
                glbAllocSize[0] += dwWQargSize;
                dwWQargSize = GetAlignedNum(dwWQargSize, GNNEEnv.WQargBankWidth * GNNEEnv.GlbBankWidth);
                glbAllocSize[1] += dwWQargSize;
                boxes.Add(new(new[] { GNNEEnv.WQargBankWidth, dwWQargSize / GNNEEnv.WQargBankWidth / GNNEEnv.GlbBankWidth }, ItemName.DwQarg));
                glbMap.Add(ItemName.DwQarg, dwQargGlb);
            }
        }

        // only support res_add for now
        TensorOnGlb ifGlb2 = new(new[] { 0, 0, 0, 0 }, DataTypes.Float16, 0);
        if (_act1 is not null)
        {
            bool is16Segs = ((TensorConst)_act1[GNNEActivation.Is16Segments]).Value.ToScalar<bool>();
            int chanNum = is16Segs ? 1 : _outputShape[1];
            int actParamNum = is16Segs ? 49 : GNNEEnv.ActNumPerChan;
            TensorOnGlb mfuAct1Glb = new(new[] { 1, 1, chanNum, actParamNum }, _act1[GNNEActivation.Act].CheckedDataType, 0);
            int act1Size = SpaceSearcher.GetActSize(chanNum, actParamNum, GetBytesPerElement(_act1[GNNEActivation.Act].CheckedDataType));
            glbAllocSize[0] += act1Size;
            act1Size = GetAlignedNum(act1Size, GNNEEnv.ActBankWidth * GNNEEnv.GlbBankWidth);
            glbAllocSize[1] += act1Size;
            boxes.Add(new(new[] { GNNEEnv.ActBankWidth, act1Size / GNNEEnv.ActBankWidth / GNNEEnv.GlbBankWidth }, ItemName.MfuAct1));
            glbMap.Add(ItemName.MfuAct1, mfuAct1Glb);

            if (_act1[GNNEActivation.InputB] != None.Default)
            {
                var act1LifA = (Call)_act1[GNNEActivation.InputA];
                var act1LifB = (Call)_act1[GNNEActivation.InputB];
                DataType act1If2Type = DataTypes.Float16;
                if (act1LifA is { Target: GNNELoad })
                {
                    act1If2Type = act1LifA.CheckedDataType;
                }
                else if (act1LifB is { Target: GNNELoad })
                {
                    act1If2Type = act1LifB.CheckedDataType;
                }

                int eTemp = e;
                int fTemp = f;

                int tensorAlignSize = 32 / GetBytesPerElement(act1If2Type);
                if ((e * f) % tensorAlignSize != 0)
                {
                    int eEnd = e + tensorAlignSize - 1;
                    int fEnd = f + tensorAlignSize - 1;
                    long minAlgin32 = 65535 * 65535L;

                    for (int i = e; i < eEnd; i++)
                    {
                        for (int j = f; j < fEnd; j++)
                        {
                            int mulRes = i * j;
                            if (mulRes % tensorAlignSize == 0)
                            {
                                if (mulRes < minAlgin32)
                                {
                                    minAlgin32 = mulRes;
                                    eTemp = i;
                                    fTemp = j;
                                }
                            }
                        }
                    }
                }

                ifGlb2 = new(new[] { n, m, eTemp, fTemp }, act1If2Type, 0);
                int ifmap2Size = ifGlb2.AllocatedBytes * GNNEEnv.NPingPongSplit;
                glbAllocSize[0] += ifmap2Size;
                ifmap2Size = GetAlignedNum(ifmap2Size, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);
                glbAllocSize[1] += ifmap2Size;
                boxes.Add(new(new[] { GNNEEnv.IfmapBankWidth, ifmap2Size / GNNEEnv.IfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ifmap2));
            }
        }

        glbMap.Add(ItemName.Ifmap2, ifGlb2);

        if (_pool is not null)
        {
            TensorOnGlb pdpAct1Glb = new(new[] { 1, 1, _outputShape[1], GNNEEnv.ActNumPerChan }, _pool[GNNEPdp0Reduce.Act].CheckedDataType, 0);
            int pdpActSize = SpaceSearcher.GetActSize(_outputShape[1], GNNEEnv.ActNumPerChan, GetBytesPerElement(_pool[GNNEPdp0Reduce.Act].CheckedDataType));
            glbAllocSize[0] += pdpActSize;
            pdpActSize = GetAlignedNum(pdpActSize, GNNEEnv.ActBankWidth * GNNEEnv.GlbBankWidth);
            glbAllocSize[1] += pdpActSize;
            boxes.Add(new(new[] { GNNEEnv.ActBankWidth, pdpActSize / GNNEEnv.ActBankWidth / GNNEEnv.GlbBankWidth }, ItemName.PdpAct1));
            glbMap.Add(ItemName.PdpAct1, pdpAct1Glb);
        }

        glbUsage[0] = glbAllocSize[0] / GNNEEnv.GlbSize;
        glbUsage[1] = glbAllocSize[1] / GNNEEnv.GlbSize;

        AllocateResult ret = new() { Boxes = boxes, GlbMap = glbMap };
        return ret;
    }

    private void ItemRecStatusInit()
    {
        for (int i = 0; i < _weightRec.Length; i++)
        {
            if (_weightRec[i] is not null)
            {
                _weightRec[i].Clear();
            }
            else
            {
                _weightRec[i] = new tensor_ccr_stat();
            }

            if (_ofmapRec[i] is not null)
            {
                _ofmapRec[i].Clear();
            }
            else
            {
                _ofmapRec[i] = new tensor_ccr_stat();
            }

            if (_g2LIfRec[i] is not null)
            {
                _g2LIfRec[i].Clear();
            }
            else
            {
                _g2LIfRec[i] = new tensor_ccr_stat();
            }

            if (_g2RWRec[i] is not null)
            {
                _g2RWRec[i].Clear();
            }
            else
            {
                _g2RWRec[i] = new tensor_ccr_stat();
            }

            if (_l2GOfRec[i] is not null)
            {
                _l2GOfRec[i].Clear();
            }
            else
            {
                _l2GOfRec[i] = new tensor_ccr_stat();
            }

            if (_l2RIf2Rec[i] is not null)
            {
                _l2RIf2Rec[i].Clear();
            }
            else
            {
                _l2RIf2Rec[i] = new tensor_ccr_stat();
            }

            if (_g2RWSliceRec[i] is not null)
            {
                _g2RWSliceRec[i].Clear();
            }
            else
            {
                _g2RWSliceRec[i] = new tensor_ccr_stat();
            }
        }
    }

    private void InitParameters(Call convNode, Call ld, Call st)
    {
        _weightSplitPattern = new(0, 0);

        _conv = convNode;
        _lif = ld;
        _lw = (Call)_conv[GNNEConv2D.Weights];
        _lact = (Call)_conv[GNNEConv2D.Act];
        _lwQarg = (Call)_conv[GNNEConv2D.WeightsBias];
        _sof = st;

        _inputShape = new GNNEShape((int)_lif.CheckedShape[0].FixedValue, (int)_lif.CheckedShape[1].FixedValue, (int)_lif.CheckedShape[2].FixedValue, (int)_lif.CheckedShape[3].FixedValue);
        _outputShape = new GNNEShape((int)_conv.CheckedShape[0].FixedValue, (int)_conv.CheckedShape[1].FixedValue, (int)_conv.CheckedShape[2].FixedValue, (int)_conv.CheckedShape[3].FixedValue);
        _convOutputShape = _outputShape;
        _weightsShape = new GNNEShape((int)_lw.CheckedShape[0].FixedValue, (int)_lw.CheckedShape[1].FixedValue, (int)_lw.CheckedShape[2].FixedValue, (int)_lw.CheckedShape[3].FixedValue);
        int[] paddings = ((TensorConst)_conv[GNNEConv2D.Padding]).Value.ToArray<int>();
        _paddingH = new(paddings[0], paddings[1]);
        _paddingW = new(paddings[2], paddings[3]);
        _strideH = ((TensorConst)_conv[GNNEConv2D.Stride]).Value.ToArray<int>()[0];
        _strideW = ((TensorConst)_conv[GNNEConv2D.Stride]).Value.ToArray<int>()[1];
        _dilationH = ((TensorConst)_conv[GNNEConv2D.Dilation]).Value.ToArray<int>()[0];
        _dilationW = ((TensorConst)_conv[GNNEConv2D.Dilation]).Value.ToArray<int>()[1];
        _groups = ((TensorConst)_conv[GNNEConv2D.Groups]).Value.ToScalar<int>();

        _icPerGroup = _inputShape[1] / _groups;
        _ocPerGroup = _outputShape[1] / _groups;
        _groupPerPass = Math.Min(Math.Max(Math.Min(GNNEEnv.PuHeight / _icPerGroup, GNNEEnv.PuWidth / _ocPerGroup), 1), _groups);

        _pool = st[GNNEStore.Input] is Call { Target: GNNEPdp0Reduce } ? (Call)st[GNNEStore.Input] : null;
        _dw = st[GNNEStore.Input] is Call { Target: GNNEPdp0DW } ? (Call)st[GNNEStore.Input] : null;
        _act1 = st[GNNEStore.Input] is Call { Target: GNNEActivation } ? (Call)st[GNNEStore.Input] : null;
        if (_act1 is not null && _act1[GNNEActivation.InputB] != None.Default)
        {
            bool hasLifA = _act1[GNNEActivation.InputA] is Call { Target: GNNELoad };
            if (hasLifA)
            {
                _lif2 = (_act1[GNNEActivation.InputA] as Call)!;
            }
            else
            {
                _lif2 = (_act1[GNNEActivation.InputB] as Call)!;
            }
        }
        else
        {
            _lif2 = null!;
        }

        _strategy = DetermineSearchStrategy();

        _inputType = _lif.CheckedDataType;
        _weightType = _lw.CheckedDataType;
        _weightGroups.Add(new WeightGroupHandler(_weightType, _weightType));
        _weightGroups.Add(new WeightGroupHandler(_weightType, _weightType)); // if_first & w_first specialized
        _outputType = _conv.CheckedDataType;
        if (_pool is not null)
        {
            _outputType = _pool.CheckedDataType;
        }

        if (_dw is not null)
        {
            _outputType = _dw.CheckedDataType;
        }

        if (_act1 is not null)
        {
            _outputType = _act1.CheckedDataType;
        }

        _fusedKernelH = 1;
        _fusedKernelW = 1;
        _fusedPaddingH = new(0, 0);
        _fusedPaddingW = new(0, 0);
        _fusedStrideH = 1;
        _fusedStrideW = 1;
        _fusedDilationH = 1;
        _fusedDilationW = 1;
        if (_pool is not null)
        {
            _outputShape = new GNNEShape((int)_pool.CheckedShape[0].FixedValue, (int)_pool.CheckedShape[1].FixedValue, (int)_pool.CheckedShape[2].FixedValue, (int)_pool.CheckedShape[3].FixedValue);
            _fusedKernelH = ((TensorConst)_pool[GNNEPdp0Reduce.Filter]).Value.ToArray<int>()[0];
            _fusedKernelW = ((TensorConst)_pool[GNNEPdp0Reduce.Filter]).Value.ToArray<int>()[1];
            paddings = ((TensorConst)_pool[GNNEPdp0Reduce.Padding]).Value.ToArray<int>();
            _fusedPaddingH = new(paddings[0], paddings[1]);
            _fusedPaddingW = new(paddings[2], paddings[3]);
            _fusedStrideH = ((TensorConst)_pool[GNNEPdp0Reduce.Stride]).Value.ToArray<int>()[0];
            _fusedStrideW = ((TensorConst)_pool[GNNEPdp0Reduce.Stride]).Value.ToArray<int>()[1];

            // sof = node_cast<gnne_store>(pool->output().connections()[0]->owner());
        }
        else if (_dw is not null)
        {
            _outputShape = new GNNEShape((int)_dw.CheckedShape[0].FixedValue, (int)_dw.CheckedShape[1].FixedValue, (int)_dw.CheckedShape[2].FixedValue, (int)_dw.CheckedShape[3].FixedValue);
            _fusedKernelH = (int)_dw[GNNEPdp0DW.Weights].CheckedShape[2].FixedValue;
            _fusedKernelW = (int)_dw[GNNEPdp0DW.Weights].CheckedShape[3].FixedValue;
            paddings = ((TensorConst)_dw[GNNEPdp0DW.Padding]).Value.ToArray<int>();
            _fusedPaddingH = new(paddings[0], paddings[1]);
            _fusedPaddingW = new(paddings[2], paddings[3]);
            _fusedStrideH = ((TensorConst)_dw[GNNEPdp0DW.Stride]).Value.ToArray<int>()[0];
            _fusedStrideW = ((TensorConst)_dw[GNNEPdp0DW.Stride]).Value.ToArray<int>()[1];
            _fusedDilationH = ((TensorConst)_dw[GNNEPdp0DW.Dilation]).Value.ToArray<int>()[0];
            _fusedDilationW = ((TensorConst)_dw[GNNEPdp0DW.Dilation]).Value.ToArray<int>()[1];
        }
        else if (_act1 is not null)
        {
            // only suport res_add for now
            _outputShape = new GNNEShape((int)_act1.CheckedShape[0].FixedValue, (int)_act1.CheckedShape[1].FixedValue, (int)_act1.CheckedShape[2].FixedValue, (int)_act1.CheckedShape[3].FixedValue);
            Assert(_outputShape.Dims.SequenceEqual(_convOutputShape.Dims));
        }

        _convOutputShape[2] = GetInputRowSegment(0, _outputShape[2], _convOutputShape[2], _fusedKernelH, _fusedStrideH, _fusedDilationH, _fusedPaddingH).Length;
        _convOutputShape[3] = GetInputColumnSegment(0, _outputShape[3], _convOutputShape[3], _fusedKernelW, _fusedStrideW, _fusedDilationW, _fusedPaddingW).Length;

        // TODO: h2c
        bool isDepthwise = _inputShape[1] == _outputShape[1] && _outputShape[1] == _groups && _groups != 1;
        _h2C = _weightsShape[1] * _weightsShape[2] <= GNNEEnv.PuHeight && _weightsShape[2] != 1 && _inputShape[2] > 200 && _inputShape[3] > 200 && !isDepthwise && _inputType != DataTypes.Int16;
    }

    private GlbSearchStrategy DetermineSearchStrategy()
    {
        if (_inputShape!.Size > _weightsShape!.Size)
        {
            return GlbSearchStrategy.IfFirst;
        }

        return GlbSearchStrategy.WFirst;
    }

    private ScheduleStrategy DetermineScheduleStrategy(List<GnneAction> actionsIfFirst, List<GnneAction> actionsWFirst)
    {
        DdrBandwidth[] ddrBandWidth = { new(), new() }; // 0--if_first_schedule;1--w_first_schedule

        foreach (var action in actionsIfFirst)
        {
            ddrBandWidth[(int)ScheduleStrategy.IfFirstSchedule].CalcBw(action);
        }

        foreach (var action in actionsWFirst)
        {
            ddrBandWidth[(int)ScheduleStrategy.WFirstSchedule].CalcBw(action);
        }

        long bwIfFirst = ddrBandWidth[(int)ScheduleStrategy.IfFirstSchedule].TotalBw[0];
        long bwWFirst = ddrBandWidth[(int)ScheduleStrategy.WFirstSchedule].TotalBw[0];
        if (bwIfFirst >= bwWFirst)
        {
            return ScheduleStrategy.WFirstSchedule;
        }
        else
        {
            return ScheduleStrategy.IfFirstSchedule;
        }
    }

    private void UpdateCcrRecStat()
    {
        // g2l_if_rec status decide
        foreach (var t in _g2LIfRec)
        {
            for (int cnt = 0; cnt < t.Count; cnt++)
            {
                if (cnt == 0)
                {
                    t[cnt].Item2.IsFirstSlice = true;
                }

                if (cnt == t.Count - 1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                }

                if (cnt < t.Count - 1 && t[cnt].Item1 != t[cnt + 1].Item1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                    t[cnt + 1].Item2.IsFirstSlice = true;
                }
            }
        }

        // g2r_w_rec status decide
        foreach (var t in _g2RWRec)
        {
            for (int cnt = 0; cnt < t.Count; cnt++)
            {
                if (cnt == 0)
                {
                    t[cnt].Item2.IsFirstSlice = true;
                }

                if (cnt == t.Count - 1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                }

                if (cnt < t.Count - 1 && t[cnt].Item1 != t[cnt + 1].Item1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                    t[cnt + 1].Item2.IsFirstSlice = true;
                }
            }
        }

        // l2g_of_rec status decide
        foreach (var t in _l2GOfRec)
        {
            for (int cnt = 0; cnt < t.Count; cnt++)
            {
                if (cnt == 0)
                {
                    t[cnt].Item2.IsFirstSlice = true;
                }

                if (cnt == t.Count - 1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                }

                if (cnt >= t.Count - 1 || t[cnt].Item1 == t[cnt + 1].Item1)
                {
                    continue;
                }

                t[cnt].Item2.IsLastSlice = true;
                t[cnt + 1].Item2.IsFirstSlice = true;
            }
        }

        // l2r_if2_rec status decide
        foreach (var t in _l2RIf2Rec)
        {
            for (int cnt = 0; cnt < t.Count; cnt++)
            {
                if (cnt == 0)
                {
                    t[cnt].Item2.IsFirstSlice = true;
                }

                if (cnt == t.Count - 1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                }

                if (cnt < t.Count - 1 && t[cnt].Item1 != t[cnt + 1].Item1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                    t[cnt + 1].Item2.IsFirstSlice = true;
                }
            }
        }

        // weight_rec status decide
        foreach (var t in _weightRec)
        {
            if (t.Count > 0)
            {
                t[0].Item2.IsFirstSlice = true;
                t[^1].Item2.IsLastSlice = true;
            }
        }

        // ofmap_rec status decide
        foreach (var t in _ofmapRec)
        {
            if (t.Count > 0)
            {
                t[0].Item2.IsFirstSlice = true;
                t[^1].Item2.IsLastSlice = true;
            }
        }

        // g2r_w_slice_rec status decide
        for (int pp = 0; pp < _g2RWSliceRec.Length; pp++)
        {
            List<SegmentND> uniqueWSlices = new();
            List<SegmentND> wSlices = new();
            tensor_ccr_stat wSlicesRecPart = new();
            for (int wSliceIdx = 0; wSliceIdx < _g2RWRec[pp].Count; wSliceIdx++)
            {
                if (_g2RWRec[pp][wSliceIdx].Item2.IsFirstSlice)
                {
                    uniqueWSlices.Clear();
                    wSlices.Clear();
                    wSlicesRecPart.Clear();
                }

                if (!uniqueWSlices.Contains(_g2RWSliceRec[pp][wSliceIdx].Item1))
                {
                    uniqueWSlices.Add(_g2RWSliceRec[pp][wSliceIdx].Item1);
                }

                wSlices.Add(_g2RWSliceRec[pp][wSliceIdx].Item1);
                wSlicesRecPart.Add(_g2RWSliceRec[pp][wSliceIdx]);
                if (_g2RWRec[pp][wSliceIdx].Item2.IsLastSlice)
                {
                    foreach (var slice in uniqueWSlices)
                    {
                        wSlicesRecPart[wSlices.FindIndex(x => x == slice)].Item2.IsFirstSlice = true;
                        wSlicesRecPart[wSlices.FindLastIndex(g2RwSliceIdx => g2RwSliceIdx == slice)].Item2.IsLastSlice = true;
                    }

                    for (int cnt = 0; cnt < wSlices.Count; cnt++)
                    {
                        wSlicesRecPart[cnt].Item2.SliceIdx = uniqueWSlices.FindIndex(x => x == wSlices[cnt]);
                        _g2RWSliceRec[pp][wSliceIdx - wSlices.Count + 1 + cnt] = wSlicesRecPart[cnt];
                    }
                }
            }

            if (_weightType == DataTypes.Int16)
            {
                for (int cnt = 1; cnt < _g2RWSliceRec[pp].Count; cnt++)
                {
                    if (_g2RWSliceRec[pp][_g2RWSliceRec[pp].Count - cnt - 1].Item2.IsFirstSlice)
                    {
                        _g2RWSliceRec[pp][_g2RWSliceRec[pp].Count - cnt].Item2.IsFirstSlice = true;
                    }

                    if (_g2RWSliceRec[pp][cnt].Item2.IsLastSlice)
                    {
                        _g2RWSliceRec[pp][cnt - 1].Item2.IsLastSlice = true;
                    }
                }

                for (int cnt = 0; cnt < _g2RWSliceRec[pp].Count; cnt++)
                {
                    _g2RWSliceRec[pp][cnt].Item2.SliceIdx = (_g2RWSliceRec[pp][cnt].Item2.SliceIdx * 2) + (cnt & 0x1);
                }
            }
        }
    }

    private void ArrangeWeights(DataType weightsType, GNNEShape weightsShape, Span<byte> oldWeights, WeightGroupHandler weightGroup)
    {
        int bytesPerElement = GetBytesPerElement(weightsType);
        var weightGroupSlice = weightGroup.WeightGroupSlice();
        byte[] newWeights = new byte[oldWeights.Length];

        if (_h2C)
        {
            int offset = 0;
            int j = 0;
            foreach (var slice in weightGroupSlice)
            {
                Assert(offset == weightGroup.WeightGroupOffset(slice) * bytesPerElement);
                for (int b = 0; b < bytesPerElement; b++)
                {
                    for (int m = 0; m < slice[0].Length; m++)
                    {
                        for (int s = 0; s < slice[3].Length; s++)
                        {
                            for (int r = 0; r < slice[2].Length; r++)
                            {
                                for (int c = 0; c < slice[1].Length; c++)
                                {
                                    int srcAddr = (((((((m + slice[0].Start) * weightsShape[1]) + c + slice[1].Start) * weightsShape[2]) + r + slice[2].Start) * weightsShape[3]) + s + slice[3].Start) * bytesPerElement;
                                    newWeights[j++] = oldWeights[srcAddr + b];
                                    offset++;
                                }
                            }
                        }
                    }
                }
            }
        }
        else
        {
            int offset = 0;
            int j = 0;
            foreach (var slice in weightGroupSlice)
            {
                Assert(offset == weightGroup.WeightGroupOffset(slice) * bytesPerElement);
                for (int b = 0; b < bytesPerElement; b++)
                {
                    for (int m = 0; m < slice[0].Length; m++)
                    {
                        for (int r = 0; r < slice[2].Length; r++)
                        {
                            for (int s = 0; s < slice[3].Length; s++)
                            {
                                for (int c = 0; c < slice[1].Length; c++)
                                {
                                    int srcAddr = (((((((m + slice[0].Start) * weightsShape[1]) + c + slice[1].Start) * weightsShape[2]) + r + slice[2].Start) * weightsShape[3]) + s + slice[3].Start) * bytesPerElement;
                                    newWeights[j++] = oldWeights[srcAddr + b];
                                    offset++;
                                }
                            }
                        }
                    }
                }
            }
        }

        newWeights.CopyTo(oldWeights);
    }

    private void ArrangeDwWeights(DataType weightsType, long[] weightsShape, Span<byte> oldWeights, WeightGroupHandler weightGroup)
    {
        int bytesPerElement = GetBytesPerElement(weightsType);
        int j = 0;
        byte[] newWeights = new byte[oldWeights.Length];
        (weightsShape[0], weightsShape[1]) = (weightsShape[1], weightsShape[0]);

        foreach (var slice in from wg in weightGroup.DwGroupSlice() let cs = wg[1].Start let ce = wg[1].End let dwShape = _dw?[GNNEPdp0DW.Weights].CheckedShape.ToValueArray() select new SegmentND(..1, cs..ce, ..(int)dwShape[2], ..(int)dwShape[3]))
        {
            for (int m = 0; m < slice[0].Length; m++)
            {
                for (int r = 0; r < slice[2].Length; r++)
                {
                    for (int s = 0; s < slice[3].Length; s++)
                    {
                        for (int c = 0; c < slice[1].Length; c++)
                        {
                            int srcAddr = (int)(((((((m + slice[0].Start) * weightsShape[1]) + c + slice[1].Start) * weightsShape[2]) + r + slice[2].Start) * weightsShape[3]) + s + slice[3].Start) * bytesPerElement;
                            for (int b = 0; b < bytesPerElement; b++)
                            {
                                newWeights[(j * GNNEEnv.PuWidth) + c] = oldWeights[srcAddr + b];
                            }
                        }

                        j++;
                    }
                }
            }
        }

        newWeights.CopyTo(oldWeights);
    }
}

internal class TileConv2DGlb : TiledGlb
{
    private TensorOnGlb _ifGlb;
    private TensorOnGlb _ofGlb;

    public TileConv2DGlb(Dictionary<ItemName, TensorOnGlb> glbMap, Dictionary<ItemName, MmuItem> items, int[] lastOutShape, int nPingPongSplit, TensorOnGlb ifGlb, TensorOnGlb ofGlb)
        : base(glbMap, items, lastOutShape, nPingPongSplit)
    {
        _ifGlb = ifGlb;
        _ofGlb = ofGlb;
    }
}
