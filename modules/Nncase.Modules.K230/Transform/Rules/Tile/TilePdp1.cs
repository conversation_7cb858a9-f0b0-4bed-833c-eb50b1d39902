﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using NetFabric.Hyperlinq;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using Nncase.Runtime.K230;
using Nncase.Schedule;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using static Nncase.Passes.Rules.K230.TileUtilities;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class TilePdp1 : RewriteRule<Pattern>
{
    private static int _count = -1;

    /// <inheritdoc/>
    public override Pattern Pattern { get; } = FusionPattern.IsGNNEPDP1Fusion();

    private PrimFunction GetReplace(Call call, GNNEPdp1 callOp, Call ld, Call st)
    {
        _count++;

        var tiledGlb = SearchGlbParameters(ld, st, call);
        long[] inShape = ld.Arguments[0].CheckedShape.ToValueArray();
        T.CreateBuffer(new(ld[GNNELoad.Input].CheckedDataType, inShape), MemoryLocation.Input, out var ddrIf);
        T.CreateBuffer(new(st.CheckedDataType, st.CheckedShape), MemoryLocation.Output, out var ddrOf);
        List<GnneAction>? actions;
        if (IsGlobalPdp(ld, call))
        {
            actions = BuildScheduleGlobalPdp(tiledGlb, callOp, call, ld, st, ddrIf, ddrOf);
        }
        else
        {
            actions = BuildSchedule(tiledGlb, call, ld, st, ddrIf, ddrOf);
        }

        var actionConverter = new ActionToInstruct();
        return T.PrimFunc($"TilePDP1_{_count}", K230RtModule.Kind, new Var(ddrIf.CheckedType), new Var(ddrOf.CheckedType)).Body(
            actionConverter.Instructions(actions),
            I.END(GP_REGISTER.x0, 0))
        .Build();
    }

    private List<GnneAction> BuildSchedule(TiledGlb glb, Call pdp, Call ld, Call st, TIR.Buffer ddrIf, TIR.Buffer ddrOf)
    {
        int[] paddingH = ((TensorConst)pdp[GNNEPdp1.Padding]).Value.ToArray<int>()[..2];
        int[] paddingW = ((TensorConst)pdp[GNNEPdp1.Padding]).Value.ToArray<int>()[2..4];
        int strideH = ((TensorConst)pdp[GNNEPdp1.Stride]).Value.ToArray<int>()[0];
        int strideW = ((TensorConst)pdp[GNNEPdp1.Stride]).Value.ToArray<int>()[1];
        int kernelH = ((TensorConst)pdp[GNNEPdp1.Filter]).Value.ToArray<int>()[0];
        int kernelW = ((TensorConst)pdp[GNNEPdp1.Filter]).Value.ToArray<int>()[1];

        List<GnneAction> actions = new();
        var gpr = new GprHandler(GNNEEnv.GprNum);
        var ssr = new SsrHandler(GNNEEnv.SsrNum);
        var ccrHandler = new CcrHandler();
        GnneActionUpdater actionUpdater = new(actions, glb, ccrHandler, gpr, ssr);

        // mmu conf
        actionUpdater.UpdateMmuConf();

        var input = ld.Arguments[0];
        var inShape = new GNNEShape(input.CheckedShape.ToValueArray().Select(x => (int)x).ToArray());
        int[] lastOutShape = glb.LastOutShape;
        var outShape = pdp.CheckedShape;
        var seq =
            from glbOutputBatch in SegmentBy(0, lastOutShape[0], (int)outShape[0].FixedValue)
            from glbOutputChannel in SegmentBy(0, lastOutShape[1], (int)outShape[1].FixedValue)
            from glbOutputRow in SegmentBy(0, lastOutShape[2], (int)outShape[2].FixedValue)
            from glbOutputColumn in SegmentBy(0, lastOutShape[3], (int)outShape[3].FixedValue)
            select new SegmentND(glbOutputBatch, glbOutputChannel, glbOutputRow, glbOutputColumn);

        int iPp = 0;
        foreach (var ofmap in seq)
        {
            var glbInputRow = GetInputRowSegment(ofmap[2].Start, ofmap[2].Length, inShape[2], kernelH, strideH, 1, new Padding(paddingH[0], paddingH[1]));
            var glbInputColumn = GetInputColumnSegment(ofmap[3].Start, ofmap[3].Length, inShape[3], kernelW, strideW, 1, new Padding(paddingW[0], paddingW[1]));
            SegmentND ifmap = new(ofmap[0], ofmap[1], glbInputRow, glbInputColumn);

            // load ifmap
            List<CcrSet> ccrSetIfmap = new();
            ccrSetIfmap.Add(new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap, iPp)), 1));
            var ifmapPp = ifmap;
            var stridesD = new[] { glb.GlbMap[ItemName.Ifmap].Dimensions[1], glb.GlbMap[ItemName.Ifmap].Dimensions[2], glb.GlbMap[ItemName.Ifmap].Dimensions[3] }.ToList();
            actionUpdater.UpdateLoadIf(ifmapPp, ld, iPp, ddrIf, 0, stridesD, ItemName.Ifmap, ccrSetIfmap);

            // transpose
            List<CcrSet> ccrSetOfmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ofmap, iPp)), 1) };
            List<CcrClr> ccrClrIfmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap, iPp))) };
            var ofmapPp = ofmap;
            actionUpdater.UpdateMfuPdp1(pdp, ifmapPp, ofmapPp, iPp, ccrSetOfmap, ccrClrIfmap);

            // store
            List<CcrClr> ccrClrOfmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ofmap, iPp))) };
            actionUpdater.UpdateStoreT(ofmapPp, st, iPp, ddrOf, 0, null!, null!, ccrClrOfmap);

            iPp = (iPp + 1) % 2;
        }

        Assert(ccrHandler.CcrSanityCheck());

        return actions;
    }

    private List<GnneAction> BuildScheduleGlobalPdp(TiledGlb glb, GNNEPdp1 globalPdp, Call pdp, Call ld, Call st, TIR.Buffer ddrIf, TIR.Buffer ddrOf)
    {
        int[] paddingH = ((TensorConst)pdp[GNNEPdp1.Padding]).Value.ToArray<int>()[..2];
        int[] paddingW = ((TensorConst)pdp[GNNEPdp1.Padding]).Value.ToArray<int>()[2..4];
        int strideH = ((TensorConst)pdp[GNNEPdp1.Stride]).Value.ToArray<int>()[0];
        int strideW = ((TensorConst)pdp[GNNEPdp1.Stride]).Value.ToArray<int>()[1];
        int kernelH = ((TensorConst)pdp[GNNEPdp1.Filter]).Value.ToArray<int>()[0];
        int kernelW = ((TensorConst)pdp[GNNEPdp1.Filter]).Value.ToArray<int>()[1];

        List<GnneAction> actions = new();
        var gpr = new GprHandler(GNNEEnv.GprNum);
        var ssr = new SsrHandler(GNNEEnv.SsrNum);
        var ccrHandler = new CcrHandler();
        GnneActionUpdater actionUpdater = new(actions, glb, ccrHandler, gpr, ssr);

        // mmu conf
        actionUpdater.UpdateMmuConf();

        int s = 0, r = 0;
        SplitGlobalPdp1(kernelW, kernelH, ref r, ref s);

        var input = ld.Arguments[0];
        var inShape = new GNNEShape(input.CheckedShape.ToValueArray().Select(x => (int)x).ToArray());
        int[] lastOutShape = glb.LastOutShape;
        var outShape = pdp.CheckedShape;
        var seq = (
            from glbOutputBatch in SegmentBy(0, lastOutShape[0], (int)outShape[0].FixedValue)
            from glbOutputChannel in SegmentBy(0, lastOutShape[1], (int)outShape[1].FixedValue)
            from glbOutputRow in SegmentBy(0, lastOutShape[2], (int)outShape[2].FixedValue)
            from glbOutputColumn in SegmentBy(0, lastOutShape[3], (int)outShape[3].FixedValue)
            select new SegmentND(glbOutputBatch, glbOutputChannel, glbOutputRow, glbOutputColumn)).ToArray();

        List<SegmentND> ifmapAll = new();
        foreach (var ofmap in seq)
        {
            var glbInputRow = GetInputRowSegment(ofmap[2].Start, ofmap[2].Length, inShape[2], kernelH, strideH, 1, new Padding(paddingH[0], paddingH[1]));
            var glbInputColumn = GetInputColumnSegment(ofmap[3].Start, ofmap[3].Length, inShape[3], kernelW, strideW, 1, new Padding(paddingW[0], paddingW[1]));
            SegmentND ifmap = new(ofmap[0], ofmap[1], glbInputRow, glbInputColumn);
            ifmapAll.Add(ifmap);
        }

        int iPp = 0;
        bool[] isFirstSlice = new bool[] { true, true };
        bool isLastPing = false;
        bool isLastPong = false;
        for (int t = 0; t < seq.Length; t++)
        {
            var ofmap = seq.ToList()[t];
            var ifmap = ifmapAll[t];

            if (seq.Length == 1)
            {
                isLastPing = true;
            }
            else
            {
                if (((t == seq.Length - 1) || (t == seq.Length - 2)) && (t & 0x1) == 0)
                {
                    isLastPing = true;
                }

                if (((t == seq.Length - 1) || (t == seq.Length - 2)) && (t & 0x1) == 1)
                {
                    isLastPong = true;
                }
            }

            if (t == seq.Length - 2 && ifmapAll[^1][1].Length == 1)
            {
                isLastPong = true;
            }

            var rSegs = GetSegmentStartEndLength(0, r, inShape[2]);
            var segs = GetSegmentStartEndLength(0, s, inShape[3]);

            // load ifmap
            List<CcrSet> ccrSetIfmap = new();
            ccrSetIfmap.Add(new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap, iPp)), rSegs.Count * segs.Count == 1 ? 1 : 2));
            var ifmapPp = new SegmentND(ifmap);
            int alignedW = GetAlignedNum(ifmap[3].Length, GetBytesPerElement(ld.CheckedDataType) == 1 ? 32 : 16);
            SegmentND ifmapPpAligned = new(ifmap[0], ifmap[1], ifmap[2], new(..alignedW, new(0, 0)));
            var stridesD = new[] { glb.GlbMap[ItemName.Ifmap].Dimensions[1], glb.GlbMap[ItemName.Ifmap].Dimensions[2], glb.GlbMap[ItemName.Ifmap].Dimensions[3] }.ToList();
            actionUpdater.UpdateLoadIf(ifmapPp, ld, iPp, ddrIf, 0, stridesD, ItemName.Ifmap, ccrSetIfmap);

            alignedW = GetAlignedNum(segs.Count, 16);
            SegmentND ofmapPp = new(ofmap[0], ofmap[1], new(..rSegs.Count, new(0, 0)), new(..segs.Count, new(0, 0)));
            SegmentND ofmapPpAligned = new(ofmap[0], ofmap[1], ofmapPp[2], new(..alignedW, new(0, 0)));

            // pool
            PDP_FUNCTION PdpFunc(MFU_PDP_OP op)
            {
                return op switch
                {
                    MFU_PDP_OP.MIN => PDP_FUNCTION.min,
                    MFU_PDP_OP.MAX => PDP_FUNCTION.max,
                    MFU_PDP_OP.AVERAGE => PDP_FUNCTION.average,
                    MFU_PDP_OP.SUM => PDP_FUNCTION.sum,
                    _ => PDP_FUNCTION.min,
                };
            }

            var pdpOp = PdpFunc(globalPdp.ReduceOp == MFU_PDP_OP.AVERAGE ? MFU_PDP_OP.SUM : globalPdp.ReduceOp);

            var sumScale1 = (Half)(1.0 / inShape[2]);
            var sumScale2 = (Half)(1.0 / inShape[3]);

            for (int i = 0; i < rSegs.Count; i++)
            {
                for (int j = 0; j < segs.Count; j++)
                {
                    int ccrClrNum = i == 0 && j == 0 ? 1 : 0;
                    List<CcrClr> ccrClrIfmap = new();
                    if (ccrClrNum > 0)
                    {
                        ccrClrIfmap.Add(new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap, iPp))));
                    }

                    if (i == 0 && j == 0 && !isFirstSlice[iPp])
                    {
                        ccrClrIfmap.Add(new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.OfmapFake, iPp))));
                    }

                    SegmentND ifmapSeg = new(ifmapPp[0], ifmapPp[1], rSegs[i], segs[j]);
                    SegmentND ofmapSeg = new(ofmapPp[0], ofmapPp[1], new(i..(i + 1), new(0, 0)), new(j..(j + 1), new(0, 0)));

                    int offsetS = GetSliceOffsetInTensor(ifmapPpAligned, ifmapSeg) * GetBytesPerElement(ld.CheckedDataType);
                    int offsetD = GetSliceOffsetInTensor(ofmapPpAligned, ofmapSeg) * GetBytesPerElement(DataTypes.Float16);

                    actionUpdater.UpdateMfuGlobalPdp1(pdp, ld.CheckedDataType, DataTypes.Float16, pdpOp, ifmapSeg, ofmapSeg, iPp, sumScale1, null!, ccrClrIfmap, offsetS, offsetD);
                }
            }

            var ifmapLast = ofmapPp;
            SegmentND ofmapLast = new(ofmapPp[0], ofmapPp[1], new(..1, new(0, 0)), new(..1, new(0, 0)));
            {
                List<CcrSet> ccrSetOfmap = new();
                ccrSetOfmap.Add(new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ofmap, iPp)), 1));
                List<CcrClr> ccrClrIfmap = new();
                ccrClrIfmap.Add(new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap, iPp))));

                int offsetS = 0;
                int offsetD = 0;
                actionUpdater.UpdateMfuGlobalPdp1(pdp, DataTypes.Float16, pdp.CheckedDataType, pdpOp, ifmapLast, ofmapLast, iPp, sumScale2, ccrSetOfmap, ccrClrIfmap, offsetS, offsetD, ItemName.Ofmap);
            }

            // store
            List<CcrClr> ccrClrOfmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ofmap, iPp))) };
            int ccrSetOfmapFakeNum = iPp == 0 ? isLastPing ? 0 : 1 : isLastPong ? 0 : 1;
            List<CcrSet> ccrSetOfmapFake = new();
            if (ccrSetOfmapFakeNum > 0)
            {
                ccrSetOfmapFake.Add(new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.OfmapFake, iPp)), 1));
            }

            var stridesS = new[] { glb.GlbMap[ItemName.Ofmap].Dimensions[1], glb.GlbMap[ItemName.Ofmap].Dimensions[2], glb.GlbMap[ItemName.Ofmap].Dimensions[3] * (pdp.CheckedDataType == DataTypes.Float16 || pdp.CheckedDataType == DataTypes.Int16 ? 1 : 2) }.ToList();
            actionUpdater.UpdateStoreT(ofmapLast, st, iPp, ddrOf, 0, null!, ccrSetOfmapFake, ccrClrOfmap, ItemName.Ofmap, stridesS);

            isFirstSlice[iPp] = false;
            iPp = (iPp + 1) % 2;
        }

        Assert(ccrHandler.CcrSanityCheck());

        return actions;
    }

    private TilePDP1Glb SearchGlbParameters(Call ld, Call st, Call pdp)
    {
        int[] paddingH = ((TensorConst)pdp[GNNEPdp1.Padding]).Value.ToArray<int>()[..2];
        int[] paddingW = ((TensorConst)pdp[GNNEPdp1.Padding]).Value.ToArray<int>()[2..4];
        int strideH = ((TensorConst)pdp[GNNEPdp1.Stride]).Value.ToArray<int>()[0];
        int strideW = ((TensorConst)pdp[GNNEPdp1.Stride]).Value.ToArray<int>()[1];
        int kernelH = ((TensorConst)pdp[GNNEPdp1.Filter]).Value.ToArray<int>()[0];
        int kernelW = ((TensorConst)pdp[GNNEPdp1.Filter]).Value.ToArray<int>()[1];

        var input = ld.Arguments[0];
        var inShape = new GNNEShape(input.CheckedShape.ToValueArray().Select(x => (int)x).ToArray());
        var outShape = new GNNEShape(pdp.CheckedShape.ToValueArray().Select(x => (int)x).ToArray());

        bool isGlobalPdp = IsGlobalPdp(ld, pdp);

        int n = 1;
        int c = 1;
        int oh = 1;
        int ow = outShape[3];
        int h = SpaceSearcher.GetInputHeight(oh, inShape[2], kernelH, outShape[2], strideH, 1, new(paddingH[0], paddingH[1]));
        int w = SpaceSearcher.GetInputHeight(ow, inShape[3], kernelW, outShape[3], strideW, 1, new(paddingW[0], paddingW[1]));

        var inType = ld.CheckedDataType;
        var allocOutputType = isGlobalPdp ? DataTypes.Float16 : pdp.CheckedDataType;

        // TODO: only split global once reduce_window2d on glb for now
        // support general pdp if nedded
        if (isGlobalPdp)
        {
            int s = 0, r = 0;
            SplitGlobalPdp1(kernelW, kernelH, ref r, ref s);
            oh = (int)Math.Ceiling(1.0 * kernelH / r);
            ow = (int)Math.Ceiling(1.0 * kernelW / s);
            Assert(ow <= 64 && oh <= 16 && oh * ow <= 256);
        }

        AllocateResult allocation = new();
        while (oh < outShape[2])
        {
            int nextOh = oh + 1;
            int nextH = SpaceSearcher.GetInputHeight(nextOh, inShape[2], kernelH, outShape[2], strideH, 1, new(paddingH[0], paddingH[1]));
            allocation = HandleAllocate(n, c, nextH, w, oh, ow, inType, allocOutputType);

            if (allocation.IsOk)
            {
                oh = nextOh;
                h = nextH;
            }
            else
            {
                break;
            }
        }

        while (c < inShape[1])
        {
            allocation = HandleAllocate(n, c + 1, h, w, oh, ow, inType, allocOutputType);

            if (allocation.IsOk)
            {
                c += 1;
            }
            else
            {
                break;
            }
        }

        while (n < inShape[0])
        {
            allocation = HandleAllocate(n + 1, c, h, w, oh, ow, inType, allocOutputType);

            if (allocation.IsOk)
            {
                n += 1;
            }
            else
            {
                break;
            }
        }

        allocation = HandleAllocate(n, c, h, w, oh, ow, inType, allocOutputType, true);
        Assert(allocation.IsOk);

        if (isGlobalPdp)
        {
            oh = 1;
            ow = 1;
        }

        var lastOutShape = new GNNEShape(n, c, oh, ow);
        return new TilePDP1Glb(allocation.GlbMap, allocation.Items, lastOutShape.Dims, GNNEEnv.NPingPongSplit, allocation.GlbMap[ItemName.Ifmap], allocation.GlbMap[ItemName.Ofmap]);
    }

    private AllocateResult HandleAllocate(int n, int c, int h, int w, int e, int f, DataType inType, DataType outType, bool isFinal = false)
    {
        var bp = new BoxPacker(16);
        Dictionary<ItemName, TensorOnGlb> glbMap = new();

        int bytesPerElement = GetBytesPerElement(inType);
        int alignedNum = bytesPerElement == 1 ? 32 : 16;
        int wTemp = GetAlignedNum(w, alignedNum);
        var ifGlb = new TensorOnGlb(new[] { n, c, h, wTemp }, inType, 0);
        int ifmapSize = ifGlb.GlbNByte * GNNEEnv.NPingPongSplit;
        ifmapSize = GetAlignedNum(ifmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);

        alignedNum = GetBytesPerElement(outType) == 1 ? 32 : 16;
        int fTemp = GetAlignedNum(f, alignedNum);
        var ofGlb = new TensorOnGlb(new[] { n, c, e, fTemp }, outType, 0);
        int ofmapSize = ofGlb.GlbNByte * GNNEEnv.NPingPongSplit;
        ofmapSize = GetAlignedNum(ofmapSize, GNNEEnv.OfmapBankWidth * GNNEEnv.GlbBankWidth);

        int basementSize = SpaceSearcher.GetBasementSize();
        basementSize = GetAlignedNum(basementSize, GNNEEnv.GlbWidth * GNNEEnv.GlbBankWidth);

        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.GlbWidth, basementSize / GNNEEnv.GlbWidth / GNNEEnv.GlbBankWidth }, ItemName.Basement));
        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.IfmapBankWidth, ifmapSize / GNNEEnv.IfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ifmap));
        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.OfmapBankWidth, ofmapSize / GNNEEnv.OfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ofmap));

        var allocation = SpaceSearcher.TryAllocate(bp);

#if DEBUG
        if (isFinal && allocation.IsOk)
        {
            bp.DisplayFinalAllocation();
        }
#endif

        if (allocation.IsOk)
        {
            ifGlb.Mmu = allocation.Items[ItemName.Ifmap];
            ofGlb.Mmu = allocation.Items[ItemName.Ofmap];
        }

        glbMap.Add(ItemName.Ifmap, ifGlb);
        glbMap.Add(ItemName.Ofmap, ofGlb);
        allocation.GlbMap = glbMap;
        return allocation;
    }

    private void SplitGlobalPdp1(int kernelW, int kernelH, ref int r, ref int s)
    {
        r = kernelH > 16 ? 16 : kernelH;
        s = Math.Min(Math.Min(256 / r, kernelW), 64);
    }

    private bool IsGlobalPdp(Call ld, Call pdp)
    {
        int kernelH = ((TensorConst)pdp[GNNEPdp1.Filter]).Value.ToArray<int>()[0];
        int kernelW = ((TensorConst)pdp[GNNEPdp1.Filter]).Value.ToArray<int>()[1];

        var input = ld.Arguments[0];
        var inShape = new GNNEShape(input.CheckedShape.ToValueArray().Select(x => (int)x).ToArray());

        return inShape[2] == kernelH
               && inShape[3] == kernelW
               && (inShape[2] > 16 || inShape[3] > 64 || inShape[2] * inShape[3] > 256);
    }
}

internal class TilePDP1Glb : TiledGlb
{
    private TensorOnGlb _ifGlb;
    private TensorOnGlb _ofGlb;

    public TilePDP1Glb(Dictionary<ItemName, TensorOnGlb> glbMap, Dictionary<ItemName, MmuItem> items, int[] lastOutShape, int nPingPongSplit, TensorOnGlb ifGlb, TensorOnGlb ofGlb)
        : base(glbMap, items, lastOutShape, nPingPongSplit)
    {
        _ifGlb = ifGlb;
        _ofGlb = ofGlb;
    }
}
