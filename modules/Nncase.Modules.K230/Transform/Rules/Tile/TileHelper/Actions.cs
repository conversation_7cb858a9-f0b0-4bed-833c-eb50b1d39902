﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.TIR;
using Nncase.TIR.Instructions;

// TODO: add conf_actions here
namespace Nncase.Passes.Rules.K230;

public class GnneAction
{
    public GnneAction(GnneActionName name)
    {
        Name = name;
    }

    public GnneActionName Name { get; }

    public int ToGlbAddr(int mmuItem, int addr)
    {
        return (mmuItem << 28) + addr;
    }
}

/// <inheritdoc />
public class GnneActionWriteGpr : GnneAction
{
    private int _gprIndex;

    public GnneActionWriteGpr(int gprIndex, int imm)
        : base(GnneActionName.WriteGpr)
    {
        _gprIndex = gprIndex;
        Imm = imm;
    }

    public int GprIndex => _gprIndex;

    public int Imm { get; }
}

/// <inheritdoc />
public class GnneActionBasementG2R : GnneAction
{
    private int _rbasement;

    public GnneActionBasementG2R(int rbasement)
        : base(GnneActionName.BasementG2R)
    {
        _rbasement = rbasement;
    }

    public int Rbasement => _rbasement;
}

/// <inheritdoc />
public class GnneActionPackStrideReg : GnneAction
{
    private Gpr _n;
    private Gpr _c;
    private Gpr _h;
    private Ssr _ss;
    private Op _conn;

    public GnneActionPackStrideReg(Gpr n, Gpr c, Gpr h, Ssr ss, Op conn = null!)
        : base(GnneActionName.PackStrideReg)
    {
        _n = n;
        _c = c;
        _h = h;
        _ss = ss;
        _conn = conn;
    }

    public Gpr N => _n;

    public Gpr C => _c;

    public Gpr H => _h;

    public Ssr Ss => _ss;

    public Op Conn => _conn;
}

public class GnneActionPackShapeReg : GnneAction
{
    public GnneActionPackShapeReg(Gpr n, Gpr c, Gpr h, Gpr w, Ssr ss)
        : base(GnneActionName.PackShapeReg)
    {
        N = n;
        C = c;
        H = h;
        W = w;
        Ss = ss;
    }

    public Gpr N { get; }

    public Gpr C { get; }

    public Gpr H { get; }

    public Gpr W { get; }

    public Ssr Ss { get; }
}

/// <inheritdoc />
public class Gnne_action_mmu_conf : GnneAction
{
    public Gnne_action_mmu_conf(MmuItem item, Gpr start, Gpr depth)
        : base(GnneActionName.MmuConf)
    {
        Item = item;
        Start = start;
        Depth = depth;
    }

    public MmuItem Item { get; }

    public Gpr Start { get; }

    public Gpr Depth { get; }
}

public class GnneActionCcrDecl : GnneAction
{
    public GnneActionCcrDecl(Gpr num)
        : base(GnneActionName.CcrDecl)
    {
        Num = num;
    }

    public Gpr Num { get; }
}

public class GnneActionCcrSet : GnneAction
{
    public GnneActionCcrSet(int ccr, int value)
        : base(GnneActionName.CcrSet)
    {
        Ccr = ccr;
        Value = value;
    }

    public int Ccr { get; }

    public int Value { get; }
}

/// <inheritdoc />
public class GnneActionCcrClr : GnneAction
{
    public GnneActionCcrClr(int ccr)
        : base(GnneActionName.CcrClr)
    {
        Ccr = ccr;
    }

    public int Ccr { get; }
}

/// <inheritdoc />
public class GnneActionIntr : GnneAction
{
    public GnneActionIntr(Gpr intrNum)
        : base(GnneActionName.Intr)
    {
        IntrNum = intrNum;
    }

    public Gpr IntrNum { get; }
}

public class GnneActionFence : GnneAction
{
    public GnneActionFence()
        : base(GnneActionName.Fence)
    {
    }
}

/// <inheritdoc />
public class GnneActionEnd : GnneAction
{
    public GnneActionEnd()
        : base(GnneActionName.End)
    {
    }
}

/// <inheritdoc />
public class Gnne_action_l2_load_conf : GnneAction
{
    public Gnne_action_l2_load_conf()
        : base(GnneActionName.L2LoadConf)
    {
        StrideD = new Ssr(-1, -1, false);
        StrideS = new Ssr(-1, -1, false);
        L2Datatype = DataTypes.UInt8;
        DdrDatatype = DataTypes.UInt8;
    }

    public Gnne_action_l2_load_conf(Ssr strideD, Ssr strideS, DataType l2Datatype, DataType ddrDatatype)
        : base(GnneActionName.L2LoadConf)
    {
        StrideD = strideD;
        StrideS = strideS;
        L2Datatype = l2Datatype;
        DdrDatatype = ddrDatatype;
    }

    public Ssr StrideD { get; }

    public Ssr StrideS { get; }

    public DataType L2Datatype { get; }

    public DataType DdrDatatype { get; }

    public static bool operator ==(Gnne_action_l2_load_conf lhs, Gnne_action_l2_load_conf rhs)
    {
        return lhs.StrideD.Value == rhs.StrideD.Value
               && lhs.StrideS.Value == rhs.StrideS.Value
               && lhs.L2Datatype == rhs.L2Datatype
               && lhs.DdrDatatype == rhs.DdrDatatype;
    }

    public static bool operator !=(Gnne_action_l2_load_conf lhs, Gnne_action_l2_load_conf rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionL2Load : GnneAction
{
    private Ssr _shape;

    public GnneActionL2Load(Gpr basement, Gpr addrD, Gpr addrS, Ssr shape, Call input, SegmentND sliceInfo, TIR.Buffer buffer, int[] layout = null!)
        : base(GnneActionName.L2Load)
    {
        Basement = basement;
        AddrD = addrD;
        AddrS = addrS;
        _shape = shape;
        Input = input;
        SliceInfo = sliceInfo;
        Buffer = buffer;
        Layout = layout;
    }

    public Gpr Basement { get; }

    public Gpr AddrD { get; }

    public Gpr AddrS { get; }

    public Ssr Shape => _shape;

    public Call Input { get; }

    public SegmentND SliceInfo { get; }

    public TIR.Buffer Buffer { get; }

    public int[] Layout { get; }
}

/// <inheritdoc />
public class GnneActionL2LoadWConf : GnneAction
{
    public GnneActionL2LoadWConf()
        : base(GnneActionName.L2LoadWConf)
    {
        LenCompressed = new Gpr(-1, new DimConst(-1), false);
        LenDecompressed = new Gpr(-1, new DimConst(-1), false);
        L2Datatype = DataTypes.UInt8;
        DdrDatatype = DataTypes.UInt8;
        EnableDecompress = false;
    }

    public GnneActionL2LoadWConf(Gpr lenCompressed, Gpr lenDecompressed, DataType l2Datatype, DataType ddrDatatype, bool enableDecompress = false)
        : base(GnneActionName.L2LoadWConf)
    {
        LenCompressed = lenCompressed;
        LenDecompressed = lenDecompressed;
        L2Datatype = l2Datatype;
        DdrDatatype = ddrDatatype;
        EnableDecompress = enableDecompress;
    }

    public Gpr LenCompressed { get; }

    public Gpr LenDecompressed { get; }

    public DataType L2Datatype { get; }

    public DataType DdrDatatype { get; }

    public bool EnableDecompress { get; }

    public static bool operator ==(GnneActionL2LoadWConf lhs, GnneActionL2LoadWConf rhs)
    {
        return lhs.LenCompressed.Value == rhs.LenCompressed.Value
               && lhs.LenDecompressed.Value == rhs.LenDecompressed.Value
               && lhs.L2Datatype == rhs.L2Datatype
               && lhs.DdrDatatype == rhs.DdrDatatype
               && lhs.EnableDecompress == rhs.EnableDecompress;
    }

    public static bool operator !=(GnneActionL2LoadWConf lhs, GnneActionL2LoadWConf rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionL2LoadW : GnneAction
{
    public GnneActionL2LoadW(Gpr basement, Gpr addrD, Gpr addrS, Gpr validCNum)
        : base(GnneActionName.L2LoadW)
    {
        Basement = basement;
        AddrD = addrD;
        AddrS = addrS;
        ValidCNum = validCNum;
    }

    public Gpr Basement { get; }

    public Gpr AddrD { get; }

    public Gpr AddrS { get; }

    public Gpr ValidCNum { get; }
}

/// <inheritdoc />
public class GnneActionL2StoreConf : GnneAction
{
    public GnneActionL2StoreConf()
        : base(GnneActionName.L2StoreConf)
    {
        StrideD = new Ssr(-1, -1, false);
        StrideS = new Ssr(-1, -1, false);
        L2Datatype = DataTypes.UInt8;
        DdrDatatype = DataTypes.UInt8;
    }

    public GnneActionL2StoreConf(Ssr strideD, Ssr strideS, DataType l2Datatype, DataType ddrDatatype)
        : base(GnneActionName.L2StoreConf)
    {
        StrideD = strideD;
        StrideS = strideS;
        L2Datatype = l2Datatype;
        DdrDatatype = ddrDatatype;
    }

    public Ssr StrideD { get; }

    public Ssr StrideS { get; }

    public DataType L2Datatype { get; }

    public DataType DdrDatatype { get; }

    public static bool operator ==(GnneActionL2StoreConf lhs, GnneActionL2StoreConf rhs)
    {
        return lhs.StrideD.Value == rhs.StrideD.Value
               && lhs.StrideS.Value == rhs.StrideS.Value
               && lhs.L2Datatype == rhs.L2Datatype
               && lhs.DdrDatatype == rhs.DdrDatatype;
    }

    public static bool operator !=(GnneActionL2StoreConf lhs, GnneActionL2StoreConf rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionL2Store : GnneAction
{
    public GnneActionL2Store(Gpr basement, Gpr addrS, Gpr addrD, Ssr shape, Call output, SegmentND sliceInfo, TIR.Buffer buffer, int[] layout = null!)
        : base(GnneActionName.L2Store)
    {
        Basement = basement;
        AddrS = addrS;
        AddrD = addrD;
        Shape = shape;
        Output = output;
        SliceInfo = sliceInfo;
        Buffer = buffer;
        Layout = layout;
    }

    public Gpr Basement { get; }

    public Gpr AddrS { get; }

    public Gpr AddrD { get; }

    public Ssr Shape { get; }

    public Call Output { get; }

    public SegmentND SliceInfo { get; }

    public TIR.Buffer Buffer { get; }

    public int[] Layout { get; }
}

/// <inheritdoc />
public class GnneActionDmLoadL1Conf : GnneAction
{
    public GnneActionDmLoadL1Conf()
        : base(GnneActionName.DmLoadL1Conf)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        StrideS = new Ssr(-1, -1, false);
        L2Datatype = DataTypes.UInt8;
        L1Type = L1_TYPE.@if_;
    }

    public GnneActionDmLoadL1Conf(int tcuId, int puId, int funct4, Ssr strideS, DataType l2Datatype, L1_TYPE l1Type)
        : base(GnneActionName.DmLoadL1Conf)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        StrideS = strideS;
        L2Datatype = l2Datatype;
        L1Type = l1Type;
    }

    public Ssr StrideS { get; }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public DataType L2Datatype { get; }

    public L1_TYPE L1Type { get; }

    public static bool operator ==(GnneActionDmLoadL1Conf lhs, GnneActionDmLoadL1Conf rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.StrideS.Value == rhs.StrideS.Value
               && lhs.L2Datatype == rhs.L2Datatype
               && lhs.L1Type == rhs.L1Type;
    }

    public static bool operator !=(GnneActionDmLoadL1Conf lhs, GnneActionDmLoadL1Conf rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionDmLoadL1 : GnneAction
{
    public GnneActionDmLoadL1(int tcuId, int puId, Gpr addrS, Ssr shape, Gpr htocWindow, L1_TYPE l1Type, SegmentND sliceInfo)
        : base(GnneActionName.DmLoadL1)
    {
        TcuId = tcuId;
        PuId = puId;
        AddrS = addrS;
        Shape = shape;
        HtocWindow = htocWindow;
        L1Type = l1Type;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public Gpr AddrS { get; }

    public Ssr Shape { get; }

    public Gpr HtocWindow { get; }

    public L1_TYPE L1Type { get; }
}

/// <inheritdoc />
public class GnneActionDmLoadWConf : GnneAction
{
    public GnneActionDmLoadWConf()
        : base(GnneActionName.DmLoadWConf)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        KernelH = -1;
        KernelW = -1;
        StrideOc = new Gpr(-1, new DimConst(-1), false);
    }

    public GnneActionDmLoadWConf(int tcuId, int puId, int funct4, int kernelH, int kernelW, Gpr strideOc)
        : base(GnneActionName.DmLoadWConf)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        KernelH = kernelH;
        KernelW = kernelW;
        StrideOc = strideOc;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public int KernelH { get; }

    public int KernelW { get; }

    public Gpr StrideOc { get; }

    public static bool operator ==(GnneActionDmLoadWConf lhs, GnneActionDmLoadWConf rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.KernelH == rhs.KernelH
               && lhs.KernelW == rhs.KernelW
               && lhs.StrideOc.Value == rhs.StrideOc.Value;
    }

    public static bool operator !=(GnneActionDmLoadWConf lhs, GnneActionDmLoadWConf rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionDmLoadWConf2 : GnneAction
{
    public GnneActionDmLoadWConf2()
        : base(GnneActionName.DmLoadWConf2)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        Groups = new Gpr(-1, new DimConst(-1), false);
        Goc = new Gpr(-1, new DimConst(-1), false);
    }

    public GnneActionDmLoadWConf2(int tcuId, int puId, int funct4, Gpr groups, Gpr goc)
        : base(GnneActionName.DmLoadWConf2)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        Groups = groups;
        Goc = goc;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public Gpr Groups { get; }

    public Gpr Goc { get; }

    public static bool operator ==(GnneActionDmLoadWConf2 lhs, GnneActionDmLoadWConf2 rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.Groups.Value == rhs.Groups.Value
               && lhs.Goc.Value == rhs.Goc.Value;
    }

    public static bool operator !=(GnneActionDmLoadWConf2 lhs, GnneActionDmLoadWConf2 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuWConf : GnneAction
{
    public GnneActionPuWConf()
        : base(GnneActionName.PuWConf)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        KernelH = -1;
        KernelW = -1;
    }

    public GnneActionPuWConf(int tcuId, int puId, int funct4, int kernelH, int kernelW)
        : base(GnneActionName.PuWConf)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        KernelH = kernelH;
        KernelW = kernelW;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public int KernelH { get; }

    public int KernelW { get; }

    public static bool operator ==(GnneActionPuWConf lhs, GnneActionPuWConf rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.KernelH == rhs.KernelH
               && lhs.KernelW == rhs.KernelW;
    }

    public static bool operator !=(GnneActionPuWConf lhs, GnneActionPuWConf rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionDmLoadWConfDeq : GnneAction
{
    public GnneActionDmLoadWConfDeq()
        : base(GnneActionName.DmLoadWConfDeq)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        QuantType = DataTypes.UInt8;
    }

    public GnneActionDmLoadWConfDeq(int tcuId, int puId, int funct4, DataType quantType)
        : base(GnneActionName.DmLoadWConfDeq)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        QuantType = quantType;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public DataType QuantType { get; }

    public static bool operator ==(GnneActionDmLoadWConfDeq lhs, GnneActionDmLoadWConfDeq rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.QuantType == rhs.QuantType;
    }

    public static bool operator !=(GnneActionDmLoadWConfDeq lhs, GnneActionDmLoadWConfDeq rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionDmLoadW : GnneAction
{
    public GnneActionDmLoadW(int tcuId, int puId, Gpr addrS, Gpr addrBw, Ssr iochannels, DM_LOAD_W_DEST destType, SegmentND sliceInfo)
        : base(GnneActionName.DmLoadW)
    {
        TcuId = tcuId;
        PuId = puId;
        AddrS = addrS;
        AddrBw = addrBw;
        Iochannels = iochannels;
        DestType = destType;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public Gpr AddrS { get; }

    public Gpr AddrBw { get; }

    public Ssr Iochannels { get; }

    public DM_LOAD_W_DEST DestType { get; }
}

/// <inheritdoc />
public class GnneActionPuFetchifConf1 : GnneAction
{
    public GnneActionPuFetchifConf1()
        : base(GnneActionName.PuFetchifConf1)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        StrideW = -1;
        StrideH = -1;
        StrideS = new Ssr(-1, -1, false);
    }

    public GnneActionPuFetchifConf1(int tcuId, int puId, int funct4, int strideW, int strideH, Ssr strideS)
        : base(GnneActionName.PuFetchifConf1)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        StrideW = strideW;
        StrideH = strideH;
        this.StrideS = strideS;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public int StrideW { get; }

    public int StrideH { get; }

    public Ssr StrideS { get; }

    public static bool operator ==(GnneActionPuFetchifConf1 lhs, GnneActionPuFetchifConf1 rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.StrideW == rhs.StrideW
               && lhs.StrideH == rhs.StrideH
               && lhs.StrideS.Value == rhs.StrideS.Value;
    }

    public static bool operator !=(GnneActionPuFetchifConf1 lhs, GnneActionPuFetchifConf1 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuFetchifConf2 : GnneAction
{
    public GnneActionPuFetchifConf2()
        : base(GnneActionName.PuFetchifConf2)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        Gic = new Gpr(-1, new DimConst(-1), false);
        GicLas = new Gpr(-1, new DimConst(-1), false);
    }

    public GnneActionPuFetchifConf2(int tcuId, int puId, int funct4, Gpr gic, Gpr gicLast)
        : base(GnneActionName.PuFetchifConf2)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        Gic = gic;
        GicLas = gicLast;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public Gpr Gic { get; }

    public Gpr GicLas { get; }

    public static bool operator ==(GnneActionPuFetchifConf2 lhs, GnneActionPuFetchifConf2 rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.Gic.Value == rhs.Gic.Value;
    }

    public static bool operator !=(GnneActionPuFetchifConf2 lhs, GnneActionPuFetchifConf2 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuFetchifConf3 : GnneAction
{
    public GnneActionPuFetchifConf3()
        : base(GnneActionName.PuFetchifConf3)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        AddrS = new Gpr(-1, new DimConst(-1), false);
        Groups = new Gpr(-1, new DimConst(-1), false);
        Shape = new Ssr(-1, -1, false);
    }

    public GnneActionPuFetchifConf3(int tcuId, int puId, int funct4, Gpr addrS, Gpr groups, Ssr shape)
        : base(GnneActionName.PuFetchifConf3)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        AddrS = addrS;
        Groups = groups;
        Shape = shape;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public Gpr AddrS { get; }

    public Gpr Groups { get; }

    public Ssr Shape { get; }

    public static bool operator ==(GnneActionPuFetchifConf3 lhs, GnneActionPuFetchifConf3 rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.AddrS.Value == rhs.AddrS.Value
               && lhs.Groups.Value == rhs.Groups.Value
               && lhs.Shape.Value == rhs.Shape.Value;
    }

    public static bool operator !=(GnneActionPuFetchifConf3 lhs, GnneActionPuFetchifConf3 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuFetchifConf4 : GnneAction
{
    public GnneActionPuFetchifConf4()
        : base(GnneActionName.PuFetchifConf4)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        PadValue = new Gpr(-1, new DimConst(-1), false);
        Pad = new Ssr(-1, -1, false);
    }

    public GnneActionPuFetchifConf4(int tcuId, int puId, int funct4, Gpr padValue, Ssr pad)
        : base(GnneActionName.PuFetchifConf4)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        PadValue = padValue;
        Pad = pad;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public Gpr PadValue { get; }

    public Ssr Pad { get; }

    public static bool operator ==(GnneActionPuFetchifConf4 lhs, GnneActionPuFetchifConf4 rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.PadValue.Value == rhs.PadValue.Value
               && lhs.Pad.Value == rhs.Pad.Value;
    }

    public static bool operator !=(GnneActionPuFetchifConf4 lhs, GnneActionPuFetchifConf4 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuFetchifConfDeq : GnneAction
{
    public GnneActionPuFetchifConfDeq()
        : base(GnneActionName.PuFetchifConfDeq)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        Ic = new Gpr(-1, new DimConst(-1), false);
        Bx = new Gpr(-1, new DimConst(-1), false);
        QuantType = DataTypes.UInt8;
    }

    public GnneActionPuFetchifConfDeq(int tcuId, int puId, int funct4, Gpr ic, Gpr bx, DataType quantType)
        : base(GnneActionName.PuFetchifConfDeq)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        Ic = ic;
        Bx = bx;
        QuantType = quantType;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public Gpr Ic { get; }

    public Gpr Bx { get; }

    public DataType QuantType { get; }

    public static bool operator ==(GnneActionPuFetchifConfDeq lhs, GnneActionPuFetchifConfDeq rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.Ic.Value == rhs.Ic.Value
               && lhs.Bx.Value == rhs.Bx.Value
               && lhs.QuantType == rhs.QuantType;
    }

    public static bool operator !=(GnneActionPuFetchifConfDeq lhs, GnneActionPuFetchifConfDeq rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuOfConf1 : GnneAction
{
    public GnneActionPuOfConf1()
        : base(GnneActionName.PuOfConf1)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        Goc = new Gpr(-1, new DimConst(-1), false);
        GocLast = new Gpr(-1, new DimConst(-1), false);
        StrideD = new Ssr(-1, -1, false);
    }

    public GnneActionPuOfConf1(int tcuId, int puId, int funct4, Gpr goc, Gpr gocLast, Ssr strideD)
        : base(GnneActionName.PuOfConf1)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        Goc = goc;
        GocLast = gocLast;
        StrideD = strideD;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public Gpr Goc { get; }

    public Gpr GocLast { get; }

    public Ssr StrideD { get; }

    public static bool operator ==(GnneActionPuOfConf1 lhs, GnneActionPuOfConf1 rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.Goc.Value == rhs.Goc.Value
               && lhs.StrideD.Value == rhs.StrideD.Value;
    }

    public static bool operator !=(GnneActionPuOfConf1 lhs, GnneActionPuOfConf1 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuOfConf2 : GnneAction
{
    public GnneActionPuOfConf2()
        : base(GnneActionName.PuOfConf2)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        AddrD = new Gpr(-1, new DimConst(-1), false);
        ShapeD = new Ssr(-1, -1, false);
    }

    public GnneActionPuOfConf2(int tcuId, int puId, int funct4, Gpr addrD, Ssr shapeD)
        : base(GnneActionName.PuOfConf2)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        AddrD = addrD;
        ShapeD = shapeD;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public Gpr AddrD { get; }

    public Ssr ShapeD { get; }

    public static bool operator ==(GnneActionPuOfConf2 lhs, GnneActionPuOfConf2 rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.AddrD.Value == rhs.AddrD.Value
               && lhs.ShapeD.Value == rhs.ShapeD.Value;
    }

    public static bool operator !=(GnneActionPuOfConf2 lhs, GnneActionPuOfConf2 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuComputeConf : GnneAction
{
    public GnneActionPuComputeConf()
        : base(GnneActionName.PuComputeConf)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        LoadPsum = false;
        ClrPsum = false;
        DestTarget = PU_OUTPUT_DEST.psum;
        ReleaseIf = false;
        Mode = PU_COMPUTE_MODE.pu_mode_normal;
    }

    public GnneActionPuComputeConf(int tcuId, int puId, int funct4, bool loadPsum, bool clrPsum, PU_OUTPUT_DEST destTarget, bool releaseIf, PU_COMPUTE_MODE mode)
        : base(GnneActionName.PuComputeConf)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        LoadPsum = loadPsum;
        ClrPsum = clrPsum;
        DestTarget = destTarget;
        ReleaseIf = releaseIf;
        Mode = mode;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public bool LoadPsum { get; }

    public bool ClrPsum { get; }

    public PU_OUTPUT_DEST DestTarget { get; }

    public bool ReleaseIf { get; }

    public PU_COMPUTE_MODE Mode { get; }

    public static bool operator ==(GnneActionPuComputeConf lhs, GnneActionPuComputeConf rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.LoadPsum == rhs.LoadPsum
               && lhs.ClrPsum == rhs.ClrPsum
               && lhs.DestTarget == rhs.DestTarget
               && lhs.ReleaseIf == rhs.ReleaseIf
               && lhs.Mode == rhs.Mode;
    }

    public static bool operator !=(GnneActionPuComputeConf lhs, GnneActionPuComputeConf rhs)
    {
        return !(lhs == rhs);
    }

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuForwardPsum : GnneAction
{
    public GnneActionPuForwardPsum(int tcuId, int puId, Gpr addr, Gpr len)
        : base(GnneActionName.PuForwardPsum)
    {
        TcuId = tcuId;
        PuId = puId;
        Addr = addr;
        Len = len;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public Gpr Addr { get; }

    public Gpr Len { get; }
}

/// <inheritdoc />
public class GnneActionPuCompute : GnneAction
{
    public GnneActionPuCompute(int tcuId, PU_OF_SHIFT_MODE mode)
        : base(GnneActionName.PuCompute)
    {
        TcuId = tcuId;
        Mode = mode;
    }

    public int TcuId { get; }

    public PU_OF_SHIFT_MODE Mode { get; }
}

/// <inheritdoc />
public class GnneActionDmLoadAct0 : GnneAction
{
    public GnneActionDmLoadAct0(int tcuId, int puId, Gpr addrS, Gpr len, ACT0_CHANNEL destChannel, bool isByChannel)
        : base(GnneActionName.DmLoadAct0)
    {
        TcuId = tcuId;
        PuId = puId;
        AddrS = addrS;
        Len = len;
        DestChannel = destChannel;
        IsByChannel = isByChannel;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public Gpr AddrS { get; }

    public Gpr Len { get; }

    public ACT0_CHANNEL DestChannel { get; }

    public bool IsByChannel { get; }
}

/// <inheritdoc />
public class GnneActionDmStoreOfConf : GnneAction
{
    public GnneActionDmStoreOfConf()
        : base(GnneActionName.DmStoreOfConf)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        StrideD = new Ssr(-1, -1, false);
        Datatype = DataTypes.UInt8;
    }

    public GnneActionDmStoreOfConf(int tcuId, int puId, int funct4, Ssr strideD, DataType datatype)
        : base(GnneActionName.DmStoreOfConf)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        StrideD = strideD;
        Datatype = datatype;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public Ssr StrideD { get; }

    public DataType Datatype { get; }

    public static bool operator ==(GnneActionDmStoreOfConf lhs, GnneActionDmStoreOfConf rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.StrideD.Value == rhs.StrideD.Value
               && lhs.Datatype == rhs.Datatype;
    }

    public static bool operator !=(GnneActionDmStoreOfConf lhs, GnneActionDmStoreOfConf rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionDmStoreOf : GnneAction
{
    public GnneActionDmStoreOf(int tcuId, int puId, Gpr addrD, Ssr shape, ACT0_CHANNEL srcChannel)
        : base(GnneActionName.DmStoreOf)
    {
        TcuId = tcuId;
        PuId = puId;
        AddrD = addrD;
        Shape = shape;
        SrcChannel = srcChannel;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public Gpr AddrD { get; }

    public Ssr Shape { get; }

    public ACT0_CHANNEL SrcChannel { get; }
}

/// <inheritdoc />
public class GnneActionAct0Src1Conf : GnneAction
{
    public GnneActionAct0Src1Conf()
        : base(GnneActionName.Act0Src1Conf)
    {
        TcuId = -1;
        PuId = -1;
        Funct3 = -1;
        Channel = ACT0_CHANNEL.pu;
        Shape = new Ssr(-1, -1, false);
        RshiftBits = -1;
    }

    public GnneActionAct0Src1Conf(int tcuId, int puId, int funct3, ACT0_CHANNEL channel, Ssr shape, int rshiftBits)
        : base(GnneActionName.Act0Src1Conf)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct3 = funct3;
        Channel = channel;
        Shape = shape;
        RshiftBits = rshiftBits;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct3 { get; }

    public ACT0_CHANNEL Channel { get; }

    public Ssr Shape { get; }

    public int RshiftBits { get; }

    public static bool operator ==(GnneActionAct0Src1Conf lhs, GnneActionAct0Src1Conf rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct3 == rhs.Funct3
               && lhs.Channel == rhs.Channel
               && lhs.Shape.Value == rhs.Shape.Value
               && lhs.RshiftBits == rhs.RshiftBits;
    }

    public static bool operator !=(GnneActionAct0Src1Conf lhs, GnneActionAct0Src1Conf rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionAct0Compute : GnneAction
{
    public GnneActionAct0Compute(int tcuId, Gpr addrD, ACT0_CHANNEL channel, ACT0_OUTPUT_DEST target, DataType destDatatype, bool isByChannel)
        : base(GnneActionName.Act0Compute)
    {
        TcuId = tcuId;
        AddrD = addrD;
        Channel = channel;
        Target = target;
        DestDatatype = destDatatype;
        IsByChannel = isByChannel;
    }

    public int TcuId { get; }

    public Gpr AddrD { get; }

    public ACT0_CHANNEL Channel { get; }

    public ACT0_OUTPUT_DEST Target { get; }

    public DataType DestDatatype { get; }

    public bool IsByChannel { get; }
}

/// <inheritdoc />
public class GnneActionMfuAct1ConfStride : GnneAction
{
    public GnneActionMfuAct1ConfStride()
        : base(GnneActionName.MfuAct1ConfStride)
    {
        Funct5 = -1;
        StrideS1 = new Ssr(-1, -1, false);
        StrideS2 = new Ssr(-1, -1, false);
        StrideD1 = new Ssr(-1, -1, false);
    }

    public GnneActionMfuAct1ConfStride(int funct5, Ssr strideS1, Ssr strideS2, Ssr strideD1)
        : base(GnneActionName.MfuAct1ConfStride)
    {
        Funct5 = funct5;
        StrideS1 = strideS1;
        StrideS2 = strideS2;
        StrideD1 = strideD1;
    }

    public int Funct5 { get; }

    public Ssr StrideS1 { get; }

    public Ssr StrideS2 { get; }

    public Ssr StrideD1 { get; }

    public static bool operator ==(GnneActionMfuAct1ConfStride lhs, GnneActionMfuAct1ConfStride rhs)
    {
        return lhs.Funct5 == rhs.Funct5
               && lhs.StrideS1.Value == rhs.StrideS1.Value
               && lhs.StrideS2.Value == rhs.StrideS2.Value
               && lhs.StrideD1.Value == rhs.StrideD1.Value;
    }

    public static bool operator !=(GnneActionMfuAct1ConfStride lhs, GnneActionMfuAct1ConfStride rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionMfuAct1ConfSrc1 : GnneAction
{
    public GnneActionMfuAct1ConfSrc1()
        : base(GnneActionName.MfuAct1ConfSrc1)
    {
        Funct5 = -1;
        Slice = new Gpr(-1, new DimConst(-1), false);
        RightRepeats = new Gpr(-1, new DimConst(-1), false);
        SliceRepeats = new Gpr(-1, new DimConst(-1), false);
        Sid = -1;
        SliceLoc = -1;
    }

    public GnneActionMfuAct1ConfSrc1(int funct5, Gpr slice, Gpr rightRepeats, Gpr sliceRepeats, int sid, int sliceLoc)
        : base(GnneActionName.MfuAct1ConfSrc1)
    {
        Funct5 = funct5;
        Slice = slice;
        RightRepeats = rightRepeats;
        SliceRepeats = sliceRepeats;
        Sid = sid;
        SliceLoc = sliceLoc;
    }

    public int Funct5 { get; }

    public Gpr Slice { get; }

    public Gpr RightRepeats { get; }

    public Gpr SliceRepeats { get; }

    public int Sid { get; }

    public int SliceLoc { get; }

    public static bool operator ==(GnneActionMfuAct1ConfSrc1 lhs, GnneActionMfuAct1ConfSrc1 rhs)
    {
        return lhs.Funct5 == rhs.Funct5
               && lhs.Slice.Value == rhs.Slice.Value
               && lhs.RightRepeats.Value == rhs.RightRepeats.Value
               && lhs.SliceRepeats.Value == rhs.SliceRepeats.Value
               && lhs.Sid == rhs.Sid
               && lhs.SliceLoc == rhs.SliceLoc;
    }

    public static bool operator !=(GnneActionMfuAct1ConfSrc1 lhs, GnneActionMfuAct1ConfSrc1 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionMfuAct1ConfSrc2 : GnneAction
{
    public GnneActionMfuAct1ConfSrc2()
        : base(GnneActionName.MfuAct1ConfSrc2)
    {
        Funct5 = -1;
        LeftRepeats = new Gpr(-1, new DimConst(-1), false);
        Shape = new Ssr(-1, -1, false);
        Sid = -1;
        SourceType = ACT1_SOURCE_TYPE.l2;
    }

    public GnneActionMfuAct1ConfSrc2(int funct5, Gpr leftRepeats, Ssr shape, int sid, ACT1_SOURCE_TYPE sourceType)
        : base(GnneActionName.MfuAct1ConfSrc2)
    {
        Funct5 = funct5;
        LeftRepeats = leftRepeats;
        Shape = shape;
        Sid = sid;
        SourceType = sourceType;
    }

    public int Funct5 { get; }

    public Gpr LeftRepeats { get; }

    public Ssr Shape { get; }

    public int Sid { get; }

    public ACT1_SOURCE_TYPE SourceType { get; }

    public static bool operator ==(GnneActionMfuAct1ConfSrc2 lhs, GnneActionMfuAct1ConfSrc2 rhs)
    {
        return lhs.Funct5 == rhs.Funct5
               && lhs.LeftRepeats.Value == rhs.LeftRepeats.Value
               && lhs.Shape.Value == rhs.Shape.Value
               && lhs.Sid == rhs.Sid
               && lhs.SourceType == rhs.SourceType;
    }

    public static bool operator !=(GnneActionMfuAct1ConfSrc2 lhs, GnneActionMfuAct1ConfSrc2 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionMfuAct1ConfDest : GnneAction
{
    private Ssr _shape;

    public GnneActionMfuAct1ConfDest()
        : base(GnneActionName.MfuAct1ConfDest)
    {
        Funct5 = -1;
        Len = new Gpr(-1, new DimConst(-1), false);
        _shape = new Ssr(-1, -1, false);
    }

    public GnneActionMfuAct1ConfDest(int funct5, Gpr len, Ssr shape)
        : base(GnneActionName.MfuAct1ConfDest)
    {
        Funct5 = funct5;
        Len = len;
        _shape = shape;
    }

    public int Funct5 { get; }

    public Gpr Len { get; }

    public Ssr Shape => _shape;

    public static bool operator ==(GnneActionMfuAct1ConfDest lhs, GnneActionMfuAct1ConfDest rhs)
    {
        return lhs.Funct5 == rhs.Funct5
               && lhs._shape.Value == rhs.Shape.Value
               && lhs.Len.Value == rhs.Len.Value;
    }

    public static bool operator !=(GnneActionMfuAct1ConfDest lhs, GnneActionMfuAct1ConfDest rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionMfuAct1ConfDeq : GnneAction
{
    public GnneActionMfuAct1ConfDeq()
        : base(GnneActionName.MfuAct1ConfDeq)
    {
        Funct5 = -1;
        Scale = new Gpr(-1, new DimConst(1), false);
        Bias = new Gpr(-1, new DimConst(1), false);
        QuantType = DataTypes.UInt8;
        Sid = -1;
        RshiftBits = -1;
    }

    public GnneActionMfuAct1ConfDeq(int funct5, Gpr scale, Gpr bias, DataType quantType, int sid, int rshiftBits)
        : base(GnneActionName.MfuAct1ConfDeq)
    {
        Funct5 = funct5;
        Scale = scale;
        Bias = bias;
        QuantType = quantType;
        Sid = sid;
        RshiftBits = rshiftBits;
    }

    public int Funct5 { get; }

    public Gpr Scale { get; }

    public Gpr Bias { get; }

    public DataType QuantType { get; }

    public int Sid { get; }

    public int RshiftBits { get; }

    public static bool operator ==(GnneActionMfuAct1ConfDeq lhs, GnneActionMfuAct1ConfDeq rhs)
    {
        return lhs.Funct5 == rhs.Funct5
               && lhs.Scale.Value == rhs.Scale.Value
               && lhs.Bias.Value == rhs.Bias.Value
               && lhs.QuantType == rhs.QuantType
               && lhs.Sid == rhs.Sid
               && lhs.RshiftBits == rhs.RshiftBits;
    }

    public static bool operator !=(GnneActionMfuAct1ConfDeq lhs, GnneActionMfuAct1ConfDeq rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionMfuAct1ConfQuant : GnneAction
{
    public GnneActionMfuAct1ConfQuant()
        : base(GnneActionName.MfuAct1ConfQuant)
    {
        Funct5 = -1;
        QuantType = DataTypes.UInt8;
        RshiftBits = -1;
    }

    public GnneActionMfuAct1ConfQuant(int funct5, DataType quant_type, int rshift_bits)
        : base(GnneActionName.MfuAct1ConfQuant)
    {
        Funct5 = funct5;
        QuantType = quant_type;
        RshiftBits = rshift_bits;
    }

    public int Funct5 { get; }

    public DataType QuantType { get; }

    public int RshiftBits { get; }

    public static bool operator ==(GnneActionMfuAct1ConfQuant lhs, GnneActionMfuAct1ConfQuant rhs)
    {
        return lhs.Funct5 == rhs.Funct5
               && lhs.QuantType == rhs.QuantType
               && lhs.RshiftBits == rhs.RshiftBits;
    }

    public static bool operator !=(GnneActionMfuAct1ConfQuant lhs, GnneActionMfuAct1ConfQuant rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionMfuAct1Conf : GnneAction
{
    public GnneActionMfuAct1Conf()
        : base(GnneActionName.MfuAct1Conf)
    {
        Funct5 = -1;
        Funct4 = MFU_ACT1_FUNCTION.add;
        IsByChannel = false;
        Is16Segments = false;
    }

    public GnneActionMfuAct1Conf(int funct5, MFU_ACT1_FUNCTION funct4, bool is_by_channel, bool is_16_segments)
        : base(GnneActionName.MfuAct1Conf)
    {
        Funct5 = funct5;
        Funct4 = funct4;
        IsByChannel = is_by_channel;
        Is16Segments = is_16_segments;
    }

    public int Funct5 { get; }

    public MFU_ACT1_FUNCTION Funct4 { get; }

    public bool IsByChannel { get; }

    public bool Is16Segments { get; }

    public static bool operator ==(GnneActionMfuAct1Conf lhs, GnneActionMfuAct1Conf rhs)
    {
        return lhs.Funct5 == rhs.Funct5
               && lhs.Funct4 == rhs.Funct4
               && lhs.IsByChannel == rhs.IsByChannel
               && lhs.Is16Segments == rhs.Is16Segments;
    }

    public static bool operator !=(GnneActionMfuAct1Conf lhs, GnneActionMfuAct1Conf rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionMfuAct1Compute : GnneAction
{
    public GnneActionMfuAct1Compute(Gpr addrD1, Gpr addrS1, Gpr addrS2, Gpr addrArg)
        : base(GnneActionName.MfuAct1Compute)
    {
        AddrD1 = addrD1;
        AddrS1 = addrS1;
        AddrS2 = addrS2;
        AddrArg = addrArg;
    }

    public Gpr AddrD1 { get; }

    public Gpr AddrS1 { get; }

    public Gpr AddrS2 { get; }

    public Gpr AddrArg { get; }
}

/// <inheritdoc />
public class GnneActionMfuTransposeConf : GnneAction
{
    public GnneActionMfuTransposeConf()
        : base(GnneActionName.MfuTransposeConf)
    {
        Funct5 = -1;
        StrideD = new Ssr(-1, -1, false);
        StrideS = new Ssr(-1, -1, false);
        L2Datatype = DataTypes.UInt8;
        Permute = MFU_TRANS_PERMUTE.CHNW;
    }

    public GnneActionMfuTransposeConf(int funct5, Ssr strideD, Ssr strideS, DataType l2Datatype, MFU_TRANS_PERMUTE permute)
        : base(GnneActionName.MfuTransposeConf)
    {
        Funct5 = funct5;
        StrideD = strideD;
        StrideS = strideS;
        L2Datatype = l2Datatype;
        Permute = permute;
    }

    public int Funct5 { get; }

    public DataType L2Datatype { get; }

    public MFU_TRANS_PERMUTE Permute { get; }

    public Ssr StrideD { get; }

    public Ssr StrideS { get; }

    public static bool operator ==(GnneActionMfuTransposeConf lhs, GnneActionMfuTransposeConf rhs)
    {
        return lhs.Funct5 == rhs.Funct5
               && lhs.StrideD.Value == rhs.StrideD.Value
               && lhs.StrideS.Value == rhs.StrideS.Value
               && lhs.L2Datatype == rhs.L2Datatype
               && lhs.Permute == rhs.Permute;
    }

    public static bool operator !=(GnneActionMfuTransposeConf lhs, GnneActionMfuTransposeConf rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionMfuTranspose : GnneAction
{
    public GnneActionMfuTranspose(Gpr addrD, Gpr addrS, Ssr shape)
        : base(GnneActionName.MfuTranspose)
    {
        AddrD = addrD;
        AddrS = addrS;
        Shape = shape;
    }

    public Gpr AddrD { get; }

    public Gpr AddrS { get; }

    public Ssr Shape { get; }
}

/// <inheritdoc />
public class GnneActionMfuPdp1Conf1 : GnneAction
{
    public GnneActionMfuPdp1Conf1()
        : base(GnneActionName.MfuPdp1Conf1)
    {
        Funct5 = -1;
        StrideW = -1;
        StrideH = -1;
        StrideD = new Ssr(-1, -1, false);
        StrideS = new Ssr(-1, -1, false);
        Funct2 = PDP_FUNCTION.max;
    }

    public GnneActionMfuPdp1Conf1(int funct5, int strideW, int strideH, Ssr strideD, Ssr strideS, PDP_FUNCTION funct2)
        : base(GnneActionName.MfuPdp1Conf1)
    {
        Funct5 = funct5;
        StrideW = strideW;
        StrideH = strideH;
        StrideD = strideD;
        StrideS = strideS;
        Funct2 = funct2;
    }

    public int Funct5 { get; }

    public int StrideW { get; }

    public int StrideH { get; }

    public PDP_FUNCTION Funct2 { get; }

    public Ssr StrideD { get; }

    public Ssr StrideS { get; }

    public static bool operator ==(GnneActionMfuPdp1Conf1 lhs, GnneActionMfuPdp1Conf1 rhs)
    {
        return lhs.Funct5 == rhs.Funct5
               && lhs.StrideW == rhs.StrideW
               && lhs.StrideH == rhs.StrideH
               && lhs.StrideD.Value == rhs.StrideD.Value
               && lhs.StrideS.Value == rhs.StrideS.Value
               && lhs.Funct2 == rhs.Funct2;
    }

    public static bool operator !=(GnneActionMfuPdp1Conf1 lhs, GnneActionMfuPdp1Conf1 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionMfuPdp1Conf2 : GnneAction
{
    public GnneActionMfuPdp1Conf2()
        : base(GnneActionName.MfuPdp1Conf2)
    {
        Funct5 = -1;
        CountW = new Gpr(-1, new DimConst(-1), false);
        CountH = new Gpr(-1, new DimConst(-1), false);
        PeH = new Gpr(-1, new DimConst(-1), false);
        PeLastH = new Gpr(-1, new DimConst(-1), false);
    }

    public GnneActionMfuPdp1Conf2(int funct5, Gpr countW, Gpr countH, Gpr peH, Gpr peLastH)
        : base(GnneActionName.MfuPdp1Conf2)
    {
        Funct5 = funct5;
        CountW = countW;
        CountH = countH;
        PeH = peH;
        PeLastH = peLastH;
    }

    public int Funct5 { get; }

    public Gpr CountW { get; }

    public Gpr CountH { get; }

    public Gpr PeH { get; }

    public Gpr PeLastH { get; }

    public static bool operator ==(GnneActionMfuPdp1Conf2 lhs, GnneActionMfuPdp1Conf2 rhs)
    {
        return lhs.Funct5 == rhs.Funct5
               && lhs.CountW.Value == rhs.CountW.Value
               && lhs.CountH.Value == rhs.CountH.Value
               && lhs.PeH.Value == rhs.PeH.Value
               && lhs.PeLastH.Value == rhs.PeLastH.Value;
    }

    public static bool operator !=(GnneActionMfuPdp1Conf2 lhs, GnneActionMfuPdp1Conf2 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionMfuPdp1Conf3 : GnneAction
{
    public GnneActionMfuPdp1Conf3()
        : base(GnneActionName.MfuPdp1Conf3)
    {
        Funct5 = -1;
        PeChannels = new Gpr(-1, new DimConst(-1), false);
        PeLastChannels = new Gpr(-1, new DimConst(-1), false);
        PadValue = new Gpr(-1, new DimConst(-1), false);
        Pad = new Ssr(-1, -1, false);
    }

    public GnneActionMfuPdp1Conf3(int funct5, Gpr peChannels, Gpr peLastChannels, Gpr padValue, Ssr pad)
        : base(GnneActionName.MfuPdp1Conf3)
    {
        Funct5 = funct5;
        PeChannels = peChannels;
        PeLastChannels = peLastChannels;
        PadValue = padValue;
        Pad = pad;
    }

    public int Funct5 { get; }

    public Gpr PeChannels { get; }

    public Gpr PeLastChannels { get; }

    public Gpr PadValue { get; }

    public Ssr Pad { get; }

    public static bool operator ==(GnneActionMfuPdp1Conf3 lhs, GnneActionMfuPdp1Conf3 rhs)
    {
        return lhs.Funct5 == rhs.Funct5
               && lhs.PeChannels.Value == rhs.PeChannels.Value
               && lhs.PeLastChannels.Value == rhs.PeLastChannels.Value
               && lhs.PadValue.Value == rhs.PadValue.Value
               && lhs.Pad.Value == rhs.Pad.Value;
    }

    public static bool operator !=(GnneActionMfuPdp1Conf3 lhs, GnneActionMfuPdp1Conf3 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionMfuPdp1Conf4 : GnneAction
{
    public GnneActionMfuPdp1Conf4()
        : base(GnneActionName.MfuPdp1Conf4)
    {
        Funct5 = -1;
        WindowW = new Gpr(-1, new DimConst(-1), false);
        WindowH = new Gpr(-1, new DimConst(-1), false);
        Scale = new Gpr(-1, new DimConst(-1), false);
        EnableH2C = false;
        EnableBw = false;
    }

    public GnneActionMfuPdp1Conf4(int funct5, Gpr windowW, Gpr windowH, Gpr scale, bool enableH2C, bool enableBw)
        : base(GnneActionName.MfuPdp1Conf4)
    {
        Funct5 = funct5;
        WindowW = windowW;
        WindowH = windowH;
        Scale = scale;
        EnableH2C = enableH2C;
        EnableBw = enableBw;
    }

    public int Funct5 { get; }

    public Gpr WindowW { get; }

    public Gpr WindowH { get; }

    public Gpr Scale { get; }

    public bool EnableH2C { get; }

    public bool EnableBw { get; }

    public static bool operator ==(GnneActionMfuPdp1Conf4 lhs, GnneActionMfuPdp1Conf4 rhs)
    {
        return lhs.Funct5 == rhs.Funct5
               && lhs.WindowW.Value == rhs.WindowW.Value
               && lhs.WindowH.Value == rhs.WindowH.Value
               && lhs.Scale.Value == rhs.Scale.Value
               && lhs.EnableH2C == rhs.EnableH2C
               && lhs.EnableBw == rhs.EnableBw;
    }

    public static bool operator !=(GnneActionMfuPdp1Conf4 lhs, GnneActionMfuPdp1Conf4 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionMfuPdp1ConfDeq : GnneAction
{
    public GnneActionMfuPdp1ConfDeq()
        : base(GnneActionName.MfuPdp1ConfDeq)
    {
        Funct5 = -1;
        Scale = new Gpr(-1, new DimConst(1), false);
        Bias = new Gpr(-1, new DimConst(-1), false);
        QuantType = DataTypes.UInt8;
        RshiftBits = -1;
    }

    public GnneActionMfuPdp1ConfDeq(int funct5, Gpr scale, Gpr bias, DataType quantType, int rshiftBits)
        : base(GnneActionName.MfuPdp1ConfDeq)
    {
        Funct5 = funct5;
        Scale = scale;
        Bias = bias;
        QuantType = quantType;
        RshiftBits = rshiftBits;
    }

    public int Funct5 { get; }

    public Gpr Scale { get; }

    public Gpr Bias { get; }

    public DataType QuantType { get; }

    public int RshiftBits { get; }

    public static bool operator ==(GnneActionMfuPdp1ConfDeq lhs, GnneActionMfuPdp1ConfDeq rhs)
    {
        return lhs.Funct5 == rhs.Funct5
               && lhs.Scale.Value == rhs.Scale.Value
               && lhs.Bias.Value == rhs.Bias.Value
               && lhs.QuantType == rhs.QuantType
               && lhs.RshiftBits == rhs.RshiftBits;
    }

    public static bool operator !=(GnneActionMfuPdp1ConfDeq lhs, GnneActionMfuPdp1ConfDeq rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionMfuPdp1ConfQuant : GnneAction
{
    public GnneActionMfuPdp1ConfQuant()
        : base(GnneActionName.MfuPdp1ConfQuant)
    {
        Funct5 = -1;
        Scale = new Gpr(-1, new DimConst(-1), false);
        Bias = new Gpr(-1, new DimConst(-1), false);
        QuantType = DataTypes.UInt8;
        RshiftBits = -1;
    }

    public GnneActionMfuPdp1ConfQuant(int funct5, Gpr scale, Gpr bias, DataType quant_type, int rshift_bits)
        : base(GnneActionName.MfuPdp1ConfQuant)
    {
        Funct5 = funct5;
        Scale = scale;
        Bias = bias;
        QuantType = quant_type;
        RshiftBits = rshift_bits;
    }

    public int Funct5 { get; }

    public Gpr Scale { get; }

    public Gpr Bias { get; }

    public DataType QuantType { get; }

    public int RshiftBits { get; }

    public static bool operator ==(GnneActionMfuPdp1ConfQuant lhs, GnneActionMfuPdp1ConfQuant rhs)
    {
        return lhs.Funct5 == rhs.Funct5
               && lhs.Scale.Value == rhs.Scale.Value
               && lhs.Bias.Value == rhs.Bias.Value
               && lhs.QuantType == rhs.QuantType
               && lhs.RshiftBits == rhs.RshiftBits;
    }

    public static bool operator !=(GnneActionMfuPdp1ConfQuant lhs, GnneActionMfuPdp1ConfQuant rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionMfuPdp1Compute : GnneAction
{
    public GnneActionMfuPdp1Compute(Gpr addrD, Gpr addrS, Ssr shape)
        : base(GnneActionName.MfuPdp1Compute)
    {
        AddrD = addrD;
        AddrS = addrS;
        Shape = shape;
    }

    public Gpr AddrD { get; }

    public Gpr AddrS { get; }

    public Ssr Shape { get; }
}

/// <inheritdoc />
public class GnneActionMfuMemset : GnneAction
{
    public GnneActionMfuMemset(Gpr addrD, Gpr value, Ssr stride, Ssr shape, DataType l2Datatype)
        : base(GnneActionName.MfuMemset)
    {
        AddrD = addrD;
        Value = value;
        Stride = stride;
        Shape = shape;
        L2Datatype = l2Datatype;
    }

    public Gpr AddrD { get; }

    public Gpr Value { get; }

    public Ssr Stride { get; }

    public Ssr Shape { get; }

    public DataType L2Datatype { get; }
}

/// <inheritdoc />
public class GnneActionPuPdp0WConf : GnneAction
{
    public GnneActionPuPdp0WConf()
        : base(GnneActionName.PuPdp0WConf)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        KernelH = -1;
        KernelW = -1;
    }

    public GnneActionPuPdp0WConf(int tcuId, int puId, int funct4, int kernelH, int kernelW)
        : base(GnneActionName.PuPdp0WConf)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        KernelH = kernelH;
        KernelW = kernelW;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public int KernelH { get; }

    public int KernelW { get; }

    public static bool operator ==(GnneActionPuPdp0WConf lhs, GnneActionPuPdp0WConf rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.KernelH == rhs.KernelH
               && lhs.KernelW == rhs.KernelW;
    }

    public static bool operator !=(GnneActionPuPdp0WConf lhs, GnneActionPuPdp0WConf rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuPdp0ModeConf : GnneAction
{
    public GnneActionPuPdp0ModeConf()
        : base(GnneActionName.PuPdp0ModeConf)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        Mode = PU_PDP0_MODE.dw;
    }

    public GnneActionPuPdp0ModeConf(int tcuId, int puId, int funct4, PU_PDP0_MODE mode)
        : base(GnneActionName.PuPdp0ModeConf)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        Mode = mode;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public PU_PDP0_MODE Mode { get; }

    public static bool operator ==(GnneActionPuPdp0ModeConf lhs, GnneActionPuPdp0ModeConf rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.Mode == rhs.Mode;
    }

    public static bool operator !=(GnneActionPuPdp0ModeConf lhs, GnneActionPuPdp0ModeConf rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuPdp0FetchifConf1 : GnneAction
{
    public GnneActionPuPdp0FetchifConf1()
        : base(GnneActionName.PuPdp0FetchifConf1)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        StrideW = -1;
        StrideH = -1;
    }

    public GnneActionPuPdp0FetchifConf1(int tcuId, int puId, int funct4, int strideW, int strideH)
        : base(GnneActionName.PuPdp0FetchifConf1)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        StrideW = strideW;
        StrideH = strideH;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public int StrideW { get; }

    public int StrideH { get; }

    public static bool operator ==(GnneActionPuPdp0FetchifConf1 lhs, GnneActionPuPdp0FetchifConf1 rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.StrideW == rhs.StrideW
               && lhs.StrideH == rhs.StrideH;
    }

    public static bool operator !=(GnneActionPuPdp0FetchifConf1 lhs, GnneActionPuPdp0FetchifConf1 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuPdp0FetchifConf2 : GnneAction
{
    public GnneActionPuPdp0FetchifConf2()
        : base(GnneActionName.PuPdp0FetchifConf2)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        Gic = new Gpr(-1, new DimConst(-1), false);
        GicLast = new Gpr(-1, new DimConst(-1), false);
    }

    public GnneActionPuPdp0FetchifConf2(int tcuId, int puId, int funct4, Gpr gic, Gpr gicLast)
        : base(GnneActionName.PuPdp0FetchifConf2)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        Gic = gic;
        GicLast = gicLast;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public Gpr Gic { get; }

    public Gpr GicLast { get; }

    public static bool operator ==(GnneActionPuPdp0FetchifConf2 lhs, GnneActionPuPdp0FetchifConf2 rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.Gic.Value == rhs.Gic.Value
               && lhs.GicLast.Value == rhs.GicLast.Value;
    }

    public static bool operator !=(GnneActionPuPdp0FetchifConf2 lhs, GnneActionPuPdp0FetchifConf2 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuPdp0FetchifConf3 : GnneAction
{
    public GnneActionPuPdp0FetchifConf3()
        : base(GnneActionName.PuPdp0FetchifConf3)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        Shape = new Ssr(-1, -1, false);
    }

    public GnneActionPuPdp0FetchifConf3(int tcuId, int puId, int funct4, Ssr shape)
        : base(GnneActionName.PuPdp0FetchifConf3)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        Shape = shape;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public Ssr Shape { get; }

    public static bool operator ==(GnneActionPuPdp0FetchifConf3 lhs, GnneActionPuPdp0FetchifConf3 rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.Shape.Value == rhs.Shape.Value;
    }

    public static bool operator !=(GnneActionPuPdp0FetchifConf3 lhs, GnneActionPuPdp0FetchifConf3 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuPdp0FetchifConf4 : GnneAction
{
    public GnneActionPuPdp0FetchifConf4()
        : base(GnneActionName.PuPdp0FetchifConf4)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        PadValue = new Gpr(-1, new DimConst(-1), false);
        Pad = new Ssr(-1, -1, false);
    }

    public GnneActionPuPdp0FetchifConf4(int tcuId, int puId, int funct4, Gpr padValue, Ssr pad)
        : base(GnneActionName.PuPdp0FetchifConf4)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        PadValue = padValue;
        Pad = pad;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public Gpr PadValue { get; }

    public Ssr Pad { get; }

    public static bool operator ==(GnneActionPuPdp0FetchifConf4 lhs, GnneActionPuPdp0FetchifConf4 rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.PadValue.Value == rhs.PadValue.Value
               && lhs.Pad.Value == rhs.Pad.Value;
    }

    public static bool operator !=(GnneActionPuPdp0FetchifConf4 lhs, GnneActionPuPdp0FetchifConf4 rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuPdp0ConfDeq : GnneAction
{
    public GnneActionPuPdp0ConfDeq()
        : base(GnneActionName.PuPdp0ConfDeq)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        Bx = new Gpr(-1, new DimConst(-1), false);
        QuantType = DataTypes.UInt8;
    }

    public GnneActionPuPdp0ConfDeq(int tcuId, int puId, int funct4, Gpr bx, DataType quantType)
        : base(GnneActionName.PuPdp0ConfDeq)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        Bx = bx;
        QuantType = quantType;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public Gpr Bx { get; }

    public DataType QuantType { get; }

    public static bool operator ==(GnneActionPuPdp0ConfDeq lhs, GnneActionPuPdp0ConfDeq rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.Bx.Value == rhs.Bx.Value
               && lhs.QuantType == rhs.QuantType;
    }

    public static bool operator !=(GnneActionPuPdp0ConfDeq lhs, GnneActionPuPdp0ConfDeq rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuPdp0OfConf : GnneAction
{
    public GnneActionPuPdp0OfConf()
        : base(GnneActionName.PuPdp0OfConf)
    {
        TcuId = -1;
        PuId = -1;
        Funct4 = -1;
        StrideD = new Ssr(-1, -1, false);
        ShapeD = new Ssr(-1, -1, false);
    }

    public GnneActionPuPdp0OfConf(int tcuId, int puId, int funct4, Ssr strideD, Ssr shapeD)
        : base(GnneActionName.PuPdp0OfConf)
    {
        TcuId = tcuId;
        PuId = puId;
        Funct4 = funct4;
        StrideD = strideD;
        ShapeD = shapeD;
    }

    public int TcuId { get; }

    public int PuId { get; }

    public int Funct4 { get; }

    public Ssr StrideD { get; }

    public Ssr ShapeD { get; }

    public static bool operator ==(GnneActionPuPdp0OfConf lhs, GnneActionPuPdp0OfConf rhs)
    {
        return lhs.TcuId == rhs.TcuId
               && lhs.PuId == rhs.PuId
               && lhs.Funct4 == rhs.Funct4
               && lhs.StrideD.Value == rhs.StrideD.Value
               && lhs.ShapeD.Value == rhs.ShapeD.Value;
    }

    public static bool operator !=(GnneActionPuPdp0OfConf lhs, GnneActionPuPdp0OfConf rhs) => !(lhs == rhs);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (ReferenceEquals(obj, null))
        {
            return false;
        }

        throw new NotImplementedException();
    }
}

/// <inheritdoc />
public class GnneActionPuPdp0Compute : GnneAction
{
    public GnneActionPuPdp0Compute(int tcuId, Gpr addrS)
        : base(GnneActionName.PuPdp0Compute)
    {
        TcuId = tcuId;
        AddrS = addrS;
    }

    public int TcuId { get; }

    public Gpr AddrS { get; }
}

/// <inheritdoc />
public class GnneActionAi2dCompute : GnneAction
{
    public GnneActionAi2dCompute()
        : base(GnneActionName.Ai2dCompute)
    {
    }
}

/// <inheritdoc />
public class GnneActionExtrw : GnneAction
{
    public GnneActionExtrw(int extrd, Gpr value, int rs, int imm)
        : base(GnneActionName.Extrw)
    {
        Extrd = extrd;
        S_value = value;
        Rs = rs;
        Imm = imm;
    }

    public int Extrd { get; }

    public Gpr S_value { get; }

    public int Rs { get; }

    public int Imm { get; }
}

/// <inheritdoc />
public class GnneActionExtraw : GnneAction
{
    public GnneActionExtraw(int extrd, Gpr value, int rs, int imm)
        : base(GnneActionName.Extraw)
    {
        Extrd = extrd;
        Value = value;
        Rs = rs;
        Imm = imm;
    }

    public int Extrd { get; }

    public Gpr Value { get; }

    public int Rs { get; }

    public int Imm { get; }
}

public class DdrBandwidth
{
    private long[] _ofBw;

    public DdrBandwidth()
    {
        IfBw = new long[] { 0, 0 };
        WBw = new long[] { 0, 0 };
        _ofBw = new long[] { 0, 0 };
        OtherBw = new long[] { 0, 0 };
        TotalBw = new long[] { 0, 0 };
    }

    public long[] TotalBw { get; private set; }

    public long[] IfBw { get; private set; }

    public long[] WBw { get; private set; }

    public long[] OtherBw { get; private set; }

    public static int GetBytesPerElement(DataType type) => type switch
    {
        { } x when x == DataTypes.Int8 || x == DataTypes.UInt8 => 1,
        { } x when x == DataTypes.BFloat16 || x == DataTypes.Int16 || x == DataTypes.Float16 => 2,
        { } x when x == DataTypes.Float32 || x == DataTypes.UInt32 || x == DataTypes.Int32 => 4,
        _ => throw new ArgumentOutOfRangeException(type.GetDisplayName()),
    };

    public void CalcBw(GnneAction action)
    {
        long wDataSizeLast = 0;
        switch (action.Name)
        {
            case GnneActionName.L2Load:
                {
                    var l2Load = (GnneActionL2Load)action;
                    var length = l2Load.SliceInfo.Shape_size;
                    var ld = l2Load.Input[GNNELoad.Input];
                    var type = ld.CheckedDataType;
                    int bytesPerElement = GetBytesPerElement(type);
                    IfBw[0] += length * bytesPerElement;
                    TotalBw[0] += length * bytesPerElement;
                    break;
                }

            case GnneActionName.L2LoadWConf: // we cannnot cal bw as to conf for inst opt
                {
                    var l2LoadWCfg = (GnneActionL2LoadWConf)action;
                    var type = l2LoadWCfg.DdrDatatype;
                    int bytesPerElement = GetBytesPerElement(type);
                    int length = ((TensorConst)l2LoadWCfg.LenCompressed.Value).Value.ToScalar<int>();
                    wDataSizeLast = length * bytesPerElement;
                    break;
                }

            case GnneActionName.L2LoadW:
                {
                    WBw[0] += wDataSizeLast;
                    TotalBw[0] += wDataSizeLast;
                    break;
                }

            case GnneActionName.L2Store:
                {
                    var l2Store = (GnneActionL2Store)action;
                    var st = l2Store.Output;
                    var type = st.CheckedDataType;
                    int bytesPerElement = GetBytesPerElement(type);
                    int length = l2Store.SliceInfo.Shape_size;
                    _ofBw[0] += length * bytesPerElement;
                    TotalBw[0] += length * bytesPerElement;
                    break;
                }
        }
    }

    public void ResetBw()
    {
        IfBw = new long[] { 0, 0 };
        WBw = new long[] { 0, 0 };
        _ofBw = new long[] { 0, 0 };
        OtherBw = new long[] { 0, 0 };
        TotalBw = new long[] { 0, 0 };
    }
}

public class ConfActions
{
    public Gnne_action_l2_load_conf? L2LoadConf { get; set; }

    public GnneActionL2LoadWConf? L2LoadWConf { get; set; }

    public GnneActionL2StoreConf? L2StoreConf { get; set; }

    public GnneActionDmLoadL1Conf? DmLoadL1Conf { get; set; }

    public GnneActionDmLoadWConf? DmLoadWConf { get; set; }

    public GnneActionDmLoadWConf2? DmLoadWConf2 { get; set; }

    public GnneActionPuWConf? PuWConf { get; set; }

    public GnneActionDmLoadWConfDeq? DmLoadWConfDeq { get; set; }

    public GnneActionPuFetchifConf1? FetchifConf1 { get; set; }

    public GnneActionPuFetchifConf2? FetchifConf2 { get; set; }

    public GnneActionPuFetchifConf3? FetchifConf3 { get; set; }

    public GnneActionPuFetchifConf4? FetchifConf4 { get; set; }

    public GnneActionPuFetchifConfDeq? FetchifConfDeq { get; set; }

    public GnneActionPuOfConf1? PuOfConf1 { get; set; }

    public GnneActionPuOfConf2? PuOfConf2 { get; set; }

    public GnneActionPuComputeConf? PuComputeConf { get; set; }

    public GnneActionDmStoreOfConf? DmStoreOfConf { get; set; }

    public GnneActionAct0Src1Conf? Act0Src1Conf { get; set; }

    public GnneActionMfuAct1ConfStride? Act1ConfStride { get; set; }

    public GnneActionMfuAct1ConfSrc1? Act1ConfSrc1 { get; set; }

    public GnneActionMfuAct1ConfSrc2? Act1ConfSrc2 { get; set; }

    public GnneActionMfuAct1ConfDest? Act1ConfDest { get; set; }

    public GnneActionMfuAct1ConfDeq? Act1ConfDeq { get; set; }

    public GnneActionMfuAct1ConfQuant? Act1ConfQuant { get; set; }

    public GnneActionMfuAct1Conf? Act1Conf { get; set; }

    public GnneActionMfuTransposeConf? TransposeConf { get; set; }

    public GnneActionMfuPdp1Conf1? Pdp1Conf1 { get; set; }

    public GnneActionMfuPdp1Conf2? Pdp1Conf2 { get; set; }

    public GnneActionMfuPdp1Conf3? Pdp1Conf3 { get; set; }

    public GnneActionMfuPdp1Conf4? Pdp1Conf4 { get; set; }

    public GnneActionMfuPdp1ConfDeq? Pdp1ConfDeq { get; set; }

    public GnneActionMfuPdp1ConfQuant? Pdp1ConfQuant { get; set; }

    public GnneActionPuPdp0WConf? Pdp0WConf { get; set; }

    public GnneActionPuPdp0ModeConf? Pdp0ModeConf { get; set; }

    public GnneActionPuPdp0FetchifConf1? Pdp0FetchifConf1 { get; set; }

    public GnneActionPuPdp0FetchifConf2? Pdp0FetchifConf2 { get; set; }

    public GnneActionPuPdp0FetchifConf3? Pdp0FetchifConf3 { get; set; }

    public GnneActionPuPdp0FetchifConf4? Pdp0FetchifConf4 { get; set; }

    public GnneActionPuPdp0ConfDeq? Pdp0ConfDeq { get; set; }

    public GnneActionPuPdp0OfConf? Pdp0OfConf { get; set; }
}
