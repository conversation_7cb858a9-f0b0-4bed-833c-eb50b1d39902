// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Runtime.CompilerServices;
using Nncase.IR;
using Nncase.IR.F;
using Nncase.TIR;
using Math = Nncase.IR.F.Math;

namespace Nncase.Passes.Rules.K230;

public static class TileExtensions
{
    public static int FixedDimensions(this TIR.Buffer b, int axis) => (int)b.Dimensions[axis].FixedValue;

    public static TensorConst Const(this TIR.Buffer b) => (TensorConst)b.MemSpan.Buffer.Start[IR.Buffers.AddressOf.Input.Index];
}
