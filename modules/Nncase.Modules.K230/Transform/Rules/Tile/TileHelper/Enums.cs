﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Runtime.InteropServices;
using Nncase.IR;
using Nncase.TIR;

namespace Nncase.Passes.Rules.K230;

public enum GlbSearchStrategy
{
    If<PERSON><PERSON><PERSON>,
    WFirst,
}

public enum ScheduleStrategy
{
    IfFirstSchedule,
    WFirstSchedule,
}

/// <summary>
/// GnneActionName.
/// </summary>
public enum GnneActionName
{
    WriteGpr,
    CcrDecl,
    CcrSet,
    CcrClr,
    MmuConf,
    MmuSetid,
    PackStrideReg,
    PackShapeReg,
    L2LoadConf,
    L2Load,
    L2LoadWConf,
    L2LoadW,
    L2StoreConf,
    L2Store,
    DmLoadL1Conf,
    DmLoadL1,
    DmLoadWConf,
    DmLoadWConf2,
    PuWConf,
    DmLoadWConfDeq,
    DmLoadW,
    PuFetchifConf1,
    PuFetchifConf2,
    PuFetchifConf3,
    PuFetchifConf4,
    PuFetchifConfDeq,
    DmLoadAct0,
    DmStoreOf,
    DmStoreOfConf,
    Act0Src1Conf,
    Act0Compute,
    PuOfConf1,
    PuOfConf2,
    PuComputeConf,
    PuCompute,
    PuForwardPsum,
    PuPdp0ModeConf,
    PuPdp0FetchifConf1,
    PuPdp0FetchifConf2,
    PuPdp0FetchifConf3,
    PuPdp0FetchifConf4,
    PuPdp0ConfDeq,
    PuPdp0WConf,
    PuPdp0OfConf,
    PuPdp0Compute,
    MfuAct1ConfStride,
    MfuAct1ConfSrc1,
    MfuAct1ConfSrc2,
    MfuAct1ConfDest,
    MfuAct1ConfDeq,
    MfuAct1ConfQuant,
    MfuAct1Conf,
    MfuAct1Compute,
    MfuTransposeConf,
    MfuTranspose,
    MfuPdp1Conf1,
    MfuPdp1Conf2,
    MfuPdp1Conf3,
    MfuPdp1Conf4,
    MfuPdp1ConfDeq,
    MfuPdp1ConfQuant,
    MfuPdp1Compute,
    MfuMemset,
    MfuMemcpy,
    Fence,
    FenceI,
    Intr,
    End,
    BasementG2R,
    Ai2dCompute,
    Extrw,
    Extraw,

    // TODO: add more actions here
}

public enum GprItemName
{
    RbasementInput,
    RbasementOutput,
    RbasementRdata,
    RbasementData,
    CcrDeclRnum,
    MmuConfRstart,
    MmuConfRdepth,
    MmuSetIdRd,
    RlenCompressed,
    RlenDecompressed,
    L2LoadWRaddrS,
    L2LoadWRaddrD,
    L2LoadRaddrS,
    L2LoadRaddrD,
    L2LoadWValidCNum,
    PackStrideRnS,
    PackStrideRcS,
    PackStrideRhS,
    PackStrideRnD,
    PackStrideRcD,
    PackStrideRhD,
    PackShapeRn,
    PackShapeRc,
    PackShapeRh,
    PackShapeRw,
    L2StoreRaddrS,
    L2StoreRaddrD,
    DmLoadL1RaddrS,
    DmLoadL1RhtocWindow,
    DmLoadWRstrideOc,
    DmLoadWRgroups,
    DmLoadWRgoc,
    DmLoadWRaddrS,
    DmLoadWRaddrBw,
    PuFetchIfRgic,
    PuFetchIfRgicLast,
    PuFetchIfRaddrS,
    PuFetchIfRgroups,
    PuFetchIfRpadValue,
    PuFetchIfRic,
    PuFetchIfRbx,
    PuOfConf1Rgoc,
    PuOfConf1RgocLast,
    PuOfConf2RaddrD,
    DmLoadAct0RaddrS,
    DmLoadAct0Rlen,
    DmStoreOfRaddrD,
    PuForwardPsumRaddr,
    PuForwardPsumRlen,
    Act0ComputeRaddrD,
    MfuAct1ConfSrc1Rslice,
    MfuAct1ConfSrc1RrightRepeats,
    MfuAct1ConfSrc1RsliceRepeats,
    MfuAct1ConfSrc2RleftRepeats,
    MfuAct1ConfDestRlen,
    MfuAct1ConfDeqRscale,
    MfuAct1ConfDeqRbias,
    MfuAct1ComputeRaddrD1,
    MfuAct1ComputeRaddrS1,
    MfuAct1ComputeRaddrS2,
    MfuAct1ComputeRaddrArg,
    MfuTransposeRaddrD,
    MfuTransposeRaddrS,
    MfuPdp1Conf2RcountW,
    MfuPdp1Conf2RcountH,
    MfuPdp1Conf2RpeH,
    MfuPdp1Conf2RpeLastH,
    MfuPdp1Conf3RpeChannels,
    MfuPdp1Conf3RpeLastChannels,
    MfuPdp1Conf3RpadValue,
    MfuPdp1Conf4RwindowW,
    MfuPdp1Conf4RwindowH,
    MfuPdp1Conf4Rscale,
    MfuPdp1ConfDeqRscale,
    MfuPdp1ConfDeqRbias,
    MfuPdp1ConfQuantRscale,
    MfuPdp1ConfQuantRbias,
    MfuPdp1ComputeRaddrD,
    MfuPdp1ComputeRaddrS,
    PuPdp0FetchifConf2Rgic,
    PuPdp0FetchifConf2RgicLast,
    PuPdp0FetchifConf4RpadValue,
    PuPdp0ConfDeqRbx,
    PuPdp0ComputeRaddrS,
    MfuMemcpyRaddrD,
    MfuMemcpyRaddrS,
    MfuMemsetRaddrD,
    MfuMemsetRv,
    IntrNum,
}

public enum SsrItemName
{
    L2LoadConfRstrideD,
    L2LoadConfRstrideS,
    L2StoreConfRstrideD,
    L2StoreConfRstrideS,
    L2LoadRshape,
    L2StoreRshape,
    DmLoadL1Rshape,
    DmLoadL1ConfRstrideS,
    DmLoadWRIochannels,
    PuFetchIfRstrideS,
    PuFetchIfRshape,
    PuFetchIfRsspad,
    PuOfConf1RstrideD,
    PuOfConf2RshapeD,
    DmStoreOfConfRstrideD,
    DmStoreOfRshape,
    Act0Src1ConfRshape,
    MfuAct1ConfStrideRstideS1,
    MfuAct1ConfStrideRstideS2,
    MfuAct1ConfStrideRstideD1,
    MfuAct1ConfSrc2Rshape,
    MfuTransposeRstrideS,
    MfuAct1ConfDestRshape,
    MfuTransposeRstrideD,
    MfuTransposeRshape,
    MfuPdp1Conf1RstrideS,
    MfuPdp1Conf1RstrideD,
    MfuPdp1Conf3Sspad,
    MfuPdp1ComputeRshape,
    PuPdp0FetchifConf3Rshape,
    PuPdp0FetchifConf4Sspad,
    PuPdp0OfConfRstrideD,
    PuPdp0OfConfRshapeD,
    MfuMemcpyRstrideD,
    MfuMemcpyRstrideS,
    MfuMemcpyRshape,
    MfuMemsetRstride,
    MfuMemsetRshape,
}

public enum TcuComputeMode
{
    NormalConv2d,
    DwConv2d,
    TransposeConv2d,
    MatMul,
    Activation,
}

public enum FusionType
{
    Conv2d,
    Transpose,
    Pdp,
    Act,
    Conv2dPdp,
    Conv2dConv2d,
    Conv2dAct1,
    LoadStore,
    Conv2dTranspose,
    Pdp0Dw, // no use, to be deleted
    Pad,
    Lstm,
    L2WAllIn,
    L2IfFullWPp,
    L2IfSplitWFull,
    L2IfSplitWPp,
    Matmul,
    Ai2dPad,
    Ai2dResize,
}

public enum ItemName
{
    None,
    Basement,
    Ifmap,
    IfmapFake,
    Ifmap2,
    Weight,
    WeightFake, // for 1st load_w
    DwWeight,
    Ofmap,
    Ofmap2,
    OfmapFake, // for 1st tcu compute
    Act,
    DwAct1,
    PdpAct1,
    MfuAct1,
    Psum,
    IfQarg,
    ResInQarg,
    WQarg,
    DwQarg,
    StoreQarg,
    MatA,
    MatB,
    MatAQarg,
    MatBQarg,
    SegFittingParam,
    LstmWXc,
    LstmWRc,
    LstmActXc,
    LstmActRc,
    LstmOfH,
    LstmOfC,
    LstmWXcQarg,
    LstmWRcQarg,
    LstmBinAct,
    LstmBinQAct,
    LstmSegFittingParamFt,
    LstmSegFittingParamGt,
    WeightPreload,
}

public enum AlignedType
{
    NoAligned,
    EAligned,
    FAligned,
    EXFAligned,
}

public enum Ai2dFormat
{
    YUV420_NV12 = 0,
    YUV420_NV21 = 1,
    YUV420_I420 = 2,
    NCHW_FMT = 3,
    RGB_packed = 4,
    RAW16 = 5,
}

public struct Ssr
{
    public Ssr(int index, long value, bool needRenewal)
    {
        Index = index;
        Value = value;
        NeedRenewal = needRenewal;
    }

    public int Index { get; set; }

    public long Value { get; set; }

    public bool NeedRenewal { get; set; }
}

public struct GprKey
{
    public int Value;
    public bool Basement;

    public GprKey(int value, bool basement)
    {
        Value = value;
        Basement = basement;
    }

    public GprKey(int value)
    {
        Value = value;
        Basement = false;
    }

    public static bool operator ==(GprKey lhs, GprKey rhs)
    {
        return lhs.Value == rhs.Value && lhs.Basement == rhs.Basement;
    }

    public static bool operator !=(GprKey lhs, GprKey rhs)
    {
        return !(lhs == rhs);
    }

    public override bool Equals(object? obj)
    {
        return obj is GprKey rhs && Value == rhs.Value && Basement == rhs.Basement;
    }
}

public struct SsrKey
{
    private long _value;
    private bool _basement;

    public SsrKey(long value, bool basement)
    {
        _value = value;
        _basement = basement;
    }

    public SsrKey(long value)
    {
        _value = value;
        _basement = false;
    }

    public static bool operator ==(SsrKey lhs, SsrKey rhs)
    {
        return lhs._value == rhs._value && lhs._basement == rhs._basement;
    }

    public static bool operator !=(SsrKey lhs, SsrKey rhs)
    {
        return !(lhs == rhs);
    }

    /// <inheritdoc/>
    public override bool Equals(object? obj)
    {
        return obj is SsrKey rhs && _value == rhs._value && _basement == rhs._basement;
    }
}

[StructLayout(LayoutKind.Explicit)]
public struct FP32
{
    [FieldOffset(0)]
    public uint U;

    [FieldOffset(0)]
    public float F;
}

public struct Ai2dSram
{
    public static int SramLen { get; } = 256;

    public static int SramSize { get; } = SramLen * SramLen;
}

public struct Ai2dConfig
{
    // 0x00
    public uint SrcCh0Ptr = 32;
    public uint SrcCh1Ptr = 32;
    public uint SrcCh2Ptr = 32;
    public uint SrcCh3Ptr = 32;

    // 0x10
    public uint DstCh0Ptr = 32;
    public uint DstCh1Ptr = 32;
    public uint DstCh2Ptr = 32;
    public uint DstCh3Ptr = 32;

    // 0x20
    public uint SrcCh0WidthLayout = 16;
    public uint SrcCh1WidthLayout = 16;
    public uint SrcCh2WidthLayout = 16;
    public uint SrcCh3WidthLayout = 16;
    public uint DstCh0WidthLayout = 16;
    public uint DstCh1WidthLayout = 16;
    public uint DstCh2WidthLayout = 16;
    public uint DstCh3WidthLayout = 16;

    // 0x30
    public uint M0 = 32;
    public uint M1 = 32;
    public uint M3 = 32;
    public uint M4 = 32;

    // 0x40
    public uint Reserved0 = 32;
    public uint Reserved1 = 32;
    public uint Channel = 3;
    public uint DstChannel = 3;
    public uint CordRound = 2;
    public uint Interpolation = 2;
    public uint PadMod = 2;
    public uint Shift = 8;
    public uint BoundInd = 4;
    public uint SrcFormat = 4;
    public uint DstFormat = 4;
    public uint BoundVal = 16;
    public uint BoundSmooth = 1;
    public uint Reserved2 = 15;

    // 0x50
    public uint Yuv2rgbCoef0 = 12;
    public uint Yuv2rgbCoef1 = 12;
    public uint Reserved3 = 8;
    public uint Yuv2RgbCoef2 = 12;
    public uint Yuv2RgbCoef3 = 12;
    public uint Reserved4 = 8;
    public uint Yuv2RgbCoef4 = 12;
    public uint Yuv2RgbCoef5 = 12;
    public uint Reserved5 = 8;
    public uint Yuv2RgbCoef6 = 12;
    public uint Yuv2RgbCoef7 = 12;
    public uint Reserved6 = 8;

    // 0x60
    public uint Yuv2RgbCoef10 = 12;
    public uint Yuv2RgbCoef11 = 12;
    public uint ConstPadCh0 = 8;
    public uint ConstPadCh1 = 8;
    public uint ConstPadCh2 = 8;
    public uint ConstPadCh3 = 8;
    public uint CrcInd = 1;
    public uint DstInd = 1;
    public uint CmdId = 1;
    public uint Sign = 1;
    public uint Reserved7 = 4;
    public uint Yuv2RgbCoef8 = 12;
    public uint Yuv2RgbCoef9 = 12;
    public uint Reserved8 = 8;
    public uint Reserved9 = 32;

    // 0x70
    public uint PadT = 16;
    public uint PadB = 16;
    public uint PadL = 16;
    public uint PadR = 16;
    public uint SrcWidthShape = 16;
    public uint SrcHeightShape = 16;
    public uint DstWidthShape = 16;
    public uint DstHeightShape = 14;
    public uint IntrMask = 1;
    public uint CscEn = 1;

    // 0x80
    public uint M2 = 32;
    public uint M5 = 32;
    public uint SrcX = 16;
    public uint SrcY = 16;
    public uint DstX = 16;
    public uint DstY = 13;
    public uint Reserved10 = 2;
    public uint Ai2dCalcEnable = 1;

    /// <summary>
    /// Initializes a new instance of the <see cref="Ai2dConfig"/> struct.
    /// </summary>
    public Ai2dConfig()
    {
        SrcCh0Ptr = 0;
        SrcCh1Ptr = 0;
        SrcCh2Ptr = 0;
        SrcCh3Ptr = 0;
        DstCh0Ptr = 0;
        DstCh1Ptr = 0;
        DstCh2Ptr = 0;
        DstCh3Ptr = 0;

        SrcCh0WidthLayout = 0;
        SrcCh1WidthLayout = 0;
        SrcCh2WidthLayout = 0;
        SrcCh3WidthLayout = 0;
        DstCh0WidthLayout = 0;
        DstCh1WidthLayout = 0;
        DstCh2WidthLayout = 0;
        DstCh3WidthLayout = 0;

        FP32 m;
        m.U = 0;  // As m and u share memory, this line has no actual meaning, and this handling is only to avoid uninitialized errors
        m.F = 1024;
        M0 = m.U;
        M1 = 0;
        M3 = 0;
        M4 = m.U;

        Reserved0 = 0;
        Reserved1 = 0;
        Channel = 0;
        DstChannel = 0;
        CordRound = 0;
        Interpolation = 0;
        PadMod = 0;
        Shift = 0;
        BoundInd = 0;
        SrcFormat = 0;
        DstFormat = 0;
        BoundVal = 0;
        BoundSmooth = 0;
        Reserved2 = 0;

        Yuv2rgbCoef0 = 256;
        Yuv2rgbCoef1 = 0;
        Reserved3 = 0;
        Yuv2RgbCoef2 = 292;
        Yuv2RgbCoef3 = (1 << 12) - 146;
        Reserved4 = 0;
        Yuv2RgbCoef4 = 256;
        Yuv2RgbCoef5 = (1 << 12) - 101;
        Reserved5 = 0;
        Yuv2RgbCoef6 = (1 << 12) - 149;
        Yuv2RgbCoef7 = 125;
        Reserved6 = 0;

        Yuv2RgbCoef10 = 0;
        Yuv2RgbCoef11 = (1 << 12) - 260;
        ConstPadCh0 = 0;
        ConstPadCh1 = 0;
        ConstPadCh2 = 0;
        ConstPadCh3 = 0;
        CrcInd = 0;
        DstInd = 0;
        CmdId = 0;
        Sign = 0;
        Reserved7 = 0;
        Yuv2RgbCoef8 = 256;
        Yuv2RgbCoef9 = 520;
        Reserved8 = 0;
        Reserved9 = 0;

        PadT = 0;
        PadB = 0;
        PadL = 0;
        PadR = 0;
        SrcWidthShape = 0;
        SrcHeightShape = 0;
        DstWidthShape = 0;
        DstHeightShape = 0;
        IntrMask = 1;
        CscEn = 0;

        M2 = 0;
        M5 = 0;
        SrcX = 0;
        SrcY = 0;
        DstX = 0;
        DstY = 0;
        Reserved10 = 0;
        Ai2dCalcEnable = 1;
    }

    public uint GetAddrValue(uint idx)
    {
        switch (idx)
        {
            case 0:
                {
                    return SrcCh0Ptr;
                }

            case 1:
                {
                    return SrcCh1Ptr;
                }

            case 2:
                {
                    return SrcCh2Ptr;
                }

            case 3:
                {
                    return SrcCh3Ptr;
                }

            case 4:
                {
                    return DstCh0Ptr;
                }

            case 5:
                {
                    return DstCh1Ptr;
                }

            case 6:
                {
                    return DstCh2Ptr;
                }

            case 7:
                {
                    return DstCh3Ptr;
                }

            case 8:
                {
                    return (SrcCh1WidthLayout << 16) + SrcCh0WidthLayout;
                }

            case 9:
                {
                    return (SrcCh3WidthLayout << 16) + SrcCh2WidthLayout;
                }

            case 10:
                {
                    return (DstCh1WidthLayout << 16) + DstCh0WidthLayout;
                }

            case 11:
                {
                    return (DstCh3WidthLayout << 16) + DstCh2WidthLayout;
                }

            case 12:
                {
                    return M0;
                }

            case 13:
                {
                    return M1;
                }

            case 14:
                {
                    return M3;
                }

            case 15:
                {
                    return M4;
                }

            case 16:
                {
                    return Reserved0;
                }

            case 17:
                {
                    return Reserved1;
                }

            case 18:
                {
                    return (DstFormat << 28)
                        | (SrcFormat << 24)
                        | (BoundInd << 20)
                        | (Shift << 12)
                        | (PadMod << 10)
                        | (Interpolation << 8)
                        | (CordRound << 6)
                        | (DstChannel << 3)
                        | Channel;
                }

            case 19:
                {
                    return (Reserved2 << 17)
                        | (BoundSmooth << 16)
                        | BoundVal;
                }

            case 20:
                {
                    return (Reserved3 << 24)
                        | (Yuv2rgbCoef1 << 12)
                        | Yuv2rgbCoef0;
                }

            case 21:
                {
                    return (Reserved4 << 24)
                        | (Yuv2RgbCoef3 << 12)
                        | Yuv2RgbCoef2;
                }

            case 22:
                {
                    return (Reserved5 << 24)
                        | (Yuv2RgbCoef5 << 12)
                        | Yuv2RgbCoef4;
                }

            case 23:
                {
                    return (Reserved6 << 24)
                        | (Yuv2RgbCoef7 << 12)
                        | Yuv2RgbCoef6;
                }

            case 24:
                {
                    return (ConstPadCh0 << 24)
                        | (Yuv2RgbCoef11 << 12)
                        | Yuv2RgbCoef10;
                }

            case 25:
                {
                    return (Reserved7 << 28)
                        | (Sign << 27)
                        | (CmdId << 26)
                        | (DstInd << 25)
                        | (CrcInd << 24)
                        | (ConstPadCh3 << 16)
                        | (ConstPadCh2 << 8)
                        | ConstPadCh1;
                }

            case 26:
                {
                    return (Reserved8 << 24)
                        | (Yuv2RgbCoef9 << 12)
                        | Yuv2RgbCoef8;
                }

            case 27:
                {
                    return Reserved9;
                }

            case 28:
                {
                    return (PadB << 16) | PadT;
                }

            case 29:
                {
                    return (PadR << 16) | PadL;
                }

            case 30:
                {
                    return (SrcHeightShape << 16) | SrcWidthShape;
                }

            case 31:
                {
                    return (CscEn << 31)
                        | (IntrMask << 30)
                        | (DstHeightShape << 16)
                        | DstWidthShape;
                }

            case 32:
                {
                    return M2;
                }

            case 33:
                {
                    return M5;
                }

            case 34:
                {
                    return (SrcY << 16) | SrcX;
                }

            case 35:
                {
                    return (Ai2dCalcEnable << 31)
                        | (Reserved10 << 29)
                        | (DstY << 16)
                        | DstX;
                }

            default:
                return 0;
        }
    }

    public string ToString(uint idx)
    {
        switch (idx)
        {
            case 0:
                {
                    return "src_ch0_ptr: " + SrcCh0Ptr.ToString();
                }

            case 1:
                {
                    return "src_ch1_ptr: " + SrcCh1Ptr.ToString();
                }

            case 2:
                {
                    return "src_ch2_ptr: " + SrcCh2Ptr.ToString();
                }

            case 3:
                {
                    return "src_ch3_ptr: " + SrcCh3Ptr.ToString();
                }

            case 4:
                {
                    return "dst_ch0_ptr: " + DstCh0Ptr.ToString();
                }

            case 5:
                {
                    return "dst_ch1_ptr: " + DstCh1Ptr.ToString();
                }

            case 6:
                {
                    return "dst_ch2_ptr: " + DstCh2Ptr.ToString();
                }

            case 7:
                {
                    return "dst_ch3_ptr: " + DstCh3Ptr.ToString();
                }

            case 8:
                {
                    return "src_ch1_width_layout: " + SrcCh1WidthLayout.ToString()
                        + "\n"
                        + "src_ch0_width_layout: " + SrcCh0WidthLayout.ToString();
                }

            case 9:
                {
                    return "src_ch3_width_layout: " + SrcCh3WidthLayout.ToString()
                        + "\n"
                        + "src_ch2_width_layout: " + SrcCh2WidthLayout.ToString();
                }

            case 10:
                {
                    return "dst_ch1_width_layout: " + DstCh1WidthLayout.ToString()
                        + "\n"
                        + "dst_ch0_width_layout: " + DstCh0WidthLayout.ToString();
                }

            case 11:
                {
                    return "dst_ch3_width_layout: " + DstCh3WidthLayout.ToString()
                        + "\n"
                        + "dst_ch2_width_layout: " + DstCh2WidthLayout.ToString();
                }

            case 12:
                {
                    FP32 m;
                    m.F = 0f; // As m and u share memory, this line has no actual meaning, and this handling is only to avoid uninitialized errors
                    m.U = M0;
                    return "M0: " + m.F.ToString();
                }

            case 13:
                {
                    FP32 m;
                    m.F = 0f; // As m and u share memory, this line has no actual meaning, and this handling is only to avoid uninitialized errors
                    m.U = M1;
                    return "M1: " + m.F.ToString();
                }

            case 14:
                {
                    FP32 m;
                    m.F = 0f; // As m and u share memory, this line has no actual meaning, and this handling is only to avoid uninitialized errors
                    m.U = M3;
                    return "M3: " + m.F.ToString();
                }

            case 15:
                {
                    FP32 m;
                    m.F = 0f; // As m and u share memory, this line has no actual meaning, and this handling is only to avoid uninitialized errors
                    m.U = M4;
                    return "M4: " + m.F.ToString();
                }

            case 16:
                {
                    return string.Empty;
                }

            case 17:
                {
                    return string.Empty;
                }

            case 18:
                {
                    return "dst_format: " + DstFormat.ToString() + "\n"
                        + "SrcFormat: " + SrcFormat.ToString() + "\n"
                        + "BoundInd: " + BoundInd.ToString() + "\n"
                        + "shift: " + Shift.ToString() + "\n"
                        + "pad_mod: " + PadMod.ToString() + "\n"
                        + "interpolation: " + Interpolation.ToString() + "\n"
                        + "cord_round: " + CordRound.ToString() + "\n"
                        + "dst_channel: " + DstChannel.ToString() + "\n"
                        + "channel: " + Channel.ToString();
                }

            case 19:
                {
                    return "bound_smooth: " + BoundSmooth.ToString() + "\n"
                        + "bound_val: " + BoundVal.ToString();
                }

            case 20:
                {
                    return "yuv2rgb_coef1: " + Yuv2rgbCoef1.ToString() + "\n"
                        + "yuv2rgb_coef0: " + Yuv2rgbCoef0.ToString();
                }

            case 21:
                {
                    return "yuv2rgb_coef3: " + Yuv2RgbCoef3.ToString() + "\n"
                        + "yuv2rgb_coef2: " + Yuv2RgbCoef2.ToString();
                }

            case 22:
                {
                    return "yuv2rgb_coef5: " + Yuv2RgbCoef5.ToString() + "\n"
                        + "yuv2rgb_coef4: " + Yuv2RgbCoef4.ToString();
                }

            case 23:
                {
                    return "yuv2rgb_coef7: " + Yuv2RgbCoef7.ToString() + "\n"
                        + "yuv2rgb_coef6: " + Yuv2RgbCoef6.ToString();
                }

            case 24:
                {
                    return "const_pad_ch0: " + ConstPadCh0.ToString() + "\n"
                        + "yuv2rgb_coef11: " + Yuv2RgbCoef11.ToString() + "\n"
                        + "yuv2rgb_coef10: " + Yuv2RgbCoef10.ToString();
                }

            case 25:
                {
                    return "sign: " + Sign.ToString() + "\n"
                        + "cmd_id: " + CmdId.ToString() + "\n"
                        + "dst_ind: " + DstInd.ToString() + "\n"
                        + "src_ind: " + CrcInd.ToString() + "\n"
                        + "const_pad_ch3: " + ConstPadCh3.ToString() + "\n"
                        + "const_pad_ch2: " + ConstPadCh2.ToString() + "\n"
                        + "const_pad_ch1: " + ConstPadCh1.ToString();
                }

            case 26:
                {
                    return "yuv2rgb_coef9: " + Yuv2RgbCoef9.ToString() + "\n"
                        + "yuv2rgb_coef8: " + Yuv2RgbCoef8.ToString();
                }

            case 27:
                {
                    return string.Empty;
                }

            case 28:
                {
                    return "pad_b: " + PadB.ToString() + "\n"
                        + "pad_t: " + PadT.ToString();
                }

            case 29:
                {
                    return "pad_r: " + PadR.ToString() + "\n"
                        + "pad_l: " + PadL.ToString();
                }

            case 30:
                {
                    return "src_height_shape: " + SrcHeightShape.ToString() + "\n"
                        + "src_width_shape: " + SrcWidthShape.ToString();
                }

            case 31:
                {
                    return "csc_en: " + CscEn.ToString() + "\n"
                        + "intr_mask: " + IntrMask.ToString() + "\n"
                        + "dst_height_shape: " + DstHeightShape.ToString() + "\n"
                        + "dst_width_shape: " + DstWidthShape.ToString();
                }

            case 32:
                {
                    FP32 m;
                    m.F = 0; // As m and u share memory, this line has no actual meaning, and this handling is only to avoid uninitialized errors
                    m.U = M2;
                    return "M2: " + m.F.ToString();
                }

            case 33:
                {
                    FP32 m;
                    m.F = 0; // As m and u share memory, this line has no actual meaning, and this handling is only to avoid uninitialized errors
                    m.U = M5;
                    return "M5: " + m.F.ToString();
                }

            case 34:
                {
                    return "SrcY: " + SrcY.ToString() + "\n"
                        + "SrcX: " + SrcX.ToString();
                }

            case 35:
                {
                    return "Ai2dCalcEnable: " + Ai2dCalcEnable.ToString() + "\n"
                        + "DstY: " + DstY.ToString() + "\n"
                        + "DstX: " + DstX.ToString();
                }

            default:
                return string.Empty;
        }
    }

    public float U32ToFloat(uint u)
    {
        FP32 m;
        m.F = 0; // As m and u share memory, this line has no actual meaning, and this handling is only to avoid uninitialized errors
        m.U = u;
        return m.F;
    }

    public float OriginM(uint u)
    {
        return U32ToFloat(u) / 1024f;
    }
}

public class Gpr
{
    public Gpr(int index, BaseExpr value, bool needRenewal)
    {
        Index = index;
        Value = value;
        NeedRenewal = needRenewal;
    }

    public int Index { get; set; }

    public BaseExpr Value { get; set; }

    public bool NeedRenewal { get; set; }
}

public class MmuItem
{
    private readonly int _id;
    private readonly int _startBank;
    private readonly int _width;
    private readonly int _startDepth;
    private readonly int _depth;

    // public item_name owner_name;
    public MmuItem()
    {
        _id = 0;
        _startBank = 0;
        _width = 0;
        _startDepth = 0;
        _depth = 0;
    }

    public MmuItem(int id, int startBank, int width, int startDepth, int depth)
    {
        _id = id;
        _startBank = startBank;
        _width = width;
        _startDepth = startDepth;
        _depth = depth;
    }

    public int Id => _id;

    public int StartBank => _startBank;

    public int Width => _width;

    public int StartDepth => _startDepth;

    public int Depth => _depth;
}

public class NodeBuffer
{
    /// <summary>
    /// Initializes a new instance of the <see cref="NodeBuffer"/> class.
    /// </summary>
    public NodeBuffer()
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="NodeBuffer"/> class.
    /// </summary>
    public NodeBuffer(NodeBuffer other)
    {
        OfBufferIndex = other.OfBufferIndex;
        OutputsSize = other.OutputsSize;
        OfmapOffset = other.OfmapOffset;
        WeightOffset = other.WeightOffset;
        ActOffset = other.ActOffset;
        WeightQargOffset = other.WeightQargOffset;
        DwWeightOffset = other.DwWeightOffset;
        DwActOffset = other.DwActOffset;
        DwWeightQargOffset = other.DwWeightQargOffset;
        Ifmap2Offset = other.Ifmap2Offset;
        Act1Offset = other.Act1Offset;
        Pdp0ActOffset = other.Pdp0ActOffset;
        WeightPreloadOffset = other.WeightPreloadOffset;
        AlignType = other.AlignType;
    }

    public int OfBufferIndex { get; set; }

    public int OutputsSize { get; set; }

    public int OfmapOffset { get; set; }

    public int WeightOffset { get; set; }

    public int ActOffset { get; set; }

    public int WeightQargOffset { get; set; }

    public int DwWeightOffset { get; set; }

    public int DwActOffset { get; set; }

    public int DwWeightQargOffset { get; set; }

    public int Ifmap2Offset { get; set; }

    public int Act1Offset { get; set; }

    public int Pdp0ActOffset { get; set; }

    public int WeightPreloadOffset { get; set; } = -1;

    public AlignedType AlignType { get; set; } = AlignedType.NoAligned;
}

public class FusionInfo
{
    public FusionInfo(List<NodeInfo> fusedNodes, int[] lastOutShape, Dictionary<ItemName, MmuItem> mmu, IRArray<IVar> inputs, Fusion fusion)
    {
        FusedNodes = fusedNodes;
        LastOutShape = lastOutShape;
        Mmu = mmu;
        Inputs = inputs;
        Fusion = fusion;
    }

    public List<NodeInfo> FusedNodes { get; set; }

    public int[] LastOutShape { get; set; }

    public Dictionary<ItemName, MmuItem> Mmu { get; set; }

    public IRArray<IVar> Inputs { get; set; }

    public Fusion Fusion { get; set; }
}

public class TensorStat
{
    public TensorStat(bool isFirstSlice, bool isLastSlice, int sliceIdx = 0)
    {
        IsFirstSlice = isFirstSlice;
        IsLastSlice = isLastSlice;
        SliceIdx = sliceIdx;
    }

    public bool IsFirstSlice { get; set; }

    public bool IsLastSlice { get; set; }

    public int SliceIdx { get; set; } // only used for weight_slice_rec now, mean nothing

    public int Stat_cnt()
    {
        int ret = 0;
        if (IsFirstSlice)
        {
            ret++;
        }

        if (IsLastSlice)
        {
            ret++;
        }

        if (IsFirstSlice && IsLastSlice)
        {
            ret = 1;
        }

        return ret;
    }
}

public class NodeInfo
{
    public NodeInfo(Call op, SegmentND ofmap, NodeBuffer nb, List<NodeInfo> children, int[] outshape)
    {
        Op = op;
        Ofmap = ofmap;
        Nb = nb;
        Children = children;
        OutShape = outshape;
    }

    public NodeInfo(Call op, SegmentND ofmap, NodeBuffer nb, List<NodeInfo> children)
    {
        Op = op;
        Ofmap = ofmap;
        Nb = nb;
        Children = children;
        OutShape = Array.Empty<int>();
    }

    public NodeInfo(Call op, List<NodeInfo> children)
    {
        Op = op;
        Ofmap = new();
        Nb = new();
        Children = children;
        OutShape = Array.Empty<int>();
    }

    public Call Op { get; set; }

    public SegmentND Ofmap { get; set; }

    public NodeBuffer Nb { get; set; }

    public List<NodeInfo> Children { get; set; }

    public int[] OutShape { get; set; }
}

public class CcrSet
{
    public CcrSet(int ccr, int value)
    {
        Ccr = ccr;
        Value = value;
    }

    public int Ccr { get; set; }

    public int Value { get; set; }
}

public class CcrClr
{
    public CcrClr(int ccr)
    {
        Ccr = ccr;
        Ccrclr = 1;
    }

    public int Ccr { get; set; }

    public int Ccrclr { get; set; }
}
