﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.Buffers;
using Nncase.Schedule;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using static Nncase.Passes.Rules.K230.TileUtilities;

namespace Nncase.Passes.Rules.K230;

public class ActionToInstruct
{
    private List<Call> _instSeq = null!;

    public ActionToInstruct()
    {
        InstSeq = new();
    }

    public List<Call> InstSeq
    {
        get => _instSeq;
        set => _instSeq = value;
    }

    public Sequential Instructions(List<GnneAction> actions)
    {
        Visit(actions);
        return InstSeq.ToArray<Call>().ToSequential();
    }

    public void Visit(List<GnneAction> actions)
    {
        foreach (var action in actions)
        {
            switch (action.Name)
            {
                case GnneActionName.WriteGpr:
                    Visit((GnneActionWriteGpr)action);
                    break;
                case GnneActionName.PackStrideReg:
                    Visit((GnneActionPackStrideReg)action);
                    break;
                case GnneActionName.PackShapeReg:
                    Visit((GnneActionPackShapeReg)action);
                    break;
                case GnneActionName.MmuConf:
                    Visit((Gnne_action_mmu_conf)action);
                    break;
                case GnneActionName.Intr:
                    Visit((GnneActionIntr)action);
                    break;
                case GnneActionName.Fence:
                    Visit((GnneActionFence)action);
                    break;
                case GnneActionName.End:
                    Visit((GnneActionEnd)action);
                    break;
                case GnneActionName.L2LoadConf:
                    Visit((Gnne_action_l2_load_conf)action);
                    break;
                case GnneActionName.L2Load:
                    Visit((GnneActionL2Load)action);
                    break;
                case GnneActionName.L2LoadWConf:
                    Visit((GnneActionL2LoadWConf)action);
                    break;
                case GnneActionName.L2LoadW:
                    Visit((GnneActionL2LoadW)action);
                    break;
                case GnneActionName.L2StoreConf:
                    Visit((GnneActionL2StoreConf)action);
                    break;
                case GnneActionName.L2Store:
                    Visit((GnneActionL2Store)action);
                    break;
                case GnneActionName.DmLoadL1Conf:
                    Visit((GnneActionDmLoadL1Conf)action);
                    break;
                case GnneActionName.DmLoadL1:
                    Visit((GnneActionDmLoadL1)action);
                    break;
                case GnneActionName.DmLoadWConf:
                    Visit((GnneActionDmLoadWConf)action);
                    break;
                case GnneActionName.DmLoadWConf2:
                    Visit((GnneActionDmLoadWConf2)action);
                    break;
                case GnneActionName.DmLoadWConfDeq:
                    Visit((GnneActionDmLoadWConfDeq)action);
                    break;
                case GnneActionName.DmLoadW:
                    Visit((GnneActionDmLoadW)action);
                    break;
                case GnneActionName.PuFetchifConf1:
                    Visit((GnneActionPuFetchifConf1)action);
                    break;
                case GnneActionName.PuFetchifConf2:
                    Visit((GnneActionPuFetchifConf2)action);
                    break;
                case GnneActionName.PuFetchifConf3:
                    Visit((GnneActionPuFetchifConf3)action);
                    break;
                case GnneActionName.PuFetchifConf4:
                    Visit((GnneActionPuFetchifConf4)action);
                    break;
                case GnneActionName.PuFetchifConfDeq:
                    Visit((GnneActionPuFetchifConfDeq)action);
                    break;
                case GnneActionName.PuWConf:
                    Visit((GnneActionPuWConf)action);
                    break;
                case GnneActionName.PuOfConf1:
                    Visit((GnneActionPuOfConf1)action);
                    break;
                case GnneActionName.PuOfConf2:
                    Visit((GnneActionPuOfConf2)action);
                    break;
                case GnneActionName.PuComputeConf:
                    Visit((GnneActionPuComputeConf)action);
                    break;
                case GnneActionName.PuCompute:
                    Visit((GnneActionPuCompute)action);
                    break;
                case GnneActionName.PuForwardPsum:
                    Visit((GnneActionPuForwardPsum)action);
                    break;
                case GnneActionName.DmLoadAct0:
                    Visit((GnneActionDmLoadAct0)action);
                    break;
                case GnneActionName.DmStoreOf:
                    Visit((GnneActionDmStoreOf)action);
                    break;
                case GnneActionName.DmStoreOfConf:
                    Visit((GnneActionDmStoreOfConf)action);
                    break;
                case GnneActionName.Act0Src1Conf:
                    Visit((GnneActionAct0Src1Conf)action);
                    break;
                case GnneActionName.Act0Compute:
                    Visit((GnneActionAct0Compute)action);
                    break;
                case GnneActionName.MfuAct1ConfStride:
                    Visit((GnneActionMfuAct1ConfStride)action);
                    break;
                case GnneActionName.MfuAct1ConfSrc1:
                    Visit((GnneActionMfuAct1ConfSrc1)action);
                    break;
                case GnneActionName.MfuAct1ConfSrc2:
                    Visit((GnneActionMfuAct1ConfSrc2)action);
                    break;
                case GnneActionName.MfuAct1ConfDest:
                    Visit((GnneActionMfuAct1ConfDest)action);
                    break;
                case GnneActionName.MfuAct1ConfDeq:
                    Visit((GnneActionMfuAct1ConfDeq)action);
                    break;
                case GnneActionName.MfuAct1ConfQuant:
                    Visit((GnneActionMfuAct1ConfQuant)action);
                    break;
                case GnneActionName.MfuAct1Conf:
                    Visit((GnneActionMfuAct1Conf)action);
                    break;
                case GnneActionName.MfuAct1Compute:
                    Visit((GnneActionMfuAct1Compute)action);
                    break;
                case GnneActionName.MfuTransposeConf:
                    Visit((GnneActionMfuTransposeConf)action);
                    break;
                case GnneActionName.MfuTranspose:
                    Visit((GnneActionMfuTranspose)action);
                    break;
                case GnneActionName.MfuPdp1Conf1:
                    Visit((GnneActionMfuPdp1Conf1)action);
                    break;
                case GnneActionName.MfuPdp1Conf2:
                    Visit((GnneActionMfuPdp1Conf2)action);
                    break;
                case GnneActionName.MfuPdp1Conf3:
                    Visit((GnneActionMfuPdp1Conf3)action);
                    break;
                case GnneActionName.MfuPdp1Conf4:
                    Visit((GnneActionMfuPdp1Conf4)action);
                    break;
                case GnneActionName.MfuPdp1ConfQuant:
                    Visit((GnneActionMfuPdp1ConfQuant)action);
                    break;
                case GnneActionName.MfuPdp1ConfDeq:
                    Visit((GnneActionMfuPdp1ConfDeq)action);
                    break;
                case GnneActionName.MfuPdp1Compute:
                    Visit((GnneActionMfuPdp1Compute)action);
                    break;
                case GnneActionName.MfuMemset:
                    Visit((GnneActionMfuMemset)action);
                    break;
                case GnneActionName.PuPdp0ModeConf:
                    Visit((GnneActionPuPdp0ModeConf)action);
                    break;
                case GnneActionName.PuPdp0FetchifConf1:
                    Visit((GnneActionPuPdp0FetchifConf1)action);
                    break;
                case GnneActionName.PuPdp0FetchifConf2:
                    Visit((GnneActionPuPdp0FetchifConf2)action);
                    break;
                case GnneActionName.PuPdp0FetchifConf3:
                    Visit((GnneActionPuPdp0FetchifConf3)action);
                    break;
                case GnneActionName.PuPdp0FetchifConf4:
                    Visit((GnneActionPuPdp0FetchifConf4)action);
                    break;
                case GnneActionName.PuPdp0ConfDeq:
                    Visit((GnneActionPuPdp0ConfDeq)action);
                    break;
                case GnneActionName.PuPdp0WConf:
                    Visit((GnneActionPuPdp0WConf)action);
                    break;
                case GnneActionName.PuPdp0OfConf:
                    Visit((GnneActionPuPdp0OfConf)action);
                    break;
                case GnneActionName.PuPdp0Compute:
                    Visit((GnneActionPuPdp0Compute)action);
                    break;
                case GnneActionName.CcrDecl:
                    Visit((GnneActionCcrDecl)action);
                    break;
                case GnneActionName.CcrSet:
                    Visit((GnneActionCcrSet)action);
                    break;
                case GnneActionName.CcrClr:
                    Visit((GnneActionCcrClr)action);
                    break;
                case GnneActionName.Extrw:
                    Visit((GnneActionExtrw)action);
                    break;
                case GnneActionName.Extraw:
                    Visit((GnneActionExtraw)action);
                    break;
                case GnneActionName.Ai2dCompute:
                    Visit((GnneActionAi2dCompute)action);
                    break;
                default:
                    throw new NotImplementedException("unsupported gnne action");
            }
        }
    }

    // alloc.start -> the offset of tensor relative to segment
    // offset -> the offset of data relative to tensor
    // because tensor may be split during the tiling, need an offset to locate data
    // offset comes from two place
    // 1. compute using slice info
    // 2. direct pass offset value (weights maybe rearranged, codegen can't compute this, value is passed from tiling)
    private Expr ToDdrAddrOffset(TIR.Buffer alloc, ulong offset, bool inputIsSlice = false, int nextConcatStart = -1)
    {
        switch (alloc.MemSpan.Buffer.Location)
        {
            // every input / output is one segment
            case MemoryLocation.Input:
                {
                    if (inputIsSlice)
                    {
                        return IR.F.Buffer.AddressOf((Expr)(BaseExpr)alloc.MemSpan) + offset;
                    }

                    return offset;
                }

            case MemoryLocation.Output:
                {
                    if (nextConcatStart >= 0)
                    {
                        return IR.F.Buffer.AddressOf((Expr)(BaseExpr)alloc.MemSpan) - nextConcatStart + offset;
                    }

                    return offset;
                }

            default:
                {
                    return IR.F.Buffer.AddressOf((Expr)(BaseExpr)alloc.MemSpan) + offset;
                }
        }
    }

    // int to_ddr_addr_offset(TIR.Buffer alloc, int offset)
    // {
    //     return alloc.Start + offset;
    // }
    private Expr ToDdrAddrOffset(TIR.Buffer alloc, SegmentND sliceInfo, bool inputIsSlice = false, int nextConcatStart = -1, int[] layout = null!)
    {
        ulong offset = (ulong)(layout is not null
            ? GetSliceOffsetInTensor(new SegmentND(0..layout[0], 0..layout[1], 0..layout[2], 0..layout[3]), sliceInfo)
            : GetSliceOffsetInTensor(new SegmentND(0..alloc.FixedDimensions(0), 0..alloc.FixedDimensions(1), 0..alloc.FixedDimensions(2), 0..alloc.FixedDimensions(3)), sliceInfo));

        return ToDdrAddrOffset(alloc, (ulong)GetBytesPerElement(alloc.ElemType) * offset);
    }

    private QUANT_TYPE ToQuantType(DataType type) => type switch
    {
        var x when x == DataTypes.UInt8 => QUANT_TYPE.u8,
        var x when x == DataTypes.Int8 => QUANT_TYPE.i8,
        var x when x == DataTypes.Int16 => QUANT_TYPE.i16,
        _ => QUANT_TYPE.disable,
    };

    private DDR_DATATYPE ToDdrType(DataType type) => type switch
    {
        var x when x == DataTypes.Int8 || x == DataTypes.UInt8 => DDR_DATATYPE.i8,
        var x when x == DataTypes.Float16 => DDR_DATATYPE.fp16,
        var x when x == DataTypes.Float32 => DDR_DATATYPE.fp32,
        var x when x == DataTypes.Int16 => DDR_DATATYPE.i16,

        // todo:i4
        // todo:i6
        _ => throw new ArgumentOutOfRangeException(type.GetDisplayName()),
    };

    private L2_DATATYPE ToL2Type(DataType type) => type switch
    {
        var x when x == DataTypes.Int8 || x == DataTypes.UInt8 => L2_DATATYPE.i8,
        var x when x == DataTypes.Float16 => L2_DATATYPE.fp16,
        var x when x == DataTypes.Int16 => L2_DATATYPE.i16,
        _ => throw new ArgumentOutOfRangeException(type.GetDisplayName()),
    };

    private ACT_OUTPUT_TYPE ToActOutputType(DataType type) => type switch
    {
        var x when x == DataTypes.Int8 => ACT_OUTPUT_TYPE.i8,
        var x when x == DataTypes.UInt8 => ACT_OUTPUT_TYPE.u8,
        var x when x == DataTypes.Float16 => ACT_OUTPUT_TYPE.fp16,
        var x when x == DataTypes.Int16 => ACT_OUTPUT_TYPE.i16,
        _ => throw new ArgumentOutOfRangeException(type.GetDisplayName()),
    };

    private void Visit(GnneActionWriteGpr action)
    {
        InstSeq.Add(I.LoadImm((GP_REGISTER)action.GprIndex, action.Imm));
    }

    private void Visit(GnneActionPackStrideReg action)
    {
        // if (action.ss().need_renewal)
        {
            var n = action.N.Value;
            var c = action.C.Value;
            var h = action.H.Value;

            // TODO: 更新为真实的stride, 比如store继承后面的concat
            // if (action.conn() and node_cast<gnne_load>(*action.conn()))
            // {
            //     auto alloc = builder_.allocation(fusion_.outer_connector(action.conn()->input_at(0)));
            //     if (n * c * h != alloc.strides_shape[1] * alloc.strides_shape[2] * alloc.strides_shape[3])
            //     {
            //         n = alloc.strides_shape[1];
            //         c = alloc.strides_shape[2];
            //         h = alloc.strides_shape[3];
            //     }
            // }
            // if (action.conn() and node_cast<gnne_store>(*action.conn()))
            // {
            //     auto alloc = builder_.allocation(fusion_.outer_connector(action.conn()->output_at(0)));
            //     if (n * c * h != alloc.strides_shape[1] * alloc.strides_shape[2] * alloc.strides_shape[3])
            //     {
            //         n = alloc.strides_shape[1];
            //         c = alloc.strides_shape[2];
            //         h = alloc.strides_shape[3];
            //     }
            // }
            if (action.N.NeedRenewal)
            {
                InstSeq.Add(I.LoadImm((GP_REGISTER)action.N.Index, (Expr)n));
            }

            if (action.C.NeedRenewal)
            {
                InstSeq.Add(I.LoadImm((GP_REGISTER)action.C.Index, (Expr)c));
            }

            if (action.H.NeedRenewal)
            {
                InstSeq.Add(I.LoadImm((GP_REGISTER)action.H.Index, (Expr)h));
            }

            InstSeq.Add(I.SS_PACK_STRIDE((GP_REGISTER)action.N.Index, (GP_REGISTER)action.C.Index, (GP_REGISTER)action.H.Index, (SHAPE_REGISTER)action.Ss.Index));
        }
    }

    private void Visit(GnneActionPackShapeReg action)
    {
        // if (action.ss().need_renewal)
        {
            if (action.N.NeedRenewal)
            {
                InstSeq.Add(I.LoadImm((GP_REGISTER)action.N.Index, (Expr)action.N.Value));
            }

            if (action.C.NeedRenewal)
            {
                InstSeq.Add(I.LoadImm((GP_REGISTER)action.C.Index, (Expr)action.C.Value));
            }

            if (action.H.NeedRenewal)
            {
                InstSeq.Add(I.LoadImm((GP_REGISTER)action.H.Index, (Expr)action.H.Value));
            }

            if (action.W.NeedRenewal)
            {
                InstSeq.Add(I.LoadImm((GP_REGISTER)action.W.Index, (Expr)action.W.Value));
            }

            InstSeq.Add(I.SS_PACK_SHAPE((GP_REGISTER)action.N.Index, (GP_REGISTER)action.C.Index, (GP_REGISTER)action.H.Index, (GP_REGISTER)action.W.Index, (SHAPE_REGISTER)action.Ss.Index));
        }
    }

    private void Visit(Gnne_action_mmu_conf action)
    {
        // TODO: 优化mmuconf，只在第一个fusion配置basement
        // if (action.item().id > 0 or builder_.is_first_fusion())
        {
            var loadStart = I.LoadImm((GP_REGISTER)action.Start.Index, (Expr)action.Start.Value);
            var loadDepth = I.LoadImm((GP_REGISTER)action.Depth.Index, (Expr)action.Depth.Value);
            var inst = I.MMU_CONF((GP_REGISTER)action.Start.Index, (GP_REGISTER)action.Depth.Index, action.Item.Id);

            if (action.Start.NeedRenewal)
            {
                InstSeq.Add(loadStart);
            }

            if (action.Depth.NeedRenewal)
            {
                InstSeq.Add(loadDepth);
            }

            InstSeq.Add(inst);
        }

        // if (builder_.is_first_fusion())
        //     builder_.is_first_fusion() = true;
    }

    private void Visit(GnneActionIntr action)
    {
        var loadRs = I.LoadImm((GP_REGISTER)action.IntrNum.Index, (Expr)action.IntrNum.Value);
        var inst = I.INTR((GP_REGISTER)action.IntrNum.Index, (Expr)action.IntrNum.Value);

        if (action.IntrNum.NeedRenewal)
        {
            InstSeq.Add(loadRs);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionFence action)
    {
        InstSeq.Add(I.FENCE());
    }

    private void Visit(GnneActionEnd action)
    {
        InstSeq.Add(I.END(GP_REGISTER.x0));
    }

    private void Visit(Gnne_action_l2_load_conf action)
    {
        var inst = I.L2_LOAD_CONF((SHAPE_REGISTER)action.StrideD.Index, (SHAPE_REGISTER)action.StrideS.Index, ToL2Type(action.L2Datatype), ToDdrType(action.DdrDatatype));
        InstSeq.Add(inst);
    }

    private void Visit(GnneActionL2Load action)
    {
        // TODO: 确认是否是这样填空, 处理basement的填空
        Expr offset = ToDdrAddrOffset(action.Buffer, action.SliceInfo, false, -1, action.Layout);

        InstSeq.Add(I.LoadDdrAddr((GP_REGISTER)action.Basement.Index, (GP_REGISTER)action.AddrS.Index, basement: (Expr)action.Basement.Value, offset: offset));
        if (action.AddrD.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.AddrD.Index, (Expr)action.AddrD.Value));
        }

        InstSeq.Add(I.L2_LOAD((GP_REGISTER)action.AddrD.Index, (GP_REGISTER)action.AddrS.Index, (SHAPE_REGISTER)action.Shape.Index));
    }

    private void Visit(GnneActionL2LoadWConf action)
    {
        var loadCompressLen = I.LoadImm((GP_REGISTER)action.LenCompressed.Index, (Expr)action.LenCompressed.Value);
        var loadDecompressLen = I.LoadImm((GP_REGISTER)action.LenDecompressed.Index, (Expr)action.LenDecompressed.Value);
        var inst = I.L2_LOAD_W_CONF((GP_REGISTER)action.LenCompressed.Index, (GP_REGISTER)action.LenDecompressed.Index, ToL2Type(action.L2Datatype), ToDdrType(action.DdrDatatype), action.EnableDecompress);

        if (action.LenCompressed.NeedRenewal)
        {
            InstSeq.Add(loadCompressLen);
        }

        if (action.LenDecompressed.NeedRenewal)
        {
            InstSeq.Add(loadDecompressLen);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionL2LoadW action)
    {
        // TODO: 确认是否是这样填空, 处理basement的填空
        InstSeq.Add(I.LoadDdrAddr((GP_REGISTER)action.Basement.Index, (GP_REGISTER)action.AddrS.Index, basement: (Expr)action.Basement.Value, offset: (Expr)action.AddrS.Value));
        if (action.AddrD.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.AddrD.Index, (Expr)action.AddrD.Value));
        }

        if (action.ValidCNum.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.ValidCNum.Index, (Expr)action.ValidCNum.Value));
        }

        InstSeq.Add(I.L2_LOAD_W((GP_REGISTER)action.AddrD.Index, (GP_REGISTER)action.AddrS.Index, (GP_REGISTER)action.ValidCNum.Index));
    }

    private void Visit(GnneActionL2StoreConf action)
    {
        var inst = I.L2_STORE_CONF((SHAPE_REGISTER)action.StrideD.Index, (SHAPE_REGISTER)action.StrideS.Index, ToL2Type(action.L2Datatype), ToDdrType(action.DdrDatatype));
        InstSeq.Add(inst);
    }

    private void Visit(GnneActionL2Store action)
    {
        // TODO: 考虑把basement放到 Buffer里面，现在先写死
        var offset = ToDdrAddrOffset(action.Buffer, action.SliceInfo, false, -1, action.Layout);
        InstSeq.Add(I.LoadDdrAddr((GP_REGISTER)action.Basement.Index, (GP_REGISTER)action.AddrD.Index, (Expr)action.Basement.Value, offset));
        if (action.AddrS.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.AddrS.Index, (Expr)action.AddrS.Value));
        }

        InstSeq.Add(I.L2_STORE((GP_REGISTER)action.AddrD.Index, (GP_REGISTER)action.AddrS.Index, (SHAPE_REGISTER)action.Shape.Index));
    }

    private void Visit(GnneActionDmLoadL1Conf action)
    {
        var inst = I.DM_LOAD_L1_CONF(action.TcuId, action.PuId, (SHAPE_REGISTER)action.StrideS.Index, ToL2Type(action.L2Datatype), action.L1Type);
        InstSeq.Add(inst);
    }

    private void Visit(GnneActionDmLoadL1 action)
    {
        var loadAddr = I.LoadImm((GP_REGISTER)action.AddrS.Index, (Expr)action.AddrS.Value);
        var loadHtocWindow = I.LoadImm((GP_REGISTER)action.HtocWindow.Index, (Expr)action.HtocWindow.Value);
        var inst = I.DM_LOAD_L1(action.TcuId, action.PuId, (GP_REGISTER)action.AddrS.Index, (GP_REGISTER)action.HtocWindow.Index, (SHAPE_REGISTER)action.Shape.Index, action.L1Type);

        if (action.AddrS.NeedRenewal)
        {
            InstSeq.Add(loadAddr);
        }

        if (action.HtocWindow.NeedRenewal)
        {
            InstSeq.Add(loadHtocWindow);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionDmLoadWConf action)
    {
        var loadStrideOc = I.LoadImm((GP_REGISTER)action.StrideOc.Index, (Expr)action.StrideOc.Value);
        var inst = I.DM_LOAD_W_CONF(action.TcuId, action.PuId, action.KernelH, action.KernelW, (GP_REGISTER)action.StrideOc.Index);

        if (action.StrideOc.NeedRenewal)
        {
            InstSeq.Add(loadStrideOc);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionDmLoadWConf2 action)
    {
        var loadGroups = I.LoadImm((GP_REGISTER)action.Groups.Index, (Expr)action.Groups.Value);
        var loadGoc = I.LoadImm((GP_REGISTER)action.Goc.Index, (Expr)action.Goc.Value);
        var inst = I.DM_LOAD_W_CONF2(action.TcuId, action.PuId, (GP_REGISTER)action.Groups.Index, (GP_REGISTER)action.Goc.Index);

        if (action.Groups.NeedRenewal)
        {
            InstSeq.Add(loadGroups);
        }

        if (action.Goc.NeedRenewal)
        {
            InstSeq.Add(loadGoc);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionDmLoadWConfDeq action)
    {
        var inst = I.DM_LOAD_W_CONF_DEQ(action.TcuId, action.PuId, ToQuantType(action.QuantType));
        InstSeq.Add(inst);
    }

    private void Visit(GnneActionDmLoadW action)
    {
        var loadAddrS = I.LoadImm((GP_REGISTER)action.AddrS.Index, (Expr)action.AddrS.Value);
        var loadAddrBw = I.LoadImm((GP_REGISTER)action.AddrBw.Index, (Expr)action.AddrBw.Value);
        var inst = I.DM_LOAD_W(action.TcuId, action.PuId, (GP_REGISTER)action.AddrS.Index, (GP_REGISTER)action.AddrBw.Index, (SHAPE_REGISTER)action.Iochannels.Index, action.DestType);

        if (action.AddrS.NeedRenewal)
        {
            InstSeq.Add(loadAddrS);
        }

        if (action.AddrBw.NeedRenewal)
        {
            InstSeq.Add(loadAddrBw);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionPuFetchifConf1 action)
    {
        var inst = I.PU_FETCHIF_CONF1(action.TcuId, action.PuId, action.StrideW, action.StrideH, (SHAPE_REGISTER)action.StrideS.Index);
        InstSeq.Add(inst);
    }

    private void Visit(GnneActionPuFetchifConf2 action)
    {
        var loadImm = I.LoadImm((GP_REGISTER)action.Gic.Index, (Expr)action.Gic.Value);
        var inst = I.PU_FETCHIF_CONF2(action.TcuId, action.PuId, (GP_REGISTER)action.Gic.Index, GP_REGISTER.x0);

        if (action.Gic.NeedRenewal)
        {
            InstSeq.Add(loadImm);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionPuFetchifConf3 action)
    {
        var loadImm = I.LoadImm((GP_REGISTER)action.AddrS.Index, (Expr)action.AddrS.Value);
        var loadGroup = I.LoadImm((GP_REGISTER)action.Groups.Index, (Expr)action.Groups.Value);
        var inst = I.PU_FETCHIF_CONF3(action.TcuId, action.PuId, (GP_REGISTER)action.AddrS.Index, (GP_REGISTER)action.Groups.Index, (SHAPE_REGISTER)action.Shape.Index);

        if (action.AddrS.NeedRenewal)
        {
            InstSeq.Add(loadImm);
        }

        if (action.Groups.NeedRenewal)
        {
            InstSeq.Add(loadGroup);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionPuFetchifConf4 action)
    {
        var loadImm = I.LoadImm((GP_REGISTER)action.PadValue.Index, (Expr)action.PadValue.Value);
        var inst = I.PU_FETCHIF_CONF4(action.TcuId, action.PuId, (GP_REGISTER)action.PadValue.Index, (SHAPE_REGISTER)action.Pad.Index);

        if (action.PadValue.NeedRenewal)
        {
            InstSeq.Add(loadImm);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionPuFetchifConfDeq action)
    {
        var loadIc = I.LoadImm((GP_REGISTER)action.Ic.Index, (Expr)action.Ic.Value);
        var loadImm = I.LoadImm((GP_REGISTER)action.Bx.Index, (Expr)action.Bx.Value);
        var inst = I.PU_FETCHIF_CONF_DEQ(action.TcuId, action.PuId, (GP_REGISTER)action.Ic.Index, (GP_REGISTER)action.Bx.Index, ToQuantType(action.QuantType));

        if (action.Ic.NeedRenewal)
        {
            InstSeq.Add(loadIc);
        }

        if (action.Bx.NeedRenewal)
        {
            InstSeq.Add(loadImm);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionPuWConf action)
    {
        var inst = I.PU_W_CONF(action.TcuId, action.PuId, action.KernelH, action.KernelW);
        InstSeq.Add(inst);
    }

    private void Visit(GnneActionPuOfConf1 action)
    {
        var loadImm = I.LoadImm((GP_REGISTER)action.Goc.Index, (Expr)action.Goc.Value);
        var inst = I.PU_OF_CONF1(action.TcuId, action.PuId, (GP_REGISTER)action.Goc.Index, GP_REGISTER.x0, (SHAPE_REGISTER)action.StrideD.Index);

        if (action.Goc.NeedRenewal)
        {
            InstSeq.Add(loadImm);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionPuOfConf2 action)
    {
        var loadImm = I.LoadImm((GP_REGISTER)action.AddrD.Index, (Expr)action.AddrD.Value);
        var inst = I.PU_OF_CONF2(action.TcuId, action.PuId, (GP_REGISTER)action.AddrD.Index, (SHAPE_REGISTER)action.ShapeD.Index);

        if (action.AddrD.NeedRenewal)
        {
            InstSeq.Add(loadImm);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionPuComputeConf action)
    {
        var inst = I.PU_COMPUTE_CONF(action.TcuId, action.PuId, action.LoadPsum, action.ClrPsum, action.DestTarget, action.ReleaseIf, action.Mode);
        InstSeq.Add(inst);
    }

    private void Visit(GnneActionPuForwardPsum action)
    {
        var loadAddr = I.LoadImm((GP_REGISTER)action.Addr.Index, (Expr)action.Addr.Value);
        var loadLen = I.LoadImm((GP_REGISTER)action.Len.Index, (Expr)action.Len.Value);
        var inst = I.PU_FORWARD_PSUM(action.TcuId, action.PuId, (GP_REGISTER)action.Addr.Index, (GP_REGISTER)action.Len.Index);

        if (action.Addr.NeedRenewal)
        {
            InstSeq.Add(loadAddr);
        }

        if (action.Len.NeedRenewal)
        {
            InstSeq.Add(loadLen);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionPuCompute action)
    {
        var inst = I.PU_COMPUTE(action.TcuId, action.Mode);
        InstSeq.Add(inst);
    }

    private void Visit(GnneActionDmLoadAct0 action)
    {
        var loadAddr = I.LoadImm((GP_REGISTER)action.AddrS.Index, (Expr)action.AddrS.Value);
        var loadLen = I.LoadImm((GP_REGISTER)action.Len.Index, (Expr)action.Len.Value);
        var inst = I.DM_LOAD_ACT0(action.TcuId, action.PuId, (GP_REGISTER)action.AddrS.Index, (GP_REGISTER)action.Len.Index, action.DestChannel, action.IsByChannel);

        if (action.AddrS.NeedRenewal)
        {
            InstSeq.Add(loadAddr);
        }

        if (action.Len.NeedRenewal)
        {
            InstSeq.Add(loadLen);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionDmStoreOf action)
    {
        var loadAddr = I.LoadImm((GP_REGISTER)action.AddrD.Index, (Expr)action.AddrD.Value);
        var inst = I.DM_STORE_OF(action.TcuId, action.PuId, (GP_REGISTER)action.AddrD.Index, (SHAPE_REGISTER)action.Shape.Index, action.SrcChannel);

        if (action.AddrD.NeedRenewal)
        {
            InstSeq.Add(loadAddr);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionDmStoreOfConf action)
    {
        var inst = I.DM_STORE_OF_CONF(action.TcuId, action.PuId, (SHAPE_REGISTER)action.StrideD.Index, ToL2Type(action.Datatype));
        InstSeq.Add(inst);
    }

    private void Visit(GnneActionAct0Src1Conf action)
    {
        var inst = I.ACT0_SRC1_CONF(action.TcuId, action.PuId, action.Channel, (SHAPE_REGISTER)action.Shape.Index, ACT0_CONF_FUNCTION.act_src1_conf, action.RshiftBits);
        InstSeq.Add(inst);
    }

    private void Visit(GnneActionAct0Compute action)
    {
        var loadImm = I.LoadImm((GP_REGISTER)action.AddrD.Index, (Expr)action.AddrD.Value);
        var inst = I.ACT0_COMPUTE((GP_REGISTER)action.AddrD.Index, action.TcuId, action.Channel, action.Target, ToActOutputType(action.DestDatatype), action.IsByChannel);

        if (action.AddrD.NeedRenewal)
        {
            InstSeq.Add(loadImm);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionMfuAct1ConfStride action)
    {
        var inst = I.MFU_ACT1_CONF_STRIDE((SHAPE_REGISTER)action.StrideS1.Index, (SHAPE_REGISTER)action.StrideS2.Index, (SHAPE_REGISTER)action.StrideD1.Index);
        InstSeq.Add(inst);
    }

    private void Visit(GnneActionMfuAct1ConfSrc1 action)
    {
        if (action.Slice.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.Slice.Index, (Expr)action.Slice.Value));
        }

        if (action.RightRepeats.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.RightRepeats.Index, (Expr)action.RightRepeats.Value));
        }

        if (action.SliceRepeats.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.SliceRepeats.Index, (Expr)action.SliceRepeats.Value));
        }

        InstSeq.Add(I.MFU_ACT1_CONF_SRC1((GP_REGISTER)action.Slice.Index, (GP_REGISTER)action.RightRepeats.Index, (GP_REGISTER)action.SliceRepeats.Index, action.Sid, (SLICE_LOCATION)action.SliceLoc));
    }

    private void Visit(GnneActionMfuAct1ConfSrc2 action)
    {
        var loadLen = I.LoadImm((GP_REGISTER)action.LeftRepeats.Index, (Expr)action.LeftRepeats.Value);
        var inst = I.MFU_ACT1_CONF_SRC2((GP_REGISTER)action.LeftRepeats.Index, (SHAPE_REGISTER)action.Shape.Index, action.Sid, action.SourceType);

        if (action.LeftRepeats.NeedRenewal)
        {
            InstSeq.Add(loadLen);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionMfuAct1ConfDest action)
    {
        var loadLen = I.LoadImm((GP_REGISTER)action.Len.Index, (Expr)action.Len.Value);
        var inst = I.MFU_ACT1_CONF_DEST((GP_REGISTER)action.Len.Index, (SHAPE_REGISTER)action.Shape.Index);

        if (action.Len.NeedRenewal)
        {
            InstSeq.Add(loadLen);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionMfuAct1ConfDeq action)
    {
        var loadScale = I.LoadImm((GP_REGISTER)action.Scale.Index, (Expr)action.Scale.Value);
        var loadBias = I.LoadImm((GP_REGISTER)action.Bias.Index, (Expr)action.Bias.Value);
        var inst = I.MFU_ACT1_CONF_DEQ((GP_REGISTER)action.Scale.Index, (GP_REGISTER)action.Bias.Index, ToQuantType(action.QuantType), action.Sid, MFU_CONF_FUNCTION.act1_conf_deq, action.RshiftBits);

        if (action.Scale.NeedRenewal)
        {
            InstSeq.Add(loadScale);
        }

        if (action.Bias.NeedRenewal)
        {
            InstSeq.Add(loadBias);
        }

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionMfuAct1ConfQuant action)
    {
        var inst = I.MFU_ACT1_CONF_QUANT(ToQuantType(action.QuantType), MFU_CONF_FUNCTION.act1_conf_quant, action.RshiftBits);
        InstSeq.Add(inst);
    }

    private void Visit(GnneActionMfuAct1Conf action)
    {
        var inst = I.MFU_ACT1_CONF(action.Funct4, action.IsByChannel, action.Is16Segments);

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionMfuAct1Compute action)
    {
        if (action.AddrD1.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.AddrD1.Index, (Expr)action.AddrD1.Value));
        }

        if (action.AddrS1.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.AddrS1.Index, (Expr)action.AddrS1.Value));
        }

        if (action.AddrS2.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.AddrS2.Index, (Expr)action.AddrS2.Value));
        }

        if (action.AddrArg.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.AddrArg.Index, (Expr)action.AddrArg.Value));
        }

        InstSeq.Add(I.MFU_ACT1_COMPUTE((GP_REGISTER)action.AddrD1.Index, (GP_REGISTER)action.AddrS1.Index, (GP_REGISTER)action.AddrS2.Index, (GP_REGISTER)action.AddrArg.Index));
    }

    private void Visit(GnneActionMfuTransposeConf action)
    {
        var inst = I.MFU_TRANSPOSE_CONF((SHAPE_REGISTER)action.StrideD.Index, (SHAPE_REGISTER)action.StrideS.Index, ToL2Type(action.L2Datatype), action.Permute);

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionMfuTranspose action)
    {
        if (action.AddrD.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.AddrD.Index, (Expr)action.AddrD.Value));
        }

        if (action.AddrS.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.AddrS.Index, (Expr)action.AddrS.Value));
        }

        InstSeq.Add(I.MFU_TRANSPOSE((GP_REGISTER)action.AddrD.Index, (GP_REGISTER)action.AddrS.Index, (SHAPE_REGISTER)action.Shape.Index));
    }

    private void Visit(GnneActionMfuPdp1Conf1 action)
    {
        var inst = I.MFU_PDP1_CONF1(action.StrideW, action.StrideH, (SHAPE_REGISTER)action.StrideS.Index, action.Funct2, (SHAPE_REGISTER)action.StrideD.Index);

        InstSeq.Add(inst);
    }

    private void Visit(GnneActionMfuPdp1Conf2 action)
    {
        if (action.CountW.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.CountW.Index, (Expr)action.CountW.Value));
        }

        if (action.CountH.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.CountH.Index, (Expr)action.CountH.Value));
        }

        if (action.PeH.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.PeH.Index, (Expr)action.PeH.Value));
        }

        if (action.PeLastH.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.PeLastH.Index, (Expr)action.PeLastH.Value));
        }

        InstSeq.Add(I.MFU_PDP1_CONF2((GP_REGISTER)action.CountW.Index, (GP_REGISTER)action.CountH.Index, (GP_REGISTER)action.PeH.Index, (GP_REGISTER)action.PeLastH.Index));
    }

    private void Visit(GnneActionMfuPdp1Conf3 action)
    {
        if (action.PeChannels.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.PeChannels.Index, (Expr)action.PeChannels.Value));
        }

        if (action.PeLastChannels.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.PeLastChannels.Index, (Expr)action.PeLastChannels.Value));
        }

        if (action.PadValue.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.PadValue.Index, (Expr)action.PadValue.Value));
        }

        InstSeq.Add(I.MFU_PDP1_CONF3((GP_REGISTER)action.PeChannels.Index, (GP_REGISTER)action.PeLastChannels.Index, (GP_REGISTER)action.PadValue.Index, (SHAPE_REGISTER)action.Pad.Index));
    }

    private void Visit(GnneActionMfuPdp1Conf4 action)
    {
        if (action.WindowW.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.WindowW.Index, (Expr)action.WindowW.Value));
        }

        if (action.WindowH.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.WindowH.Index, (Expr)action.WindowH.Value));
        }

        if (action.Scale.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.Scale.Index, (Expr)action.Scale.Value));
        }

        InstSeq.Add(I.MFU_PDP1_CONF4((GP_REGISTER)action.WindowW.Index, (GP_REGISTER)action.WindowH.Index, (GP_REGISTER)action.Scale.Index, action.EnableH2C, action.EnableBw));
    }

    private void Visit(GnneActionMfuPdp1ConfQuant action)
    {
        if (action.Scale.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.Scale.Index, (Expr)action.Scale.Value));
        }

        if (action.Bias.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.Bias.Index, (Expr)action.Bias.Value));
        }

        InstSeq.Add(I.MFU_PDP1_CONF_QUANT((GP_REGISTER)action.Scale.Index, (GP_REGISTER)action.Bias.Index, ToQuantType(action.QuantType), MFU_CONF_FUNCTION.pdp1_conf_quant, action.RshiftBits));
    }

    private void Visit(GnneActionMfuPdp1ConfDeq action)
    {
        if (action.Scale.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.Scale.Index, (Expr)action.Scale.Value));
        }

        if (action.Bias.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.Bias.Index, (Expr)action.Bias.Value));
        }

        InstSeq.Add(I.MFU_PDP1_CONF_DEQ((GP_REGISTER)action.Scale.Index, (GP_REGISTER)action.Bias.Index, ToQuantType(action.QuantType), MFU_CONF_FUNCTION.pdp1_conf_deq, action.RshiftBits));
    }

    private void Visit(GnneActionMfuPdp1Compute action)
    {
        if (action.AddrD.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.AddrD.Index, (Expr)action.AddrD.Value));
        }

        if (action.AddrS.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.AddrS.Index, (Expr)action.AddrS.Value));
        }

        InstSeq.Add(I.MFU_PDP1_COMPUTE((GP_REGISTER)action.AddrD.Index, (GP_REGISTER)action.AddrS.Index, (SHAPE_REGISTER)action.Shape.Index));
    }

    private void Visit(GnneActionMfuMemset action)
    {
        if (action.AddrD.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.AddrD.Index, (Expr)action.AddrD.Value));
        }

        if (action.Value.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.Value.Index, (Expr)action.Value.Value));
        }

        InstSeq.Add(I.MFU_MEMSET((GP_REGISTER)action.AddrD.Index, (GP_REGISTER)action.Value.Index, (SHAPE_REGISTER)action.Stride.Index, (SHAPE_REGISTER)action.Shape.Index, ToL2Type(action.L2Datatype)));
    }

    private void Visit(GnneActionPuPdp0WConf action)
    {
        InstSeq.Add(I.PU_PDP0_W_CONF(action.TcuId, action.PuId, action.KernelH, action.KernelW));
    }

    private void Visit(GnneActionPuPdp0ModeConf action)
    {
        InstSeq.Add(I.PU_PDP0_MODE_CONF(action.TcuId, action.PuId, action.Mode));
    }

    private void Visit(GnneActionPuPdp0FetchifConf1 action)
    {
        InstSeq.Add(I.PU_PDP0_FETCHIF_CONF1(action.TcuId, action.PuId, action.StrideW, action.StrideH));
    }

    private void Visit(GnneActionPuPdp0FetchifConf2 action)
    {
        if (action.Gic.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.Gic.Index, (Expr)action.Gic.Value));
        }

        if (action.GicLast.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.GicLast.Index, (Expr)action.GicLast.Value));
        }

        InstSeq.Add(I.PU_PDP0_FETCHIF_CONF2(action.TcuId, action.PuId, (GP_REGISTER)action.Gic.Index, (GP_REGISTER)action.GicLast.Index));
    }

    private void Visit(GnneActionPuPdp0FetchifConf3 action)
    {
        InstSeq.Add(I.PU_PDP0_FETCHIF_CONF3(action.TcuId, action.PuId, (SHAPE_REGISTER)action.Shape.Index));
    }

    private void Visit(GnneActionPuPdp0FetchifConf4 action)
    {
        if (action.PadValue.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.PadValue.Index, (Expr)action.PadValue.Value));
        }

        InstSeq.Add(I.PU_PDP0_FETCHIF_CONF4(action.TcuId, action.PuId, (GP_REGISTER)action.PadValue.Index, (SHAPE_REGISTER)action.Pad.Index));
    }

    private void Visit(GnneActionPuPdp0ConfDeq action)
    {
        if (action.Bx.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.Bx.Index, (Expr)action.Bx.Value));
        }

        InstSeq.Add(I.PU_PDP0_CONF_DEQ(action.TcuId, action.PuId, (GP_REGISTER)action.Bx.Index, ToQuantType(action.QuantType)));
    }

    private void Visit(GnneActionPuPdp0OfConf action)
    {
        InstSeq.Add(I.PU_PDP0_OF_CONF(action.TcuId, action.PuId, (SHAPE_REGISTER)action.StrideD.Index, (SHAPE_REGISTER)action.ShapeD.Index));
    }

    private void Visit(GnneActionPuPdp0Compute action)
    {
        if (action.AddrS.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.AddrS.Index, (Expr)action.AddrS.Value));
        }

        InstSeq.Add(I.PU_PDP0_COMPUTE(action.TcuId, (GP_REGISTER)action.AddrS.Index));
    }

    private void Visit(GnneActionCcrDecl action)
    {
        if (action.Num.NeedRenewal)
        {
            InstSeq.Add(I.LoadImm((GP_REGISTER)action.Num.Index, (Expr)action.Num.Value));
        }

        InstSeq.Add(I.CCR_DECL((GP_REGISTER)action.Num.Index));
    }

    private void Visit(GnneActionCcrSet action)
    {
        InstSeq.Add(I.CCR_SET(action.Ccr, action.Value));
    }

    private void Visit(GnneActionCcrClr action)
    {
        InstSeq.Add(I.CCR_CLR(action.Ccr));
    }

    private void Visit(GnneActionExtrw action)
    {
        InstSeq.Add(I.LoadImm((GP_REGISTER)action.Rs, (Expr)action.S_value.Value));
        InstSeq.Add(I.EXTRW(action.Extrd, (GP_REGISTER)action.Rs, action.Imm));
    }

    private void Visit(GnneActionExtraw action)
    {
        InstSeq.Add(I.LoadImm((GP_REGISTER)action.Rs, (Expr)action.Value.Value));
        InstSeq.Add(I.EXTRAW(action.Extrd, (GP_REGISTER)action.Rs, action.Imm));
    }

    private void Visit(GnneActionAi2dCompute action)
    {
        var inst = I.AI2D_COMPUTE();
        InstSeq.Add(inst);
    }
}
