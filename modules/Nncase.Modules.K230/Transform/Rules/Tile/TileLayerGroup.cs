﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Tensors;
using Nncase.Runtime.K230;
using Nncase.Schedule;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using static Nncase.Passes.Rules.K230.TileUtilities;
using Buffer = Nncase.TIR.Buffer;
using tensor_ccr_stat = System.Collections.Generic.List<System.Tuple<Nncase.TIR.SegmentND, Nncase.Passes.Rules.K230.TensorStat>>;

namespace Nncase.Passes.Rules.K230;

public class TileLayerGroup
{
    private static int _count = -1;

    private readonly ILogger<TileLayerGroup> _logger = CompileSessionScope.GetCurrentThrowIfNull().GetRequiredService<ILogger<TileLayerGroup>>();
    private readonly Dictionary<Call, WeightGroupHandler> _weightGroups = new(); // if_first & w_first specialized
    private readonly Dictionary<Call, Tuple<int, int>> _weightSplitPattern = new();
    private readonly List<Tuple<Call, int>> _nodesQueNeedClearFake = new();

    private readonly Dictionary<IVar, Buffer> _ifBufferMap = new(ReferenceEqualityComparer.Instance);
    private readonly Dictionary<Call, L1FusedType> _l1FusedInfos = new();
    private CcrHandler _ccrHandler = new();
    private GprHandler _gpr = new();
    private SsrHandler _ssr = new();

    private NodeInfo _preNi = null!;
    private NodeInfo _ni = null!;
    private NodeInfo _l1FuseNi = null!;
    private bool _l1Fused;
    private bool _swapAB;
    private bool _h2C;
    private int _memsetValue;

    private SegmentND _ifmap = null!;
    private SegmentND _ifmap2 = null!;
    private int _ifmapOffset;
    private int _ifmap2Offset;
    private ItemName _src2ItemName;
    private SegmentND _ofmap = null!;
    private int _ofmapOffset;
    private SegmentND _ofmapSt = null!;
    private SegmentND _ifmapLd = null!;
    private SegmentND _ofmapConv = null!;
    private SegmentND _ifmapA = null!;
    private SegmentND _ifmapB = null!;

    // conv around para start
    private DataType _inputType = null!;
    private DataType _outputType = null!;
    private DataType _weightType = null!;

    // private DataType _psumType = null!;
    private DataType _if2Type = null!;

    private Call _conv = null!;
    private Call _pool = null!;
    private Call _dw = null!;
    private Call _act1 = null!;
    private Call _resize = null!;

    private int _icPerGroup;
    private int _ocPerGroup;
    private int _groupPerPass;

    private Call? _lif;
    private Call? _lw;
    private Call? _lact;
    private Call? _lwQarg;
    private Call? _sof;

    private long[]? _inputShape;
    private long[]? _outputShape;
    private long[]? _convOutputShape;
    private long[]? _weightsShape;
    private Padding? _paddingH;
    private Padding? _paddingW;
    private int _strideH;
    private int _strideW;
    private int _dilationH;
    private int _dilationW;
    private int _groups;

    private int _fusedKernelH;
    private int _fusedKernelW;
    private Padding? _fusedPaddingH;
    private Padding? _fusedPaddingW;
    private int _fusedStrideH;
    private int _fusedStrideW;
    private int _fusedDilationH;

    private int _fusedDilationW;

    // conv around para end

    // act1 around special para start
    private Call? _lif2;

    // act1 around special para end

    // pdp1 around special para start
    private Call? _pdp1;

    // pdp1 around special para start

    // transpose around special para start
    private Call? _transpose;

    // transpose around special para start

    // transpose around special para start
    private Call? _cat;

    // transpose around special para start
    private WeightGroupHandler _weightGroup = new(DataTypes.UInt8, DataTypes.UInt8);
    private SegmentND? _weight;

    private bool _isGlobalPdp;

    // record tensor info to control ccr
    private Dictionary<Call, List<tensor_ccr_stat>> _nodesWeightRec = new();
    private Dictionary<Call, List<tensor_ccr_stat>> _nodesOfmapRec = new();
    private Dictionary<Call, List<tensor_ccr_stat>> _nodesG2LIfRec = new();
    private Dictionary<Call, List<tensor_ccr_stat>> _nodesG2RWRec = new();
    private Dictionary<Call, List<tensor_ccr_stat>> _nodesL2GOfRec = new();
    private Dictionary<Call, List<tensor_ccr_stat>> _nodesL2RIf2Rec = new();
    private Dictionary<Call, List<tensor_ccr_stat>> _nodesG2RWSliceRec = new();
    private Dictionary<Call, List<tensor_ccr_stat>> _nodesAi2dIfRec = new();
    private Dictionary<Call, List<tensor_ccr_stat>> _nodesAi2dOfRec = new();
    private List<List<Tuple<Call, int>>> _nodesQuenesAsW = new(2) { new List<Tuple<Call, int>>(), new List<Tuple<Call, int>>() }; // as to of buf idx

    private int _if1BufIdx = -1;
    private int _if2BufIdx = -1;
    private int _ofBufIdx = -1;
    private int _weightBufIdx = -1;

    private enum L1FusedType
    {
        NoFused,
        FusedPool,
        FusedAct1,
        FusedDw,
    }

    public PrimFunction BuildSchedule(FusionInfo fusionInfo)
    {
        _count++;

        TiledGlb glb = new();

        InitParameters(fusionInfo);

        GetSliceInfo(fusionInfo, glb, out var currSliceInfo, out var preSliceInfo);

        List<Sequential> instSeq = new();
        List<TIR.Buffer> ifBuffers = new();
        List<TIR.Buffer> ofBuffers = new();

        _gpr = new(GNNEEnv.GprNum);
        _ssr = new(GNNEEnv.SsrNum);
        _ccrHandler = new CcrHandler();

        // mmu conf
        instSeq.Add(BuildMmu(glb));

        // loop over each slice and op
        ItemRecStatusInit(currSliceInfo);
        for (int sliceIdx = 0; sliceIdx < currSliceInfo.Count; sliceIdx++)
        {
            var curSlice = currSliceInfo[sliceIdx];
            var preSlice = preSliceInfo[sliceIdx];
            for (int nodeIdx = 0; nodeIdx < curSlice.Count;)
            {
                UpdateL2FusePara(fusionInfo, curSlice[nodeIdx], preSlice, glb, true, nodeIdx == 0); // init parameter
                ItemRecStatusUpdate();
                if (_conv is not null)
                {
                    BuildConv2d(glb, true, ifBuffers);
                }

                if (_resize is not null)
                {
                    BuildResize(glb, curSlice[sliceIdx], true);
                }

                nodeIdx += _l1Fused ? 2 : 1;
            }
        }

        UpdateCcrRecStat();
        UpdateAi2dCcrRecStat();

        _logger.LogTrace($"{fusionInfo.FusedNodes[^1].Op} -> slice: {currSliceInfo.Count}, layer: {currSliceInfo[0].Count}");
        for (int sliceIdx = 0; sliceIdx < currSliceInfo.Count; sliceIdx++)
        {
            var curSlice = currSliceInfo[sliceIdx];
            var preSlice = preSliceInfo[sliceIdx];
            bool firstSlice = sliceIdx == 0;
            var ifBuffersCopy = new List<TIR.Buffer>(ifBuffers);
            var ofBuffersCopy = new List<TIR.Buffer>(ofBuffers);

            _logger.LogTrace($"dim2-> start: {curSlice[^1].Ofmap[2].Start}, end: {curSlice[^1].Ofmap[2].End}, dim3-> start: {curSlice[^1].Ofmap[3].Start}, end: {curSlice[^1].Ofmap[3].End}");
            for (int nodeIdx = 0; nodeIdx < curSlice.Count;)
            {
                UpdateL2FusePara(fusionInfo, curSlice[nodeIdx], preSlice, glb, false, nodeIdx == 0); // init parameter

                // std::cout << fuse->subgraph().name() << "-> slice:" << slice_idx + 1 << "/" << curr_slice_info.size() << ", layer:" << std::distance(cur_slice.begin(), node_it) + ((true == l1_fused) ? 2 : 1) << "/" << cur_slice.size() << std::endl;
                if (_lif is not null)
                {
                    int iPp = 0;
                    instSeq.Add(BuildLoadIf(glb, iPp, ifBuffers, firstSlice, ifBuffersCopy));
                }

                if (_conv is not null)
                {
                    instSeq.Add(BuildConv2d(glb, false, ifBuffers, firstSlice, ifBuffersCopy));
                }

                if (_act1 is not null && !_l1Fused)
                {
                    instSeq.Add(BuildAct1(glb, ifBuffers, firstSlice, firstSlice, ifBuffersCopy));
                }

                if (_pdp1 is not null)
                {
                    instSeq.Add(BuildPdp1(glb));
                }

                if (_transpose is not null)
                {
                    instSeq.Add(BuildTranspose(glb));
                }

                if (_cat is not null)
                {
                    // build_schedule_cat(action_updater, glb, fuse);
                }

                if (_resize is not null)
                {
                    BuildResize(glb, curSlice[sliceIdx], false);
                }

                if (_sof is not null)
                {
                    int ofPp = 0;
                    instSeq.Add(BuildStore(glb, ofPp, ofBuffers, preSliceInfo[sliceIdx], firstSlice, ofBuffersCopy, fusionInfo.Fusion));
                }

                nodeIdx += _l1Fused ? 2 : 1;
            }

            Assert(ifBuffersCopy.Count == 0 && ofBuffersCopy.Count == 0);
        }

        Assert(_ccrHandler.CcrSanityCheck());

        var buffers = fusionInfo.Inputs.Select(v => _ifBufferMap[v]).ToList();
        buffers.AddRange(ofBuffers);
        return T.PrimFunc($"TileLayerGroup_{_count}", K230RtModule.Kind, buffers.Select(b => new Var(b.CheckedType)).ToArray()).Body(instSeq.ToArray()).Body(I.END(GP_REGISTER.x0)).Build();
    }

    private Sequential BuildMmu(TiledGlb glb)
    {
        List<GnneAction> actions = new();
        GnneActionUpdater actionUpdater = new(actions, glb, _ccrHandler, _gpr, _ssr);

        actionUpdater.UpdateMmuConf();

        var actionConverter = new ActionToInstruct();
        return actionConverter.Instructions(actionUpdater.Actions);
    }

    private Sequential BuildLoadIf(TiledGlb glb, int iPp, List<TIR.Buffer> ifBuffers, bool isFirstSlice, List<TIR.Buffer> ifBuffersCopy)
    {
        List<GnneAction> actions = new();
        GnneActionUpdater actionUpdater = new(actions, glb, _ccrHandler, _gpr, _ssr);

        TIR.Buffer? ddrIf;
        if (isFirstSlice)
        {
            var actualCall = _lif;
            if (_lif![GNNELoad.Input] is Call { Target: Reshape } reshape)
            {
                actualCall = reshape;
            }

            T.CreateBuffer(new(_lif![GNNELoad.Input].CheckedDataType, actualCall!.CheckedShape), MemoryLocation.Input, out ddrIf);
            ifBuffers.Add(ddrIf);
            _ifBufferMap.Add(actualCall is { Target: GNNELoad } ? (Var)actualCall[GNNELoad.Input] : (Var)actualCall[Reshape.Input], ddrIf);
        }
        else
        {
            ddrIf = ifBuffersCopy[0];
            ifBuffersCopy.RemoveAt(0);
        }

        var (ccrsToSet, ccrsToClr) = GetCcrSetAndClrVec(_ni);
        var stridesD = new[] { glb.GlbMap[ItemName.Ofmap].Dimensions[1], glb.GlbMap[ItemName.Ofmap].Dimensions[2], glb.GlbMap[ItemName.Ofmap].Dimensions[3] }.ToList();
        actionUpdater.UpdateLoadIf(_ofmapSt, _lif!, iPp, ddrIf, _ni.Nb.OfmapOffset, stridesD, ItemName.Ifmap, ccrsToSet, ccrsToClr, _h2C, _memsetValue, null!, null!, _lif!.CheckedShape.ToValueArray().Select(x => (int)x).ToArray());

        var actionConverter = new ActionToInstruct();
        return actionConverter.Instructions(actionUpdater.Actions);
    }

    private Sequential BuildStore(TiledGlb glb, int ofPp, List<TIR.Buffer> ofBuffers, Dictionary<Call, NodeInfo> preSliceInfo, bool isFirstSlice, List<TIR.Buffer> ofBuffersCopy, Fusion fusion)
    {
        List<GnneAction> actions = new();
        GnneActionUpdater actionUpdater = new(actions, glb, _ccrHandler, _gpr, _ssr);

        TIR.Buffer? ddrOf;
        if (isFirstSlice)
        {
            T.CreateBuffer(new(_sof!.CheckedDataType, fusion.Body.CheckedShape), MemoryLocation.Output, out ddrOf);
            ofBuffers.Add(ddrOf);
        }
        else
        {
            ddrOf = ofBuffersCopy[0];
            ofBuffersCopy.RemoveAt(0);
        }

        List<CcrSet> ccrsToSet;
        List<CcrClr> ccrsToClr;
        (ccrsToSet, ccrsToClr) = GetCcrSetAndClrVec(_ni);
        actionUpdater.UpdateStoreT(_ofmapSt, _sof!, ofPp, ddrOf, preSliceInfo[(_ni.Op[GNNEStore.Input] as Call)!].Nb.OfmapOffset, null!, ccrsToSet, ccrsToClr, ItemName.Ifmap);

        var actionConverter = new ActionToInstruct();
        return actionConverter.Instructions(actionUpdater.Actions);
    }

    private Sequential BuildConv2d(TiledGlb glb, bool weightGroupOnly, List<TIR.Buffer> ifBuffers, bool firstSlice = false, List<TIR.Buffer>? ifBuffersCopy = null)
    {
        List<GnneAction> actions = new();
        GnneActionUpdater actionUpdater = new(actions, glb, _ccrHandler, _gpr, _ssr);

        byte[] oldWeights = ((TensorConst)((Call)_ni.Op[GNNEConv2D.Weights])[GNNELoadW.Input]).Value.BytesBuffer.ToArray();
        byte[] newWeights = new byte[oldWeights.Length];
        Array.Copy(oldWeights, newWeights, newWeights.Length);
        T.AttachBuffer(Const.FromTensor(Tensor.FromBytes(DataTypes.UInt8, newWeights.ToArray(), new long[] { newWeights.Length })), out var ddrW);

        T.AttachBuffer((TensorConst)((Call)_ni.Op[GNNEConv2D.WeightsBias])[GNNELoadW.Input], out var ddrWQarg);
        T.AttachBuffer((TensorConst)((Call)_ni.Op[GNNEConv2D.Act])[GNNELoadW.Input], out var ddrAct);
        TIR.Buffer? ddrDW = null, ddrDWQarg = null, ddrDWAct = null, ddrAct1 = null, ddrPdpAct = null, ddrIf2 = null;
        if (_dw is not null)
        {
            byte[] oldDw = ((TensorConst)((Call)_dw[GNNEPdp0DW.Weights])[GNNELoadW.Input]).Value.BytesBuffer.ToArray();
            byte[] newDw = new byte[oldDw.Length];
            Array.Copy(oldDw, newDw, newDw.Length);
            T.AttachBuffer(Const.FromTensor(Tensor.FromBytes(DataTypes.UInt8, newDw.ToArray(), new long[] { newDw.Length })), out ddrDW);

            T.AttachBuffer((TensorConst)((Call)_dw[GNNEPdp0DW.WeightsBias])[GNNELoadW.Input], out ddrDWQarg);
            T.AttachBuffer((TensorConst)((Call)_dw[GNNEPdp0DW.Act])[GNNELoadW.Input], out ddrDWAct);
        }

        if (_pool is not null)
        {
            T.AttachBuffer((TensorConst)((Call)_pool[GNNEPdp0Reduce.Act])[GNNELoadW.Input], out ddrPdpAct);
        }

        if (_act1 is not null)
        {
            T.AttachBuffer((TensorConst)((Call)_act1[GNNEActivation.Act])[GNNELoadW.Input], out ddrAct1);
            if (_lif2 is not null)
            {
                if (_lif2[GNNELoad.Input] is not TensorConst)
                {
                    if (!weightGroupOnly && firstSlice)
                    {
                        T.CreateBuffer(new(_lif2[GNNELoad.Input].CheckedDataType, _lif2.CheckedShape), MemoryLocation.Input, out ddrIf2);
                        ifBuffers.Add(ddrIf2);
                        _ifBufferMap.Add((Var)_lif2[GNNELoad.Input], ddrIf2);
                    }
                    else
                    {
                        if (!weightGroupOnly && !firstSlice)
                        {
                            ddrIf2 = ifBuffersCopy![0];
                            ifBuffersCopy.RemoveAt(0);
                        }
                    }
                }
                else
                {
                    T.AttachBuffer((TensorConst)_lif2[GNNELoad.Input], out ddrIf2);
                }
            }
        }

        BuildScheduleConv(actionUpdater, glb, ddrW, ddrWQarg, ddrAct, ddrDW!, ddrDWQarg!, ddrDWAct!, ddrAct1!, ddrPdpAct!, ddrIf2!, weightGroupOnly, firstSlice);
        if (weightGroupOnly)
        {
            return null!;
        }

        var weights = ddrW.Const().Value.BytesBuffer;
        ArrangeWeights(_weightType, _weightsShape!, weights, _weightGroup);
        if (_dw is not null)
        {
            ArrangeDwWeights(_dw[GNNEPdp0DW.Weights].CheckedDataType, _dw[GNNEPdp0DW.Weights].CheckedShape.ToValueArray(), ddrDW!.Const().Value.BytesBuffer, _weightGroup);
        }

        var actionConverter = new ActionToInstruct();
        return actionConverter.Instructions(actionUpdater.Actions);
    }

    private Sequential BuildAct1(TiledGlb glb, List<TIR.Buffer> ifBuffers, bool firstSlice, bool isFirstSlice, List<TIR.Buffer> ifBuffersCopy)
    {
        List<GnneAction> actions = new();
        GnneActionUpdater actionUpdater = new(actions, glb, _ccrHandler, _gpr, _ssr);

        TIR.Buffer? ddrIf2 = null;
        T.AttachBuffer((TensorConst)((Call)_act1[GNNEActivation.Act])[GNNELoadW.Input], out var ddrAct1);
        if (_lif2 is not null)
        {
            if (_lif2[GNNELoad.Input] is not TensorConst)
            {
                if (isFirstSlice)
                {
                    T.CreateBuffer(new(_lif2[GNNELoad.Input].CheckedDataType, _lif2.CheckedShape), MemoryLocation.Input, out ddrIf2);
                    ifBuffers.Add(ddrIf2);
                    _ifBufferMap.Add((Var)_lif2[GNNELoad.Input], ddrIf2);
                }
                else
                {
                    ddrIf2 = ifBuffersCopy[0];
                    ifBuffersCopy.RemoveAt(0);
                }
            }
            else
            {
                T.AttachBuffer((TensorConst)_lif2[GNNELoad.Input], out ddrIf2);
            }
        }

        BuildScheduleAct1(actionUpdater, glb, ddrAct1, ddrIf2!, firstSlice);
        var actionConverter = new ActionToInstruct();
        return actionConverter.Instructions(actionUpdater.Actions);
    }

    private Sequential BuildPdp1(TiledGlb glb)
    {
        List<GnneAction> actions = new();
        GnneActionUpdater actionUpdater = new(actions, glb, _ccrHandler, _gpr, _ssr);
        BuildSchedulePdp1(actionUpdater, glb);
        var actionConverter = new ActionToInstruct();
        return actionConverter.Instructions(actionUpdater.Actions);
    }

    private Sequential BuildTranspose(TiledGlb glb)
    {
        List<GnneAction> actions = new();
        GnneActionUpdater actionUpdater = new(actions, glb, _ccrHandler, _gpr, _ssr);

        BuildScheduleTranspose(actionUpdater, glb);
        var actionConverter = new ActionToInstruct();
        return actionConverter.Instructions(actionUpdater.Actions);
    }

    private Sequential BuildResize(TiledGlb glb, NodeInfo currNode, bool weightGroupOnly, bool firstSlice = false)
    {
        List<GnneAction> actions = new();
        GnneActionUpdater actionUpdater = new(actions, glb, _ccrHandler, _gpr, _ssr);
        BuildScheduleResize(actionUpdater, glb, weightGroupOnly, currNode);
        if (!weightGroupOnly)
        {
            var actionConverter = new ActionToInstruct();
            return actionConverter.Instructions(actionUpdater.Actions);
        }

        return null!;
    }

    private void BuildScheduleConv(GnneActionUpdater actionUpdater, TiledGlb glb, TIR.Buffer ddrW, TIR.Buffer ddrWQarg, TIR.Buffer ddrAct, TIR.Buffer ddrDw, TIR.Buffer ddrDWQarg, TIR.Buffer ddrDWAct, TIR.Buffer ddrAct1, TIR.Buffer ddrPdpAct, TIR.Buffer ddrIf2, bool weightGroupOnly, bool firstSlice = false)
    {
        int iPp = 0;
        int ofPp = 0;
        int wPp = 0;

        if (!weightGroupOnly && firstSlice)
        {
            // weights bias
            if (_weightType == DataTypes.UInt8 || _weightType == DataTypes.Int16)
            {
                List<CcrSet> ccrsToSet = new();
                List<CcrClr> ccrsToClr = null!;
                ccrsToSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.WQarg)), 1));
                actionUpdater.UpdateLoadWQarg(_lwQarg!, _weightGroup, ddrWQarg, _ni.Nb.WeightQargOffset, ccrsToSet, ccrsToClr);
            }

            // act param
            {
                List<CcrSet> ccrsToSet = new();
                List<CcrClr> ccrsToClr = new();
                ccrsToSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Act)), 1));
                actionUpdater.UpdateLoadAct(_lact!, ddrAct, ItemName.Act, _ni.Nb.ActOffset, ccrsToSet, ccrsToClr);
            }

            if (_dw is not null)
            {
                // load dw weights
                // load dw w qarg
                List<CcrSet> ccrsToSet = new();
                List<CcrClr> ccrsToClr = null!;
                ccrsToSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.DwWeight)), 1));
                actionUpdater.UpdateLoadDw(_dw, _weightGroup, ddrDw, _l1FuseNi.Nb.DwWeightOffset, ccrsToSet, ccrsToClr!);
                if (_dw[GNNEPdp0DW.Weights].CheckedDataType == DataTypes.UInt8)
                {
                    ccrsToSet = new();
                    ccrsToClr = null!;
                    ccrsToSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.DwQarg)), 1));
                    actionUpdater.UpdateLoadDwQarg(_dw, _weightGroup, ddrDWQarg, _l1FuseNi.Nb.DwWeightQargOffset, ccrsToSet, ccrsToClr);
                }
            }

            if (_act1 is not null)
            {
                // load act1
                List<CcrSet> ccrsToSet = new();
                List<CcrClr> ccrsToClr = null!;
                ccrsToSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.MfuAct1)), 1));
                actionUpdater.UpdateLoadAct((_act1[GNNEActivation.Act] as Call)!, ddrAct1, ItemName.MfuAct1, _l1FuseNi.Nb.Act1Offset, ccrsToSet, ccrsToClr);
            }

            // load act0
            if (_dw is not null)
            {
                List<CcrSet> ccrsToSet = new();
                List<CcrClr> ccrsToClr = null!;
                ccrsToSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.DwAct1)), 1));
                actionUpdater.UpdateLoadAct((_dw[GNNEPdp0DW.Act] as Call)!, ddrDWAct, ItemName.DwAct1, _l1FuseNi.Nb.DwActOffset, ccrsToSet, ccrsToClr);
            }
            else if (_pool is not null)
            {
                List<CcrSet> ccrsToSet = new();
                List<CcrClr> ccrsToClr = null!;
                ccrsToSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.PdpAct1)), 1));
                actionUpdater.UpdateLoadAct((_pool[GNNEPdp0Reduce.Act] as Call)!, ddrPdpAct, ItemName.PdpAct1, _l1FuseNi.Nb.Pdp0ActOffset, ccrsToSet, ccrsToClr);
            }
        }

        _weightGroup.Current_aligned_offset_init();
        if (weightGroupOnly && _weightBufIdx != -1)
        {
            _nodesWeightRec[_conv][_weightBufIdx].Add(new(_weight!, new(false, false)));
        }

        if (!weightGroupOnly)
        {
            if (_act1 is not null && _lif2 is not null && _ifmap[1].End == _inputShape![1])
            {
                Assert(_if2BufIdx == -1);
                int ccrSetNum = _nodesL2RIf2Rec[_conv][0][0].Item2 is { IsFirstSlice: true, IsLastSlice: true } ? 1 : 2;
                List<CcrSet> ccrSetIfmap2 = new() { new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ifmap2)), ccrSetNum) };
                List<int> stridesD = new() { glb.GlbMap[ItemName.Ifmap2].Dimensions[1], glb.GlbMap[ItemName.Ifmap2].Dimensions[2], glb.GlbMap[ItemName.Ifmap2].Dimensions[3] };
                actionUpdater.UpdateLoadIf(_ifmap2, _lif2, ofPp, ddrIf2, _ifmap2Offset, stridesD, _src2ItemName, ccrSetIfmap2);
            }
        }

        BuildL1Schedule(actionUpdater, glb, _ifmap, _weight!, _ofmap, iPp, ofPp, wPp, _ifmap2, weightGroupOnly, _weightGroup, ddrW);

        if (weightGroupOnly)
        {
            _nodesOfmapRec[_conv][_ofBufIdx].Add(new(_ofmap, new(false, false)));
        }
        else
        {
            _nodesOfmapRec[_conv][_ofBufIdx].RemoveAt(0);
        }
    }

    private void BuildScheduleResize(GnneActionUpdater actionUpdater, TiledGlb glb, bool weightGroupOnly, NodeInfo currNode)
    {
        // if (!weight_group_only)
        // {
        //     get_resize_rec_iter(resize);
        // }
        //
        // List<CcrSet> ccrs_to_set = new();
        // List<CcrClr> ccrs_to_clr = new();
        // Segment1D glb_input_row = new (  0..input_shape[2], Padding.Zero()  );
        // Segment1D glb_input_column =new  (  0..input_shape[3], Padding.Zero()  );
        // ifmap = new (  ifmap[0], ifmap[1], glb_input_row, glb_input_column  );
        //
        // ai2d_config config;
        // ai2d_util_kpu.update_static_param(config, resize);
        // ai2d_util_kpu.update_resize_param(config, resize);
        // int[] dst_hw = new int[2];
        // ai2d_util.resize_sram_search(config, ofmap, ifmap, dst_hw);
        //
        // List<Segment1D> output_batch_seg = get_segment_start_end_length(0, 1, output_shape[0]);
        // List<Segment1D> sram_c_segs = get_segment_start_end_length(0, 4, ofmap[1].Length);
        // List<Segment1D> sram_h_segs = get_segment_start_end_length(0, dst_hw[0], ofmap[2].Length);
        // List<Segment1D> sram_w_segs = get_segment_start_end_length(0, dst_hw[1], ofmap[3].Length);
        //
        // // ai2d
        // List<CcrClr> ccr_clr_ifmap  =new();
        // int inst_len = 0;
        // foreach (var glb_output_batch in output_batch_seg)
        // {
        //     Segment1D glb_input_batch = glb_output_batch;
        //     foreach (var sram_oc in sram_c_segs)
        //     {
        //         bool c_changed = true;
        //         foreach (var sram_oh in sram_h_segs)
        //         {
        //             foreach (var sram_ow in sram_w_segs)
        //             {
        //                 if (!weight_group_only)
        //                 {
        //                     ai2d_util_kpu.update_resize_param(config, resize);
        //                     List<float> dst_00 = new List<float>() { sram_ow.Start, sram_oh.Start };
        //                     List<float> dst_xy =new List<float>(){ sram_ow.End - 1, sram_oh.End - 1 };
        //                     List<float> M_ori_scale = new List<float>(){ config.origin_M(config.M0), config.origin_M(config.M1),
        //                         config.origin_M(config.M3), config.origin_M(config.M4) };
        //                     List<float> M_ori_bias = new List<float>(){ config.origin_M(config.M2), config.origin_M(config.M5) };
        //
        //                     var src_00 = ai2d_util.M_mul_add(M_ori_scale, M_ori_bias, dst_00);
        //                     var src_xy = ai2d_util.M_mul_add(M_ori_scale, M_ori_bias, dst_xy);
        //
        //                     int src_x = (int)Math.Max(Math.Floor((double)src_00[0]), 0);
        //                     int src_y = (int)Math.Max(Math.Floor((double)src_00[1]), 0);
        //                     int src_width_end = (int)Math.Min(Math.Ceiling((double)src_xy[0]), input_shape[3] - 1);
        //                     int src_height_end = (int)Math.Min(Math.Ceiling((double)src_xy[1]), input_shape[2] - 1);
        //
        //                     List<float> ori_00 = new List<float>(){ 0, 0 };
        //                     var ori_src_00 = ai2d_util.M_mul_add(M_ori_scale, M_ori_bias, ori_00);
        //                     float offset_M2 = 0.0f;
        //                     if (src_x != 0)
        //                         offset_M2 = src_00[0] - src_x - ori_src_00[0];
        //                     float offset_M5 = 0.0f;
        //                     if (src_y != 0)
        //                         offset_M5 = src_00[1] - src_y - ori_src_00[1];
        //
        //                     int src_width = src_width_end - src_x + 1;
        //                     int src_height = src_height_end - src_y + 1;
        //
        //                     Segment1D sram_h =new(  src_y..(src_y+src_height), Padding.Zero()  );
        //                     Segment1D sram_w = new (  src_x..(src_x + src_width), Padding.Zero()  );
        //                     SegmentND ifmap_pp  = new (  glb_input_batch, sram_oc, sram_h, sram_w  );
        //                     SegmentND ofmap_pp  = new (  glb_output_batch, sram_oc, sram_oh, sram_ow  );
        //
        //                     ai2d_util_kpu.update_dynamic_param(glb, config, ifmap_pp, ofmap_pp, 0, ifmap, ofmap, offset_M2, offset_M5, input_type, output_type, ifmap_offset, ofmap_offset);
        //
        //                     int if_clr_cnt = ai2d_if_rec_iter[if1_buf_idx][0].Item2.stat_cnt();
        //                     ai2d_if_rec_iter[if1_buf_idx].RemoveAt(0);
        //                     if (if_clr_cnt == 1)
        //                         ccr_clr_ifmap.Add(new(  ccr_handler_.get_ccr_item(ccr_handler_.get_name(item_name.ofmap, if1_buf_idx))  ));
        //
        //                     int ccr_set_cnt = 0;
        //                     int ccr_set_value = 0;
        //                     var of_ccr_stat = ai2d_of_rec_iter[of_buf_idx][0];
        //                     ai2d_of_rec_iter[of_buf_idx].RemoveAt(0);
        //                     if (of_ccr_stat.Item2.is_last_slice)
        //                     {
        //                         ccr_set_cnt = 1;
        //                         ccr_set_value = get_ccr_set_according_post_nodes();
        //                     }
        //
        //                         List<CcrSet> ccr_set_ofmap = new();
        //                     if (ccr_set_cnt > 0)
        //                     ccr_set_ofmap.Add(new(  ccr_handler_.get_ccr_item(ccr_handler_.get_name(item_name.ofmap, of_buf_idx)), ccr_set_value  ) );
        //
        //                     bool write_all = false;
        //                     if (c_changed)
        //                     {
        //                         inst_len += 16 * 9;
        //                         c_changed = false;
        //                         write_all = true;
        //                     }
        //                     else
        //                     {
        //                         inst_len += 16 * 2;
        //                     }
        //
        //                     bool is_last_hw = sram_oh == sram_h_segs[^1] && sram_ow == sram_w_segs[^1];
        //                     int next_inst_len = inst_len;
        //                     if (!is_last_hw)
        //                     {
        //                         next_inst_len += 16 * 2;
        //                     }
        //                     else if (sram_oc != sram_c_segs[^1])
        //                     {
        //                         next_inst_len += 16 * 9;
        //                     }
        //                     if (next_inst_len > 1024)
        //                     {
        //                         inst_len = 0;
        //                         config.intr_mask = 0;
        //                     }
        //                     else if (sram_oc == sram_c_segs[^1] && sram_oh == sram_h_segs[^1] && sram_ow == sram_w_segs[^1])
        //                     {
        //                         config.intr_mask = 0;
        //                     }
        //                     else
        //                     {
        //                         config.intr_mask = 1;
        //                     }
        //
        //                     // config all ext register
        //                     action_updater.update_ai2d_resize(config, env_, ccr_set_ofmap, ccr_clr_ifmap, write_all);
        //                     if (config.intr_mask == 0)
        //                         ccr_clr_ifmap.Clear();
        //                 }
        //                 else
        //                 {
        //                     ai2d_if_rec_iter[if1_buf_idx].Add(new(ifmap, new tensor_stat (  false, false  )));
        //                     ai2d_of_rec_iter[of_buf_idx].Add(new (ofmap, new tensor_stat (  false, false  )));
        //                 }
        //             }
        //         }
        //     }
        // }
        //
        // if (weight_group_only)
        // {
        //     ofmap_rec_iter[of_buf_idx].Add(new (ofmap, new tensor_stat (  false, false  )));
        // }
        // else
        // {
        //     ofmap_rec_iter[of_buf_idx].RemoveAt(0);
        // }
    }

    private void BuildScheduleAct1(GnneActionUpdater actionUpdater, TiledGlb glb, TIR.Buffer ddrAct, TIR.Buffer ddrIf2, bool firstSlice = false)
    {
        int iPp = 0;

        // load act for all
        List<CcrSet> ccrsToSet = new();
        List<CcrClr> ccrsToClr = null!;
        if (firstSlice)
        {
            ccrsToSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.MfuAct1)), 1));
            actionUpdater.UpdateLoadAct(_lact!, ddrAct, ItemName.MfuAct1, _ni.Nb.Act1Offset, ccrsToSet, ccrsToClr);
        }

        if (_lif2 is not null)
        {
            // load src2
            List<CcrSet> ccrSetIfmap = new() { new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ifmap2)), 1) };
            List<int> stridesD = new() { glb.GlbMap[ItemName.Ifmap2].Dimensions[1], glb.GlbMap[ItemName.Ifmap2].Dimensions[2], glb.GlbMap[ItemName.Ifmap2].Dimensions[3] };
            actionUpdater.UpdateLoadIf(_ifmap2, _lif2, iPp, ddrIf2, _ifmap2Offset, stridesD, ItemName.Ifmap2, ccrSetIfmap);
        }

        // activation
        bool inputBIsUninit = _act1[GNNEActivation.InputB] == None.Default;
        var input1Type = _act1[GNNEActivation.InputA].CheckedDataType;
        var input2Type = input1Type;
        if (!inputBIsUninit)
        {
            input2Type = _act1[GNNEActivation.InputB].CheckedDataType;
        }

        DeQuantizeParam deqParams1 = new(0, 1);
        if (input1Type != DataTypes.Float16)
        {
            deqParams1 = ((TensorConst)_act1[GNNEActivation.DeqAParams]).Value.ToScalar<DeQuantizeParam>();
        }

        var deqParams2 = deqParams1;
        if (!inputBIsUninit && input2Type != DataTypes.Float16)
        {
            deqParams2 = ((TensorConst)_act1[GNNEActivation.DeqBParams]).Value.ToScalar<DeQuantizeParam>();
        }

        int rshiftBits1 = ((TensorConst)_act1[GNNEActivation.InAShiftBits]).Value.ToScalar<int>();
        int rshiftBits2 = rshiftBits1;
        if (!inputBIsUninit)
        {
            rshiftBits2 = ((TensorConst)_act1[GNNEActivation.InBShiftBits]).Value.ToScalar<int>();
        }

        int outShiftBits = ((TensorConst)_act1[GNNEActivation.OutShiftBits]).Value.ToScalar<int>();
        bool is16Segments = ((TensorConst)_act1[GNNEActivation.Is16Segments]).Value.ToScalar<bool>();

        if (_act1[GNNEActivation.InputA] is Call { Target: GNNELoad } && !inputBIsUninit && _lif2 is not null && _swapAB)
        {
            input1Type = _act1[GNNEActivation.InputB].CheckedDataType;
            input2Type = _act1[GNNEActivation.InputA].CheckedDataType;
            deqParams1 = ((TensorConst)_act1[GNNEActivation.DeqBParams]).Value.ToScalar<DeQuantizeParam>();
            deqParams2 = ((TensorConst)_act1[GNNEActivation.DeqAParams]).Value.ToScalar<DeQuantizeParam>();
            rshiftBits1 = ((TensorConst)_act1[GNNEActivation.InBShiftBits]).Value.ToScalar<int>();
            rshiftBits2 = ((TensorConst)_act1[GNNEActivation.InAShiftBits]).Value.ToScalar<int>();
        }

        (ccrsToSet, ccrsToClr) = GetCcrSetAndClrVec(_ni);
        if (_lif2 is not null)
        {
            ccrsToClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ifmap2))));
        }

        if (firstSlice)
        {
            ccrsToClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.MfuAct1))));
        }

        List<int> if2Stride = null!;
        var ifmapSrc1 = _ifmap;
        var ifmapSrc2 = _ifmap2;
        if (_lif2 is null && _ifmap2.Shape_size != 0)
        {
            if2Stride = new() { glb.GlbMap[ItemName.Ifmap2].Dimensions[1], glb.GlbMap[ItemName.Ifmap2].Dimensions[2], glb.GlbMap[ItemName.Ifmap2].Dimensions[3] };
            ifmapSrc1 = _ifmapA;
            ifmapSrc2 = _ifmapB;
        }

        if (_ifmap2.Shape_size == 0 && (ifmapSrc1.Shape_size != 0) && ((_ofmap[0].Length % ifmapSrc1[0].Length != 0) || (_ofmap[1].Length % ifmapSrc1[1].Length != 0) || (_ofmap[2].Length % ifmapSrc1[2].Length != 0) || (_ofmap[3].Length % ifmapSrc1[3].Length != 0)))
        {
            ifmapSrc1 = _ofmap;
        }

        if (_ifmap2.Shape_size != 0 && _ifmap.Shape_size > _ifmap2.Shape_size && _ifmap.Shape_size % _ifmap2.Shape_size != 0)
        {
            Assert(_ifmap[0].Start <= _ifmap2[0].Start
                && _ifmap[1].Start <= _ifmap2[1].Start
                && _ifmap[2].Start <= _ifmap2[2].Start
                && _ifmap[3].Start <= _ifmap2[3].Start);
            _ifmapOffset += GetSliceOffsetInTensor(_ifmap, _ifmap2) * GetBytesPerElement(input1Type);
        }
        else if (_ifmap2.Shape_size != 0 && _ifmap2.Shape_size > _ifmap.Shape_size && _ifmap2.Shape_size % _ifmap.Shape_size != 0)
        {
            Assert(_ifmap[0].Start >= _ifmap2[0].Start
                && _ifmap[1].Start >= _ifmap2[1].Start
                && _ifmap[2].Start >= _ifmap2[2].Start
                && _ifmap[3].Start >= _ifmap2[3].Start);
            _ifmap2Offset += GetSliceOffsetInTensor(_ifmap2, _ifmap) * GetBytesPerElement(input2Type);
        }

        actionUpdater.UpdateMfuAct1(ifmapSrc1, ifmapSrc2, _ofmap, ACT1_SOURCE_TYPE.l2, ACT1_SOURCE_TYPE.l2, input1Type, input2Type, _act1.CheckedDataType, deqParams1, deqParams2, rshiftBits1, rshiftBits2, outShiftBits, is16Segments, iPp, ccrsToSet, ccrsToClr, _ifmapOffset, _ifmap2Offset, _ni.Nb.OfmapOffset, _ni.Nb.Act1Offset, _src2ItemName, ItemName.Ifmap, ItemName.MfuAct1, ((GNNEActivation)_act1.Target).Type == GnneActivationType.Mul ? MFU_ACT1_FUNCTION.mul : MFU_ACT1_FUNCTION.add, ItemName.Ofmap, null!, if2Stride);
    }

    private void BuildSchedulePdp1(GnneActionUpdater action_updater, TiledGlb glb)
    {
        int iPp = 0;

        // pool
        if (!_isGlobalPdp)
        {
            List<CcrSet> ccrsToSet;
            List<CcrClr> ccrsToClr;
            (ccrsToSet, ccrsToClr) = GetCcrSetAndClrVec(_ni);
            int[] padding = ((TensorConst)_pdp1![GNNEPdp1.Padding]).Value.ToArray<int>();
            Padding paddingH = new(padding[0], padding[1]);
            Padding paddingW = new(padding[2], padding[3]);
            int[] filters = ((TensorConst)_pdp1[GNNEPdp1.Filter]).Value.ToArray<int>();
            int[] strides = ((TensorConst)_pdp1[GNNEPdp1.Stride]).Value.ToArray<int>();
            var glbInputRow = GetInputRowSegment(_ofmap[2].Start, _ofmap[2].Length, (int)_inputShape![2], filters[0], strides[0], 1, paddingH);
            var glbInputColumn = GetInputRowSegment(_ofmap[3].Start, _ofmap[3].Length, (int)_inputShape[3], filters[1], strides[1], 1, paddingW);
            SegmentND ifmapResize = new(_ifmap[0], _ifmap[1], glbInputRow, glbInputColumn);
            int bytesPerElement = GetBytesPerElement(_pdp1[GNNEPdp1.Input].CheckedDataType);
            int c = glb.GlbMap[ItemName.Ifmap].Dimensions[1];
            int h = glb.GlbMap[ItemName.Ifmap].Dimensions[2];
            int w = glb.GlbMap[ItemName.Ifmap].Dimensions[3];
            int sliceOffset = ((ifmapResize[0].Start - _ifmap[0].Start) * c * h * w * bytesPerElement)
                               + ((ifmapResize[1].Start - _ifmap[1].Start) * h * w * bytesPerElement)
                               + ((ifmapResize[2].Start - _ifmap[2].Start) * w * bytesPerElement)
                               + ((ifmapResize[3].Start - _ifmap[3].Start) * bytesPerElement) + _ifmapOffset;

            action_updater.UpdateMfuPdp1(_pdp1, ifmapResize, _ofmap, iPp, ccrsToSet, ccrsToClr, sliceOffset, _ni.Nb.OfmapOffset);
        }
        else
        {
            List<CcrSet> ccrsToSet;
            List<CcrClr> ccrsToClr;
            (ccrsToSet, ccrsToClr) = GetCcrSetAndClrVec(_ni);

            int kernelH = ((TensorConst)_pdp1![GNNEPdp1.Filter]).Value.ToArray<int>()[0];
            int kernelW = ((TensorConst)_pdp1[GNNEPdp1.Filter]).Value.ToArray<int>()[1];

            (int R, int S) SplitGlobalPdp1(int w, int h)
            {
                int r = h > 16 ? 16 : h;
                int s = Math.Min(Math.Min(256 / r, w), 64);
                return (r, s);
            }

            (int r, int s) = SplitGlobalPdp1(kernelW, kernelH);

            var ofmapMid = new SegmentND(_ofmap);
            int hMid = GetSegmentStartEndLength(0, r, (int)_inputShape![2]).Count;
            ofmapMid[2] = new(..hMid, Padding.Zero());

            var lastGlb = glb.GlbMap[ItemName.Ofmap];
            glb.GlbMap[ItemName.Ofmap] = new(new[] { ofmapMid[0].Length, ofmapMid[1].Length, ofmapMid[2].Length, lastGlb.Dimensions[3] }, DataTypes.Float16, 0, lastGlb.Mmu);

            List<SegmentND> ofmapAll = new() { ofmapMid };
            List<SegmentND> ifmapAll = new() { _ifmap };

            for (int t = 0; t < ofmapAll.Count; t++)
            {
                var ofmap = ofmapAll[t];
                var ifmap = ifmapAll[t];

                var rSegs = GetSegmentStartEndLength(0, r, (int)_inputShape[2]);
                var segs = GetSegmentStartEndLength(0, s, (int)_inputShape[3]);

                var ifmapPp = new SegmentND(ifmap);
                int alignedW = GetAlignedNum(ifmap[3].Length, GetBytesPerElement(_inputType) == 1 ? 32 : 16);
                SegmentND ifmapPpAligned = new(ifmap[0], ifmap[1], ifmap[2], new(..alignedW, Padding.Zero()));
                alignedW = GetAlignedNum(segs.Count, 16);
                SegmentND ofmapPp = new(ofmap[0], ofmap[1], new(..rSegs.Count, Padding.Zero()), new(..segs.Count, Padding.Zero()));
                SegmentND ofmapPpAligned = new(ofmap[0], ofmap[1], new(..rSegs.Count, Padding.Zero()), new(..alignedW, Padding.Zero()));

                // pool
                PDP_FUNCTION PdpFunc(MFU_PDP_OP op)
                {
                    return op switch
                    {
                        MFU_PDP_OP.MIN => PDP_FUNCTION.min,
                        MFU_PDP_OP.MAX => PDP_FUNCTION.max,
                        MFU_PDP_OP.AVERAGE => PDP_FUNCTION.average,
                        MFU_PDP_OP.SUM => PDP_FUNCTION.sum,
                        _ => PDP_FUNCTION.min,
                    };
                }

                var pdpOp = PdpFunc(((GNNEPdp1)_pdp1.Target).ReduceOp == MFU_PDP_OP.AVERAGE ? MFU_PDP_OP.SUM : ((GNNEPdp1)_pdp1.Target).ReduceOp);

                // float sum_scale_1_f32 = pool->pdp_op() == pdp_function_t::average ? 1.f / (input_shape[2] * input_shape[3]) : 1.f;
                // float sum_scale_2_f32 = 1.f;
                // int32_t shift = int32_t(std::log2f(1.f / sum_scale_1_f32));
                var sumScale1 = (Half)(1.0f / _inputShape[2]);
                var sumScale2 = (Half)(1.0f / _inputShape[3]);

                for (int i = 0; i < rSegs.Count; i++)
                {
                    for (int j = 0; j < segs.Count; j++)
                    {
                        int ccrClrNum = i == 0 && j == 0 ? 1 : 0;
                        List<CcrClr> ccrClrIfmap = new();
                        if (ccrClrNum > 0)
                        {
                            ccrClrIfmap.Add(ccrsToClr[0]);
                        }

                        SegmentND ifmapSeg = new(ifmapPp[0], ifmapPp[1], rSegs[i], segs[j]);
                        SegmentND ofmapSeg = new(ofmapPp[0], ofmapPp[1], new(i..(i + 1), Padding.Zero()), new(j..(j + 1), Padding.Zero()));

                        List<int> ofStride = new() { ofmapPpAligned[1].Length, ofmapPpAligned[2].Length, ofmapPpAligned[3].Length };
                        int offsetS = (GetSliceOffsetInTensor(ifmapPpAligned, ifmapSeg) * GetBytesPerElement(_inputType)) + _ifmapOffset;
                        int offsetD = (GetSliceOffsetInTensor(ofmapPpAligned, ofmapSeg) * GetBytesPerElement(DataTypes.Float16)) + _ofmapOffset;

                        action_updater.UpdateMfuGlobalPdp1(_pdp1, _inputType, DataTypes.Float16, pdpOp, ifmapSeg, ofmapSeg, iPp, sumScale1, null!, ccrClrIfmap, offsetS, offsetD, ItemName.Ifmap, null!, ofStride);
                    }
                }

                var ifmapLast = ofmapPp;
                SegmentND ofmapLast = new(ofmapPp[0], ofmapPp[1], new(..1, Padding.Zero()), new(..1, Padding.Zero()));
                {
                    List<int> ifStride = new() { ofmapPp[1].Length, ofmapPp[2].Length, ofmapPpAligned[3].Length };
                    List<int> ofStride = new() { ofmapPp[1].Length, ofmapLast[2].Length, lastGlb.Dimensions[3] };
                    int offsetS = _ofmapOffset;
                    int offsetD = _ofmapOffset;
                    action_updater.UpdateMfuGlobalPdp1(_pdp1, DataTypes.Float16, _pdp1.CheckedDataType, pdpOp, ifmapLast, ofmapLast, iPp, sumScale2, ccrsToSet, null!, offsetS, offsetD, ItemName.Ofmap, ifStride, ofStride);
                }
            }

            // restore glb info
            glb.GlbMap[ItemName.Ofmap] = lastGlb;
        }
    }

    private void BuildScheduleTranspose(GnneActionUpdater actionUpdater, TiledGlb glb)
    {
        int iPp = 0;
        var perm = ((GNNETranspose)_transpose!.Target).Perm;
        List<CcrSet> ccrsToSet;
        List<CcrClr> ccrsToClr;
        (ccrsToSet, ccrsToClr) = GetCcrSetAndClrVec(_ni);
        actionUpdater.UpdateMfuTranspose(_ifmap, _ofmap, _inputType, perm, iPp, ccrsToSet, ccrsToClr, _ifmapOffset, _ofmapOffset);
    }

    private void BuildL1Schedule(GnneActionUpdater actionUpdater, TiledGlb glb, SegmentND ifmap1, SegmentND weight1, SegmentND psum, int iPp, int ofPp, int wPp, SegmentND ifmap2, bool weightGroupOnly, WeightGroupHandler weightGroup, TIR.Buffer ddrW)
    {
        int l1Pp = 0;
        var l1Tile = L1Search(glb, weight1, psum);
        var eSeg = GetSegmentStartEndLength(psum[2].Start, l1Tile[2], psum[2].End);
        var fSeg = GetSegmentStartEndLength(psum[3].Start, l1Tile[3], psum[3].End);
        var nSeg = GetSegmentStartEndLength(psum[0].Start, 1, psum[0].End);
        var rSeg = GetSegmentStartEndLength(weight1[2].Start, l1Tile[4], weight1[2].End);
        var seg = GetSegmentStartEndLength(weight1[3].Start, l1Tile[5], weight1[3].End);
        var ret = GetL1McSeg(ifmap1, psum, l1Tile[1], l1Tile[0]);
        var mSeg = ret[0];
        var cSeg = ret[1];

        int rLen = l1Tile[4];
        int len = Math.Min(GNNEEnv.PuKernelSpad / 2, l1Tile[5]);
        if (_dilationH > 1)
        {
            rLen = 1;
        }

        if (_dilationW > 1)
        {
            len = 1;
        }

        bool restartUpdateG2LIf = true;
        bool restartUpdateWeight = true;
        bool restartUpdateOfmap = true;
        bool restartUpdateL2RIf2 = true;
        foreach (var m in mSeg)
        {
            foreach (var n in nSeg)
            {
                foreach (var e in eSeg)
                {
                    // TODO: 支持1x1(conv)
                    Segment1D hConvOut;
                    if (Conv1X1(_conv))
                    {
                        int eStart = e.Start * _ofmapSt[2].Length;
                        int eLength = e.Length * _ofmapSt[2].Length;
                        hConvOut = GetInputRowSegment(eStart, eLength, (int)_conv.CheckedShape.ToValueArray()[2], _fusedKernelH, _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
                        hConvOut = hConvOut / _ofmapConv[2].Length;
                    }
                    else
                    {
                        hConvOut = GetInputRowSegment(e.Start, e.Length, (int)_convOutputShape![2], _fusedKernelH, _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
                    }

                    var h = GetInputRowSegment(hConvOut.Start, hConvOut.Length, (int)_inputShape![2], (int)_weightsShape![2], _strideH, _dilationH, _paddingH!);

                    foreach (var f in fSeg)
                    {
                        Segment1D wConvOut;
                        if (Conv1X1(_conv))
                        {
                            Assert(f.Length == _ofmap[3].Length);
                            int fStart = f.Start / _ofmapSt[2].Length;
                            int fLength = f.Length / _ofmapSt[2].Length;
                            wConvOut = GetInputRowSegment(fStart, fLength, (int)_conv.CheckedShape.ToValueArray()[3], _fusedKernelW, _fusedStrideW, _fusedDilationW, _fusedPaddingW!);
                            wConvOut = wConvOut * _ofmapConv[2].Length;
                        }
                        else
                        {
                            wConvOut = GetInputColumnSegment(f.Start, f.Length, (int)_convOutputShape![3], _fusedKernelW, _fusedStrideW, _fusedDilationW, _fusedPaddingW!);
                        }

                        var w = GetInputColumnSegment(wConvOut.Start, wConvOut.Length, (int)_inputShape[3], (int)_weightsShape[3], _strideW, _dilationW, _paddingW!);

                        SegmentND l2GOf = new(n, m, e, f);
                        RestoreTensorShape(_conv, ItemName.Ofmap, l2GOf);

                        SegmentND r2LPsum = new(n, m, hConvOut, wConvOut);
                        SegmentND r2LPsumRestore = new(n, m, hConvOut, wConvOut);
                        RestoreTensorShape(_conv, ItemName.Psum, r2LPsumRestore);

                        int cs0 = m.Start / _ocPerGroup * _icPerGroup;
                        int ce0 = ((m.End - 1) / _ocPerGroup * _icPerGroup) + _icPerGroup;
                        Segment1D wcInloop = new(..0, Padding.Zero());

                        bool ofmapCalStart = cSeg[0].Start == 0;
                        foreach (var c in cSeg)
                        {
                            if (c.Start < cs0 || c.End > ce0)
                            {
                                continue;
                            }

                            int cs = c.Start % _icPerGroup;
                            int ce = Math.Min(cs + c.Length, _icPerGroup);
                            wcInloop = new(cs..ce, Padding.Zero());

                            foreach (var rL2 in rSeg)
                            {
                                foreach (var l2 in seg)
                                {
                                    var cInloop = c;
                                    var rInloopSeg = GetSegmentStartEndLength(rL2.Start, rLen, rL2.End);
                                    var inloopSeg = GetSegmentStartEndLength(l2.Start, len, l2.End);
                                    var nInloop = n;
                                    var mInloop = m;
                                    SegmentND ifmapSlice = new(nInloop, cInloop, h, w);
                                    SegmentND weightSlice = new(m, wcInloop, rL2, l2);

                                    bool dmLoadIfSn = false;
                                    var g2LIf = ShiftInputTensor(ifmapSlice, weightSlice, (int)_weightsShape[2], (int)_weightsShape[3], _strideH, _strideW, _dilationH, _dilationW);
                                    if (g2LIf[0].Length > 0 && g2LIf[1].Length > 0 && g2LIf[2].Length > 0 && g2LIf[3].Length > 0)
                                    {
                                        dmLoadIfSn = true;
                                        if (!weightGroupOnly)
                                        {
                                            int ifClrCnt = _nodesG2LIfRec[_conv][_if1BufIdx][0].Item2.Stat_cnt();
                                            _nodesG2LIfRec[_conv][_if1BufIdx].RemoveAt(0);
                                            List<CcrClr> g2LIfCcrClr = new();
                                            if (ifClrCnt > 0)
                                            {
                                                g2LIfCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, _if1BufIdx))));
                                            }

                                            SegmentND g2LIfRestore = new(g2LIf);
                                            bool restored = RestoreTensorShape(_conv);
                                            int strideN = 0;
                                            int strideC = 0;
                                            int strideH = 0;
                                            if (restored)
                                            {
                                                if (_preNi.Nb.AlignType == AlignedType.EAligned)
                                                {
                                                    strideN = ifmap1[1].Length;
                                                    strideC = ifmap1[2].Length;
                                                    strideH = glb.GlbMap[ItemName.Ifmap].Dimensions[2] * glb.GlbMap[ItemName.Ifmap].Dimensions[3];
                                                }
                                                else if (_preNi.Nb.AlignType == AlignedType.FAligned)
                                                {
                                                    RestoreTensorShape(_conv, ItemName.Ifmap, g2LIfRestore);
                                                    strideN = ifmap1[1].Length;
                                                    strideC = ifmap1[2].Length * _ifmapLd[2].Length;
                                                    strideH = glb.GlbMap[ItemName.Ifmap].Dimensions[3];
                                                }
                                                else
                                                {
                                                    strideN = ifmap1[1].Length;
                                                    strideC = ifmap1[2].Length;
                                                    strideH = ifmap1[3].Length;
                                                }
                                            }

                                            actionUpdater.UpdateG2LIf(g2LIfRestore, ifmap1, _lif!, iPp, null!, g2LIfCcrClr, _ifmapOffset, _inputType, ItemName.Ifmap, restored, strideN, strideC, strideH, _h2C, (int)_weightsShape[2], ((TensorConst)_conv[GNNEConv2D.Stride]).Value.ToArray<int>()[0]);
                                        }
                                        else
                                        {
                                            if (_nodesG2LIfRec[_conv][_if1BufIdx].Count > 0)
                                            {
                                                _nodesG2LIfRec[_conv][_if1BufIdx][^1].Item2.IsLastSlice = restartUpdateG2LIf;
                                            }

                                            _nodesG2LIfRec[_conv][_if1BufIdx].Add(new(ifmap1, new TensorStat(restartUpdateG2LIf, false)));
                                            restartUpdateG2LIf = false;
                                        }
                                    }

                                    foreach (var rInloop in rInloopSeg)
                                    {
                                        foreach (var sInloop in inloopSeg)
                                        {
                                            SegmentND l2RW = new(mInloop, wcInloop, rInloop, sInloop);
                                            var l2RIf = ShiftInputTensor(ifmapSlice, l2RW, (int)_weightsShape[2], (int)_weightsShape[3], _strideH, _strideW, _dilationH, _dilationW);

                                            int posW = _weightType == DataTypes.Int16 ? 2 : 1;
                                            int posIf = _inputType == DataTypes.Int16 ? 2 : 1;
                                            for (int pW = 0; pW < posW; pW++)
                                            {
                                                for (int pIf = 0; pIf < posIf; pIf++)
                                                {
                                                    if (!weightGroupOnly)
                                                    {
                                                        int g2RWCcrSetNum = 0;
                                                        int g2RWCcrClrNum;
                                                        int sliceIdx;
                                                        ItemName weightItem;
                                                        int weightOffset;
                                                        if (_weightBufIdx != -1)
                                                        {
                                                            var weightRecStat = _nodesWeightRec[_conv][_weightBufIdx][0];
                                                            var g2RWRecStat = _nodesG2RWRec[_conv][_weightBufIdx][0];
                                                            _nodesG2RWRec[_conv][_weightBufIdx].RemoveAt(0);
                                                            if (g2RWRecStat.Item2.IsLastSlice)
                                                            {
                                                                _nodesWeightRec[_conv][_weightBufIdx].RemoveAt(0);
                                                            }

                                                            var g2RWSliceRecStat = _nodesG2RWSliceRec[_conv][_weightBufIdx][0];
                                                            _nodesG2RWSliceRec[_conv][_weightBufIdx].RemoveAt(0);
                                                            Assert(l2RW == g2RWSliceRecStat.Item1 && g2RWRecStat.Item1 == weightRecStat.Item1);
                                                            if (g2RWSliceRecStat.Item2.IsFirstSlice)
                                                            {
                                                                int wClrCnt = _nodesQuenesAsW[_weightBufIdx][0].Item2 != 0 && g2RWRecStat.Item2.IsFirstSlice ? 1 : 0;
                                                                List<CcrClr> ccrClrW = new();
                                                                if (wClrCnt > 0)
                                                                {
                                                                    ccrClrW.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.WeightFake, _weightBufIdx))));
                                                                }

                                                                List<CcrSet> ccrSetW = new() { new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Weight, (_weightBufIdx << 1) + (g2RWSliceRecStat.Item2.SliceIdx & 0x1))), 1) };
                                                                actionUpdater.UpdateLoadW(l2RW, _lw!, weightGroup, wPp, ddrW, ccrSetW, ccrClrW, _ni.Nb.WeightOffset, _h2C, 0, ItemName.Weight, pW);
                                                            }

                                                            if (g2RWRecStat.Item2.IsLastSlice && _nodesQuenesAsW[_weightBufIdx].Count > 0)
                                                            {
                                                                g2RWCcrSetNum = _nodesQuenesAsW[_weightBufIdx].Count > 1 ? 1 : 0;
                                                                _nodesQuenesAsW[_weightBufIdx].RemoveAt(0);
                                                            }

                                                            g2RWCcrClrNum = g2RWSliceRecStat.Item2.IsFirstSlice ? 1 : 0;
                                                            sliceIdx = g2RWSliceRecStat.Item2.SliceIdx;
                                                            weightItem = ItemName.Weight;
                                                            weightOffset = _ni.Nb.WeightOffset;
                                                        }
                                                        else
                                                        {
                                                            g2RWCcrSetNum = 0;
                                                            g2RWCcrClrNum = 0;
                                                            sliceIdx = 0; // means nothing
                                                            weightItem = ItemName.WeightPreload;
                                                            weightOffset = _ni.Nb.WeightPreloadOffset;
                                                        }

                                                        List<CcrSet> g2RWCcrSet = new();
                                                        if (g2RWCcrSetNum > 0)
                                                        {
                                                            g2RWCcrSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.WeightFake, _weightBufIdx)), 1));
                                                        }

                                                        List<CcrClr> g2RWCcrClr = new();
                                                        if (g2RWCcrClrNum > 0)
                                                        {
                                                            g2RWCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Weight, (_weightBufIdx << 1) + (sliceIdx & 0x1)))));
                                                        }

                                                        if (_ccrHandler.GetValue(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.WQarg))) > 0)
                                                        {
                                                            g2RWCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.WQarg))));
                                                        }

                                                        actionUpdater.UpdateG2RW(l2RW, weightGroup, _ocPerGroup, _lw!, wPp, pW, g2RWCcrSet, g2RWCcrClr, weightOffset, _ni.Nb.WeightQargOffset, weightItem, ItemName.WQarg, _h2C);
                                                    }
                                                    else
                                                    {
                                                        weightGroup.UpdateWeightGroup(l2RW);
                                                        if (_weightBufIdx != -1)
                                                        {
                                                            if (_nodesG2RWRec[_conv][_weightBufIdx].Count > 0)
                                                            {
                                                                _nodesG2RWRec[_conv][_weightBufIdx][^1].Item2.IsLastSlice = restartUpdateWeight;
                                                            }

                                                            _nodesG2RWRec[_conv][_weightBufIdx].Add(new(weight1, new TensorStat(restartUpdateWeight, false)));
                                                            _nodesG2RWSliceRec[_conv][_weightBufIdx].Add(new(l2RW, new TensorStat(false, false, -1)));
                                                            restartUpdateWeight = false;
                                                        }
                                                    }

                                                    if (!weightGroupOnly)
                                                    {
                                                        actionUpdater.UpdateL2RIf(l2RIf, g2LIf, _strideH, _strideW, _icPerGroup, _lif!, 0, pIf, ((TensorConst)_conv[GNNEConv2D.DeqBias]).Value.ToArray<int>()[0], _inputType, _h2C, (int)_weightsShape[2]);
                                                    }

                                                    bool releaseIf = false;
                                                    if (sInloop == inloopSeg[^1] && rInloop == rInloopSeg[^1] && pW == posW - 1 && pIf == posIf - 1 && dmLoadIfSn)
                                                    {
                                                        releaseIf = true;
                                                        dmLoadIfSn = false;
                                                    }

                                                    bool loopStart = false;
                                                    if (ofmapCalStart && pW == 0 && pIf == 0)
                                                    {
                                                        loopStart = true;
                                                        ofmapCalStart = false;
                                                    }

                                                    bool loopEnd = wcInloop.End == _weightsShape[1] && sInloop.End == weight1[3].End && rInloop.End == weight1[2].End && pW == posW - 1 && pIf == posIf - 1;

                                                    var destType = _outputType;
                                                    var destTarget = ACT0_OUTPUT_DEST.dm;
                                                    if (_pool is not null || _dw is not null || _act1 is not null)
                                                    {
                                                        destTarget = ACT0_OUTPUT_DEST.psum;
                                                        destType = _conv.CheckedDataType;
                                                    }

                                                    if (!weightGroupOnly)
                                                    {
                                                        int shift = ((TensorConst)_conv[GNNEConv2D.ShiftBits]).Value.ToScalar<int>();

                                                        int l2GOfCcrSetNum = 0;
                                                        int l2GOfCcrClrNum = 0;
                                                        int actCcrClrNum = 0;
                                                        int ccrSetValue = 0;
                                                        if (loopEnd && destTarget == ACT0_OUTPUT_DEST.dm)
                                                        {
                                                            var ofCcrStat = _nodesL2GOfRec[_conv][_ofBufIdx][0];
                                                            _nodesL2GOfRec[_conv][_ofBufIdx].RemoveAt(0);
                                                            if (ofCcrStat.Item2.IsFirstSlice && _nodesQueNeedClearFake.Count > 0 && _nodesQueNeedClearFake[0].Item1 == _conv)
                                                            {
                                                                l2GOfCcrClrNum = _nodesQueNeedClearFake[0].Item2 == 0 ? 0 : 1;
                                                                _nodesQueNeedClearFake.RemoveAt(0);
                                                            }

                                                            if (ofCcrStat.Item2.IsLastSlice)
                                                            {
                                                                l2GOfCcrSetNum = 1;
                                                                ccrSetValue = GetCcrSetAccordingPostNodes(_ni);
                                                            }
                                                        }

                                                        if (loopEnd && _ccrHandler.GetValue(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Act))) > 0)
                                                        {
                                                            actCcrClrNum = 1;
                                                        }

                                                        List<CcrSet> l2GOfCcrSet = new();
                                                        if (l2GOfCcrSetNum > 0)
                                                        {
                                                            l2GOfCcrSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, _ofBufIdx)), ccrSetValue));
                                                        }

                                                        List<CcrClr> l2GOfCcrClr = new();
                                                        if (l2GOfCcrClrNum > 0)
                                                        {
                                                            l2GOfCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.OfmapFake, _ofBufIdx))));
                                                        }

                                                        List<CcrClr> actCcrClr = new();
                                                        if (actCcrClrNum > 0)
                                                        {
                                                            actCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Act))));
                                                        }

                                                        actionUpdater.UpdateR2LPsum(shift, r2LPsum, r2LPsumRestore, _ofmapSt, ofPp, l1Pp, destTarget, releaseIf, Math.Max(pW, pIf), TcuComputeMode.NormalConv2d, loopStart, loopEnd, _strideH, _strideW, _ocPerGroup, _inputType, _weightType, destType, _lact!.CheckedDataType, l2GOfCcrSet, l2GOfCcrClr, _ni.Nb.ActOffset, _ni.Nb.OfmapOffset, actCcrClr);
                                                    }
                                                    else if (loopEnd && destTarget == ACT0_OUTPUT_DEST.dm)
                                                    {
                                                        if (_nodesL2GOfRec[_conv][_ofBufIdx].Count > 0)
                                                        {
                                                            _nodesL2GOfRec[_conv][_ofBufIdx][^1].Item2.IsLastSlice = restartUpdateOfmap;
                                                        }

                                                        _nodesL2GOfRec[_conv][_ofBufIdx].Add(new(psum, new TensorStat(restartUpdateOfmap, false)));
                                                        restartUpdateOfmap = false;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        if (!weightGroupOnly)
                        {
                            if ((_pool is not null || _dw is not null || _act1 is not null) && wcInloop.End == _weightsShape[1])
                            {
                                Segment1D none = new(..0, new(0, 0));
                                SegmentND l2RDw = new(none, none, none, none);
                                SegmentND l2RIf2 = new(none, none, none, none);
                                List<CcrClr> l2RIf2CcrClr = new();
                                int act0Offset = _l1FuseNi.Nb.Pdp0ActOffset;
                                if (_act1 is not null && _act1[GNNEActivation.InputB] != None.Default)
                                {
                                    l2RIf2 = r2LPsumRestore;

                                    int bufIdx = _if2BufIdx == -1 ? 0 : _if2BufIdx;
                                    int if2ClrCnt = _nodesL2RIf2Rec[_conv][bufIdx][0].Item2.Stat_cnt();
                                    _nodesL2RIf2Rec[_conv][bufIdx].RemoveAt(0);
                                    if (_lif2 is not null)
                                    {
                                        if (if2ClrCnt > 0)
                                        {
                                            l2RIf2CcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ifmap2))));
                                        }
                                    }
                                    else
                                    {
                                        if (if2ClrCnt > 0)
                                        {
                                            l2RIf2CcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, _if2BufIdx))));
                                        }
                                    }
                                }

                                if (_dw is not null)
                                {
                                    long[] dwShape = _dw[GNNEPdp0DW.Weights].CheckedShape.ToValueArray();
                                    l2RDw = new(new(..1, Padding.Zero()), m, new(..(int)dwShape[2], Padding.Zero()), new(..(int)dwShape[3], Padding.Zero()));
                                    act0Offset = _l1FuseNi.Nb.DwActOffset;
                                }

                                int l2GOfCcrSetNum = 0;
                                int l2GOfCcrClrNum = 0;
                                int ccrSetValue = 0;
                                var ofCcrStat = _nodesL2GOfRec[_conv][_ofBufIdx][0];
                                _nodesL2GOfRec[_conv][_ofBufIdx].RemoveAt(0);
                                if (ofCcrStat.Item2.IsFirstSlice && _nodesQueNeedClearFake.Count > 0 && _nodesQueNeedClearFake[0].Item1 == _conv)
                                {
                                    l2GOfCcrClrNum = _nodesQueNeedClearFake[0].Item2 == 0 ? 0 : 1;
                                    _nodesQueNeedClearFake.RemoveAt(0);
                                }

                                if (ofCcrStat.Item2.IsLastSlice)
                                {
                                    l2GOfCcrSetNum = 1;
                                    ccrSetValue = GetCcrSetAccordingPostNodes(_ni.Children[0]);
                                }

                                List<CcrClr> dwCcrClr = new();
                                List<CcrClr> act1CcrClr = new();
                                if (_ccrHandler.GetValue(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.DwWeight))) > 0)
                                {
                                    dwCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.DwWeight))));
                                }

                                if (_ccrHandler.GetValue(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.DwQarg))) > 0)
                                {
                                    dwCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.DwQarg))));
                                }

                                if (_ccrHandler.GetValue(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.DwAct1))) > 0)
                                {
                                    act1CcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.DwAct1))));
                                }

                                if (_ccrHandler.GetValue(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.MfuAct1))) > 0)
                                {
                                    act1CcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.MfuAct1))));
                                }

                                if (_ccrHandler.GetValue(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.PdpAct1))) > 0)
                                {
                                    act1CcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.PdpAct1))));
                                }

                                List<CcrSet> l2GOfCcrSet = new();
                                if (l2GOfCcrSetNum > 0)
                                {
                                    l2GOfCcrSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, _ofBufIdx)), ccrSetValue));
                                }

                                List<CcrClr> l2GOfCcrClr = new();
                                if (l2GOfCcrClrNum > 0)
                                {
                                    l2GOfCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.OfmapFake, _ofBufIdx))));
                                }

                                actionUpdater.UpdateL2RPsum(_dw!, _pool!, _act1!, l2RIf2, ifmap2, l2RDw, r2LPsumRestore, l2GOf, _ofmapSt, ofPp, l1Pp, ACT0_OUTPUT_DEST.dm, TcuComputeMode.NormalConv2d, _ocPerGroup, weightGroup, l2RIf2CcrClr, l2GOfCcrSet, l2GOfCcrClr, _ifmap2Offset, act0Offset, _l1FuseNi.Nb.Act1Offset, _ni.Nb.OfmapOffset, _l1FuseNi.Nb.DwWeightOffset, _l1FuseNi.Nb.DwWeightQargOffset, _src2ItemName, dwCcrClr, act1CcrClr, _swapAB);

                                l1Pp = (l1Pp + 1) % 2;
                            }
                        }
                        else
                        {
                            if (_act1 is not null && wcInloop.End == _weightsShape[1] && _act1[GNNEActivation.InputB] != None.Default)
                            {
                                int bufIdx = _if2BufIdx == -1 ? 0 : _if2BufIdx;
                                if (_nodesL2RIf2Rec[_conv][bufIdx].Count > 0)
                                {
                                    _nodesL2RIf2Rec[_conv][bufIdx][^1].Item2.IsLastSlice = restartUpdateL2RIf2;
                                }

                                _nodesL2RIf2Rec[_conv][bufIdx].Add(new(ifmap2, new TensorStat(restartUpdateL2RIf2, false)));
                                restartUpdateL2RIf2 = false;
                            }

                            if ((_pool is not null || _dw is not null || _act1 is not null) && wcInloop.End == _weightsShape[1])
                            {
                                if (_nodesL2GOfRec[_conv][_ofBufIdx].Count > 0)
                                {
                                    _nodesL2GOfRec[_conv][_ofBufIdx][^1].Item2.IsLastSlice = restartUpdateOfmap;
                                }

                                _nodesL2GOfRec[_conv][_ofBufIdx].Add(new(psum, new TensorStat(restartUpdateOfmap, false)));
                                restartUpdateOfmap = false;
                            }
                        }
                    }
                }
            }
        }
    }

    private List<List<Segment1D>> GetL1McSeg(SegmentND ifmap, SegmentND psum, int mInloop, int cInloop)
    {
        int group = Math.Max(ifmap[1].Length / _icPerGroup, 1);
        List<Segment1D> mSeg = new();
        List<Segment1D> cSeg = new();
        if (group >= 2 && _groupPerPass < 2)
        {
            for (int i = 0; i < group; i++)
            {
                var mSegTmp = GetSegmentStartEndLength(psum[1].Start + (i * _ocPerGroup), mInloop, psum[1].Start + ((i + 1) * _ocPerGroup));
                var cSegTmp = GetSegmentStartEndLength(ifmap[1].Start + (i * _icPerGroup), cInloop, ifmap[1].Start + ((i + 1) * _icPerGroup));
                mSeg.AddRange(mSegTmp);
                cSeg.AddRange(cSegTmp);
            }
        }
        else
        {
            mSeg = GetSegmentStartEndLength(psum[1].Start, mInloop, psum[1].End);
            cSeg = GetSegmentStartEndLength(ifmap[1].Start, cInloop, ifmap[1].End);
        }

        return new() { mSeg, cSeg };
    }

    private int GetCcrSetAccordingPostNodes(NodeInfo currNode)
    {
        int ccrSetValue = 0;

        int outputSize = _l1Fused ? _l1FuseNi.Nb.OutputsSize : _ni.Nb.OutputsSize;
        var postNode1 = currNode.Children[0].Op;
        switch (postNode1)
        {
            case { Target: GNNEConv2D }:
            case { Target: GNNEActivation } when ((GNNEActivation)postNode1.Target).InputFromL1.Count > 0 &&
                                                 ((((GNNEActivation)postNode1.Target).InputFromL1[0] && postNode1[GNNEActivation.InputA] != currNode.Op) ||
                                                  (((GNNEActivation)postNode1.Target).InputFromL1[1] && postNode1[GNNEActivation.InputB] != currNode.Op)):
                {
                    bool curNodeIsIf2 = false;
                    if (postNode1 is { Target: GNNEActivation })
                    {
                        curNodeIsIf2 = true;
                        if (((GNNEActivation)postNode1.Target).InputFromL1[0])
                        {
                            postNode1 = (Call)postNode1[GNNEActivation.InputA];
                        }
                        else
                        {
                            postNode1 = (Call)postNode1[GNNEActivation.InputB];
                        }
                    }

                    if (_nodesL2RIf2Rec.ContainsKey(postNode1) && _nodesL2RIf2Rec[postNode1][_ofBufIdx].Count > 0 && curNodeIsIf2)
                    {
                        ccrSetValue += _nodesL2RIf2Rec[postNode1][_ofBufIdx][0].Item2 is { IsFirstSlice: true, IsLastSlice: true } ? 1 : 2;
                    }
                    else
                    {
                        if (_nodesG2LIfRec[postNode1][_ofBufIdx].Count > 0)
                        {
                            ccrSetValue += _nodesG2LIfRec[postNode1][_ofBufIdx][0].Item2 is { IsFirstSlice: true, IsLastSlice: true } ? 1 : 2;
                        }
                    }

                    break;
                }

            case { Target: Ai2dResize }:
                {
                    // TODO: add as if2
                    {
                        if (_nodesAi2dIfRec[postNode1][_ofBufIdx].Count > 0)
                        {
                            ccrSetValue += _nodesAi2dIfRec[postNode1][_ofBufIdx][0].Item2 is { IsFirstSlice: true, IsLastSlice: true } ? 1 : 2;
                        }
                    }

                    break;
                }

            default:
                {
                    if (outputSize == 2 && postNode1 is { Target: Concat })
                    {
                        ccrSetValue += 0;
                    }
                    else
                    {
                        ccrSetValue += 1;
                    }

                    break;
                }
        }

        if (outputSize != 2)
        {
            return ccrSetValue;
        }

        var postNode2 = currNode.Children[1].Op;
        switch (postNode2)
        {
            // be used as if2
            case { Target: GNNEConv2D }:
            case { Target: GNNEActivation } when ((GNNEActivation)postNode2.Target).InputFromL1.Count > 0 &&
                                                 ((((GNNEActivation)postNode2.Target).InputFromL1[0] && postNode2[GNNEActivation.InputA] != currNode.Op) ||
                                                  (((GNNEActivation)postNode2.Target).InputFromL1[1] && postNode2[GNNEActivation.InputB] != currNode.Op)):
                {
                    bool curNodeIsIf2 = false;
                    if (postNode2 is { Target: GNNEActivation })
                    {
                        curNodeIsIf2 = true;
                        if (((GNNEActivation)postNode2.Target).InputFromL1[0])
                        {
                            postNode2 = (Call)postNode2[GNNEActivation.InputA];
                        }
                        else
                        {
                            postNode2 = (Call)postNode2[GNNEActivation.InputB];
                        }
                    }

                    if (_nodesL2RIf2Rec.ContainsKey(postNode2) && _nodesL2RIf2Rec[postNode2][_ofBufIdx].Count > 0 && curNodeIsIf2)
                    {
                        ccrSetValue += _nodesL2RIf2Rec[postNode2][_ofBufIdx][0].Item2 is { IsFirstSlice: true, IsLastSlice: true } ? 1 : 2;
                    }
                    else
                    {
                        if (_nodesG2LIfRec[postNode2][_ofBufIdx].Count > 0)
                        {
                            ccrSetValue += _nodesG2LIfRec[postNode2][_ofBufIdx][0].Item2 is { IsFirstSlice: true, IsLastSlice: true } ? 1 : 2;
                        }
                    }

                    break;
                }

            case { Target: Ai2dResize }:
                {
                    // TODO: add as if2
                    {
                        if (_nodesAi2dIfRec[postNode2][_ofBufIdx].Count > 0)
                        {
                            ccrSetValue += _nodesAi2dIfRec[postNode2][_ofBufIdx][0].Item2 is { IsFirstSlice: true, IsLastSlice: true } ? 1 : 2;
                        }
                    }

                    break;
                }

            default:
                {
                    if (outputSize == 2 && postNode2 is { Target: Concat })
                    {
                        ccrSetValue += 0;
                    }
                    else
                    {
                        ccrSetValue += 1;
                    }

                    break;
                }
        }

        return ccrSetValue;
    }

    private bool Conv1X1(Call conv)
    {
        bool ret = false;
        if (conv is null)
        {
            return ret;
        }

        long[] inputShape = conv[GNNEConv2D.Input].CheckedShape.ToValueArray();
        long[] outputShape = conv.CheckedShape.ToValueArray();
        long[] weightsShape = conv[GNNEConv2D.Weights].CheckedShape.ToValueArray();
        int[] paddings = ((TensorConst)conv[GNNEConv2D.Padding]).Value.ToArray<int>();
        Padding paddingH = new(paddings[0], paddings[1]);
        Padding paddingW = new(paddings[2], paddings[3]);
        int strideH = ((TensorConst)conv[GNNEConv2D.Stride]).Value.ToArray<int>()[0];
        int strideW = ((TensorConst)conv[GNNEConv2D.Stride]).Value.ToArray<int>()[1];
        int dilationH = ((TensorConst)conv[GNNEConv2D.Dilation]).Value.ToArray<int>()[0];
        int dilationW = ((TensorConst)conv[GNNEConv2D.Dilation]).Value.ToArray<int>()[1];
        int groups = ((TensorConst)conv[GNNEConv2D.Groups]).Value.ToScalar<int>();
        bool isDepthwise = inputShape[1] == outputShape[1] && outputShape[1] == groups && groups != 1;
        var inputType = conv[GNNEConv2D.Input].CheckedDataType;

        if (conv is not null
            && !isDepthwise
            && groups == 1
            && strideH == 1
            && strideW == 1
            && weightsShape[2] == 1
            && weightsShape[3] == 1
            && dilationH == 1
            && dilationW == 1
            && paddingH.Sum() == 0
            && paddingW.Sum() == 0
            && (inputType == DataTypes.Int8 || inputType == DataTypes.UInt8)
            && outputShape[2] * outputShape[3] < 512
            && weightsShape[1] % 24 == 0
            && _ifmapLd[2].Range.Equals(_ofmapSt[2].Range) && _ifmapLd[3].Range.Equals(_ofmapSt[3].Range))
        {
            ret = true;
        }

        return ret;
    }

    private bool RestoreTensorShape(Call convCall, ItemName item_type = ItemName.None, SegmentND slice = null!)
    {
        if (convCall == null)
        {
            throw new ArgumentNullException(nameof(convCall));
        }

        if (!Conv1X1(convCall))
        {
            return false;
        }

        switch (item_type)
        {
            case ItemName.None:
                return true;
            case ItemName.Ofmap:
            case ItemName.Psum:
                {
                    var dim2 = _ofmapSt[2];
                    var dim3 = _ofmapSt[3];
                    if (item_type == ItemName.Psum)
                    {
                        dim2 = _ofmapConv[2];
                        dim3 = _ofmapConv[3];
                    }

                    Assert(slice[3].Start % dim3.Length == 0 && slice[3].End % dim3.Length == 0);

                    slice[0] = slice[0]; // no change for dim0

                    slice[1] = new((slice[1].Start * slice[2].Length)..(slice[1].End * slice[2].Length), Padding.Zero());

                    slice[2] = new((dim2.Start + (slice[3].Start / dim3.Length))..(dim2.Start + (slice[3].End / dim3.Length)), dim2.Padding);

                    slice[3] = new(dim3.Start..dim3.End, dim3.Padding);
                    break;
                }

            default:
                {
                    var dim2 = _ifmapLd[2];
                    var dim3 = _ifmapLd[3];

                    Assert(slice[3].Start % dim3.Length == 0 && slice[3].End % dim3.Length == 0);

                    slice[0] = slice[0]; // no change for dim0
                    slice[1] = slice[1]; // no change for dim0

                    slice[2] = new((slice[2].Start * slice[3].Length / dim3.Length)..(slice[2].End * slice[3].Length / dim3.Length), dim2.Padding);

                    slice[3] = new((slice[3].Start / dim2.Length)..(slice[3].End / dim2.Length), dim3.Padding);
                    break;
                }
        }

        return true;
    }

    private List<int> L1Search(TiledGlb glb, SegmentND weight1, SegmentND psum)
    {
        bool isConv1X1 = Conv1X1(_conv);
        bool stipeSearch = (isConv1X1 || _l1FusedInfos[_conv] == L1FusedType.FusedAct1 || _l1FusedInfos[_conv] == L1FusedType.NoFused) && !_h2C;

        int c = Math.Min(GNNEEnv.PuHeight, _groupPerPass * _icPerGroup);
        int m = Math.Min(GNNEEnv.PuWidth, _groupPerPass * _ocPerGroup);
        int h = 1;
        int w = 1;
        int e = 1;
        int f = stipeSearch ? _ofmap[3].Length : 1;
        int fStep = stipeSearch ? _ofmap[3].Length : 1;
        bool retried = false;
    retry:
        int r = _weightSplitPattern[_conv].Item1 == 0 || _dilationH > 3 ? 1 : _weightSplitPattern[_conv].Item1;
        int s = _weightSplitPattern[_conv].Item2 == 0 || _dilationW > 3 ? 1 : _weightSplitPattern[_conv].Item2;

        long[] originalConvOutputShape = _conv.CheckedShape.ToValueArray();

        int hConvOut;
        if (isConv1X1)
        {
            int eSt = e * _ofmapSt[2].Length;
            hConvOut = SpaceSearcher.GetInputHeight(eSt, (int)originalConvOutputShape[2], _fusedKernelH, _ofmapSt[2].Length, _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
            hConvOut /= _ofmapConv[2].Length;
        }
        else
        {
            hConvOut = SpaceSearcher.GetInputHeight(e, (int)_convOutputShape![2], _fusedKernelH, (int)_outputShape![2], _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
        }

        h = SpaceSearcher.GetInputHeight(hConvOut, (int)_inputShape![2], r, (int)_convOutputShape![2], _strideH, _dilationH, Padding.Zero());

        int wConvOut;
        if (isConv1X1)
        {
            Assert(f % _ofmapSt[2].Length == 0);
            int fSt = f / _ofmapSt[2].Length;
            wConvOut = SpaceSearcher.GetInputHeight(fSt, (int)originalConvOutputShape[3], _fusedKernelW, _ofmapSt[3].Length, _fusedStrideW, _fusedDilationW, _fusedPaddingW!);
            wConvOut = wConvOut * _ofmapConv[2].Length;
        }
        else
        {
            wConvOut = SpaceSearcher.GetInputHeight(f, (int)_convOutputShape[3], _fusedKernelW, (int)_outputShape![3], _fusedStrideW, _fusedDilationW, _fusedPaddingW!);
        }

        w = SpaceSearcher.GetInputHeight(wConvOut, (int)_inputShape[3], s, (int)_convOutputShape[3], _strideW, _dilationW, Padding.Zero());

        int psumPingPangSplit = 1;
        if (_pool is not null || _dw is not null || _act1 is not null)
        {
            psumPingPangSplit = 2;
        }

        int ifBytesPerElementGlb = GetBytesPerElement(_inputType);
        bool ok = HandleL1Allocate(h, w, Math.Max(e, hConvOut), Math.Max(f, wConvOut), psumPingPangSplit, ifBytesPerElementGlb);
        if (!ok && !retried)
        {
            retried = true;
            f = 1;
            fStep = 1;
            goto retry;
        }

        if (!ok)
        {
            throw new NotSupportedException("L1 is too small");
        }

        bool IncreaseEByStep1()
        {
            int nextE = e + 1;
            int nextHConvOut;
            if (isConv1X1)
            {
                int eSt = nextE * _ofmapSt[2].Length;
                nextHConvOut = SpaceSearcher.GetInputHeight(eSt, (int)originalConvOutputShape[2], _fusedKernelH, _ofmapSt[2].Length, _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
                nextHConvOut = nextHConvOut / _ofmapConv[2].Length;
            }
            else
            {
                nextHConvOut = SpaceSearcher.GetInputHeight(nextE, (int)_convOutputShape[2], _fusedKernelH, (int)_outputShape![2], _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
            }

            int nextH = SpaceSearcher.GetInputHeight(nextHConvOut, (int)_inputShape[2], r, (int)_convOutputShape[2], _strideH, _dilationH, new(0, 0));

            bool ok = HandleL1Allocate(nextH, w, Math.Max(nextE, nextHConvOut), Math.Max(f, wConvOut), psumPingPangSplit, ifBytesPerElementGlb);
            if (ok)
            {
                e = nextE;
                h = nextH;
                hConvOut = nextHConvOut;
            }

            return ok;
        }

        bool IncreaseFByStep1()
        {
            int nextF = f + fStep;
            int nextWConvOut;
            if (isConv1X1)
            {
                int fSt = nextF / _ofmapSt[2].Length;
                nextWConvOut = SpaceSearcher.GetInputHeight(fSt, (int)originalConvOutputShape[3], _fusedKernelW, _ofmapSt[3].Length, _fusedStrideW, _fusedDilationW, _fusedPaddingW!);
                nextWConvOut *= _ofmapConv[2].Length;
            }
            else
            {
                nextWConvOut = SpaceSearcher.GetInputHeight(nextF, (int)_convOutputShape[3], _fusedKernelW, (int)_outputShape![3], _fusedStrideW, _fusedDilationW, _fusedPaddingW!);
            }

            int nextW = SpaceSearcher.GetInputHeight(nextWConvOut, (int)_inputShape[3], s, (int)_convOutputShape[3], _strideW, _dilationW, new(0, 0));

            bool ok = HandleL1Allocate(h, nextW, Math.Max(e, hConvOut), Math.Max(nextF, nextWConvOut), psumPingPangSplit, ifBytesPerElementGlb);
            if (ok)
            {
                f = nextF;
                w = nextW;
                wConvOut = nextWConvOut;
            }

            return ok;
        }

        bool IncreaseRBy1()
        {
            int nextR = r + 1;
            int nextHConvOut = SpaceSearcher.GetInputHeight(e, (int)originalConvOutputShape[2], _fusedKernelH, (int)_outputShape![2], _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
            int nextH = SpaceSearcher.GetInputHeight(nextHConvOut, (int)_inputShape[2], nextR, (int)_convOutputShape[2], _strideH, _dilationH, new(0, 0));

            bool ok = HandleL1Allocate(nextH, w, Math.Max(e, nextHConvOut), Math.Max(f, wConvOut), psumPingPangSplit, ifBytesPerElementGlb);
            if (!ok)
            {
                return ok;
            }

            r = nextR;
            h = nextH;
            hConvOut = nextHConvOut;

            return ok;
        }

        bool IncreaseRBy11X1Conv()
        {
            int nextR = r + 1;
            int nextHConvOut = SpaceSearcher.GetInputHeight(e, (int)_convOutputShape[2], _fusedKernelH, (int)_outputShape![2], _fusedStrideH, _fusedDilationH, _fusedPaddingH!);
            int nextH = SpaceSearcher.GetInputHeight(nextHConvOut, (int)_inputShape[2], nextR, (int)_convOutputShape[2], _strideH, _dilationH, new(0, 0));

            bool ok = HandleL1Allocate(nextH, _ofmap[2].Length * _ofmap[3].Length, Math.Max(e, nextHConvOut), Math.Max(f, wConvOut), psumPingPangSplit, ifBytesPerElementGlb);
            if (ok)
            {
                r = nextR;
                h = nextH;
                hConvOut = nextHConvOut;
            }

            return ok;
        }

        bool IncreaseSBy1()
        {
            int nextS = s + 1;
            int nextWConvOut = SpaceSearcher.GetInputHeight(f, (int)_convOutputShape[3], _fusedKernelW, (int)_outputShape![3], _fusedStrideW, _fusedDilationW, _fusedPaddingW!);
            int nextW = SpaceSearcher.GetInputHeight(nextWConvOut, (int)_inputShape[3], nextS, (int)_convOutputShape[3], _strideW, _dilationW, new(0, 0));

            bool ok = HandleL1Allocate(h, nextW, Math.Max(e, hConvOut), Math.Max(f, nextWConvOut), psumPingPangSplit, ifBytesPerElementGlb);
            if (ok)
            {
                s = nextS;
                w = nextW;
                wConvOut = nextWConvOut;
            }

            return ok;
        }

        // increase e&f, but reserve some space for the increase of r&s
        while (f < psum[3].Length && e < psum[2].Length && h * w * ifBytesPerElementGlb < GNNEEnv.IfL1SizePerChan / 2)
        {
            if (f <= e)
            {
                if (!IncreaseFByStep1())
                {
                    break;
                }
            }
            else
            {
                if (!IncreaseEByStep1())
                {
                    break;
                }
            }
        }

        while (f < psum[3].Length && h * w * ifBytesPerElementGlb < GNNEEnv.IfL1SizePerChan / 2)
        {
            if (!IncreaseFByStep1())
            {
                break;
            }
        }

        while (e < psum[2].Length && h * w * ifBytesPerElementGlb < GNNEEnv.IfL1SizePerChan / 2)
        {
            if (!IncreaseEByStep1())
            {
                break;
            }
        }

        if (_weightSplitPattern[_conv].Equals(new Tuple<int, int>(0, 0)))
        {
            // increase r&s to avoid the cutting of the kernel is too small
            while (s < weight1[3].Length && r < weight1[2].Length && s < 31 && r < 31 && _dilationW <= 3 && _dilationH <= 3)
            {
                if (s <= r)
                {
                    if (!IncreaseSBy1())
                    {
                        break;
                    }
                }
                else
                {
                    if (isConv1X1)
                    {
                        if (!IncreaseRBy11X1Conv())
                        {
                            break;
                        }
                    }
                    else
                    {
                        if (!IncreaseRBy1())
                        {
                            break;
                        }
                    }
                }
            }

            while (s < weight1[3].Length && s < 31 && _dilationW <= 3)
            {
                if (!IncreaseSBy1())
                {
                    break;
                }
            }

            while (r < weight1[2].Length && r < 31 && _dilationH <= 3)
            {
                if (isConv1X1)
                {
                    if (!IncreaseRBy11X1Conv())
                    {
                        break;
                    }
                }
                else
                {
                    if (!IncreaseRBy1())
                    {
                        break;
                    }
                }
            }
        }

        // increase e&f as large as possible
        while (f < psum[3].Length && e < psum[2].Length)
        {
            if (f <= e)
            {
                if (!IncreaseFByStep1())
                {
                    break;
                }
            }
            else
            {
                if (!IncreaseEByStep1())
                {
                    break;
                }
            }
        }

        while (f < psum[3].Length)
        {
            if (!IncreaseFByStep1())
            {
                break;
            }
        }

        while (e < psum[2].Length)
        {
            if (!IncreaseEByStep1())
            {
                break;
            }
        }

        if (_dilationW > 3)
        {
            r = 1;
        }

        if (_dilationH > 3)
        {
            s = 1;
        }

        _weightSplitPattern[_conv] = new(r, s);

        return new()
        {
            c,
            m,
            e,
            f,
            r,
            s,
        };
    }

    private bool HandleL1Allocate(int h, int w, int e, int f, int psumPingPangSplit, int ifBytesPerElementGlb)
    {
        if (e * f > GNNEEnv.PsumL1ElePerChan / psumPingPangSplit)
        {
            return false;
        }

        return GNNEEnv.PuHeight * h * w * ifBytesPerElementGlb <= GNNEEnv.IfL1Size;
    }

    private void ArrangeWeights(DataType weightsType, long[] weightsShape, Span<byte> oldWeights, WeightGroupHandler weightGroup)
    {
        int bytesPerElement = GetBytesPerElement(weightsType);
        var weightGroupSlice = weightGroup.WeightGroupSlice();
        byte[] newWeights = new byte[oldWeights.Length];

        if (_h2C)
        {
            int offset = 0;
            int j = 0;
            foreach (var slice in weightGroupSlice)
            {
                Assert(offset == weightGroup.WeightGroupOffset(slice) * bytesPerElement);
                for (int b = 0; b < bytesPerElement; b++)
                {
                    for (int m = 0; m < slice[0].Length; m++)
                    {
                        for (int s = 0; s < slice[3].Length; s++)
                        {
                            for (int r = 0; r < slice[2].Length; r++)
                            {
                                for (int c = 0; c < slice[1].Length; c++)
                                {
                                    int srcAddr = (int)(((((((m + slice[0].Start) * weightsShape[1]) + c + slice[1].Start) * weightsShape[2]) + r + slice[2].Start) * weightsShape[3]) + s + slice[3].Start) * bytesPerElement;
                                    newWeights[j++] = oldWeights[srcAddr + b];
                                    offset++;
                                }
                            }
                        }
                    }
                }
            }
        }
        else
        {
            long[] inputShape = _conv[GNNEConv2D.Input].CheckedShape.ToValueArray();
            long[] outputShape = _conv.CheckedShape.ToValueArray();
            long[] convOutputShape = _conv.CheckedShape.ToValueArray();
            long[] wShape = _conv[GNNEConv2D.Weights].CheckedShape.ToValueArray();
            ReshapeConv(_conv, ref inputShape, ref outputShape, ref convOutputShape, ref wShape);

            int offset = 0;
            int j = 0;
            foreach (var slice in weightGroupSlice)
            {
                Assert(offset == weightGroup.WeightGroupOffset(slice) * bytesPerElement);
                for (int b = 0; b < bytesPerElement; b++)
                {
                    for (int m = 0; m < slice[0].Length; m++)
                    {
                        for (int r = 0; r < slice[2].Length; r++)
                        {
                            for (int s = 0; s < slice[3].Length; s++)
                            {
                                for (int c = 0; c < slice[1].Length; c++)
                                {
                                    var srcAddr = (int)(((((((m + slice[0].Start) * wShape[1]) + c + slice[1].Start) * wShape[2]) + r + slice[2].Start) * wShape[3]) + s + slice[3].Start) * bytesPerElement;
                                    newWeights[j++] = oldWeights[srcAddr + b];
                                    offset++;
                                }
                            }
                        }
                    }
                }
            }
        }

        newWeights.CopyTo(oldWeights);
    }

    private void ArrangeDwWeights(DataType weightsType, long[] weightsShape, Span<byte> oldWeights, WeightGroupHandler weightGroup)
    {
        int bytesPerElement = GetBytesPerElement(weightsType);
        int offset = 0;
        int j = 0;
        byte[] newWeights = new byte[oldWeights.Length];
        (weightsShape[0], weightsShape[1]) = (weightsShape[1], weightsShape[0]);

        foreach (var slice in from wg in weightGroup.DwGroupSlice() let cs = wg[1].Start let ce = wg[1].End let dwShape = _dw[GNNEPdp0DW.Weights].CheckedShape.ToValueArray() select new SegmentND(..1, cs..ce, ..(int)dwShape[2], ..(int)dwShape[3]))
        {
            for (int m = 0; m < slice[0].Length; m++)
            {
                for (int r = 0; r < slice[2].Length; r++)
                {
                    for (int s = 0; s < slice[3].Length; s++)
                    {
                        for (int c = 0; c < slice[1].Length; c++)
                        {
                            int srcAddr = (int)(((((((m + slice[0].Start) * weightsShape[1]) + c + slice[1].Start) * weightsShape[2]) + r + slice[2].Start) * weightsShape[3]) + s + slice[3].Start) * bytesPerElement;
                            for (int b = 0; b < bytesPerElement; b++)
                            {
                                newWeights[(j * GNNEEnv.PuWidth) + c] = oldWeights[srcAddr + b];
                            }

                            offset++;
                        }

                        j++;
                    }
                }
            }
        }

        newWeights.CopyTo(oldWeights);
    }

    private void InitParameters(FusionInfo fusionInfo)
    {
        if (_weightGroups.Count > 0)
        {
            _weightGroups.Clear();
        }

        foreach (var info in fusionInfo.FusedNodes)
        {
            if (info.Op is { Target: GNNEConv2D })
            {
                var wType = info.Op[GNNEConv2D.Weights].CheckedDataType;
                _weightGroups.Add(info.Op, new WeightGroupHandler(wType, wType));
                _weightSplitPattern.Add(info.Op, new(0, 0));
            }
        }

        _l1FusedInfos.Clear();
    }

    private void GetSliceInfo(FusionInfo fusionInfo, TiledGlb glb, out List<List<NodeInfo>> currSliceInfo, out List<Dictionary<Call, NodeInfo>> preSliceInfo)
    {
        currSliceInfo = new();
        preSliceInfo = new();

        // get all tiling segments
        long[] outputShape = fusionInfo.FusedNodes[^1].Op.CheckedShape.ToValueArray();
        int[] lastOutShape = fusionInfo.LastOutShape;
        List<SegmentND> slices = new();
        var outputBatchSeg = GetSegmentStartEndLength(0, lastOutShape[0], (int)outputShape[0]);
        foreach (var glbOutputBatch in outputBatchSeg)
        {
            var outputChanSeg = GetSegmentStartEndLength(0, lastOutShape[1], (int)outputShape[1]);
            slices.AddRange(from glbOutputChannel in outputChanSeg let outputRowSeg = GetSegmentStartEndLength(0, lastOutShape[2], (int)outputShape[2]) from glbOutputRow in outputRowSeg let outputColSeg = GetSegmentStartEndLength(0, lastOutShape[3], (int)outputShape[3]) from glbOutputColumn in outputColSeg select new SegmentND(glbOutputBatch, glbOutputChannel, glbOutputRow, glbOutputColumn));
        }

        Dictionary<int, int> ofBufMap = new() { { 0, 0 }, { 1, 1 }, { 2, 2 } };

        int OfBufferIdx(Call currNode)
        {
            foreach (var n in fusionInfo.FusedNodes)
            {
                if (currNode == n.Op)
                {
                    return ofBufMap[n.Nb.OfBufferIndex];
                }
            }

            return -1;
        }

        NodeBuffer GetPreNodeBuffer(Call node)
        {
            foreach (var n in fusionInfo.FusedNodes)
            {
                if (node == n.Op)
                {
                    return new NodeBuffer(n.Nb);
                }
            }

            return new NodeBuffer();
        }

        Dictionary<int, int> ofBufOffset = new() { { 0, 0 }, { 1, 0 }, { 2, 0 } };

        int GetOfBufOffset(int bufIdx)
        {
            foreach (var node in fusionInfo.FusedNodes)
            {
                // if offset != 0, must be same in each node.
                Assert(ofBufOffset[node.Nb.OfBufferIndex] == 0 || ofBufOffset[node.Nb.OfBufferIndex] == node.Nb.OfmapOffset);
                ofBufOffset[node.Nb.OfBufferIndex] = node.Nb.OfmapOffset;
            }

            return bufIdx == -1 ? -1 : ofBufOffset[bufIdx];
        }

        Dictionary<int, int> weightBufOffset = new() { { 0, 0 }, { 1, 0 } };

        int GetWeightBufOffset(Call curNode, NodeBuffer nb, int sliceIdx)
        {
            int convCnt = 0; // record the num of gnne conv2d
            int bufOffset = 0;
            foreach (var node in fusionInfo.FusedNodes)
            {
                // if offset != 0, must be same in each node.
                if (node.Op is { Target: GNNEConv2D })
                {
                    Assert(node.Nb.WeightOffset == weightBufOffset[0] || node.Nb.WeightOffset == weightBufOffset[1] || (weightBufOffset[0] == 0 && weightBufOffset[1] == 0));
                    weightBufOffset[node.Nb.WeightOffset != 0 ? 1 : 0] = node.Nb.WeightOffset;
                    convCnt++;
                }

                if (node.Op == curNode)
                {
                    bufOffset = nb.WeightOffset;
                }
            }

            if (curNode is { Target: GNNEConv2D })
            {
                // buf idx change according to the number of conv2d as well as the slice_idx
                int bufIdx = (bufOffset != 0 ? 1 : 0 + (sliceIdx * convCnt)) & 0x1;
                bufOffset = weightBufOffset[bufIdx];
            }

            return bufOffset;
        }

        Call prevNode;

        // loop over each slice and op
        var nodes = fusionInfo.FusedNodes;
        for (int sliceIdx = 0; sliceIdx < slices.Count; sliceIdx++)
        {
            List<NodeInfo> currSlice = new();
            Dictionary<Call, NodeInfo> preSlice = new();

            int GetOfBufferNum()
            {
                int ofBufferNum = currSlice[0].Nb.OfBufferIndex;
                ofBufferNum = currSlice.Select(node => node.Nb.OfBufferIndex).Prepend(ofBufferNum).Max();

                return ofBufferNum + 1;
            }

            void UpdateOfBufMap(int bufNum1)
            {
                // try to get the the second-to-last element, becasuse the last is store, and store has no output buf.
                ofBufMap[0] = (currSlice[^2].Nb.OfBufferIndex + 1) % bufNum1;
                ofBufMap[1] = (currSlice[^2].Nb.OfBufferIndex + 2) % bufNum1;
                ofBufMap[2] = (currSlice[^2].Nb.OfBufferIndex + 3) % bufNum1;
            }

            for (int l = nodes.Count - 1; l >= 0; l--)
            {
                var op = nodes[l].Op;
                if (op is { Target: GNNEStore })
                {
                    prevNode = (Call)op[GNNEStore.Input];
                    var nb = GetPreNodeBuffer(prevNode);
                    nb.OfBufferIndex = OfBufferIdx(prevNode);
                    nb.OfmapOffset = GetOfBufOffset(nb.OfBufferIndex);
                    nb.WeightOffset = GetWeightBufOffset(prevNode, nb, sliceIdx);
                    currSlice.Add(new(op, slices[sliceIdx], nodes[l].Nb, nodes[l].Children));
                    preSlice.Add(prevNode, new(prevNode, slices[sliceIdx], nb, new()
                    {
                        currSlice.Find(n => n.Op == op)!,
                    }));
                }

                switch (op)
                {
                    case { Target: GNNEConv2D }:
                        {
                            long[] curInputShape = op[GNNEConv2D.Input].CheckedShape.ToValueArray();
                            var curOfmap = preSlice[op].Ofmap;
                            long[] weightsShape = op[GNNEConv2D.Weights].CheckedShape.ToValueArray();
                            int[] padding = ((TensorConst)op[GNNEConv2D.Padding]).Value.ToArray<int>();
                            Padding paddingH = new(padding[0], padding[1]);
                            Padding paddingW = new(padding[2], padding[3]);
                            int strideH = ((TensorConst)op[GNNEConv2D.Stride]).Value.ToArray<int>()[0];
                            int strideW = ((TensorConst)op[GNNEConv2D.Stride]).Value.ToArray<int>()[1];
                            int dilationH = ((TensorConst)op[GNNEConv2D.Dilation]).Value.ToArray<int>()[0];
                            int dilationW = ((TensorConst)op[GNNEConv2D.Dilation]).Value.ToArray<int>()[1];

                            var glbInputRow = GetInputRowSegment(curOfmap[2].Start, curOfmap[2].Length, (int)curInputShape[2], (int)weightsShape[2], strideH, dilationH, paddingH);
                            var glbInputCol = GetInputColumnSegment(curOfmap[3].Start, curOfmap[3].Length, (int)curInputShape[3], (int)weightsShape[3], strideW, dilationW, paddingW);

                            prevNode = (Call)op[GNNEConv2D.Input];
                            SegmentND curr_ifmap = new(curOfmap[0], new(..(int)curInputShape[1], Padding.Zero()), glbInputRow, glbInputCol);
                            var nb = GetPreNodeBuffer(prevNode);
                            nb.OfBufferIndex = OfBufferIdx(prevNode);
                            nb.OfmapOffset = GetOfBufOffset(nb.OfBufferIndex);
                            nb.WeightOffset = GetWeightBufOffset(prevNode, nb, sliceIdx);
                            if (preSlice.ContainsKey(prevNode))
                            {
                                var tmp = preSlice[prevNode].Ofmap;
                                preSlice[prevNode].Nb = nb;
                                preSlice[prevNode].Children.Add(preSlice[op]);
                                preSlice[prevNode].Ofmap = tmp + curr_ifmap;
                            }
                            else
                            {
                                preSlice.Add(prevNode, new(prevNode, curr_ifmap, nb, new() { preSlice[op] }));
                            }

                            currSlice.Add(preSlice[op]);
                            break;
                        }

                    case { Target: GNNEPdp0DW }:
                        {
                            long[] curInputShape = op[GNNEPdp0DW.Input].CheckedShape.ToValueArray();
                            var curOfmap = preSlice[op].Ofmap;
                            long[] weightsShape = op[GNNEPdp0DW.Weights].CheckedShape.ToValueArray();
                            int[] padding = ((TensorConst)op[GNNEPdp0DW.Padding]).Value.ToArray<int>();
                            Padding paddingH = new(padding[0], padding[1]);
                            Padding paddingW = new(padding[2], padding[3]);
                            int strideH = ((TensorConst)op[GNNEPdp0DW.Stride]).Value.ToArray<int>()[0];
                            int strideW = ((TensorConst)op[GNNEPdp0DW.Stride]).Value.ToArray<int>()[1];
                            int dilationH = ((TensorConst)op[GNNEPdp0DW.Dilation]).Value.ToArray<int>()[0];
                            int dilationW = ((TensorConst)op[GNNEPdp0DW.Dilation]).Value.ToArray<int>()[1];

                            var glbInputRow = GetInputRowSegment(curOfmap[2].Start, curOfmap[2].Length, (int)curInputShape[2], (int)weightsShape[2], strideH, dilationH, paddingH);
                            var glbInputCol = GetInputColumnSegment(curOfmap[3].Start, curOfmap[3].Length, (int)curInputShape[3], (int)weightsShape[3], strideW, dilationW, paddingW);

                            SegmentND currIfmap = new(curOfmap[0], new(..(int)curInputShape[1], Padding.Zero()), glbInputRow, glbInputCol);
                            prevNode = (Call)op[GNNEPdp0DW.Input];
                            var nb = GetPreNodeBuffer(prevNode);
                            nb.OfBufferIndex = OfBufferIdx(prevNode);
                            nb.OfmapOffset = GetOfBufOffset(nb.OfBufferIndex);
                            nb.WeightOffset = GetWeightBufOffset(prevNode, nb, sliceIdx);
                            if (preSlice.ContainsKey(prevNode))
                            {
                                var tmp = preSlice[prevNode].Ofmap;
                                preSlice[prevNode].Nb = nb;
                                preSlice[prevNode].Children.Add(preSlice[op]);
                                preSlice[prevNode].Ofmap = tmp + currIfmap;
                            }
                            else
                            {
                                preSlice.Add(prevNode, new NodeInfo(prevNode, currIfmap, nb, new() { preSlice[op] }));
                            }

                            currSlice.Add(preSlice[op]);
                            break;
                        }

                    case { Target: GNNEPdp0Reduce }:
                        {
                            long[] curInputShape = op[GNNEPdp0Reduce.Input].CheckedShape.ToValueArray();
                            var curOfmap = preSlice[op].Ofmap;
                            int[] padding = ((TensorConst)op[GNNEPdp0Reduce.Padding]).Value.ToArray<int>();
                            Padding paddingH = new(padding[0], padding[1]);
                            Padding paddingW = new(padding[2], padding[3]);
                            int strideH = ((TensorConst)op[GNNEPdp0Reduce.Stride]).Value.ToArray<int>()[0];
                            int strideW = ((TensorConst)op[GNNEPdp0Reduce.Stride]).Value.ToArray<int>()[1];
                            int dilationH = 1;
                            int dilationW = 1;
                            int kernelH = ((TensorConst)op[GNNEPdp0Reduce.Filter]).Value.ToArray<int>()[0];
                            int kernelW = ((TensorConst)op[GNNEPdp0Reduce.Filter]).Value.ToArray<int>()[1];

                            var glbInputRow = GetInputRowSegment(curOfmap[2].Start, curOfmap[2].Length, (int)curInputShape[2], kernelH, strideH, dilationH, paddingH);
                            var glbInputCol = GetInputColumnSegment(curOfmap[3].Start, curOfmap[3].Length, (int)curInputShape[3], kernelW, strideW, dilationW, paddingW);

                            SegmentND currIfmap = new(curOfmap[0], new(..(int)curInputShape[1], Padding.Zero()), glbInputRow, glbInputCol);
                            prevNode = (Call)op[GNNEPdp0Reduce.Input];
                            var nb = GetPreNodeBuffer(prevNode);
                            nb.OfBufferIndex = OfBufferIdx(prevNode);
                            nb.OfmapOffset = GetOfBufOffset(nb.OfBufferIndex);
                            nb.WeightOffset = GetWeightBufOffset(prevNode, nb, sliceIdx);
                            if (preSlice.ContainsKey(prevNode))
                            {
                                var tmp = preSlice[prevNode].Ofmap;
                                preSlice[prevNode].Nb = nb;
                                preSlice[prevNode].Children.Add(preSlice[op]);
                                preSlice[prevNode].Ofmap = tmp + currIfmap;
                            }
                            else
                            {
                                preSlice.Add(prevNode, new NodeInfo(prevNode, currIfmap, nb, new() { preSlice[op] }));
                            }

                            currSlice.Add(preSlice[op]);
                            break;
                        }

                    case { Target: GNNEPdp1 }:
                        {
                            long[] curInputShape = op[GNNEPdp1.Input].CheckedShape.ToValueArray();
                            var curOfmap = preSlice[op].Ofmap;
                            int[] padding = ((TensorConst)op[GNNEPdp1.Padding]).Value.ToArray<int>();
                            Padding paddingH = new(padding[0], padding[1]);
                            Padding paddingW = new(padding[2], padding[3]);
                            int strideH = ((TensorConst)op[GNNEPdp1.Stride]).Value.ToArray<int>()[0];
                            int strideW = ((TensorConst)op[GNNEPdp1.Stride]).Value.ToArray<int>()[1];
                            int dilationH = 1;
                            int dilationW = 1;
                            int kernelH = ((TensorConst)op[GNNEPdp1.Filter]).Value.ToArray<int>()[0];
                            int kernelW = ((TensorConst)op[GNNEPdp1.Filter]).Value.ToArray<int>()[1];

                            var glbInputRow = GetInputRowSegment(curOfmap[2].Start, curOfmap[2].Length, (int)curInputShape[2], kernelH, strideH, dilationH, paddingH);
                            var glbInputCol = GetInputColumnSegment(curOfmap[3].Start, curOfmap[3].Length, (int)curInputShape[3], kernelW, strideW, dilationW, paddingW);

                            SegmentND curr_ifmap = new(curOfmap[0], new(..(int)curInputShape[1], Padding.Zero()), glbInputRow, glbInputCol);
                            prevNode = (Call)op[GNNEPdp1.Input];
                            var nb = GetPreNodeBuffer(prevNode);
                            nb.OfBufferIndex = OfBufferIdx(prevNode);
                            nb.OfmapOffset = GetOfBufOffset(nb.OfBufferIndex);
                            nb.WeightOffset = GetWeightBufOffset(prevNode, nb, sliceIdx);
                            if (preSlice.ContainsKey(prevNode))
                            {
                                var tmp = preSlice[prevNode].Ofmap;
                                preSlice[prevNode].Nb = nb;
                                preSlice[prevNode].Children.Add(preSlice[op]);
                                preSlice[prevNode].Ofmap = tmp + curr_ifmap;
                            }
                            else
                            {
                                preSlice.Add(prevNode, new NodeInfo(prevNode, curr_ifmap, nb, new() { preSlice[op] }));
                            }

                            currSlice.Add(preSlice[op]);
                            break;
                        }

                    case { Target: GNNETranspose }:
                        {
                            long[] curInputShape = op[GNNETranspose.Input].CheckedShape.ToValueArray();
                            var curOfmap = preSlice[op].Ofmap;
                            var perm = ((GNNETranspose)op.Target).Perm;

                            Segment1D[] GetInputSeg(Segment1D d0, Segment1D d1, Segment1D d2, Segment1D d3, MFU_TRANS_PERMUTE perm)
                            {
                                return perm switch
                                {
                                    MFU_TRANS_PERMUTE.NCHW => new[] { d0, d1, d2, d3 },
                                    MFU_TRANS_PERMUTE.NCWH => new[] { d0, d1, d3, d2 },
                                    MFU_TRANS_PERMUTE.NHCW => new[] { d0, d2, d1, d3 },
                                    MFU_TRANS_PERMUTE.NHWC => new[] { d0, d3, d1, d2 },
                                    MFU_TRANS_PERMUTE.NWCH => new[] { d0, d2, d3, d1 },
                                    MFU_TRANS_PERMUTE.NWHC => new[] { d0, d3, d2, d1 },
                                    MFU_TRANS_PERMUTE.CNHW => new[] { d1, d0, d2, d3 },
                                    MFU_TRANS_PERMUTE.CNWH => new[] { d1, d0, d3, d2 },
                                    MFU_TRANS_PERMUTE.CHNW => new[] { d2, d0, d1, d3 },
                                    MFU_TRANS_PERMUTE.CHWN => new[] { d3, d0, d1, d2 },
                                    MFU_TRANS_PERMUTE.CWNH => new[] { d2, d0, d3, d1 },
                                    MFU_TRANS_PERMUTE.CWHN => new[] { d3, d0, d2, d1 },
                                    MFU_TRANS_PERMUTE.HNCW => new[] { d1, d2, d0, d3 },
                                    MFU_TRANS_PERMUTE.HNWC => new[] { d1, d3, d0, d2 },
                                    MFU_TRANS_PERMUTE.HCNW => new[] { d2, d1, d0, d3 },
                                    MFU_TRANS_PERMUTE.HCWN => new[] { d3, d1, d0, d2 },
                                    MFU_TRANS_PERMUTE.HWNC => new[] { d2, d3, d0, d1 },
                                    MFU_TRANS_PERMUTE.HWCN => new[] { d3, d2, d0, d1 },
                                    MFU_TRANS_PERMUTE.WNCH => new[] { d1, d2, d3, d0 },
                                    MFU_TRANS_PERMUTE.WNHC => new[] { d1, d3, d2, d0 },
                                    MFU_TRANS_PERMUTE.WCNH => new[] { d2, d1, d3, d0 },
                                    MFU_TRANS_PERMUTE.WCHN => new[] { d3, d1, d2, d0 },
                                    MFU_TRANS_PERMUTE.WHNC => new[] { d2, d3, d1, d0 },
                                    MFU_TRANS_PERMUTE.WHCN => new[] { d3, d2, d1, d0 },
                                    _ => new[] { d0, d1, d2, d3 },
                                };
                            }

                            var input_segs = GetInputSeg(curOfmap[0], curOfmap[1], curOfmap[2], curOfmap[3], perm);
                            var glbInputN = input_segs[0];
                            var glbInputC = input_segs[1];
                            var glbInputH = input_segs[2];
                            var glbInputW = input_segs[3];
                            SegmentND curr_ifmap = new(glbInputN, glbInputC, glbInputH, glbInputW);
                            prevNode = (Call)op[GNNETranspose.Input];
                            var nb = GetPreNodeBuffer(prevNode);
                            nb.OfBufferIndex = OfBufferIdx(prevNode);
                            nb.OfmapOffset = GetOfBufOffset(nb.OfBufferIndex);
                            nb.WeightOffset = GetWeightBufOffset(prevNode, nb, sliceIdx);
                            if (preSlice.ContainsKey(prevNode))
                            {
                                var tmp = preSlice[prevNode].Ofmap;
                                preSlice[prevNode].Nb = nb;
                                preSlice[prevNode].Children.Add(preSlice[op]);
                                preSlice[prevNode].Ofmap = tmp + curr_ifmap;
                            }
                            else
                            {
                                preSlice.Add(prevNode, new NodeInfo(prevNode, curr_ifmap, nb, new() { preSlice[op] }));
                            }

                            currSlice.Add(preSlice[op]);
                            break;
                        }

                    case { Target: Concat }:
                        {
                            {
                                long[] inputShape = op[Concat.Input][0].CheckedShape.ToValueArray();
                                Segment1D inputSeg0 = new(..(int)inputShape[0], Padding.Zero());
                                Segment1D inputSeg1 = new(..(int)inputShape[1], Padding.Zero());
                                Segment1D inputSeg2 = new(..(int)inputShape[2], Padding.Zero());
                                Segment1D inputSeg3 = new(..(int)inputShape[3], Padding.Zero());
                                SegmentND currIfmap = new(inputSeg0, inputSeg1, inputSeg2, inputSeg3);
                                prevNode = (Call)op[Concat.Input][0];
                                var nb = GetPreNodeBuffer(prevNode);
                                var children = nodes.Find(n => n.Op == prevNode)!.Children;
                                nb.OfBufferIndex = OfBufferIdx(prevNode);
                                nb.OfmapOffset = GetOfBufOffset(nb.OfBufferIndex);
                                nb.WeightOffset = GetWeightBufOffset(prevNode, nb, sliceIdx);
                                if (preSlice.ContainsKey(prevNode))
                                {
                                    var tmp = preSlice[prevNode].Ofmap;
                                    preSlice[prevNode].Nb = nb;
                                    preSlice[prevNode].Children.Add(preSlice[op]);
                                    preSlice[prevNode].Ofmap = tmp + currIfmap;
                                }
                                else
                                {
                                    preSlice.Add(prevNode, new NodeInfo(prevNode, currIfmap, nb, new() { preSlice[op] }));
                                }
                            }

                            {
                                long[] inputShape = op[Concat.Input][1].CheckedShape.ToValueArray();
                                Segment1D inputSeg0 = new(..(int)inputShape[0], Padding.Zero());
                                Segment1D inputSeg1 = new(..(int)inputShape[1], Padding.Zero());
                                Segment1D inputSeg2 = new(..(int)inputShape[2], Padding.Zero());
                                Segment1D inputSeg3 = new(..(int)inputShape[3], Padding.Zero());
                                SegmentND currIfmap = new(inputSeg0, inputSeg1, inputSeg2, inputSeg3);
                                prevNode = (Call)op[Concat.Input][1];
                                var nb = GetPreNodeBuffer(prevNode);
                                var children = nodes.Find(n => n.Op == prevNode)!.Children;
                                nb.OfBufferIndex = OfBufferIdx(prevNode);
                                nb.OfmapOffset = GetOfBufOffset(nb.OfBufferIndex);
                                nb.WeightOffset = GetWeightBufOffset(prevNode, nb, sliceIdx);
                                if (preSlice.ContainsKey(prevNode))
                                {
                                    var tmp = preSlice[prevNode].Ofmap;
                                    preSlice[prevNode] = new NodeInfo(prevNode, tmp + currIfmap, nb, children);
                                }
                                else
                                {
                                    preSlice.Add(prevNode, new NodeInfo(prevNode, currIfmap, nb, children));
                                }
                            }

                            currSlice.Add(preSlice[op]);
                            break;
                        }

                    case { Target: Ai2dResize }:
                        {
                            long[] curInputShape = op[Ai2dResize.Input].CheckedShape.ToValueArray();
                            var curOfmap = preSlice[_resize].Ofmap;

                            // TODO: support slice
                            Segment1D glbInputRow = new(..(int)curInputShape[2], Padding.Zero());
                            Segment1D glbInputCol = new(..(int)curInputShape[3], Padding.Zero());

                            SegmentND curr_ifmap = new(curOfmap[0], new(..(int)curInputShape[1], Padding.Zero()), glbInputRow, glbInputCol);
                            prevNode = (Call)op[Ai2dResize.Input];
                            var nb = GetPreNodeBuffer(prevNode);
                            nb.OfBufferIndex = OfBufferIdx(prevNode);
                            if (preSlice.ContainsKey(prevNode))
                            {
                                var tmp = preSlice[prevNode].Ofmap;
                                preSlice[prevNode].Nb = nb;
                                preSlice[prevNode].Children.Add(preSlice[op]);
                                preSlice[prevNode].Ofmap = tmp + curr_ifmap;
                            }
                            else
                            {
                                preSlice.Add(prevNode, new NodeInfo(prevNode, curr_ifmap, nb, new() { preSlice[op] }));
                            }

                            currSlice.Add(preSlice[op]);
                            break;
                        }

                    case { Target: GNNEActivation }:
                        {
                            var curOfmap = preSlice[op].Ofmap;

                            bool inputBIsUninit = op[GNNEActivation.InputB] == None.Default;

                            var (ifmapA, ifmapB) = GetAct1InputsShape(
                                op[GNNEActivation.InputA].CheckedShape.ToValueArray(),
                                inputBIsUninit ? op[GNNEActivation.InputA].CheckedShape.ToValueArray() : op[GNNEActivation.InputB].CheckedShape.ToValueArray(),
                                op.CheckedShape.ToValueArray(),
                                curOfmap);

                            if (nodes.Find(n => n.Op == op[GNNEActivation.InputA]) is not null)
                            {
                                SegmentND currIfmap = new(ifmapA[0], ifmapA[1], ifmapA[2], ifmapA[3]);
                                prevNode = (Call)op[GNNEActivation.InputA];
                                var nb = GetPreNodeBuffer(prevNode);
                                nb.OfBufferIndex = OfBufferIdx(prevNode);
                                nb.OfmapOffset = GetOfBufOffset(nb.OfBufferIndex);
                                nb.WeightOffset = GetWeightBufOffset(prevNode, nb, sliceIdx);
                                if (preSlice.ContainsKey(prevNode))
                                {
                                    var tmp = preSlice[prevNode].Ofmap;
                                    preSlice[prevNode].Nb = nb;
                                    preSlice[prevNode].Children.Add(preSlice[op]);
                                    preSlice[prevNode].Ofmap = tmp + currIfmap;
                                }
                                else
                                {
                                    preSlice.Add(prevNode, new NodeInfo(prevNode, currIfmap, nb, new() { preSlice[op] }));
                                }
                            }

                            if (!inputBIsUninit && nodes.Find(n => n.Op == op[GNNEActivation.InputB]) is not null)
                            {
                                SegmentND currIfmap = new(ifmapB[0], ifmapB[1], ifmapB[2], ifmapB[3]);
                                prevNode = (Call)op[GNNEActivation.InputB];
                                var nb = GetPreNodeBuffer(prevNode);
                                nb.OfBufferIndex = OfBufferIdx(prevNode);
                                nb.OfmapOffset = GetOfBufOffset(nb.OfBufferIndex);
                                nb.WeightOffset = GetWeightBufOffset(prevNode, nb, sliceIdx);
                                if (preSlice.ContainsKey(prevNode))
                                {
                                    var tmp = preSlice[prevNode].Ofmap;
                                    preSlice[prevNode].Nb = nb;
                                    preSlice[prevNode].Children.Add(preSlice[op]);
                                    preSlice[prevNode].Ofmap = tmp + currIfmap;
                                }
                                else
                                {
                                    preSlice.Add(prevNode, new NodeInfo(prevNode, currIfmap, nb, new() { preSlice[op] }));
                                }
                            }

                            currSlice.Add(preSlice[op]);
                            break;
                        }

                    case { Target: GNNELoad }:
                        currSlice.Add(preSlice[op]);
                        break;
                }
            }

            currSlice.Reverse();
            foreach (var ni in currSlice)
            {
                ni.Nb.OutputsSize = ni.Children.Count;
            }

            foreach (var ni in preSlice.Values)
            {
                ni.Nb.OutputsSize = ni.Children.Count;
            }

            currSliceInfo.Add(currSlice);
            preSliceInfo.Add(preSlice);

            int bufNum = GetOfBufferNum();
            UpdateOfBufMap(bufNum);
        }

        glb.Items = fusionInfo.Mmu;
        glb.LastOutShape = lastOutShape;

        // fake glb, only used for mmu_item, the dimations will be updated in get_glb_layouts.
        foreach (var mmu in fusionInfo.Mmu)
        {
            glb.GlbMap.Add(mmu.Key, new TensorOnGlb(new[] { 0, 0, 0, 0 }, DataTypes.Float16, 0));
            glb.GlbMap[mmu.Key].Mmu = mmu.Value;
        }

        glb.GlbMap.Add(ItemName.Ifmap, new TensorOnGlb(new[] { 0, 0, 0, 0 }, DataTypes.Float16, 0));
        glb.GlbMap[ItemName.Ifmap].Mmu = fusionInfo.Mmu[ItemName.Ofmap];
        if (fusionInfo.Mmu.ContainsKey(ItemName.Ifmap2))
        {
            return;
        }

        // dummy glb for ifmap2
        glb.GlbMap.Add(ItemName.Ifmap2, new TensorOnGlb(new[] { 0, 0, 0, 0 }, DataTypes.Float16, 0));
        glb.GlbMap[ItemName.Ifmap2].Mmu = fusionInfo.Mmu[ItemName.Ofmap];
    }

    private Tuple<SegmentND, SegmentND> GetAct1InputsShape(long[] inputAShape, long[] inputBShape, long[] outputShape, SegmentND slice)
    {
        long[] minShape = new long[4];
        for (int i = 0; i < 4; i++)
        {
            minShape[i] = Math.Min(inputAShape[i], inputBShape[i]);
        }

        long[] scaleA = new long[4], scaleB = new long[4], scaleOut = new long[4];
        for (int i = 0; i < 4; i++)
        {
            scaleA[i] = minShape[i] == 1 ? 1 : inputAShape[i] / minShape[i];
            scaleB[i] = minShape[i] == 1 ? 1 : inputBShape[i] / minShape[i];
            scaleOut[i] = minShape[i] == 1 ? 1 : outputShape[i] / minShape[i];
        }

        var glbOutputBatch = slice[0];

        var glbInputBatchA = glbOutputBatch / (int)(scaleOut[0] / scaleA[0]);
        var glbInputBatchB = glbOutputBatch / (int)(scaleOut[0] / scaleB[0]);
        if (inputAShape[0] == 1)
        {
            glbInputBatchA = new(..1, Padding.Zero());
        }

        if (inputBShape[0] == 1)
        {
            glbInputBatchB = new(..1, Padding.Zero());
        }

        var glbOutputChannel = slice[1];
        var glbInputChannelA = glbOutputChannel / (int)(scaleOut[1] / scaleA[1]);
        var glbInputChannelB = glbOutputChannel / (int)(scaleOut[1] / scaleB[1]);
        if (inputAShape[1] == 1)
        {
            glbInputChannelA = new(..1, Padding.Zero());
        }

        if (inputBShape[1] == 1)
        {
            glbInputChannelB = new(..1, Padding.Zero());
        }

        var glbOutputRow = slice[2];
        var glbInputRowA = glbOutputRow / (int)(scaleOut[2] / scaleA[2]);
        var glbInputRowB = glbOutputRow / (int)(scaleOut[2] / scaleB[2]);
        if (inputAShape[2] == 1)
        {
            glbInputRowA = new(..1, Padding.Zero());
        }

        if (inputBShape[2] == 1)
        {
            glbInputRowB = new(..1, Padding.Zero());
        }

        var glbOutputColumn = slice[3];
        var glbInputColA = glbOutputColumn / (int)(scaleOut[3] / scaleA[3]);
        var glbInputColB = glbOutputColumn / (int)(scaleOut[3] / scaleB[3]);
        if (inputAShape[3] == 1)
        {
            glbInputColA = new(..1, Padding.Zero());
        }

        if (inputBShape[3] == 1)
        {
            glbInputColB = new(..1, Padding.Zero());
        }

        SegmentND ifmapA = new(glbInputBatchA, glbInputChannelA, glbInputRowA, glbInputColA);
        SegmentND ifmapB = new(glbInputBatchB, glbInputChannelB, glbInputRowB, glbInputColB);

        return new(ifmapA, ifmapB);
    }

    private void ItemRecStatusInit(List<List<NodeInfo>> currSliceInfo)
    {
        _nodesWeightRec.Clear();
        _nodesOfmapRec.Clear();
        _nodesG2LIfRec.Clear();
        _nodesG2RWRec.Clear();
        _nodesL2GOfRec.Clear();
        _nodesL2RIf2Rec.Clear();
        _nodesG2RWSliceRec.Clear();
        _nodesAi2dIfRec.Clear();
        _nodesAi2dOfRec.Clear();
        _nodesQuenesAsW.Clear();
        _nodesQuenesAsW = new List<List<Tuple<Call, int>>>(2) { new List<Tuple<Call, int>>(), new List<Tuple<Call, int>>() }; // as to of buf idx
        _nodesQueNeedClearFake.Clear();

        // get the node need to clear fake: first to the buf in current slice, slice>0, and it is conv
        for (int sliceIdx = 0; sliceIdx < currSliceInfo.Count - 1; sliceIdx++)
        {
            int lastNodeBufIdx = currSliceInfo[sliceIdx][^2].Nb.OfBufferIndex;
            for (int i = 0; i < currSliceInfo[sliceIdx + 1].Count - 1; i++)
            {
                var node = currSliceInfo[sliceIdx + 1][i];
                if (node.Nb.OfBufferIndex == lastNodeBufIdx)
                {
                    if (node.Op is { Target: GNNEConv2D })
                    {
                        _nodesQueNeedClearFake.Add(new(node.Op, sliceIdx + 1));
                    }

                    break;
                }
            }
        }

        if (_nodesQueNeedClearFake.Count > 0)
        {
            _nodesQueNeedClearFake.Insert(0, new(_nodesQueNeedClearFake[0].Item1, 0));
        }
    }

    private void UpdateL2FusePara(FusionInfo fusionInfo, NodeInfo curNode, Dictionary<Call, NodeInfo> sliceInfo, TiledGlb glb, bool weightGroupOnly = false, bool firstLayer = false)
    {
        _conv = null!;
        _pool = null!;
        _dw = null!;
        _act1 = null!;
        _lif = null;
        _lw = null;
        _lact = null;
        _lwQarg = null;
        _sof = null;
        _lif2 = null;
        _pdp1 = null;
        _transpose = null;
        _cat = null;
        _resize = null!;
        _preNi = null!;
        _ni = null!;
        _l1FuseNi = null!;
        _l1Fused = false;
        if (firstLayer)
        {
            _l1FusedInfos.Clear();
        }

        _l1FusedInfos.Add(curNode.Op, L1FusedType.NoFused);
        _swapAB = false;
        _h2C = false;
        _src2ItemName = ItemName.Ifmap2;
        Segment1D none = new(..0, Padding.Zero());
        _ifmap = new(none, none, none, none);
        _ifmap2 = _ifmap;
        _ofmap = _ifmap;
        _ofmapSt = _ifmap;
        _ifmapLd = _ifmap;
        _ofmapConv = _ifmap;
        _ifmapA = _ifmap;
        _ifmapB = _ifmap;

        _if2Type = DataTypes.Float16;

        _ni = curNode;

        _if1BufIdx = -1;
        _if2BufIdx = -1;
        _ofBufIdx = -1;
        _weightBufIdx = -1;

        _lif = curNode.Op is { Target: GNNELoad } && curNode.Op[GNNELoad.Input] is not TensorConst ? curNode.Op : null;
        if (_lif is not null)
        {
            _ifmap = _ni.Ofmap;
            _ifmapOffset = 0;
            _ofmap = _ni.Ofmap;

            _if1BufIdx = -1;
            _if2BufIdx = -1;
            _ofBufIdx = _ni.Nb.OfBufferIndex;
            _inputType = _lif[GNNELoad.Input].CheckedDataType;
            _outputType = _lif.CheckedDataType;

            // TODO: 支持h2c
            // gnne_conv2d *post_conv = node_cast<gnne_conv2d>(lif->output().connections()[0]->owner());
            // h2c = post_conv and (post_conv->weights().shape()[1] * post_conv->weights().shape()[2] <= env_.pu_height()) and (post_conv->weights().shape()[2] != 1) and post_conv->input().shape()[2] > 200 and post_conv->input().shape()[3] > 200 and (!post_conv->is_depthwise());
            // memset_value = h2c ? post_conv->deq_bias() : 0;
            var postConv = curNode.Children[0].Op;
            if (postConv is { Target: GNNEConv2D })
            {
                long[] weightsShape = postConv[GNNEConv2D.Weights].CheckedShape.ToValueArray();
                long[] inputShape = postConv[GNNEConv2D.Input].CheckedShape.ToValueArray();
                long[] outputShape = postConv.CheckedShape.ToValueArray();
                int groups = ((TensorConst)postConv[GNNEConv2D.Groups]).Value.ToScalar<int>();
                bool isDepthwise = inputShape[1] == outputShape[1] && outputShape[1] == groups && groups != 1;
                int deqBias = ((TensorConst)postConv[GNNEConv2D.DeqBias]).Value.ToScalar<int>();
                _h2C = postConv is { Target: GNNEConv2D } && weightsShape[1] * weightsShape[2] <= GNNEEnv.PuHeight && weightsShape[2] != 1 && inputShape[2] > 200 && inputShape[3] > 200 && !isDepthwise && (_outputType != DataTypes.Int16);
                _memsetValue = _h2C ? deqBias : 0;
            }
        }

        if (curNode.Op is { Target: GNNEStore })
        {
            _sof = curNode.Op;
            var preNode = (Call)_sof[GNNEStore.Input];
            _ifmap = sliceInfo[preNode].Ofmap;
            _ifmapOffset = sliceInfo[preNode].Nb.OfmapOffset;
            _ofmap = _ni.Ofmap;

            _if1BufIdx = sliceInfo[preNode].Nb.OfBufferIndex;
            _if2BufIdx = -1;
            _ofBufIdx = sliceInfo[preNode].Nb.OfBufferIndex;
            _inputType = preNode.CheckedDataType;
            _outputType = _sof.CheckedDataType;
        }

        if (curNode.Op is { Target: GNNEConv2D })
        {
            _conv = curNode.Op;
            _preNi = sliceInfo[(Call)_conv[GNNEConv2D.Input]];
            _inputShape = _conv[GNNEConv2D.Input].CheckedShape.ToValueArray();
            _outputShape = _conv.CheckedShape.ToValueArray();
            _convOutputShape = _outputShape;
            _weightsShape = _conv[GNNEConv2D.Weights].CheckedShape.ToValueArray();
            int[] paddings = ((TensorConst)_conv[GNNEConv2D.Padding]).Value.ToArray<int>();
            _paddingH = new(paddings[0], paddings[1]);
            _paddingW = new(paddings[2], paddings[3]);
            _strideH = ((TensorConst)_conv[GNNEConv2D.Stride]).Value.ToArray<int>()[0];
            _strideW = ((TensorConst)_conv[GNNEConv2D.Stride]).Value.ToArray<int>()[1];
            _dilationH = ((TensorConst)_conv[GNNEConv2D.Dilation]).Value.ToArray<int>()[0];
            _dilationW = ((TensorConst)_conv[GNNEConv2D.Dilation]).Value.ToArray<int>()[1];
            _groups = ((TensorConst)_conv[GNNEConv2D.Groups]).Value.ToScalar<int>();
            int deqBias = ((TensorConst)_conv[GNNEConv2D.DeqBias]).Value.ToScalar<int>();
            bool isDepthwise = _inputShape[1] == _outputShape[1] && _outputShape[1] == _groups && _groups != 1;

            _icPerGroup = _groups == 1 ? (int)_weightsShape[1] : (int)_inputShape[1] / _groups;
            _ocPerGroup = (int)_outputShape[1] / _groups;
            _groupPerPass = Math.Min(Math.Max(Math.Min(GNNEEnv.PuHeight / _icPerGroup, GNNEEnv.PuWidth / _ocPerGroup), 1), _groups);

            if (curNode.Children[0].Op is { Target: GNNEPdp0Reduce })
            {
                _pool = curNode.Children[0].Op;
            }

            if (curNode.Children[0].Op is { Target: GNNEPdp0DW })
            {
                _dw = curNode.Children[0].Op;
            }

            if (curNode.Children[0].Op is { Target: GNNEActivation } &&
                curNode.Op.CheckedDataType == DataTypes.Float16 &&
                curNode.Children.Count == 1 &&
                ((GNNEActivation)curNode.Children[0].Op.Target).InputFromL1.Count > 0 &&
                ((((GNNEActivation)curNode.Children[0].Op.Target).InputFromL1[0] &&
                  curNode.Children[0].Op[GNNEActivation.InputA] == curNode.Op) ||
                 (((GNNEActivation)curNode.Children[0].Op.Target).InputFromL1[1] &&
                  curNode.Children[0].Op[GNNEActivation.InputB] == curNode.Op)))
            {
                _act1 = curNode.Children[0].Op;
            }

            _inputType = _conv[GNNEConv2D.Input].CheckedDataType;
            _weightType = _conv[GNNEConv2D.Weights].CheckedDataType;
            _outputType = _conv.CheckedDataType;
            if (_pool is not null)
            {
                _outputType = _pool.CheckedDataType;
            }

            if (_dw is not null)
            {
                _outputType = _dw.CheckedDataType;
            }

            if (_act1 is not null)
            {
                _outputType = _act1.CheckedDataType;
            }

            _lw = (Call)_conv[GNNEConv2D.Weights];
            _lact = (Call)_conv[GNNEConv2D.Act];
            _lwQarg = (Call)_conv[GNNEConv2D.WeightsBias];

            var preConv = (Call)_conv[GNNEConv2D.Input];
            _ifmap = sliceInfo[preConv].Ofmap;
            if (_groups == 1)
            {
                _ifmap = new(_ifmap[0], new(..(int)_weightsShape[1], Padding.Zero()), _ifmap[2], _ifmap[3]);
            }

            _ifmapOffset = sliceInfo[preConv].Nb.OfmapOffset;
            _ofmap = _ni.Ofmap;
            _ofmapOffset = _ni.Nb.OfmapOffset;
            _ofmapConv = _ni.Ofmap;

            _h2C = _weightsShape[1] * _weightsShape[2] <= GNNEEnv.PuHeight && _weightsShape[2] != 1 && _conv[GNNEConv2D.Input] is Call { Target: GNNELoad } && _inputShape[2] > 200 && _inputShape[3] > 200 && !isDepthwise && (_inputType != DataTypes.Int16);
            _memsetValue = _h2C ? deqBias : 0;

            _fusedKernelH = 1;
            _fusedKernelW = 1;
            _fusedPaddingH = Padding.Zero();
            _fusedPaddingW = Padding.Zero();
            _fusedStrideH = 1;
            _fusedStrideW = 1;
            _fusedDilationH = 1;
            _fusedDilationW = 1;
            if (_pool is not null)
            {
                _outputShape = _pool.CheckedShape.ToValueArray();
                _fusedKernelH = ((TensorConst)_pool[GNNEPdp0Reduce.Filter]).Value.ToArray<int>()[0];
                _fusedKernelW = ((TensorConst)_pool[GNNEPdp0Reduce.Filter]).Value.ToArray<int>()[1];
                int[] padding = ((TensorConst)_pool[GNNEPdp0Reduce.Padding]).Value.ToArray<int>();
                _fusedPaddingH = new(padding[0], padding[1]);
                _fusedPaddingW = new(padding[2], padding[3]);
                _fusedStrideH = ((TensorConst)_pool[GNNEPdp0Reduce.Stride]).Value.ToArray<int>()[0];
                _fusedStrideW = ((TensorConst)_pool[GNNEPdp0Reduce.Stride]).Value.ToArray<int>()[1];

                _ofmap = sliceInfo[_pool].Ofmap;
                _ofmapOffset = sliceInfo[_pool].Nb.OfmapOffset;
                _l1FuseNi = sliceInfo[_pool];

                _l1Fused = true;
                _l1FusedInfos[curNode.Op] = L1FusedType.FusedPool;

                // l1_fused_pair.emplace(std::make_pair(pool, cur_node.op));
            }
            else if (_dw is not null)
            {
                _outputShape = _dw.CheckedShape.ToValueArray();
                long[] dwShape = _dw[GNNEPdp0DW.Weights].CheckedShape.ToValueArray();
                _fusedKernelH = (int)dwShape[2];
                _fusedKernelW = (int)dwShape[3];
                int[] padding = ((TensorConst)_dw[GNNEPdp0DW.Padding]).Value.ToArray<int>();
                _fusedPaddingH = new(padding[0], padding[1]);
                _fusedPaddingW = new(padding[2], padding[3]);
                _fusedStrideH = ((TensorConst)_dw[GNNEPdp0DW.Stride]).Value.ToArray<int>()[0];
                _fusedStrideW = ((TensorConst)_dw[GNNEPdp0DW.Stride]).Value.ToArray<int>()[1];
                _fusedDilationH = ((TensorConst)_dw[GNNEPdp0DW.Dilation]).Value.ToArray<int>()[0];
                _fusedDilationW = ((TensorConst)_dw[GNNEPdp0DW.Dilation]).Value.ToArray<int>()[1];

                _ofmap = sliceInfo[_dw].Ofmap;
                _ofmapOffset = sliceInfo[_dw].Nb.OfmapOffset;
                _l1FuseNi = sliceInfo[_dw];

                _l1Fused = true;
                _l1FusedInfos[curNode.Op] = L1FusedType.FusedDw;

                // l1_fused_pair.emplace(std::make_pair(dw, cur_node.op));
            }
            else if (_act1 is not null)
            {
                bool inputBIsUninit = _act1[GNNEActivation.InputB] == None.Default;

                // only suport res_add for now
                _outputShape = _act1.CheckedShape.ToValueArray();
                Assert(_outputShape.SequenceEqual(_convOutputShape));

                _ofmap = sliceInfo[_act1].Ofmap;
                _ofmapOffset = sliceInfo[_act1].Nb.OfmapOffset;

                // l1_fused_pair.emplace(std::make_pair(act1, cur_node.op));
                if (!inputBIsUninit)
                {
                    var act1Info = sliceInfo[_act1];

                    if (_act1[GNNEActivation.InputA] is Call { Target: GNNELoad })
                    {
                        var act1LifA = (Call)_act1[GNNEActivation.InputA];
                        if (act1LifA[GNNELoad.Input] is Var && sliceInfo.ContainsKey(act1LifA))
                        {
                            _if2BufIdx = sliceInfo[act1LifA].Nb.OfBufferIndex;
                            _src2ItemName = ItemName.Ifmap2;
                            _ifmap2 = sliceInfo[act1LifA].Ofmap;
                            _ifmap2Offset = sliceInfo[act1LifA].Nb.OfmapOffset;
                            _if2BufIdx = sliceInfo[act1LifA].Nb.OfBufferIndex;
                            _if2Type = act1LifA.CheckedDataType;
                        }
                        else
                        {
                            _lif2 = act1LifA;
                            _ifmap2 = _ofmap;
                            _ifmap2Offset = act1Info.Nb.Ifmap2Offset;
                            _src2ItemName = ItemName.Ifmap2;
                            _if2Type = act1LifA.CheckedDataType;
                        }
                    }
                    else if (_act1[GNNEActivation.InputB] is Call { Target: GNNELoad })
                    {
                        var act1LifB = (Call)_act1[GNNEActivation.InputB];
                        if (act1LifB[GNNELoad.Input] is Var && sliceInfo.ContainsKey(act1LifB))
                        {
                            _if2BufIdx = sliceInfo[act1LifB].Nb.OfBufferIndex;
                            _src2ItemName = ItemName.Ifmap2;
                            _ifmap2 = sliceInfo[act1LifB].Ofmap;
                            _ifmap2Offset = sliceInfo[act1LifB].Nb.OfmapOffset;
                            _if2BufIdx = sliceInfo[act1LifB].Nb.OfBufferIndex;
                            _if2Type = act1LifB.CheckedDataType;
                        }
                        else
                        {
                            _lif2 = act1LifB;
                            _ifmap2 = _ofmap;
                            _ifmap2Offset = act1Info.Nb.Ifmap2Offset;
                            _src2ItemName = ItemName.Ifmap2;
                            _if2Type = _act1[GNNEActivation.InputB].CheckedDataType;
                        }
                    }
                    else
                    {
                        _src2ItemName = ItemName.Ifmap2;
                        if (((GNNEActivation)_act1.Target).InputFromL1[0])
                        {
                            var nodePreActB = sliceInfo[(Call)_act1[GNNEActivation.InputB]];
                            _ifmap2 = nodePreActB.Ofmap;
                            _ifmap2Offset = nodePreActB.Nb.OfmapOffset;
                            _if2BufIdx = nodePreActB.Nb.OfBufferIndex;
                            _if2Type = _act1[GNNEActivation.InputB].CheckedDataType;

                            // if (l1_fused_pair.contains(act1_conv_b))
                            // {
                            //     nodes_quenes_as_if2_raw.emplace(l1_fused_pair.at(act1_conv_b));
                            // }
                            // else
                            // {
                            //     nodes_quenes_as_if2_raw.emplace(act1_conv_b);
                            // }
                        }
                        else if (((GNNEActivation)_act1.Target).InputFromL1[1])
                        {
                            var nodePreActA = sliceInfo[(Call)_act1[GNNEActivation.InputA]];
                            _ifmap2 = nodePreActA.Ofmap;
                            _ifmap2Offset = nodePreActA.Nb.OfmapOffset;
                            _if2BufIdx = nodePreActA.Nb.OfBufferIndex;
                            _if2Type = _act1[GNNEActivation.InputA].CheckedDataType;
                            _swapAB = true;

                            // if (l1_fused_pair.contains(act1_conv_a))
                            // {
                            //     nodes_quenes_as_if2_raw.emplace(l1_fused_pair.at(act1_conv_a));
                            // }
                            // else
                            // {
                            //     nodes_quenes_as_if2_raw.emplace(act1_conv_a);
                            // }
                        }
                        else
                        {
                            throw new NotSupportedException("not support conv_act1 fuse type!");
                        }
                    }
                }

                _l1FuseNi = sliceInfo[_act1];
                _l1Fused = true;
                _l1FusedInfos[curNode.Op] = L1FusedType.FusedAct1;
            }

            _convOutputShape[2] = GetInputRowSegment(0, (int)_outputShape[2], (int)_convOutputShape[2], _fusedKernelH, _fusedStrideH, _fusedDilationH, _fusedPaddingH).Length;
            _convOutputShape[3] = GetInputColumnSegment(0, (int)_outputShape[3], (int)_convOutputShape[3], _fusedKernelW, _fusedStrideW, _fusedDilationW, _fusedPaddingW).Length;

            _weightGroup = _weightGroups[_conv];
            _weight = new(new(..(int)_weightsShape[0], Padding.Zero()), new(..(int)_weightsShape[1], Padding.Zero()), new(..(int)_weightsShape[2], Padding.Zero()), new(..(int)_weightsShape[3], Padding.Zero()));

            _if1BufIdx = sliceInfo[preConv].Nb.OfBufferIndex;
            _ofBufIdx = _l1Fused ? _l1FuseNi.Nb.OfBufferIndex : _ni.Nb.OfBufferIndex;
            _weightBufIdx = _ni.Nb.WeightPreloadOffset == -1 ? _ni.Nb.WeightOffset == 0 ? 0 : 1 : -1;
        }

        if (curNode.Op is { Target: GNNEActivation })
        {
            _act1 = curNode.Op;
            _lact = (Call)_act1[GNNEActivation.Act];
            _outputShape = _act1.CheckedShape.ToValueArray();
            _outputType = _act1.CheckedDataType;

            _ofmap = _ni.Ofmap;
            _ofmapOffset = _ni.Nb.OfmapOffset;
            bool inputBIsUninit = _act1[GNNEActivation.InputB] == None.Default;

            // two source
            if (!inputBIsUninit)
            {
                (_ifmapA, _ifmapB) = GetAct1InputsShape(_act1[GNNEActivation.InputA].CheckedShape.ToValueArray(), _act1[GNNEActivation.InputB].CheckedShape.ToValueArray(), _act1.CheckedShape.ToValueArray(), _ofmap);
                if (_act1[GNNEActivation.InputA] is Call { Target: GNNELoad } && _act1[GNNEActivation.InputB] is Call { Target: GNNELoad })
                {
                    var act1LifA = _act1[GNNEActivation.InputA] as Call;
                    var act1LifB = _act1[GNNEActivation.InputB] as Call;
                    _src2ItemName = ItemName.Ifmap2;
                    _ifmap2Offset = _ni.Nb.Ifmap2Offset;
                    if (sliceInfo.ContainsKey(act1LifA!))
                    {
                        _ifmap = sliceInfo[act1LifA!].Ofmap;
                        _ifmapOffset = sliceInfo[act1LifA!].Nb.OfmapOffset;
                        _lif2 = act1LifB;
                        _ifmap2 = _ifmapB;
                        _if1BufIdx = sliceInfo[act1LifA!].Nb.OfBufferIndex;
                        _if2BufIdx = -1;
                        _ofBufIdx = _ni.Nb.OfBufferIndex;
                        _if2Type = act1LifB!.CheckedDataType;
                        _inputType = act1LifB.CheckedDataType;
                        Assert(_ifmap == _ifmapA);
                    }
                    else
                    {
                        Assert(sliceInfo.ContainsKey(act1LifB!));
                        _ifmap = sliceInfo[act1LifB!].Ofmap;
                        _ifmapOffset = sliceInfo[act1LifB!].Nb.OfmapOffset;
                        _lif2 = act1LifA;
                        _ifmap2 = _ifmapA;
                        _if1BufIdx = sliceInfo[act1LifB!].Nb.OfBufferIndex;
                        _if2BufIdx = -1;
                        _ofBufIdx = _ni.Nb.OfBufferIndex;
                        _if2Type = act1LifA!.CheckedDataType;
                        _inputType = act1LifB!.CheckedDataType;
                        Assert(_ifmap == _ifmapB);
                    }
                }
                else if (_act1[GNNEActivation.InputA] is Call { Target: GNNELoad } && !sliceInfo.ContainsKey((Call)_act1[GNNEActivation.InputA]))
                {
                    var act1LifA = _act1[GNNEActivation.InputA] as Call;
                    var nodePreAct = _act1[GNNEActivation.InputB] as Call;
                    _ifmap = sliceInfo[nodePreAct!].Ofmap;
                    _ifmapOffset = sliceInfo[nodePreAct!].Nb.OfmapOffset;
                    _ifmap2 = _ifmapA;
                    _src2ItemName = ItemName.Ifmap2;
                    _ifmap2Offset = _ni.Nb.Ifmap2Offset;
                    _lif2 = act1LifA;
                    _if1BufIdx = sliceInfo[nodePreAct!].Nb.OfBufferIndex;
                    _if2BufIdx = -1;
                    _ofBufIdx = _ni.Nb.OfBufferIndex;
                    _if2Type = act1LifA!.CheckedDataType;
                    _inputType = nodePreAct!.CheckedDataType;
                    _swapAB = true;
                    Assert(_ifmap == _ifmapB);
                }
                else if (_act1[GNNEActivation.InputB] is Call { Target: GNNELoad } && !sliceInfo.ContainsKey((Call)_act1[GNNEActivation.InputB]))
                {
                    var act1LifB = _act1[GNNEActivation.InputB] as Call;
                    var nodePreAct = _act1[GNNEActivation.InputA] as Call;
                    _ifmapOffset = sliceInfo[nodePreAct!].Nb.OfmapOffset;
                    _ifmap = sliceInfo[nodePreAct!].Ofmap;
                    _ifmap2 = _ifmapB;
                    _src2ItemName = ItemName.Ifmap2;
                    _ifmap2Offset = _ni.Nb.Ifmap2Offset;
                    _lif2 = act1LifB;
                    _if1BufIdx = sliceInfo[nodePreAct!].Nb.OfBufferIndex;
                    _if2BufIdx = -1;
                    _ofBufIdx = _ni.Nb.OfBufferIndex;
                    _if2Type = act1LifB!.CheckedDataType;
                    _inputType = nodePreAct!.CheckedDataType;
                    Assert(_ifmap == _ifmapA);
                }
                else
                {
                    var preNodeA = _act1[GNNEActivation.InputA] as Call;
                    var preNodeB = _act1[GNNEActivation.InputB] as Call;

                    _ifmap = sliceInfo[preNodeA!].Ofmap;
                    _ifmapOffset = sliceInfo[preNodeA!].Nb.OfmapOffset;
                    _ifmap2 = sliceInfo[preNodeB!].Ofmap;
                    _ifmap2Offset = sliceInfo[preNodeB!].Nb.OfmapOffset;
                    _src2ItemName = ItemName.Ifmap2;
                    _if1BufIdx = sliceInfo[preNodeA!].Nb.OfBufferIndex;
                    _if2BufIdx = sliceInfo[preNodeB!].Nb.OfBufferIndex;
                    _ofBufIdx = _ni.Nb.OfBufferIndex;
                    _if2Type = preNodeB!.CheckedDataType;
                    _inputType = preNodeA!.CheckedDataType;

                    // assert(ifmap == ifmap_a and ifmap2 == ifmap_b);
                    Assert(_ifmap2.Shape_size > 0);
                }
            }

            // one source
            else
            {
                var preNode = _act1[GNNEActivation.InputA] as Call;

                _ifmap = sliceInfo[preNode!].Ofmap;
                _ifmapOffset = sliceInfo[preNode!].Nb.OfmapOffset;
                _if1BufIdx = sliceInfo[preNode!].Nb.OfBufferIndex;
                _if2BufIdx = -1;
                _ofBufIdx = _ni.Nb.OfBufferIndex;
                _inputType = preNode!.CheckedDataType;

                _if2Type = _inputType;
            }
        }

        if (curNode.Op is { Target: GNNEPdp1 })
        {
            _pdp1 = curNode.Op;
            var preNode = _pdp1[GNNEPdp1.Input] as Call;
            _ifmap = sliceInfo[preNode!].Ofmap;
            _ifmapOffset = sliceInfo[preNode!].Nb.OfmapOffset;

            _inputType = preNode!.CheckedDataType;
            _outputType = _pdp1.CheckedDataType;

            _ofmap = _ni.Ofmap;
            _ofmapOffset = _ni.Nb.OfmapOffset;

            _inputShape = _pdp1[GNNEPdp1.Input].CheckedShape.ToValueArray();
            _outputShape = _pdp1.CheckedShape.ToValueArray();

            _if1BufIdx = sliceInfo[preNode].Nb.OfBufferIndex;
            _if2BufIdx = -1;
            _ofBufIdx = _ni.Nb.OfBufferIndex;

            int filterH = ((TensorConst)_pdp1[GNNEPdp1.Filter]).Value.ToArray<int>()[0];
            int filterW = ((TensorConst)_pdp1[GNNEPdp1.Filter]).Value.ToArray<int>()[1];

            if (_inputShape[2] == filterH && _inputShape[3] == filterW
                                           && (_inputShape[2] > 16 || _inputShape[3] > 64 ||
                                               _inputShape[2] * _inputShape[3] > 256))
            {
                _isGlobalPdp = true;
            }
            else
            {
                _isGlobalPdp = false;
            }
        }

        if (curNode.Op is { Target: GNNETranspose })
        {
            _transpose = curNode.Op;
            var preNode = _transpose[GNNETranspose.Input] as Call;
            _ifmap = sliceInfo[preNode!].Ofmap;
            _ifmapOffset = sliceInfo[preNode!].Nb.OfmapOffset;

            _inputType = preNode!.CheckedDataType;
            _outputType = _transpose.CheckedDataType;

            _ofmap = _ni.Ofmap;
            _ofmapOffset = _ni.Nb.OfmapOffset;

            _inputShape = preNode.CheckedShape.ToValueArray();
            _outputShape = _transpose.CheckedShape.ToValueArray();

            _if1BufIdx = sliceInfo[preNode].Nb.OfBufferIndex;
            _if2BufIdx = -1;
            _ofBufIdx = _ni.Nb.OfBufferIndex;
        }

        if (curNode.Op is { Target: Concat })
        {
            _cat = curNode.Op;
            _outputType = _cat.CheckedDataType;

            var preNode = _cat[Concat.Input][1] as Call;
            _ifmap = sliceInfo[preNode!].Ofmap;
            _ifmapOffset = sliceInfo[preNode!].Nb.OfmapOffset;
            _if1BufIdx = sliceInfo[preNode!].Nb.OfBufferIndex;

            preNode = _cat[Concat.Input][0] as Call;
            _ifmap2 = sliceInfo[preNode!].Ofmap;
            _ifmap2Offset = sliceInfo[preNode!].Nb.OfmapOffset;
            _if2BufIdx = sliceInfo[preNode!].Nb.OfBufferIndex;

            _ofmap = _ni.Ofmap;
            _ofmapOffset = _ni.Nb.OfmapOffset;
            _ofBufIdx = _ni.Nb.OfBufferIndex;
            Assert(_if1BufIdx != _ofBufIdx);
        }

        if (curNode.Op is { Target: Ai2dResize })
        {
            _resize = curNode.Op;
            var preNode = _resize[Ai2dResize.Input] as Call;
            _ifmap = sliceInfo[preNode!].Ofmap;
            _ifmapOffset = sliceInfo[preNode!].Nb.OfmapOffset;

            _inputType = preNode!.CheckedDataType;
            _outputType = preNode.CheckedDataType;

            _ofmap = _ni.Ofmap;
            _ofmapOffset = _ni.Nb.OfmapOffset;

            _inputShape = preNode.CheckedShape.ToValueArray();
            _outputShape = _resize.CheckedShape.ToValueArray();

            _if1BufIdx = sliceInfo[preNode].Nb.OfBufferIndex;
            _if2BufIdx = -1;
            _ofBufIdx = _ni.Nb.OfBufferIndex;
        }

        // TODO: 支持1x1的优化
        _ofmapSt = _ofmap;
        var preConvCall = _conv is not null ? (Call)_conv![GNNEConv2D.Input] : null;
        _ifmapLd = preConvCall is not null ? sliceInfo[preConvCall].Ofmap : _ifmap;
        bool conv1X1 = ReshapeConv(_conv!, ref _inputShape!, ref _outputShape!, ref _convOutputShape!, ref _weightsShape!);
        if (conv1X1)
        {
            int cNum = _conv![GNNEConv2D.Weights].CheckedShape.ToValueArray()[1] % 24 == 0 ? 24 : _conv[GNNEConv2D.Weights].CheckedShape.ToValueArray()[1] % 20 == 0 ? 20 : 16;
            _ifmap = new(
                new(.._ifmap[0].Length, Padding.Zero()),
                new(..cNum, Padding.Zero()),
                new(..(_ifmap[1].Length / cNum), Padding.Zero()),
                new(..(_ifmap[2].Length * _ifmap[3].Length), Padding.Zero()));
            _ofmap = new(
                new(.._ofmap[0].Length, Padding.Zero()),
                new(.._ofmap[1].Length, Padding.Zero()),
                new(..1, Padding.Zero()),
                new(..(_ofmap[2].Length * _ofmap[3].Length), Padding.Zero()));

            _convOutputShape[2] = GetInputRowSegment(0, (int)_outputShape[2], (int)_convOutputShape[2], _fusedKernelH, _fusedStrideH, _fusedDilationH, _fusedPaddingH!).Length;
            _convOutputShape[3] = GetInputColumnSegment(0, (int)_outputShape[3], (int)_convOutputShape[3], _fusedKernelW, _fusedStrideW, _fusedDilationW, _fusedPaddingW!).Length;

            _weight = new(
                new(..(int)_weightsShape[0], Padding.Zero()),
                new(..(int)_weightsShape[1], Padding.Zero()),
                new(..(int)_weightsShape[2], Padding.Zero()),
                new(..(int)_weightsShape[3], Padding.Zero()));
        }

        if (!weightGroupOnly)
        {
            GetGlbLayouts(fusionInfo, curNode, glb, sliceInfo);
        }
    }

    private bool ReshapeConv(Call conv, ref long[] inputShape, ref long[] outputShape, ref long[] convOutputShape, ref long[] weightsShape)
    {
        if (Conv1X1(conv))
        {
            Assert(conv[GNNEConv2D.Weights].CheckedShape.ToValueArray()[1] % 24 == 0);
            int cNum = 24;
            while (conv[GNNEConv2D.Weights].CheckedShape.ToValueArray()[1] % cNum != 0)
            {
                cNum--;
            }

            inputShape = new[] { conv[GNNEConv2D.Input].CheckedShape.ToValueArray()[0], cNum, conv[GNNEConv2D.Weights].CheckedShape.ToValueArray()[1] / cNum, conv[GNNEConv2D.Input].CheckedShape.ToValueArray()[2] * conv[GNNEConv2D.Input].CheckedShape.ToValueArray()[3] };
            weightsShape = new[] { conv[GNNEConv2D.Weights].CheckedShape.ToValueArray()[0], cNum, conv[GNNEConv2D.Weights].CheckedShape.ToValueArray()[1] / cNum, 1 };
            outputShape = new[] { conv.CheckedShape.ToValueArray()[0], conv.CheckedShape.ToValueArray()[1], 1, conv.CheckedShape.ToValueArray()[2] * conv.CheckedShape.ToValueArray()[3] };
            convOutputShape = new[] { conv.CheckedShape.ToValueArray()[0], conv.CheckedShape.ToValueArray()[1], 1, conv.CheckedShape.ToValueArray()[2] * conv.CheckedShape.ToValueArray()[3] };
            if (_dw is not null)
            {
                outputShape = new[] { _dw.CheckedShape.ToValueArray()[0], _dw.CheckedShape.ToValueArray()[1], 1, _dw.CheckedShape.ToValueArray()[2] * _dw.CheckedShape.ToValueArray()[3] };
            }

            if (_pool is not null)
            {
                outputShape = new[] { _pool.CheckedShape.ToValueArray()[0], _pool.CheckedShape.ToValueArray()[1], 1, _pool.CheckedShape.ToValueArray()[2] * _pool.CheckedShape.ToValueArray()[3] };
            }

            if (_act1 is not null)
            {
                outputShape = new[] { _act1.CheckedShape.ToValueArray()[0], _act1.CheckedShape.ToValueArray()[1], 1, _act1.CheckedShape.ToValueArray()[2] * _act1.CheckedShape.ToValueArray()[3] };
            }

            return true;
        }

        return false;
    }

    private void GetGlbLayouts(FusionInfo fusionInfo, NodeInfo curNode, TiledGlb glb, Dictionary<Call, NodeInfo> sliceInfo)
    {
        // function description:
        // this function is designed to modify the glb layout of the if, if2, of tensor;
        // the if2 layout should be modified when the current node's dimension of if2 is not equal to zero;
        // the if layout should be modified in 2 situations:
        //   first: current node is pdp1 or transpose;
        //   second: prenode is pdp1, transpose or act1&resadd&fused;
        // the of layout should be modified in 2 situations:
        //   first: current node is pdp1 or transpose;
        //   second: postnode is pdp1, transpose or act1&resadd&fused;
        var align = (int a, int b, int alignElement) =>
        {
            int aTemp = a;
            int bTemp = b;

            if ((a * b) % alignElement == 0)
            {
                return new Tuple<int, int>(aTemp, bTemp);
            }

            int eEnd = a + alignElement - 1;
            int fEnd = b + alignElement - 1;
            long minAlgin32 = 65535U * 65535U;

            for (int i = a; i < eEnd; i++)
            {
                for (int j = b; j < fEnd; j++)
                {
                    int mulRes = i * j;
                    if (mulRes % alignElement != 0)
                    {
                        continue;
                    }

                    if (mulRes >= minAlgin32)
                    {
                        continue;
                    }

                    minAlgin32 = mulRes;
                    aTemp = i;
                    bTemp = j;
                }
            }

            return new Tuple<int, int>(aTemp, bTemp);
        };

        // get ofmap layout
        var mmu = glb.GlbMap[ItemName.Ofmap].Mmu;
        var realOutNode = curNode;
        if (curNode.Op is { Target: GNNEConv2D } && _act1 is not null)
        {
            realOutNode = curNode.Children[0];
        }

        switch (realOutNode.Nb.AlignType)
        {
            case AlignedType.FAligned:
                {
                    int f = _ofmapSt[3].Length;
                    int alignedNum = GetBytesPerElement(_outputType) == 1 ? 32 : 16;
                    int fTemp = GetAlignedNum(f, alignedNum);
                    glb.GlbMap[ItemName.Ofmap] = new(new[] { _ofmapSt[0].Length, _ofmapSt[1].Length, _ofmapSt[2].Length, fTemp }, _outputType, 0, mmu);
                    break;
                }

            case AlignedType.EAligned:
                {
                    int e = _ofmapSt[2].Length;
                    int alignedNum = GetBytesPerElement(_outputType) == 1 ? 32 : 16;
                    int eTemp = GetAlignedNum(e, alignedNum);
                    glb.GlbMap[ItemName.Ofmap] = new(new[] { _ofmapSt[0].Length, _ofmapSt[1].Length, eTemp, _ofmapSt[3].Length }, _outputType, 0, mmu);
                    break;
                }

            default:
                glb.GlbMap[ItemName.Ofmap] = new(new[] { _ofmapSt[0].Length, _ofmapSt[1].Length, _ofmapSt[2].Length, _ofmapSt[3].Length }, _outputType, 0, mmu);
                break;
        }

        // get ifmap layout
        int inputIndex = 0;
        if (curNode.Op is { Target: GNNEActivation } &&
            curNode.Op[GNNEActivation.InputB] != None.Default &&
            sliceInfo.ContainsKey((Call)curNode.Op[GNNEActivation.InputB]) &&
            !sliceInfo.ContainsKey((Call)curNode.Op[GNNEActivation.InputA]))
        {
            inputIndex = 1;
        }

        mmu = glb.GlbMap[ItemName.Ofmap].Mmu;
        if (curNode.Op is not { Target: GNNELoad })
        {
            if (sliceInfo[(Call)curNode.Op.Arguments[inputIndex]].Nb.AlignType == AlignedType.FAligned)
            {
                int h = _ifmapLd[2].Length;
                int w = _ifmapLd[3].Length;
                int hTemp = h;
                int alignedNum = GetBytesPerElement(_inputType) == 1 ? 32 : 16;
                int wTemp = GetAlignedNum(w, alignedNum);
                glb.GlbMap[ItemName.Ifmap] = new(new[] { _ifmapLd[0].Length, _ifmapLd[1].Length, hTemp, wTemp }, _inputType, 0, mmu);
            }
            else if (sliceInfo[(Call)curNode.Op.Arguments[inputIndex]].Nb.AlignType == AlignedType.EAligned)
            {
                int h = _ifmapLd[2].Length;
                int w = _ifmapLd[3].Length;
                int alignedNum = GetBytesPerElement(_inputType) == 1 ? 32 : 16;
                int hTemp = GetAlignedNum(h, alignedNum);
                int wTemp = w;
                glb.GlbMap[ItemName.Ifmap] = new(new[] { _ifmapLd[0].Length, _ifmapLd[1].Length, hTemp, wTemp }, _inputType, 0, mmu);
            }
            else
            {
                var ifType = _inputType;
                glb.GlbMap[ItemName.Ifmap] = new(new[] { _ifmapLd[0].Length, _ifmapLd[1].Length, _ifmapLd[2].Length, _ifmapLd[3].Length }, ifType, 0, mmu);
            }
        }

        // get double inputs gnne_activation's layout
        var if2Mmu = fusionInfo.Mmu.ContainsKey(ItemName.Ifmap2) ? fusionInfo.Mmu[ItemName.Ifmap2] : glb.GlbMap[ItemName.Ifmap2].Mmu;
        if (curNode.Op is { Target: GNNEActivation } && curNode.Op[GNNEActivation.InputB] != None.Default)
        {
            inputIndex = 1;
            if (sliceInfo.ContainsKey((Call)curNode.Op[GNNEActivation.InputB]) &&
                !sliceInfo.ContainsKey((Call)curNode.Op[GNNEActivation.InputA]))
            {
                inputIndex = 0;
            }

            int h = _ifmap2[2].Length;
            int w = _ifmap2[3].Length;
            int tensorAlignSize = 32 / GetBytesPerElement(_if2Type);
            int hTemp;
            int wTemp;

            if (!sliceInfo.ContainsKey((Call)curNode.Op.Arguments[inputIndex]))
            {
                hTemp = h;
                wTemp = w;
            }
            else
            {
                if2Mmu = glb.GlbMap[ItemName.Ofmap].Mmu;
                switch (sliceInfo[(Call)curNode.Op.Arguments[inputIndex]].Nb.AlignType)
                {
                    case AlignedType.FAligned:
                        hTemp = h;
                        wTemp = GetAlignedNum(w, tensorAlignSize);
                        break;
                    case AlignedType.EAligned:
                        hTemp = GetAlignedNum(h, tensorAlignSize);
                        wTemp = w;
                        break;
                    case AlignedType.NoAligned:
                    case AlignedType.EXFAligned:
                    default:
                        hTemp = h;
                        wTemp = w;
                        break;
                }
            }

            glb.GlbMap[ItemName.Ifmap2] = new(new[] { _ifmap2[0].Length, _ifmap2[1].Length, hTemp, wTemp }, _if2Type, 0, if2Mmu);
        }
        else
        {
            glb.GlbMap[ItemName.Ifmap2] = new(new[] { _ifmap2[0].Length, _ifmap2[1].Length, _ifmap2[2].Length, _ifmap2[3].Length }, _if2Type, 0, if2Mmu);
        }

        // get layout for act l1 fuse
        if (curNode.Op is { Target: GNNEConv2D } && _act1 is not null && _act1[GNNEActivation.InputB] != None.Default)
        {
            inputIndex = ((GNNEActivation)_act1.Target).InputFromL1[0] ? 1 : 0;
            int h = _ifmap2[2].Length;
            int w = _ifmap2[3].Length;
            int tensorAlignSize = 32 / GetBytesPerElement(_if2Type);
            int hTemp;
            int wTemp;

            if (!sliceInfo.ContainsKey((Call)_act1.Arguments[inputIndex]))
            {
                hTemp = GetAlignedNum(h, tensorAlignSize);
                wTemp = w;
            }
            else
            {
                if2Mmu = glb.GlbMap[ItemName.Ofmap].Mmu;
                switch (sliceInfo[(Call)_act1.Arguments[inputIndex]].Nb.AlignType)
                {
                    case AlignedType.FAligned:
                        hTemp = h;
                        wTemp = GetAlignedNum(w, tensorAlignSize);
                        break;
                    case AlignedType.EAligned:
                        hTemp = GetAlignedNum(h, tensorAlignSize);
                        wTemp = w;
                        break;
                    case AlignedType.NoAligned:
                    case AlignedType.EXFAligned:
                    default:
                        hTemp = h;
                        wTemp = w;
                        break;
                }
            }

            glb.GlbMap[ItemName.Ifmap2] = new(new[] { _ifmap2[0].Length, _ifmap2[1].Length, hTemp, wTemp }, _if2Type, 0, if2Mmu);
        }

        if (!_h2C)
        {
            return;
        }

        if (_lif is not null)
        {
            var postConv = curNode.Children[0].Op;
            int[] padding = ((TensorConst)postConv[GNNEConv2D.Padding]).Value.ToArray<int>();
            var oldMmu = glb.GlbMap[ItemName.Ofmap].Mmu;
            glb.GlbMap[ItemName.Ofmap] = new(new[] { _ofmapSt[0].Length, _ofmapSt[1].Length, _ofmapSt[2].Length + padding[0] + padding[1], _ofmapSt[3].Length }, _outputType, 0, oldMmu);

            _ofmapSt[2].Padding = _ofmap[2].Padding;
            _ofmapSt[3].Padding = _ofmap[3].Padding;

            glb.GlbMap[ItemName.Ifmap] = glb.GlbMap[ItemName.Ofmap];
        }
        else
        {
            int[] padding = ((TensorConst)_conv[GNNEConv2D.Padding]).Value.ToArray<int>();
            var oldMmu = glb.GlbMap[ItemName.Ifmap].Mmu;
            glb.GlbMap[ItemName.Ifmap] = new(new[] { _ifmapLd[0].Length, _ifmapLd[1].Length, _ifmapLd[2].Length + padding[0] + padding[1], _ifmapLd[3].Length }, _inputType, 0, oldMmu);

            _ifmapLd[2].Padding = _ifmap[2].Padding;
            _ifmapLd[3].Padding = _ifmap[3].Padding;
        }
    }

    private void ItemRecStatusUpdate()
    {
        if (_conv is not null)
        {
            if (!_nodesWeightRec.ContainsKey(_conv))
            {
                _nodesWeightRec.Add(_conv, new() { new(), new() });
            }

            if (!_nodesOfmapRec.ContainsKey(_conv))
            {
                _nodesOfmapRec.Add(_conv, new() { new(), new(), new() });
            }

            if (!_nodesG2LIfRec.ContainsKey(_conv))
            {
                _nodesG2LIfRec.Add(_conv, new() { new(), new(), new() });
            }

            if (!_nodesG2RWRec.ContainsKey(_conv))
            {
                _nodesG2RWRec.Add(_conv, new() { new(), new() });
            }

            if (!_nodesL2GOfRec.ContainsKey(_conv))
            {
                _nodesL2GOfRec.Add(_conv, new() { new(), new(), new() });
            }

            if (!_nodesL2RIf2Rec.ContainsKey(_conv))
            {
                _nodesL2RIf2Rec.Add(_conv, new() { new(), new(), new() });
            }

            if (!_nodesG2RWSliceRec.ContainsKey(_conv))
            {
                _nodesG2RWSliceRec.Add(_conv, new() { new(), new() });
            }

            if (_weightBufIdx != -1)
            {
                _nodesQuenesAsW[_weightBufIdx].Add(new Tuple<Call, int>(_conv, 0));
            }
        }

        if (_resize is null)
        {
            return;
        }

        if (!_nodesOfmapRec.ContainsKey(_resize))
        {
            _nodesOfmapRec.Add(_resize, new());
        }

        if (_nodesAi2dIfRec.ContainsKey(_resize))
        {
            _nodesAi2dIfRec.Add(_resize, new());
        }

        if (_nodesAi2dOfRec.ContainsKey(_resize))
        {
            _nodesAi2dOfRec.Add(_resize, new());
        }
    }

    private void UpdateCcrRecStat()
    {
        // g2l_if_rec status decide
        foreach (var t in _nodesG2LIfRec.Values.SelectMany(iter => iter))
        {
            for (int cnt = 0; cnt < t.Count; cnt++)
            {
                if (cnt == 0)
                {
                    t[cnt].Item2.IsFirstSlice = true;
                }

                if (cnt == t.Count - 1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                }

                if (cnt < t.Count - 1 && t[cnt].Item1 != t[cnt + 1].Item1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                    t[cnt + 1].Item2.IsFirstSlice = true;
                }
            }
        }

        // g2r_w_rec status decide
        foreach (var t in _nodesG2RWRec.Values.SelectMany(iter => iter))
        {
            for (int cnt = 0; cnt < t.Count; cnt++)
            {
                if (cnt == 0)
                {
                    t[cnt].Item2.IsFirstSlice = true;
                }

                if (cnt == t.Count - 1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                }

                if (cnt < t.Count - 1 && t[cnt].Item1 != t[cnt + 1].Item1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                    t[cnt + 1].Item2.IsFirstSlice = true;
                }
            }
        }

        // l2g_of_rec status decide
        foreach (var t in _nodesL2GOfRec.Values.SelectMany(iter => iter))
        {
            for (int cnt = 0; cnt < t.Count; cnt++)
            {
                if (cnt == 0)
                {
                    t[cnt].Item2.IsFirstSlice = true;
                }

                if (cnt == t.Count - 1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                }

                if (cnt < t.Count - 1 && t[cnt].Item1 != t[cnt + 1].Item1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                    t[cnt + 1].Item2.IsFirstSlice = true;
                }
            }
        }

        // l2r_if2_rec status decide
        foreach (var t in _nodesL2RIf2Rec.Values.SelectMany(iter => iter))
        {
            for (int cnt = 0; cnt < t.Count; cnt++)
            {
                if (cnt == 0)
                {
                    t[cnt].Item2.IsFirstSlice = true;
                }

                if (cnt == t.Count - 1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                }

                if (cnt < t.Count - 1 && t[cnt].Item1 != t[cnt + 1].Item1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                    t[cnt + 1].Item2.IsFirstSlice = true;
                }
            }
        }

        // weight_rec status decide
        foreach (var t in _nodesWeightRec.Values.SelectMany(iter => iter.Where(t => t.Count > 0)))
        {
            t[0].Item2.IsFirstSlice = true;
            t[^1].Item2.IsLastSlice = true;
        }

        // ofmap_rec status decide
        foreach (var t in from iter in _nodesOfmapRec.Values from t in iter where t.Count > 0 select t)
        {
            t[0].Item2.IsFirstSlice = true;
            t[^1].Item2.IsLastSlice = true;
        }

        // g2r_w_slice_rec status decide
        foreach (var iter in _nodesG2RWSliceRec)
        {
            var g2RWRec = _nodesG2RWRec[iter.Key];
            for (int pp = 0; pp < iter.Value.Count; pp++)
            {
                List<SegmentND> uniqueWSlices = new();
                List<SegmentND> wSlices = new();
                tensor_ccr_stat wSlicesRecPart = new();
                for (int g2RWSliceIdx = 0; g2RWSliceIdx < g2RWRec[pp].Count; g2RWSliceIdx++)
                {
                    if (g2RWRec[pp][g2RWSliceIdx].Item2.IsFirstSlice)
                    {
                        uniqueWSlices.Clear();
                        wSlices.Clear();
                        wSlicesRecPart.Clear();
                    }

                    if (!uniqueWSlices.Contains(iter.Value[pp][g2RWSliceIdx].Item1))
                    {
                        uniqueWSlices.Add(iter.Value[pp][g2RWSliceIdx].Item1);
                    }

                    wSlices.Add(iter.Value[pp][g2RWSliceIdx].Item1);
                    wSlicesRecPart.Add(iter.Value[pp][g2RWSliceIdx]);
                    if (!g2RWRec[pp][g2RWSliceIdx].Item2.IsLastSlice)
                    {
                        continue;
                    }

                    foreach (var slice in uniqueWSlices)
                    {
                        wSlicesRecPart[wSlices.IndexOf(slice)].Item2.IsFirstSlice = true;
                        wSlicesRecPart[wSlicesRecPart.Count - 1 - wSlices.IndexOf(slice)].Item2.IsLastSlice = true;
                    }

                    for (int cnt = 0; cnt < wSlices.Count; cnt++)
                    {
                        wSlicesRecPart[cnt].Item2.SliceIdx = uniqueWSlices.IndexOf(wSlices[cnt]);
                        iter.Value[pp][g2RWSliceIdx - wSlices.Count + 1 + cnt] = wSlicesRecPart[cnt];
                    }
                }

                var conv2dRec = iter.Key;
                if (conv2dRec is not { Target: GNNEConv2D } ||
                    conv2dRec[GNNEConv2D.Weights].CheckedDataType != DataTypes.Int16)
                {
                    continue;
                }

                {
                    for (int cnt = 1; cnt < iter.Value[pp].Count; cnt++)
                    {
                        if (iter.Value[pp][iter.Value[pp].Count - cnt - 1].Item2.IsFirstSlice)
                        {
                            iter.Value[pp][iter.Value[pp].Count - cnt].Item2.IsFirstSlice = true;
                        }

                        if (iter.Value[pp][cnt].Item2.IsLastSlice)
                        {
                            iter.Value[pp][cnt - 1].Item2.IsLastSlice = true;
                        }
                    }

                    for (int cnt = 0; cnt < iter.Value[pp].Count; cnt++)
                    {
                        iter.Value[pp][cnt].Item2.SliceIdx = (iter.Value[pp][cnt].Item2.SliceIdx * 2) + (cnt & 0x1);
                    }
                }
            }
        }

        foreach (var t in _nodesQuenesAsW)
        {
            for (int idx = 0; idx < t.Count; idx++)
            {
                t[idx] = new Tuple<Call, int>(t[idx].Item1, idx);
            }
        }
    }

    private void UpdateAi2dCcrRecStat()
    {
        // g2l_if_rec status decide
        foreach (var t in _nodesAi2dIfRec.Values.SelectMany(iter => iter))
        {
            for (int cnt = 0; cnt < t.Count; cnt++)
            {
                if (cnt == 0)
                {
                    t[cnt].Item2.IsFirstSlice = true;
                }

                if (cnt == t.Count - 1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                }

                if (cnt >= t.Count - 1 || t[cnt].Item1 == t[cnt + 1].Item1)
                {
                    continue;
                }

                t[cnt].Item2.IsLastSlice = true;
                t[cnt + 1].Item2.IsFirstSlice = true;
            }
        }

        // l2g_of_rec status decide
        foreach (var t in _nodesAi2dOfRec.Values.SelectMany(iter => iter))
        {
            for (int cnt = 0; cnt < t.Count; cnt++)
            {
                if (cnt == 0)
                {
                    t[cnt].Item2.IsFirstSlice = true;
                }

                if (cnt == t.Count - 1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                }

                if (cnt >= t.Count - 1 || t[cnt].Item1 == t[cnt + 1].Item1)
                {
                    continue;
                }

                t[cnt].Item2.IsLastSlice = true;
                t[cnt + 1].Item2.IsFirstSlice = true;
            }
        }

        // ofmap_rec status decide
        foreach (var t in from iter in _nodesOfmapRec.Values from t in iter where t.Count > 0 select t)
        {
            t[0].Item2.IsFirstSlice = true;
            t[^1].Item2.IsLastSlice = true;
        }
    }

    private Tuple<List<CcrSet>, List<CcrClr>> GetCcrSetAndClrVec(NodeInfo currNode)
    {
        List<CcrSet> ccrsToSet = new();
        List<CcrClr> ccrsToClr = new();

        if (!GNNEEnv.UseCcr)
        {
            return new(ccrsToSet, ccrsToClr);
        }

        Assert(currNode.Op is not { Target: GNNEConv2D }); // conv process independently;

        if (currNode.Op is { Target: GNNEStore })
        {
            if (_nodesQueNeedClearFake.Count > 0)
            {
                ccrsToSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.OfmapFake, _ofBufIdx)), 1));
            }
        }
        else
        {
            int ccrSetValue = GetCcrSetAccordingPostNodes(currNode);
            if (ccrSetValue != 0)
            {
                ccrsToSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, _ofBufIdx)), ccrSetValue));
            }
        }

        if (currNode.Op is not { Target: GNNELoad } && currNode.Op is not { Target: GNNEStore })
        {
            if (_if1BufIdx != -1)
            {
                ccrsToClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, _if1BufIdx))));
            }

            if (_if2BufIdx != -1 && currNode.Op is not { Target: Concat })
            {
                ccrsToClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, _if2BufIdx))));
            }
        }
        else if (currNode.Op is { Target: GNNEStore })
        {
            ccrsToClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, _if1BufIdx))));
        }

        return new(ccrsToSet, ccrsToClr);
    }
}

internal class TileLayerGroupGlb : TiledGlb
{
    private TensorOnGlb _ifGlb;
    private TensorOnGlb _ofGlb;

    public TileLayerGroupGlb(Dictionary<ItemName, TensorOnGlb> glbMap, Dictionary<ItemName, MmuItem> items, int[] lastOutShape, int nPingPongSplit, TensorOnGlb ifGlb, TensorOnGlb ofGlb)
        : base(glbMap, items, lastOutShape, nPingPongSplit)
    {
        _ifGlb = ifGlb;
        _ofGlb = ofGlb;
    }
}
