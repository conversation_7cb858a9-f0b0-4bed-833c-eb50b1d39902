// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using Nncase.Runtime.K230;
using Nncase.Schedule;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using static Nncase.Passes.Rules.K230.TileUtilities;
using tensor_ccr_stat = System.Collections.Generic.List<System.Tuple<Nncase.TIR.SegmentND, Nncase.Passes.Rules.K230.TensorStat>>;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class TileLoadStore : RewriteRule<Pattern>
{
    private static int _count = -1;

    private CcrHandler _ccrHandler = new();
    private GprHandler _gpr = new();
    private SsrHandler _ssr = new();

    private DataType? _inputType;
    private DataType? _outputType;
    private Call? _lif;
    private Call? _sof;
    private GNNEShape? _inputShape;
    private GNNEShape? _outputShape;

    public override Pattern Pattern { get; } = FusionPattern.IsGNNELoadStoreFusion();

    private PrimFunction GetReplace(Call ldCall, Call stCall)
    {
        _count++;
        var inShape = ldCall[GNNELoad.Input].CheckedShape.ToValueArray();
        T.CreateBuffer(new(ldCall[GNNELoad.Input].CheckedDataType, inShape), MemoryLocation.Input, out var ddrIf);
        T.CreateBuffer(new(stCall.CheckedDataType, stCall.CheckedShape), MemoryLocation.Output, out var ddrOf);
        InitParameters(ldCall, stCall);
        TiledGlb glb = SearchGlbParameters();
        var actions = BuildSchedule(glb, ddrIf, ddrOf);
        var actionConverter = new ActionToInstruct();
        return T.PrimFunc($"TileLoadStore_{_count}", K230RtModule.Kind, new Var(ddrIf.CheckedType), new Var(ddrOf.CheckedType)).Body(
            actionConverter.Instructions(actions),
            I.END(GP_REGISTER.x0, 0))
        .Build();
    }

    private void InitParameters(Call ld, Call st)
    {
        _lif = ld;
        _sof = st;

        _inputType = _lif.CheckedDataType;
        _outputType = _sof.CheckedDataType;

        _inputShape = new GNNEShape((int)_lif.CheckedShape[0].FixedValue, (int)_lif.CheckedShape[1].FixedValue, (int)_lif.CheckedShape[2].FixedValue, (int)_lif.CheckedShape[3].FixedValue);
        _outputShape = new GNNEShape((int)_sof.CheckedShape[0].FixedValue, (int)_sof.CheckedShape[1].FixedValue, (int)_sof.CheckedShape[2].FixedValue, (int)_sof.CheckedShape[3].FixedValue);
    }

    private TileLoadStoreGlb SearchGlbParameters()
    {
        int n = 1;
        int c = 1;

        int h = 1;
        ref int oh = ref h;
        int ow = _outputShape![3];
        int w = _inputShape![3];

        int nPingPongSplit = GNNEEnv.NPingPongSplit;

        // try to ping pong with minimal tile
        var allocation = HandleAllocate(n, c, h, w, oh, ow, nPingPongSplit);

        if (!allocation.IsOk)
        {
            nPingPongSplit = 1;
        }

        while (h < _inputShape[2] && h < (1 << 16) - 1)
        {
            int nextH = h + 1;
            allocation = HandleAllocate(n, c, nextH, w, nextH, ow, nPingPongSplit);
            if (allocation.IsOk)
            {
                h += 1;
            }
            else
            {
                break;
            }
        }

        while (c < _inputShape[1] && c < (1 << 16) - 1)
        {
            allocation = HandleAllocate(n, c + 1, h, w, oh, ow, nPingPongSplit);
            if (allocation.IsOk)
            {
                c += 1;
            }
            else
            {
                break;
            }
        }

        while (n < _inputShape[0] && n < (1 << 16) - 1)
        {
            allocation = HandleAllocate(n + 1, c, h, w, oh, ow, nPingPongSplit);
            if (allocation.IsOk)
            {
                n += 1;
            }
            else
            {
                break;
            }
        }

        allocation = HandleAllocate(n, c, h, w, oh, ow, nPingPongSplit, true);
        Assert(allocation.IsOk);

        GNNEShape lastOutShape = new(n, c, oh, ow);
        var glb = new TileLoadStoreGlb(allocation.GlbMap, allocation.Items, lastOutShape.Dims, GNNEEnv.NPingPongSplit, allocation.GlbMap[ItemName.Ifmap], allocation.GlbMap[ItemName.Ofmap]);

        return glb;
    }

    private AllocateResult HandleAllocate(int n, int c, int h, int w, int e, int f, int nPingPongSplit, bool isFinal = false)
    {
        List<BoxOnGlb> boxes = new();
        Dictionary<ItemName, TensorOnGlb> glbMap = new();

        int cPp = (int)Math.Ceiling(1.0 * c / nPingPongSplit);

        TensorOnGlb ifGlb = new(new[] { n, cPp, h, w }, _inputType!, 0);
        int ifmapSize = ifGlb.AllocatedBytes * nPingPongSplit;
        ifmapSize = GetAlignedNum(ifmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);
        TensorOnGlb ofGlb = new(new[] { n, cPp, h, w }, _inputType!, 0);

        int basementSize = SpaceSearcher.GetBasementSize();
        basementSize = GetAlignedNum(basementSize, GNNEEnv.GlbWidth * GNNEEnv.GlbBankWidth);

        boxes.Add(new BoxOnGlb(
            new[] { GNNEEnv.GlbWidth, basementSize / GNNEEnv.GlbWidth / GNNEEnv.GlbBankWidth },
            ItemName.Basement));
        boxes.Add(new BoxOnGlb(
            new[] { GNNEEnv.IfmapBankWidth, ifmapSize / GNNEEnv.IfmapBankWidth / GNNEEnv.GlbBankWidth },
            ItemName.Ifmap));

        var bp = new BoxPacker(16);
        bp.Boxes = boxes;
        var allocation = SpaceSearcher.TryAllocate(bp);

#if DEBUG
        if (isFinal && allocation.IsOk)
        {
            bp.DisplayFinalAllocation();
        }
#endif

        if (allocation.IsOk)
        {
            ifGlb.Mmu = allocation.Items[ItemName.Ifmap];
            ofGlb.Mmu = allocation.Items[ItemName.Ifmap];
        }

        glbMap.Add(ItemName.Ifmap, ifGlb);
        glbMap.Add(ItemName.Ofmap, ofGlb);

        AllocateResult ret = new();
        ret.IsOk = allocation.IsOk;
        ret.Items = allocation.Items;
        ret.GlbMap = glbMap;

        return ret;
    }

    private List<GnneAction> BuildSchedule(TiledGlb glb, TIR.Buffer ddrIf, TIR.Buffer ddrOf)
    {
        List<GnneAction> actions = new();
        var gpr = new GprHandler(GNNEEnv.GprNum);
        var ssr = new SsrHandler(GNNEEnv.SsrNum);
        var ccrHandler = new CcrHandler();
        GnneActionUpdater actionUpdater = new(actions, glb, ccrHandler, gpr, ssr);

        // mmu conf
        actionUpdater.UpdateMmuConf();

        int[] strides = ((TensorConst)_sof![GNNEStore.Strides]).Value.ToArray<int>();
        var outputSegment = (int start, int end, int dim) =>
        {
            int s = (int)Math.Ceiling(1.0 * start / strides[dim]);
            int e = (int)Math.Ceiling(1.0 * end / strides[dim]);
            Segment1D wcInloop = new(s..e, new(0, 0));
            return wcInloop;
        };

        var inputBatchSeg = GetSegmentStartEndLength(0, glb.LastOutShape[0], _inputShape![0]);
        foreach (var glbInputBatch in inputBatchSeg)
        {
            var glbOutputBatch = outputSegment(glbInputBatch.Start, glbInputBatch.End, 0);
            var inputChanSeg = GetSegmentStartEndLength(0, glb.LastOutShape[1], _inputShape[1]);
            foreach (var glbInputChannel in inputChanSeg)
            {
                var glbOutputChannel = outputSegment(glbInputChannel.Start, glbInputChannel.End, 1);
                var inputRowSeg = GetSegmentStartEndLength(0, glb.LastOutShape[2], _inputShape[2]);
                foreach (var glbInputRow in inputRowSeg)
                {
                    var glbOutputRow = outputSegment(glbInputRow.Start, glbInputRow.End, 2);
                    var inputColSeg = GetSegmentStartEndLength(0, glb.LastOutShape[3], _inputShape[3]);
                    foreach (var glbInputColumn in inputColSeg)
                    {
                        var glbOutputColumn = outputSegment(glbInputColumn.Start, glbInputColumn.End, 3);
                        SegmentND ofmap = new(glbOutputBatch, glbOutputChannel, glbOutputRow, glbOutputColumn);
                        SegmentND ifmap = new(glbOutputBatch, glbInputChannel, glbInputRow, glbInputColumn);

                        int cPpSplitSize = (int)Math.Ceiling(1.0 * glbInputChannel.Length / glb.NPingPongSplit);
                        var inChanSplit = GetSegmentStartEndLength(glbInputChannel.Start, cPpSplitSize, glbInputChannel.End);
                        for (int iPp = 0; iPp < inChanSplit.Count; iPp++)
                        {
                            var cPpSplit = inChanSplit[iPp];
                            var ocPpSplit = outputSegment(cPpSplit.Start, cPpSplit.End, 1);

                            // load ifmap
                            List<CcrSet> ccrSetIfmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap, iPp)), 1) };
                            SegmentND ifmapPp = new(ifmap[0], cPpSplit, ifmap[2], ifmap[3]);
                            actionUpdater.UpdateLoadIf(ifmapPp, _lif!, iPp, ddrIf, 0, null!, ItemName.Ifmap, ccrSetIfmap);

                            // store ofmap
                            List<CcrClr> ccrClrOfmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap, iPp))) };
                            SegmentND ofmapPp = new(ofmap[0], ocPpSplit, ofmap[2], ofmap[3]);
                            SegmentND ofmapPpSt = new(ofmap[0], ocPpSplit, ofmap[2], ofmap[3]);
                            ofmapPpSt[0] = new((ofmapPpSt[0].Start * strides[0])..ofmapPpSt[0].End, new(0, 0));
                            ofmapPpSt[1] = new((ofmapPpSt[1].Start * strides[1])..ofmapPpSt[1].End, new(0, 0));
                            ofmapPpSt[2] = new((ofmapPpSt[2].Start * strides[2])..ofmapPpSt[2].End, new(0, 0));
                            ofmapPpSt[3] = new((ofmapPpSt[3].Start * strides[3])..ofmapPpSt[3].End, new(0, 0));

                            int offset = GetSliceOffsetInTensor(ifmapPp, ofmapPpSt);
                            actionUpdater.UpdateStoreT(ofmapPp, _sof, iPp, ddrOf, offset, null!, null!, ccrClrOfmap);
                        }
                    }
                }
            }
        }

        Assert(ccrHandler.CcrSanityCheck());

        return actions;
    }
}

internal class TileLoadStoreGlb : TiledGlb
{
    private TensorOnGlb _ifGlb;
    private TensorOnGlb _ofGlb;

    public TileLoadStoreGlb(Dictionary<ItemName, TensorOnGlb> glbMap, Dictionary<ItemName, MmuItem> items, int[] lastOutShape, int nPingPongSplit, TensorOnGlb ifGlb, TensorOnGlb ofGlb)
        : base(glbMap, items, lastOutShape, nPingPongSplit)
    {
        _ifGlb = ifGlb;
        _ofGlb = ofGlb;
    }
}
