﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using Nncase.Runtime.K230;
using Nncase.Schedule;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using static Nncase.Passes.Rules.K230.TileUtilities;
using tensor_ccr_stat = System.Collections.Generic.List<System.Tuple<Nncase.TIR.SegmentND, Nncase.Passes.Rules.K230.TensorStat>>;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class TileConv2dTranspose : RewriteRule<Pattern>
{
    private static int _count = -1;

    private readonly tensor_ccr_stat[] _weightRec = new tensor_ccr_stat[2];
    private readonly tensor_ccr_stat[] _ofmapRec = new tensor_ccr_stat[2];
    private readonly tensor_ccr_stat[] _g2LIfRec = new tensor_ccr_stat[2];
    private readonly tensor_ccr_stat[] _g2RWRec = new tensor_ccr_stat[2];
    private readonly tensor_ccr_stat[] _l2GOfRec = new tensor_ccr_stat[2];
    private readonly tensor_ccr_stat[] _g2RWSliceRec = new tensor_ccr_stat[2];
    private CcrHandler _ccrHandler = new();
    private WeightGroupHandler? _weightGroup;

    private GprHandler _gpr = new();
    private SsrHandler _ssr = new();

    private GlbSearchStrategy _strategy;
    private DataType? _inputType;
    private DataType? _outputType;
    private DataType? _weightType;

    // private DataType? _psumType;
    private Call? _conv;

    private int _icPerGroup;
    private int _ocPerGroup;
    private int _groupPerPass;

    private Call? _lif;
    private Call? _lw;
    private Call? _lact;
    private Call? _lwQarg;
    private Call? _sof;

    private GNNEShape? _inputShape;
    private GNNEShape? _outputShape;
    private GNNEShape? _weightsShape;
    private Padding? _paddingH;
    private Padding? _paddingW;
    private int _outputPaddingH;
    private int _outputPaddingW;
    private int _strideH;
    private int _strideW;
    private int _dilationH;
    private int _dilationW;
    private int _groups;
    private int _kernelH;
    private int _kernelW;

    private ImmParam _immParam;

    public override Pattern Pattern { get; } = FusionPattern.IsGNNEConv2DTransposeFusion();

    private PrimFunction GetReplace(Call call, Call ld, Call st)
    {
        _count++;

        InitParameters(call, ld, st);
        var tiledGlb = SearchGlbParameters();
        long[] inShape = ld[GNNELoad.Input].CheckedShape.ToValueArray();
        T.CreateBuffer(new(ld[GNNELoad.Input].CheckedDataType, inShape), MemoryLocation.Input, out var ddrIf);
        T.CreateBuffer(new(st.CheckedDataType, st.CheckedShape), MemoryLocation.Output, out var ddrOf);

        // T.AttachBuffer((TensorConst)((Call)call[GNNEConv2DTranspose.Weights])[GNNELoadW.Input], out var ddrW);
        byte[] oldWeights = ((TensorConst)((Call)call[GNNEConv2DTranspose.Weights])[GNNELoadW.Input]).Value.BytesBuffer.ToArray();
        byte[] newWeights = new byte[oldWeights.Length];
        Array.Copy(oldWeights, newWeights, newWeights.Length);
        T.AttachBuffer(Const.FromTensor(Tensor.FromBytes(DataTypes.UInt8, newWeights.ToArray(), new long[] { newWeights.Length })), out var ddrW);

        T.AttachBuffer((TensorConst)((Call)call[GNNEConv2DTranspose.WeightsBias])[GNNELoadW.Input], out var ddrWQarg);
        T.AttachBuffer((TensorConst)((Call)call[GNNEConv2DTranspose.Act])[GNNELoadW.Input], out var ddrAct);

        ItemRecStatusInit();
        BuildSchedule(tiledGlb, call, ld, st, ddrIf, ddrOf, ddrW, ddrWQarg, ddrAct, true, _weightGroup!);
        var actions = BuildSchedule(tiledGlb, call, ld, st, ddrIf, ddrOf, ddrW, ddrWQarg, ddrAct, false, _weightGroup!);

        var weights = ddrW.Const().Value.BytesBuffer;
        ArrangeWeights(_weightType!, _weightsShape!, weights, _weightGroup!);
        var actionConverter = new ActionToInstruct();
        return T.PrimFunc($"TileConv2dTranspose_{_count}", K230RtModule.Kind, new Var(ddrIf.CheckedType), new Var(ddrOf.CheckedType)).Body(
            actionConverter.Instructions(actions),
            I.END(GP_REGISTER.x0, 0))
        .Build();
    }

    private List<GnneAction> BuildSchedule(TiledGlb glb, Call conv, Call ld, Call st, TIR.Buffer ddrIf, TIR.Buffer ddrOf, TIR.Buffer ddrW, TIR.Buffer ddrWQarg, TIR.Buffer ddrAct, bool weightGroupOnly, WeightGroupHandler weightGroup)
    {
        List<GnneAction> actions = new();
        if (!weightGroupOnly)
        {
            _gpr = new(GNNEEnv.GprNum);
            _ssr = new(GNNEEnv.SsrNum);
            _ccrHandler = new CcrHandler();
        }

        GnneActionUpdater actionUpdater = new(actions, glb, _ccrHandler, _gpr, _ssr);

        if (!weightGroupOnly)
        {
            UpdateCcrRecStat();
        }

        // mmu conf
        if (!weightGroupOnly)
        {
            actionUpdater.UpdateMmuConf();
        }

        int ofPp = 0;
        int iPp = -1;
        int wPp = -1;
        int ofBufNum = GNNEEnv.NPingPongSplit;

        var outputBatchSeg = GetSegmentStartEndLength(0, glb.LastOutShape[0], _outputShape![0]);
        var outputRowSeg = GetSegmentStartEndLength(0, glb.LastOutShape[2], _immParam.ImmE);
        var outputColSeg = GetSegmentStartEndLength(0, glb.LastOutShape[3], _immParam.ImmF);
        List<Segment1D> outChanSeg = new();
        List<Segment1D> inChanSeg = new();
        if (glb.LastOutShape[1] >= _ocPerGroup)
        {
            outChanSeg = GetSegmentStartEndLength(0, glb.LastOutShape[1], _outputShape[1]);
            inChanSeg = GetSegmentStartEndLength(0, glb.GlbMap[ItemName.Ifmap].Dimensions[1], _inputShape![1]);
        }
        else
        {
            for (int g = 0; g < _groups; g++)
            {
                var outputChanSegTmp = GetSegmentStartEndLength(g * _ocPerGroup, glb.LastOutShape[1], (g + 1) * _ocPerGroup);
                var inputChanSegTmp = GetSegmentStartEndLength(g * _icPerGroup, glb.GlbMap[ItemName.Ifmap].Dimensions[1], (g + 1) * _icPerGroup);
                outChanSeg.AddRange(outputChanSegTmp);
                inChanSeg.AddRange(inputChanSegTmp);
            }
        }

        // weights bias
        if (!weightGroupOnly)
        {
            if (_weightType == DataTypes.UInt8 || _weightType == DataTypes.Int16)
            {
                actionUpdater.UpdateLoadWQarg(_lwQarg!, weightGroup, ddrWQarg);
            }
        }

        // act param
        if (!weightGroupOnly)
        {
            actionUpdater.UpdateLoadAct(_lact!, ddrAct);
        }

        Segment1D none = new(..0, new(0, 0));
        var pingPongIf = new List<SegmentND> { new(none, none, none, none), new(none, none, none, none) };
        var pingPongW = new List<SegmentND> { new(none, none, none, none), new(none, none, none, none) };
        int l1Pp = 0;
        foreach (var glbOutputBatch in outputBatchSeg)
        {
            foreach (var glbOutputRow in outputRowSeg)
            {
                var glbInputRow = GetVnocInputRowSegment(glbOutputRow.Start, glbOutputRow.Length, _immParam.PadTop, _immParam.PadBottom, _inputShape![2], _weightsShape![2], _strideH);
                foreach (var glbOutputColumn in outputColSeg)
                {
                    var glbInputColumn = GetVnocInputColSegment(glbOutputColumn.Start, glbOutputColumn.Length, _immParam.PadLeft, _immParam.PadRight, _inputShape[3], _weightsShape[3], _strideW);
                    foreach (var glbOutputChannel in outChanSeg)
                    {
                        SegmentND ofmap = new(glbOutputBatch, glbOutputChannel, glbOutputRow, glbOutputColumn);
                        int cs0 = glbOutputChannel.Start / _ocPerGroup * _icPerGroup;
                        int ce0 = ((glbOutputChannel.End - 1) / _ocPerGroup * _icPerGroup) + _icPerGroup;

                        foreach (var glbInputChannel in inChanSeg)
                        {
                            if (glbInputChannel.Start < cs0 || glbInputChannel.End > ce0)
                            {
                                continue;
                            }

                            int cs = glbInputChannel.Start % _icPerGroup;
                            int ce = Math.Min(cs + glbInputChannel.Length, _icPerGroup);
                            SegmentND weight = new(glbOutputChannel, new(cs..ce, new(0, 0)), new(.._weightsShape[2], new(0, 0)), new(.._weightsShape[3], new(0, 0)));

                            if (pingPongW[0] == weight)
                            {
                                wPp = 0;
                            }
                            else if (pingPongW[1] == weight)
                            {
                                wPp = 1;
                            }
                            else
                            {
                                wPp = (wPp + 1) % 2;

                                weightGroup.Current_aligned_offset_init();
                                if (weightGroupOnly)
                                {
                                    _weightRec[wPp].Add(new(weight, new(false, false)));
                                }

                                pingPongW[wPp] = weight;
                            }

                            SegmentND ifmap = new(glbOutputBatch, glbInputChannel, glbInputRow, glbInputColumn);

                            if (pingPongIf[0] == ifmap)
                            {
                                iPp = 0;
                            }
                            else if (pingPongIf[1] == ifmap)
                            {
                                iPp = 1;
                            }
                            else
                            {
                                iPp = (iPp + 1) % 2;
                                if (!weightGroupOnly)
                                {
                                    if (_g2LIfRec[iPp].Count > 0 && _g2LIfRec[iPp][0].Item1 == ifmap)
                                    {
                                        int ccrSetNum = _g2LIfRec[iPp][0].Item2 is { IsFirstSlice: true, IsLastSlice: true } ? 1 : 2;
                                        List<CcrSet> ccrSetIfmap = new() { new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ifmap, iPp)), ccrSetNum) };
                                        List<int> stridesD = new() { glb.GlbMap[ItemName.Ifmap].Dimensions[1], glb.GlbMap[ItemName.Ifmap].Dimensions[2], glb.GlbMap[ItemName.Ifmap].Dimensions[3] };
                                        actionUpdater.UpdateLoadIf(ifmap, _lif!, iPp, ddrIf, 0, stridesD, ItemName.Ifmap, ccrSetIfmap);
                                    }
                                }

                                pingPongIf[iPp] = ifmap;
                            }

                            // l1 schedule
                            BuildL1Schedule(actionUpdater, glb, ifmap, weight, ofmap, iPp, ofPp, wPp, weightGroupOnly, weightGroup, l1Pp, ddrW);
                        }

                        if (!weightGroupOnly)
                        {
                            SegmentND ofmapSt = new(ofmap[0], ofmap[1], ofmap[2], new(Math.Max(ofmap[3].Start, _immParam.FStart)..Math.Min(ofmap[3].End, _immParam.FEnd), new(0, 0)));
                            if (ofmap[2].Start == 0)
                            {
                                int start = _immParam.EStart;
                                int end = _immParam.EStart + (ofmapSt[2].End - _immParam.EStart);
                                ofmapSt[2] = new(start..end, new(0, 0));
                            }

                            if (ofmapSt[2].End == _immParam.ImmE)
                            {
                                int start = _immParam.EEnd - (_immParam.EEnd - ofmapSt[2].Start);
                                int end = _immParam.EEnd;
                                ofmapSt[2] = new Segment1D(start..end, new(0, 0));
                            }

                            SegmentND ofmapStDdr = new(ofmapSt[0], ofmapSt[1], new((ofmapSt[2].Start - _immParam.EStart)..(ofmapSt[2].End - _immParam.EStart), new(0, 0)), new((ofmapSt[3].Start - _immParam.FStart)..(ofmapSt[3].End - _immParam.FStart), new(0, 0)));

                            int offset = GetSliceOffsetInTensor(ofmap, ofmapSt) * glb.GlbMap[ItemName.Ofmap].DType.SizeInBytes;

                            int ofmapSetCnt = _ofmapRec[ofPp][0].Item2.IsLastSlice ? 0 : 1;
                            _ofmapRec[ofPp].RemoveAt(0);
                            List<CcrSet> ccrSetOfmap = new();
                            if (ofmapSetCnt > 0)
                            {
                                ccrSetOfmap.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.OfmapFake, ofPp)), 1));
                            }

                            List<CcrClr> ccrClrOfmap = new() { new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, ofPp))) };
                            actionUpdater.UpdateStoreT(ofmap, _sof!, ofPp, ddrOf, offset, ofmapStDdr, ccrSetOfmap, ccrClrOfmap);
                        }
                        else
                        {
                            _ofmapRec[ofPp].Add(new(ofmap, new(false, false)));
                        }

                        ofPp = (ofPp + 1) % ofBufNum;
                    }
                }
            }
        }

        // pe_usage(&pe_usage_param);
        Assert(_ccrHandler.CcrSanityCheck());

        return actions;
    }

    private List<List<Segment1D>> GetL1McSeg(SegmentND ifmap, SegmentND psum, int mInloop, int cInloop)
    {
        int group = Math.Max(ifmap[1].Length / _icPerGroup, 1);
        List<Segment1D> mSeg = new();
        List<Segment1D> cSeg = new();
        if (group >= 2 && _groupPerPass < 2)
        {
            for (int i = 0; i < group; i++)
            {
                var mSegTmp = GetSegmentStartEndLength(psum[1].Start + (i * _ocPerGroup), mInloop, psum[1].Start + ((i + 1) * _ocPerGroup));
                var cSegTmp = GetSegmentStartEndLength(ifmap[1].Start + (i * _icPerGroup), cInloop, ifmap[1].Start + ((i + 1) * _icPerGroup));
                mSeg.AddRange(mSegTmp);
                cSeg.AddRange(cSegTmp);
            }
        }
        else
        {
            mSeg = GetSegmentStartEndLength(psum[1].Start, mInloop, psum[1].End);
            cSeg = GetSegmentStartEndLength(ifmap[1].Start, cInloop, ifmap[1].End);
        }

        return new() { mSeg, cSeg };
    }

    private bool HandleL1Allocate(int h, int w, int e, int f, int psumPingPangSplit, int ifBytesPerElementGlb)
    {
        if (e * f > GNNEEnv.PsumL1ElePerChan / psumPingPangSplit)
        {
            return false;
        }

        return GNNEEnv.PuHeight * h * w * ifBytesPerElementGlb <= GNNEEnv.IfL1Size;
    }

    private List<int> L1Search(TiledGlb glb, SegmentND weight, SegmentND psum)
    {
        int GlbInputHeight(int e) => Math.Min((int)Math.Ceiling((decimal)(1.0f * (e + _weightsShape![2] - _strideH - 1) / _strideH)) + 1, _immParam.PaddedH - _immParam.PadTop - _immParam.PadBottom);
        int GlbInputWidth(int f) => Math.Min((int)Math.Ceiling((decimal)(1.0f * (f + _weightsShape![3] - _strideW - 1) / _strideW)) + 1, _immParam.PaddedW - _immParam.PadLeft - _immParam.PadRight);

        int c = Math.Min(GNNEEnv.PuHeight, _groupPerPass * _icPerGroup);
        int m = Math.Min(GNNEEnv.PuWidth, _groupPerPass * _ocPerGroup);
        int h = 1;
        int w = 1;
        int e = 1;
        int f = 1;

        h = GlbInputHeight(e);
        w = GlbInputWidth(f);

        int ifBytesPerElementGlb = GetBytesPerElement(_inputType!);
        bool ok = HandleL1Allocate(h, w, e, f, 1, ifBytesPerElementGlb);
        if (!ok)
        {
            throw new NotSupportedException("exceeds L1 size");
        }

        while (f < psum[3].Length && e < psum[2].Length)
        {
            if (f <= e)
            {
                int nextF = f + 1;
                int nextW = GlbInputWidth(nextF);

                ok = HandleL1Allocate(h, nextW, e, nextF, 1, ifBytesPerElementGlb);
                if (ok)
                {
                    f = nextF;
                    w = nextW;
                }
                else
                {
                    break;
                }
            }
            else
            {
                int nextE = e + 1;
                int nextH = GlbInputHeight(nextE);

                ok = HandleL1Allocate(nextH, w, nextE, f, 1, ifBytesPerElementGlb);
                if (ok)
                {
                    e = nextE;
                    h = nextH;
                }
                else
                {
                    break;
                }
            }
        }

        while (e < psum[2].Length)
        {
            int nextE = e + 1;
            int nextH = GlbInputHeight(nextE);

            ok = HandleL1Allocate(nextH, w, nextE, f, 1, ifBytesPerElementGlb);
            if (ok)
            {
                e = nextE;
                h = nextH;
            }
            else
            {
                break;
            }
        }

        while (f < psum[3].Length)
        {
            int nextF = f + 1;
            int nextW = GlbInputWidth(nextF);

            ok = HandleL1Allocate(h, nextW, e, nextF, 1, ifBytesPerElementGlb);
            if (ok)
            {
                f = nextF;
                w = nextW;
            }
            else
            {
                break;
            }
        }

        return new() { c, m, e, f };
    }

    private void BuildL1Schedule(GnneActionUpdater actionUpdater, TiledGlb glb, SegmentND ifmap, SegmentND weight, SegmentND psum, int iPp, int ofPp, int wPp, bool weightGroupOnly, WeightGroupHandler weightGroup, int l1Pp, TIR.Buffer ddrW)
    {
        var l1Tile = L1Search(glb, weight, psum);
        var eSeg = GetSegmentStartEndLength(psum[2].Start, l1Tile[2], psum[2].End);
        var fSeg = GetSegmentStartEndLength(psum[3].Start, l1Tile[3], psum[3].End);
        var nSeg = GetSegmentStartEndLength(psum[0].Start, 1, psum[0].End);
        var ret = GetL1McSeg(ifmap, psum, l1Tile[1], l1Tile[0]);
        var mSeg = ret[0];
        var cSeg = ret[1];

        int rLen = 1;
        int sLen = 1;
        foreach (var m in mSeg)
        {
            foreach (var n in nSeg)
            {
                foreach (var e in eSeg)
                {
                    var h = GetVnocInputRowSegment(e.Start, e.Length, _immParam.PadTop, _immParam.PadBottom, _inputShape![2], _weightsShape![2], _strideH);
                    foreach (var f in fSeg)
                    {
                        var w = GetVnocInputColSegment(f.Start, f.Length, _immParam.PadLeft, _immParam.PadRight, _inputShape[3], _weightsShape[3], _strideW);
                        SegmentND l2GOf = new(n, m, e, f);

                        int cs0 = m.Start / _ocPerGroup * _icPerGroup;
                        int ce0 = ((m.End - 1) / _ocPerGroup * _icPerGroup) + _icPerGroup;

                        bool ofmapCalStart = true; // to fix the problem r2l_psum != 0 but l2r_if == 0; which means the psum is calculation by the pad value

                        foreach (var c in cSeg)
                        {
                            if (c.Start < cs0 || c.End > ce0)
                            {
                                continue;
                            }

                            int cs = c.Start % _icPerGroup;
                            int ce = Math.Min(cs + c.Length, _icPerGroup);
                            Segment1D wcInloop = new(cs..ce, new(0, 0));

                            var cInloop = c;
                            var rInloopSeg = GetSegmentStartEndLength(0, rLen, _weightsShape[2]);
                            var inloopSeg = GetSegmentStartEndLength(0, sLen, _weightsShape[3]);
                            var nInloop = n;
                            var mInloop = m;

                            bool dmLoadIfSn = false; // to fix the problem the l2r_if == 0 at the first element of kernal, but l2r_if != 0 at the last first element of kernal;
                            SegmentND g2LIf = new(nInloop, cInloop, h, w);
                            if (g2LIf[0].Length > 0 && g2LIf[1].Length > 0 && g2LIf[2].Length > 0 && g2LIf[3].Length > 0)
                            {
                                dmLoadIfSn = true;
                                if (!weightGroupOnly)
                                {
                                    int ifClrCnt = _g2LIfRec[iPp][0].Item2.Stat_cnt();
                                    _g2LIfRec[iPp].RemoveAt(0);
                                    List<CcrClr> g2LIfCcrClr = new();
                                    if (ifClrCnt > 0)
                                    {
                                        g2LIfCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ifmap, iPp))));
                                    }

                                    actionUpdater.UpdateG2LIf(g2LIf, ifmap, _lif!, iPp, null!, g2LIfCcrClr);
                                }
                                else
                                {
                                    _g2LIfRec[iPp].Add(new(ifmap, new(false, false)));
                                }
                            }

                            foreach (var rInloop in rInloopSeg)
                            {
                                foreach (var inloop in inloopSeg)
                                {
                                    SegmentND l2RW = new(mInloop, wcInloop, rInloop, inloop);
                                    var ifmapH = GetVnocInputRowSegmentGivenRi(l2GOf[2].Start, l2GOf[2].Length, _immParam.PadTop, _immParam.PadBottom, _inputShape[2], l2RW[2].Start, _strideH);
                                    var ifmapW = GetVnocInputColumnSegmentGivenSi(l2GOf[3].Start, l2GOf[3].Length, _immParam.PadLeft, _immParam.PadRight, _inputShape[3], l2RW[3].Start, _strideW);
                                    SegmentND l2RIf = new(nInloop, cInloop, ifmapH, ifmapW);
                                    var ofmapH = GetVnocOutputRowSegmentGivenRi(ifmapH.Start, ifmapH.Length, l2RW[2].Start, _strideH, ifmapH.Padding.Before, ifmapH.Padding.After, _inputShape[2]);
                                    var ofmapW = GetVnocOutputRowSegmentGivenRi(ifmapW.Start, ifmapW.Length, l2RW[3].Start, _strideW, ifmapW.Padding.Before, ifmapW.Padding.After, _inputShape[3]);

                                    SegmentND r2LPsum = new(nInloop, mInloop, ofmapH, ofmapW);

                                    if (ofmapH.Length == 0 || ofmapW.Length == 0)
                                    {
                                        continue;
                                    }

                                    int posW = _weightType == DataTypes.Int16 ? 2 : 1;
                                    int posIf = _inputType == DataTypes.Int16 ? 2 : 1;
                                    for (int pW = 0; pW < posW; pW++)
                                    {
                                        for (int pIf = 0; pIf < posIf; pIf++)
                                        {
                                            if (!weightGroupOnly)
                                            {
                                                var weightRecStat = _weightRec[wPp][0];
                                                var g2RWRecStat = _g2RWRec[wPp][0];
                                                _g2RWRec[wPp].RemoveAt(0);
                                                if (g2RWRecStat.Item2.IsLastSlice)
                                                {
                                                    _weightRec[wPp].RemoveAt(0);
                                                }

                                                var g2RWSliceRecStat = _g2RWSliceRec[wPp][0];
                                                _g2RWSliceRec[wPp].RemoveAt(0);
                                                Assert(l2RW == g2RWSliceRecStat.Item1 && g2RWRecStat.Item1 == weightRecStat.Item1);
                                                if (g2RWSliceRecStat.Item2.IsFirstSlice)
                                                {
                                                    int wClrCnt = !weightRecStat.Item2.IsFirstSlice && g2RWRecStat.Item2.IsFirstSlice ? 1 : 0;
                                                    List<CcrClr> ccrClrW = new();
                                                    if (wClrCnt > 0)
                                                    {
                                                        ccrClrW.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.WeightFake, wPp))));
                                                    }

                                                    List<CcrSet> ccrSetW = new() { new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Weight, (wPp << 1) + (g2RWSliceRecStat.Item2.SliceIdx & 0x1))), 1) };
                                                    actionUpdater.UpdateLoadW(l2RW, _lw!, weightGroup, wPp, ddrW, ccrSetW, ccrClrW, 0, false, 0, ItemName.Weight, pW);
                                                }

                                                int g2RWCcrSetNum = 0;
                                                if (g2RWRecStat.Item2.IsLastSlice && _weightRec[wPp].Count > 0)
                                                {
                                                    g2RWCcrSetNum = 1;
                                                }

                                                List<CcrSet> g2RWCcrSet = new();
                                                if (g2RWCcrSetNum > 0)
                                                {
                                                    g2RWCcrSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.WeightFake, wPp)), 1));
                                                }

                                                List<CcrClr> g2RWCcrClr = new();
                                                if (g2RWSliceRecStat.Item2.IsFirstSlice)
                                                {
                                                    g2RWCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Weight, (wPp << 1) + (g2RWSliceRecStat.Item2.SliceIdx & 0x1)))));
                                                }

                                                actionUpdater.UpdateG2RW(l2RW, weightGroup, _ocPerGroup, _lw!, wPp, pW, g2RWCcrSet, g2RWCcrClr);
                                            }
                                            else
                                            {
                                                weightGroup.UpdateWeightGroup(l2RW);
                                                _g2RWRec[wPp].Add(new(weight, new(false, false)));
                                                _g2RWSliceRec[wPp].Add(new(l2RW, new(false, false, -1)));
                                            }

                                            if (!weightGroupOnly)
                                            {
                                                actionUpdater.UpdateL2RIf(l2RIf, g2LIf, _strideH, _strideW, _icPerGroup, _lif!, 0, pIf, ((TensorConst)_conv![GNNEConv2DTranspose.DeqBias]).Value.ToScalar<int>());
                                            }

                                            bool releaseIf = false;
                                            if (inloop == inloopSeg[^1] && rInloop == rInloopSeg[^1] && pW == posW - 1 && pIf == posIf - 1 && dmLoadIfSn)
                                            {
                                                releaseIf = true;
                                                dmLoadIfSn = false;
                                            }

                                            bool loopStart = false;
                                            if (ofmapCalStart && pW == 0 && pIf == 0)
                                            {
                                                loopStart = true;
                                                ofmapCalStart = false;
                                            }

                                            bool loopEnd = wcInloop.End == _weightsShape[1] && inloop.End == weight[3].End && rInloop.End == weight[2].End && pW == posW - 1 && pIf == posIf - 1;

                                            if (!weightGroupOnly)
                                            {
                                                int l2GOfCcrSetNum = 0;
                                                int l2GOfCcrClrNum = 0;
                                                if (loopEnd)
                                                {
                                                    var ofCcrStat = _l2GOfRec[ofPp][0];
                                                    _l2GOfRec[ofPp].RemoveAt(0);
                                                    if (ofCcrStat.Item2.IsFirstSlice && !_ofmapRec[ofPp][0].Item2.IsFirstSlice)
                                                    {
                                                        l2GOfCcrClrNum = 1;
                                                    }

                                                    if (ofCcrStat.Item2.IsLastSlice)
                                                    {
                                                        l2GOfCcrSetNum = 1;
                                                    }
                                                }

                                                List<CcrSet> l2GOfCcrSet = new();
                                                if (l2GOfCcrSetNum > 0)
                                                {
                                                    l2GOfCcrSet.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Ofmap, ofPp)), 1));
                                                }

                                                List<CcrClr> l2GOfCcrClr = new();
                                                if (l2GOfCcrClrNum > 0)
                                                {
                                                    l2GOfCcrClr.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.OfmapFake, ofPp))));
                                                }

                                                int shift = ((TensorConst)_conv![GNNEConv2DTranspose.ShiftBits]).Value.ToScalar<int>();
                                                var destTarget = ACT0_OUTPUT_DEST.dm;
                                                actionUpdater.UpdateR2LPsum(shift, r2LPsum, l2GOf, psum, ofPp, l1Pp, destTarget, releaseIf, Math.Max(pW, pIf), TcuComputeMode.TransposeConv2d, loopStart, loopEnd, _strideH, _strideW, _ocPerGroup, _inputType!, _weightType!, _outputType!, _lact!.CheckedDataType, l2GOfCcrSet, l2GOfCcrClr);
                                            }
                                            else if (loopEnd)
                                            {
                                                _l2GOfRec[ofPp].Add(new(psum, new(false, false)));
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private void ArrangeWeights(DataType weightsType, GNNEShape weightsShape, Span<byte> oldWeights, WeightGroupHandler weightGroup)
    {
        int bytesPerElement = GetBytesPerElement(weightsType);
        var weightGroupSlice = weightGroup.WeightGroupSlice();
        byte[] newWeights = new byte[oldWeights.Length];

        int offset = 0;
        int j = 0;
        foreach (var slice in weightGroupSlice)
        {
            Assert(offset == weightGroup.WeightGroupOffset(slice) * bytesPerElement);
            for (int b = 0; b < bytesPerElement; b++)
            {
                for (int m = 0; m < slice[0].Length; m++)
                {
                    for (int r = 0; r < slice[2].Length; r++)
                    {
                        for (int s = 0; s < slice[3].Length; s++)
                        {
                            for (int c = 0; c < slice[1].Length; c++)
                            {
                                int srcAddr = (((((((m + slice[0].Start) * weightsShape[1]) + c + slice[1].Start) * weightsShape[2]) + r + slice[2].Start) * weightsShape[3]) + s + slice[3].Start) * bytesPerElement;
                                newWeights[j++] = oldWeights[srcAddr + b];
                                offset++;
                            }
                        }
                    }
                }
            }
        }

        newWeights.CopyTo(oldWeights);
    }

    private void UpdateCcrRecStat()
    {
        // g2l_if_rec status decide
        foreach (var t in _g2LIfRec)
        {
            for (int cnt = 0; cnt < t.Count; cnt++)
            {
                if (cnt == 0)
                {
                    t[cnt].Item2.IsFirstSlice = true;
                }

                if (cnt == t.Count - 1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                }

                if (cnt < t.Count - 1 && t[cnt].Item1 != t[cnt + 1].Item1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                    t[cnt + 1].Item2.IsFirstSlice = true;
                }
            }
        }

        // g2r_w_rec status decide
        foreach (var t in _g2RWRec)
        {
            for (int cnt = 0; cnt < t.Count; cnt++)
            {
                if (cnt == 0)
                {
                    t[cnt].Item2.IsFirstSlice = true;
                }

                if (cnt == t.Count - 1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                }

                if (cnt < t.Count - 1 && t[cnt].Item1 != t[cnt + 1].Item1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                    t[cnt + 1].Item2.IsFirstSlice = true;
                }
            }
        }

        // l2g_of_rec status decide
        foreach (var t in _l2GOfRec)
        {
            for (int cnt = 0; cnt < t.Count; cnt++)
            {
                if (cnt == 0)
                {
                    t[cnt].Item2.IsFirstSlice = true;
                }

                if (cnt == t.Count - 1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                }

                if (cnt < t.Count - 1 && t[cnt].Item1 != t[cnt + 1].Item1)
                {
                    t[cnt].Item2.IsLastSlice = true;
                    t[cnt + 1].Item2.IsFirstSlice = true;
                }
            }
        }

        // weight_rec status decide
        foreach (var t in _weightRec)
        {
            if (t.Count > 0)
            {
                t[0].Item2.IsFirstSlice = true;
                t[^1].Item2.IsLastSlice = true;
            }
        }

        // ofmap_rec status decide
        foreach (var t in _ofmapRec)
        {
            if (t.Count > 0)
            {
                t[0].Item2.IsFirstSlice = true;
                t[^1].Item2.IsLastSlice = true;
            }
        }

        // g2r_w_slice_rec status decide
        for (int pp = 0; pp < _g2RWSliceRec.Length; pp++)
        {
            List<SegmentND> uniqueWSlices = new();
            List<SegmentND> wSlices = new();
            tensor_ccr_stat wSlicesRecPart = new();
            for (int g2RWSliceIdx = 0; g2RWSliceIdx < _g2RWRec[pp].Count; g2RWSliceIdx++)
            {
                if (_g2RWRec[pp][g2RWSliceIdx].Item2.IsFirstSlice)
                {
                    uniqueWSlices.Clear();
                    wSlices.Clear();
                    wSlicesRecPart.Clear();
                }

                if (!uniqueWSlices.Contains(_g2RWSliceRec[pp][g2RWSliceIdx].Item1))
                {
                    uniqueWSlices.Add(_g2RWSliceRec[pp][g2RWSliceIdx].Item1);
                }

                wSlices.Add(_g2RWSliceRec[pp][g2RWSliceIdx].Item1);
                wSlicesRecPart.Add(_g2RWSliceRec[pp][g2RWSliceIdx]);
                if (_g2RWRec[pp][g2RWSliceIdx].Item2.IsLastSlice)
                {
                    foreach (var slice in uniqueWSlices)
                    {
                        wSlicesRecPart[wSlices.FindIndex(x => x == slice)].Item2.IsFirstSlice = true;
                        wSlicesRecPart[wSlices.FindLastIndex(rWSliceIdx => rWSliceIdx == slice)].Item2.IsLastSlice = true;
                    }

                    for (int cnt = 0; cnt < wSlices.Count; cnt++)
                    {
                        wSlicesRecPart[cnt].Item2.SliceIdx = uniqueWSlices.FindIndex(x => x == wSlices[cnt]);
                        _g2RWSliceRec[pp][g2RWSliceIdx - wSlices.Count + 1 + cnt] = wSlicesRecPart[cnt];
                    }
                }
            }

            if (_weightType == DataTypes.Int16)
            {
                for (int cnt = 1; cnt < _g2RWSliceRec[pp].Count; cnt++)
                {
                    if (_g2RWSliceRec[pp][_g2RWSliceRec[pp].Count - cnt - 1].Item2.IsFirstSlice)
                    {
                        _g2RWSliceRec[pp][_g2RWSliceRec[pp].Count - cnt].Item2.IsFirstSlice = true;
                    }

                    if (_g2RWSliceRec[pp][cnt].Item2.IsLastSlice)
                    {
                        _g2RWSliceRec[pp][cnt - 1].Item2.IsLastSlice = true;
                    }
                }

                for (int cnt = 0; cnt < _g2RWSliceRec[pp].Count; cnt++)
                {
                    _g2RWSliceRec[pp][cnt].Item2.SliceIdx = (_g2RWSliceRec[pp][cnt].Item2.SliceIdx * 2) + (cnt & 0x1);
                }
            }
        }
    }

    private void ItemRecStatusInit()
    {
        for (int i = 0; i < _weightRec.Length; i++)
        {
            if (_weightRec[i] is not null)
            {
                _weightRec[i].Clear();
            }
            else
            {
                _weightRec[i] = new tensor_ccr_stat();
            }

            if (_ofmapRec[i] is not null)
            {
                _ofmapRec[i].Clear();
            }
            else
            {
                _ofmapRec[i] = new tensor_ccr_stat();
            }

            if (_g2LIfRec[i] is not null)
            {
                _g2LIfRec[i].Clear();
            }
            else
            {
                _g2LIfRec[i] = new tensor_ccr_stat();
            }

            if (_g2RWRec[i] is not null)
            {
                _g2RWRec[i].Clear();
            }
            else
            {
                _g2RWRec[i] = new tensor_ccr_stat();
            }

            if (_l2GOfRec[i] is not null)
            {
                _l2GOfRec[i].Clear();
            }
            else
            {
                _l2GOfRec[i] = new tensor_ccr_stat();
            }

            if (_g2RWSliceRec[i] is not null)
            {
                _g2RWSliceRec[i].Clear();
            }
            else
            {
                _g2RWSliceRec[i] = new tensor_ccr_stat();
            }
        }
    }

    private TileConv2dTransposeGlb SearchGlbParameters()
    {
        int GetGlbInputHeight(int e) => Math.Min((int)Math.Ceiling((decimal)(1.0f * (e + _weightsShape![2] - _strideH - 1) / _strideH)) + 1, _immParam.PaddedH - _immParam.PadTop - _immParam.PadBottom);
        int GlbInputWidth(int f) => Math.Min((int)Math.Ceiling((decimal)(1.0f * (f + _weightsShape![3] - _strideW - 1) / _strideW)) + 1, _immParam.PaddedW - _immParam.PadLeft - _immParam.PadRight);

        int n = 1;
        int c = 1;
        int r = _weightsShape![2];
        int s = _weightsShape[3];

        int e = 1;
        int f = Math.Min(GNNEEnv.PuWidth, _immParam.ImmF);
        if (f < GNNEEnv.PuWidth)
        {
            e = Math.Min((int)Math.Ceiling(1.0 * GNNEEnv.PuWidth / f), _immParam.ImmE);
        }

        int h = GetGlbInputHeight(e);
        int w = GlbInputWidth(f);
        int m = 1;

        AllocateResult allocation;

        // pre allocate
        while (c < _icPerGroup)
        {
            allocation = HandleAllocate(n, c + 1, h, w, r, s, m, e, f);
            if (allocation.IsOk)
            {
                c += 1;
            }
            else
            {
                break;
            }
        }

        while (m < GNNEEnv.PuWidth && m < _ocPerGroup)
        {
            allocation = HandleAllocate(n, c, h, w, r, s, m + 1, e, f);
            if (allocation.IsOk)
            {
                m += 1;
            }
            else
            {
                break;
            }
        }

        // two strategies foreach now
        if (_strategy == GlbSearchStrategy.IfFirst)
        {
            if (_groupPerPass == 1)
            {
                while (m < _outputShape![1])
                {
                    if (r == _weightsShape[2] && s == _weightsShape[3] && c >= _weightsShape[1] && m >= _outputShape[1] / 2)
                    {
                        break;
                    }

                    int nextM = m + 1;
                    int nextC = c;
                    if (m >= _ocPerGroup)
                    {
                        nextM = m + _ocPerGroup;
                        nextC = c + _icPerGroup;
                    }

                    allocation = HandleAllocate(n, nextC, h, w, r, s, nextM, e, f);
                    if (allocation.IsOk)
                    {
                        m = nextM;
                        c = nextC;
                    }
                    else
                    {
                        break;
                    }
                }
            }
            else
            {
                while (m < _outputShape![1])
                {
                    if (r == _weightsShape[2] && s == _weightsShape[3] && c >= _weightsShape[1] && m >= _weightsShape[0] / 2)
                    {
                        break;
                    }

                    int nextM = Math.Min(m + (_groupPerPass * _ocPerGroup), _outputShape[1]);
                    int nextC = Math.Min(c + (_groupPerPass * _icPerGroup), _inputShape![1]);
                    allocation = HandleAllocate(n, nextC, h, w, r, s, nextM, e, f);
                    if (allocation.IsOk)
                    {
                        m = nextM;
                        c = nextC;
                    }
                    else
                    {
                        break;
                    }
                }
            }

            while (e < _immParam.ImmE)
            {
                int nextE = e + 1;
                int nextH = GetGlbInputHeight(nextE);

                allocation = HandleAllocate(n, c, nextH, w, r, s, m, nextE, f);
                if (allocation.IsOk)
                {
                    e = nextE;
                    h = nextH;
                }
                else
                {
                    break;
                }
            }

            while (f < _immParam.ImmF)
            {
                int nextF = f + 1;
                int nextW = GlbInputWidth(nextF);

                allocation = HandleAllocate(n, c, h, nextW, r, s, m, e, nextF);
                if (allocation.IsOk)
                {
                    f = nextF;
                    w = nextW;
                }
                else
                {
                    break;
                }
            }

            while (n < _outputShape[0])
            {
                allocation = HandleAllocate(n + 1, c, h, w, r, s, m, e, f);
                if (allocation.IsOk)
                {
                    n += 1;
                }
                else
                {
                    break;
                }
            }
        }
        else if (_strategy == GlbSearchStrategy.WFirst)
        {
            while (e < _immParam.ImmE)
            {
                int nextE = e + 1;
                int nextH = GetGlbInputHeight(nextE);

                allocation = HandleAllocate(n, c, nextH, w, r, s, m, nextE, f);
                if (allocation.IsOk)
                {
                    e = nextE;
                    h = nextH;
                }
                else
                {
                    break;
                }
            }

            while (f < _immParam.ImmF)
            {
                int nextF = f + 1;
                int nextW = GlbInputWidth(nextF);

                allocation = HandleAllocate(n, c, h, nextW, r, s, m, e, nextF);
                if (allocation.IsOk)
                {
                    f = nextF;
                    w = nextW;
                }
                else
                {
                    break;
                }
            }

            while (n < _outputShape![0])
            {
                if (c == _inputShape![1] && h == _immParam.ImmE && w == _immParam.ImmF && n >= _outputShape[0] / 2)
                {
                    break;
                }

                allocation = HandleAllocate(n + 1, c, h, w, r, s, m, e, f);
                if (allocation.IsOk)
                {
                    n += 1;
                }
                else
                {
                    break;
                }
            }

            if (_groupPerPass == 1)
            {
                while (m < _outputShape[1])
                {
                    int nextM = m + 1;
                    int nextC = c;
                    if (m >= _ocPerGroup)
                    {
                        nextM = m + _ocPerGroup;
                        nextC = c + _icPerGroup;
                    }

                    allocation = HandleAllocate(n, nextC, h, w, r, s, nextM, e, f);
                    if (allocation.IsOk)
                    {
                        m = nextM;
                        c = nextC;
                    }
                    else
                    {
                        break;
                    }
                }
            }
            else
            {
                while (m < _outputShape[1])
                {
                    int nextM = Math.Min(m + (_groupPerPass * _ocPerGroup), _outputShape[1]);
                    int nextC = Math.Min(c + (_groupPerPass * _icPerGroup), _inputShape![1]);
                    allocation = HandleAllocate(n, nextC, h, w, r, s, nextM, e, f);
                    if (allocation.IsOk)
                    {
                        m = nextM;
                        c = nextC;
                    }
                    else
                    {
                        break;
                    }
                }
            }
        }

        // m % GNNEEnv.pu_width() == 0
        if (m % GNNEEnv.PuWidth != 0 && m > GNNEEnv.PuWidth && m <= _ocPerGroup)
        {
            m = m / GNNEEnv.PuWidth * GNNEEnv.PuWidth;
        }

        allocation = HandleAllocate(n, c, h, w, r, s, m, e, f, true);
        Assert(allocation.IsOk);

        var lastOutShape = new GNNEShape(n, m, e, f);
        var glb = new TileConv2dTransposeGlb(allocation.GlbMap, allocation.Items, lastOutShape.Dims, GNNEEnv.NPingPongSplit, allocation.GlbMap[ItemName.Ifmap], allocation.GlbMap[ItemName.Ofmap]);

        return glb;
    }

    private AllocateResult HandleAllocate(int n, int c, int h, int w, int r, int s, int m, int e, int f, bool isFinal = false)
    {
        var glbUsage = new List<float> { 0, 0 };
        var boxes = GetBoxes(n, c, h, w, r, s, m, e, f, glbUsage);

        var bp = new BoxPacker(16) { Boxes = boxes.Boxes };

        var allocation = SpaceSearcher.TryAllocate(bp);
        allocation.GlbMap = boxes.GlbMap;

#if DEBUG
        if (isFinal && allocation.IsOk)
        {
            bp.DisplayFinalAllocation();
            Console.WriteLine("fusion type: conv2d/conv2d_pdp/conv2d_dw/conv2d_act1");
            Console.WriteLine($"GLB usage(tensor, mmu): {glbUsage[0]}\t{glbUsage[1]}");
            Console.WriteLine("--------------------------");
        }
#endif

        if (allocation.IsOk)
        {
            foreach (var map in boxes.GlbMap)
            {
                if (allocation.Items.ContainsKey(map.Key))
                {
                    map.Value.Mmu = allocation.Items[map.Key];
                }
            }
        }

        return allocation;
    }

    private void InitParameters(Call convNode, Call ld, Call st)
    {
        _conv = convNode;

        _lif = ld;
        _lw = (Call)_conv[GNNEConv2DTranspose.Weights];
        _lact = (Call)_conv[GNNEConv2DTranspose.Act];
        _lwQarg = (Call)_conv[GNNEConv2DTranspose.WeightsBias];
        _sof = st;

        _inputShape = new GNNEShape((int)_lif.CheckedShape[0].FixedValue, (int)_lif.CheckedShape[1].FixedValue, (int)_lif.CheckedShape[2].FixedValue, (int)_lif.CheckedShape[3].FixedValue);
        _outputShape = new GNNEShape((int)_conv.CheckedShape[0].FixedValue, (int)_conv.CheckedShape[1].FixedValue, (int)_conv.CheckedShape[2].FixedValue, (int)_conv.CheckedShape[3].FixedValue);
        _groups = ((TensorConst)_conv[GNNEConv2DTranspose.Groups]).Value.ToScalar<int>();
        _weightsShape = new GNNEShape((int)_lw.CheckedShape[0].FixedValue * _groups, (int)_lw.CheckedShape[1].FixedValue / _groups, (int)_lw.CheckedShape[2].FixedValue, (int)_lw.CheckedShape[3].FixedValue);
        int[] paddings = ((TensorConst)_conv[GNNEConv2DTranspose.Padding]).Value.ToArray<int>();
        _paddingH = new Padding(paddings[0], paddings[1]);
        _paddingW = new Padding(paddings[2], paddings[3]);
        _outputPaddingH = ((TensorConst)_conv[GNNEConv2DTranspose.OutputPadding]).Value.ToArray<int>()[0];
        _outputPaddingW = ((TensorConst)_conv[GNNEConv2DTranspose.OutputPadding]).Value.ToArray<int>()[1];
        _strideH = ((TensorConst)_conv[GNNEConv2DTranspose.Stride]).Value.ToArray<int>()[0];
        _strideW = ((TensorConst)_conv[GNNEConv2DTranspose.Stride]).Value.ToArray<int>()[1];
        _dilationH = ((TensorConst)_conv[GNNEConv2DTranspose.Dilation]).Value.ToArray<int>()[0];
        _dilationW = ((TensorConst)_conv[GNNEConv2DTranspose.Dilation]).Value.ToArray<int>()[1];
        _kernelH = (int)_lw.CheckedShape[2].FixedValue;
        _kernelW = (int)_lw.CheckedShape[3].FixedValue;

        _icPerGroup = _inputShape[1] / _groups;
        _ocPerGroup = _outputShape[1] / _groups;
        _groupPerPass = Math.Min(Math.Max(Math.Min(GNNEEnv.PuHeight / _icPerGroup, GNNEEnv.PuWidth / _ocPerGroup), 1), _groups);
        _strategy = DetermineSearchStrategy();

        _inputType = _lif.CheckedDataType;
        _weightType = _lw.CheckedDataType;
        _weightGroup = new WeightGroupHandler(_weightType, _weightType);
        _outputType = _conv.CheckedDataType;

        (_immParam.PaddedH, _immParam.ImmE) = GetConvTPadHImmE(_inputShape[2], _strideH, _kernelH, _outputShape[2]);
        (_immParam.PaddedW, _immParam.ImmF) = GetConvTPadWImmF(_inputShape[3], _strideW, _kernelW, _outputShape[3]);
        var padH = CalPadTopBottom(_immParam.PaddedH, _inputShape[2]);
        _immParam.PadTop = padH.Before;
        _immParam.PadBottom = padH.After;
        if (_paddingH.Before > 0)
        {
            _immParam.EStart = _paddingH.Before;
            _immParam.EEnd = _immParam.EStart + _outputShape[2];
        }

        var padW = CalPadLeftRight(_immParam.PaddedW, _inputShape[3]);
        _immParam.PadLeft = padW.Before;
        _immParam.PadRight = padW.After;
        if (_paddingW.Before > 0)
        {
            _immParam.FStart = _paddingW.Before;
            _immParam.FEnd = _immParam.FStart + _outputShape[3];
        }

        (_immParam.EStart, _immParam.EEnd) = GetConvTOutputStartEnd(_inputShape[2], _immParam.PaddedH, _immParam.ImmE, _outputShape[2], _paddingH, _outputPaddingH);
        (_immParam.FStart, _immParam.FEnd) = GetConvTOutputStartEnd(_inputShape[3], _immParam.PaddedW, _immParam.ImmF, _outputShape[3], _paddingW, _outputPaddingW);
    }

    private GlbSearchStrategy DetermineSearchStrategy()
    {
        if (_inputShape?.Size > _weightsShape?.Size)
        {
            return GlbSearchStrategy.IfFirst;
        }

        return GlbSearchStrategy.WFirst;
    }

    private AllocateResult GetBoxes(int n, int c, int h, int w, int r, int s, int m, int e, int f, List<float> glbUsage)
    {
        List<BoxOnGlb> boxes = new();
        Dictionary<ItemName, TensorOnGlb> glbMap = new();
        List<float> glbAllocSize = new() { 0, 0 };

        int ifNPingPongSplit = GNNEEnv.NPingPongSplit;
        if (n == _inputShape![0] && c == _inputShape[1] && h == _inputShape[2] && w == _inputShape[3])
        {
            ifNPingPongSplit = 1;
        }

        TensorOnGlb ifGlb = new(new[] { n, c, h, w }, _inputType!, 0);
        int ifmapSize = ifGlb.AllocatedBytes * ifNPingPongSplit;
        glbAllocSize[0] += ifmapSize;
        ifmapSize = GetAlignedNum(ifmapSize, GNNEEnv.IfmapBankWidth * GNNEEnv.GlbBankWidth);
        glbAllocSize[1] += ifmapSize;

        int wC = c;
        if (wC > _icPerGroup)
        {
            wC = _icPerGroup;
        }

        TensorOnGlb wGlb = new(new[] { m, wC, r, s }, _weightType!, 0);
        int wSize = SpaceSearcher.GetWeightSize(r, s, wC, m, GetBytesPerElement(_weightType!)) * GNNEEnv.NPingPongSplit;
        wGlb.AllocatedBytes = wSize / GNNEEnv.NPingPongSplit;
        glbAllocSize[0] += wSize;
        wSize = GetAlignedNum(wSize, GNNEEnv.WBankWidth * GNNEEnv.GlbBankWidth);
        glbAllocSize[1] += wSize;

        TensorOnGlb ofGlb = new(new[] { n, m, e, f }, _outputType!, 0);
        int ofmapSize = ofGlb.AllocatedBytes * GNNEEnv.NPingPongSplit;
        glbAllocSize[0] += ofmapSize;
        ofmapSize = GetAlignedNum(ofmapSize, GNNEEnv.OfmapBankWidth * GNNEEnv.GlbBankWidth);
        glbAllocSize[1] += ofmapSize;

        TensorOnGlb actGlb = new(new[] { 1, 1, _outputShape![1], GNNEEnv.ActNumPerChan }, _lact!.CheckedDataType, 0);
        int actSize = SpaceSearcher.GetActSize(_outputShape[1], GNNEEnv.ActNumPerChan, GetBytesPerElement(_lact.CheckedDataType));
        glbAllocSize[0] += actSize;
        actSize = GetAlignedNum(actSize, GNNEEnv.ActBankWidth * GNNEEnv.GlbBankWidth);
        glbAllocSize[1] += actSize;

        int basementSize = SpaceSearcher.GetBasementSize();
        basementSize = GetAlignedNum(basementSize, GNNEEnv.GlbWidth * GNNEEnv.GlbBankWidth);

        boxes.Add(new(new[] { GNNEEnv.GlbWidth, basementSize / GNNEEnv.GlbWidth / GNNEEnv.GlbBankWidth }, ItemName.Basement));
        boxes.Add(new(new[] { GNNEEnv.IfmapBankWidth, ifmapSize / GNNEEnv.IfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ifmap));
        boxes.Add(new(new[] { GNNEEnv.WBankWidth, wSize / GNNEEnv.WBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Weight));
        boxes.Add(new(new[] { GNNEEnv.OfmapBankWidth, ofmapSize / GNNEEnv.OfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ofmap));
        boxes.Add(new(new[] { GNNEEnv.ActBankWidth, actSize / GNNEEnv.ActBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Act));

        glbMap.Add(ItemName.Ifmap, ifGlb);
        glbMap.Add(ItemName.Weight, wGlb);
        glbMap.Add(ItemName.Ofmap, ofGlb);
        glbMap.Add(ItemName.Act, actGlb);

        // only uint8 and int16 have bias
        if (_weightType == DataTypes.UInt8 || _weightType == DataTypes.Int16)
        {
            TensorOnGlb wQargGlb = new(new[] { 1, 1, 1, _outputShape[1] }, _lwQarg!.CheckedDataType, 0);
            int wQargSize = SpaceSearcher.GetWQargSize(_outputShape[1], GetBytesPerElement(_lwQarg.CheckedDataType));

            bool isDepthwise = _inputShape[1] == _outputShape[1] && _outputShape[1] == _groups && _groups != 1;
            if (isDepthwise)
            {
                wQargSize = SpaceSearcher.GetWQargSize(_outputShape[1] * GNNEEnv.PuWidth, GetBytesPerElement(_lwQarg.CheckedDataType));
            }

            glbAllocSize[0] += wQargSize;
            wQargSize = GetAlignedNum(wQargSize, GNNEEnv.WQargBankWidth * GNNEEnv.GlbBankWidth);
            glbAllocSize[1] += wQargSize;
            boxes.Add(new(new[] { GNNEEnv.WQargBankWidth, wQargSize / GNNEEnv.WQargBankWidth / GNNEEnv.GlbBankWidth }, ItemName.WQarg));
            glbMap.Add(ItemName.WQarg, wQargGlb);
        }

        glbUsage[0] = glbAllocSize[0] / GNNEEnv.GlbSize;
        glbUsage[1] = glbAllocSize[1] / GNNEEnv.GlbSize;

        AllocateResult ret = new()
        {
            Boxes = boxes,
            GlbMap = glbMap,
        };
        return ret;
    }

    private struct ImmParam
    {
        public int PadTop;
        public int PadBottom;
        public int PadLeft;
        public int PadRight;
        public int PaddedH;
        public int ImmE;
        public int EStart;
        public int EEnd;
        public int PaddedW;
        public int ImmF;
        public int FStart;
        public int FEnd;
    }
}

internal class TileConv2dTransposeGlb : TiledGlb
{
    private TensorOnGlb _ifGlb;
    private TensorOnGlb _ofGlb;

    public TileConv2dTransposeGlb(Dictionary<ItemName, TensorOnGlb> glbMap, Dictionary<ItemName, MmuItem> items, int[] lastOutShape, int nPingPongSplit, TensorOnGlb ifGlb, TensorOnGlb ofGlb)
        : base(glbMap, items, lastOutShape, nPingPongSplit)
    {
        _ifGlb = ifGlb;
        _ofGlb = ofGlb;
    }
}
