// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using Nncase.Runtime.K230;
using Nncase.Schedule;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using static Nncase.Passes.Rules.K230.TileUtilities;
using tensor_ccr_stat = System.Collections.Generic.List<System.Tuple<Nncase.TIR.SegmentND, Nncase.Passes.Rules.K230.TensorStat>>;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class TileResize : RewriteRule<Pattern>
{
    private static int _count = -1;
    private readonly tensor_ccr_stat[] _r2GOfRec = new tensor_ccr_stat[2];
    private readonly tensor_ccr_stat[] _ofmapRec = new tensor_ccr_stat[2];

    /// <inheritdoc/>
    public override Pattern Pattern { get; } = FusionPattern.IsAi2DResizeFusion();

    private PrimFunction GetReplace(Call call, Call ld, Call st)
    {
        Ai2dResize resize = (Ai2dResize)call.Target;
        _count++;
        ItemRecStatusInit();
        var tiledGlb = SearchGlbParameters(ld, st);
        long[] inShape = ld[GNNELoad.Input].CheckedShape.ToValueArray();
        T.CreateBuffer(new(ld[GNNELoad.Input].CheckedDataType, inShape), MemoryLocation.Input, out var ddrIf);
        T.CreateBuffer(new(st.CheckedDataType, st.CheckedShape), MemoryLocation.Output, out var ddrOf);
        BuildSchedule(tiledGlb, resize, ld, st, ddrIf, ddrOf, true);
        var actions = BuildSchedule(tiledGlb, resize, ld, st, ddrIf, ddrOf, false);
        var actionConverter = new ActionToInstruct();
        return T.PrimFunc($"TileResize_{_count}", K230RtModule.Kind, new Var(ddrIf.CheckedType), new Var(ddrOf.CheckedType)).Body(
            actionConverter.Instructions(actions),
            I.END(GP_REGISTER.x0, 0))
        .Build();
    }

    private List<GnneAction> BuildSchedule(TiledGlb glb, Ai2dResize resize, Call ld, Call st, TIR.Buffer ddrIf, TIR.Buffer ddrOf, bool recStat)
    {
        List<GnneAction> actions = new();
        var gpr = new GprHandler(GNNEEnv.GprNum);
        var ssr = new SsrHandler(GNNEEnv.SsrNum);
        var ccrHandler = new CcrHandler();
        GnneActionUpdater actionUpdater = new(actions, glb, ccrHandler, gpr, ssr);
        Assert(!(resize.AlignCorners && resize.HalfPixelCenters));

        // ccr set/clr record
        if (!recStat)
        {
            for (var pp = 0; pp < _r2GOfRec.Length; pp++)
            {
                for (var cnt = 0; cnt < _r2GOfRec[pp].Count; cnt++)
                {
                    if (cnt == 0)
                    {
                        _r2GOfRec[pp][cnt].Item2.IsFirstSlice = true;
                    }

                    if (cnt == _r2GOfRec[pp].Count - 1)
                    {
                        _r2GOfRec[pp][cnt].Item2.IsLastSlice = true;
                    }

                    if (cnt < _r2GOfRec[pp].Count - 1 && _r2GOfRec[pp][cnt].Item1 != _r2GOfRec[pp][cnt + 1].Item1)
                    {
                        _r2GOfRec[pp][cnt].Item2.IsLastSlice = true;
                        _r2GOfRec[pp][cnt + 1].Item2.IsFirstSlice = true;
                    }
                }
            }

            for (var pp = 0; pp < _ofmapRec.Length; pp++)
            {
                if (_ofmapRec[pp].Count > 0)
                {
                    _ofmapRec[pp][0].Item2.IsFirstSlice = true;
                    _ofmapRec[pp][_ofmapRec[pp].Count - 1].Item2.IsLastSlice = true;
                }
            }

            // mmu conf
            if (!recStat)
            {
                actionUpdater.UpdateMmuConf();
            }
        }

        var inShape = new GNNEShape(ld.CheckedShape.ToValueArray().Select(x => (int)x).ToArray());
        var outputShape = new GNNEShape(st.CheckedShape.ToValueArray().Select(x => (int)x).ToArray());
        var inType = ld.CheckedDataType;
        var outputType = st[GNNEStore.Input].CheckedDataType;
        var outputBatchSeg = GetSegmentStartEndLength(0, glb.LastOutShape[0], outputShape[0]);
        var outputChanSeg = GetSegmentStartEndLength(0, glb.LastOutShape[1], outputShape[1]);
        var outputRowSeg = GetSegmentStartEndLength(0, glb.LastOutShape[2], outputShape[2]);
        var outputColSeg = GetSegmentStartEndLength(0, glb.LastOutShape[3], outputShape[3]);

        int ifPP = 0;
        foreach (var glbOutputBatch in outputBatchSeg)
        {
            Segment1D glbInputBatch = glbOutputBatch;
            foreach (var glbOutputChannel in outputChanSeg)
            {
                Segment1D glbInputChannel = glbOutputChannel;
                foreach (var glbOutputRow in outputRowSeg)
                {
                    Segment1D glbInputRow = new Segment1D(0..inShape[2], Padding.Zero());
                    foreach (var glbOutputColumn in outputColSeg)
                    {
                        Segment1D glbInputColumn = new(0..(int)inShape[3], Padding.Zero());
                        SegmentND ofmap = new SegmentND(glbOutputBatch, glbOutputChannel, glbOutputRow, glbOutputColumn);
                        SegmentND ifmap = new SegmentND(glbInputBatch, glbInputChannel, glbInputRow, glbInputColumn);

                        Ai2dConfig config = new();
                        Ai2dUtilKpuUpdateStaticParam(ld, st, resize, ref config);
                        Ai2dUtilKpuUpdateResizeParam(ld, st, resize, ref config);
                        List<int> dst_hw = new List<int> { 0, 0 };
                        Ai2dUtilResizeSramSearch(config, ofmap, ifmap, dst_hw);

                        var cSegs = inType == DataTypes.Int16 ? 2 : 4;
                        List<Segment1D> sram_c_segs = TileUtilities.GetSegmentStartEndLength(glbOutputChannel.Start, cSegs, glbOutputChannel.End);
                        List<Segment1D> sram_h_segs = TileUtilities.GetSegmentStartEndLength(glbOutputRow.Start, dst_hw[0], glbOutputRow.End);
                        List<Segment1D> sram_w_segs = TileUtilities.GetSegmentStartEndLength(glbOutputColumn.Start, dst_hw[1], glbOutputColumn.End);

                        // load
                        if (!recStat)
                        {
                            List<CcrSet> ccrSetIfmap = new() { new(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap, ifPP)), sram_c_segs.Count * sram_h_segs.Count * sram_w_segs.Count > 1 ? 2 : 1) };
                            int offset = 0;
                            List<int> stridesD = new() { ifmap[1].Length, ifmap[2].Length, ifmap[3].Length };
                            actionUpdater.UpdateLoadIf(ifmap, ld, ifPP, ddrIf, offset, stridesD, ItemName.Ifmap, ccrSetIfmap, null!);
                        }

                        List<CcrClr> ccrClrIfmap = new();

                        // ai2d
                        int inst_len = 0;
                        foreach (var sram_oc in sram_c_segs)
                        {
                            bool c_changed = true;
                            foreach (var sram_oh in sram_h_segs)
                            {
                                foreach (var sram_ow in sram_w_segs)
                                {
                                    if (!recStat)
                                    {
                                        Ai2dUtilKpuUpdateResizeParam(ld, st, resize, ref config);
                                        List<float> dst_00 = new() { (float)sram_ow.Start, (float)sram_oh.Start };
                                        List<float> dst_xy = new() { (float)(sram_ow.End - 1), (float)(sram_oh.End - 1) };
                                        List<float> mOriScale = new() { config.OriginM(config.M0), config.OriginM(config.M1), config.OriginM(config.M3), config.OriginM(config.M4) };
                                        List<float> mOriBias = new() { config.OriginM(config.M2), config.OriginM(config.M5) };

                                        var src_00 = Ai2dUtilMMulAdd(mOriScale, mOriBias, dst_00);
                                        var src_xy = Ai2dUtilMMulAdd(mOriScale, mOriBias, dst_xy);

                                        int src_x = Math.Max((int)Math.Floor(src_00[0]), 0);
                                        int src_y = Math.Max((int)Math.Floor(src_00[1]), 0);
                                        int src_width_end = Math.Min((int)Math.Ceiling(src_xy[0]), (int)(inShape[3] - 1));
                                        int src_height_end = Math.Min((int)Math.Ceiling(src_xy[1]), (int)(inShape[2] - 1));

                                        List<float> ori_00 = new() { 0f, 0f };
                                        var ori_src_00 = Ai2dUtilMMulAdd(mOriScale, mOriBias, ori_00);
                                        float offset_M2 = 0f;
                                        if (src_x != 0)
                                        {
                                            offset_M2 = src_00[0] - src_x - ori_src_00[0];
                                        }

                                        float offset_M5 = 0f;
                                        if (src_y != 0)
                                        {
                                            offset_M5 = src_00[1] - src_y - ori_src_00[1];
                                        }

                                        int src_width = src_width_end - src_x + 1;
                                        int src_height = src_height_end - src_y + 1;

                                        Segment1D sram_h = new Segment1D(src_y..(src_y + src_height), Padding.Zero());
                                        Segment1D sram_w = new Segment1D(src_x..(src_x + src_width), Padding.Zero());
                                        SegmentND ifmap_pp = new SegmentND(ifmap[0], sram_oc, sram_h, sram_w);
                                        SegmentND ofmap_pp = new SegmentND(ofmap[0], sram_oc, sram_oh, sram_ow);

                                        Ai2dUtilKpuUpdateDynamicParam(glb, ref config, ifmap_pp, ofmap_pp, ifPP, ifmap, ofmap, offset_M2, offset_M5, inType, outputType);

                                        int ccr_clr_cnt = 0;
                                        if (TileUtilities.IsFirstSlice(ofmap_pp, ofmap) || TileUtilities.IsLastSlice(ofmap_pp, ofmap))
                                        {
                                            ccr_clr_cnt = 1;
                                        }

                                        if (ccr_clr_cnt == 1)
                                        {
                                            ccrClrIfmap.Add(new CcrClr(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ifmap, ifPP))));
                                        }

                                        int r2g_of_ccr_set_num = 0;
                                        int r2g_of_ccr_clr_num = 0;
                                        var of_ccr_stat = _r2GOfRec[ifPP][0];
                                        _r2GOfRec[ifPP].RemoveAt(0);
                                        if (of_ccr_stat.Item2.IsFirstSlice && (!_ofmapRec[ifPP][0].Item2.IsFirstSlice))
                                        {
                                            r2g_of_ccr_clr_num = 1;
                                        }

                                        if (of_ccr_stat.Item2.IsLastSlice)
                                        {
                                            r2g_of_ccr_set_num = 1;
                                        }

                                        if (r2g_of_ccr_clr_num > 0)
                                        {
                                            ccrClrIfmap.Add(new CcrClr(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.OfmapFake, ifPP))));
                                        }

                                        List<CcrSet> ccr_set_ofmap = new();
                                        if (r2g_of_ccr_set_num > 0)
                                        {
                                            ccr_set_ofmap.Add(new CcrSet(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ofmap, ifPP)), 1));
                                        }

                                        bool write_all = false;
                                        if (c_changed)
                                        {
                                            inst_len += 16 * 9;
                                            c_changed = false;
                                            write_all = true;
                                        }
                                        else
                                        {
                                            inst_len += 16 * 2;
                                        }

                                        bool is_last_hw = sram_oh == sram_h_segs[^1] && sram_ow == sram_w_segs[^1];
                                        int next_inst_len = inst_len;
                                        if (!is_last_hw)
                                        {
                                            next_inst_len += 16 * 2;
                                        }
                                        else if (sram_oc != sram_c_segs[^1])
                                        {
                                            next_inst_len += 16 * 9;
                                        }

                                        if (next_inst_len > 1024)
                                        {
                                            inst_len = 0;
                                            config.IntrMask = 0;
                                        }
                                        else if (sram_oc == sram_c_segs[^1] && sram_oh == sram_h_segs[^1] && sram_ow == sram_w_segs[^1])
                                        {
                                            config.IntrMask = 0;
                                        }
                                        else
                                        {
                                            config.IntrMask = 1;
                                        }

                                        // config all ext register
                                        actionUpdater.UpdateAi2dResize(config, ccr_set_ofmap, ccrClrIfmap, write_all);
                                        if (config.IntrMask == 0)
                                        {
                                            ccrClrIfmap.Clear();
                                        }
                                    }
                                    else
                                    {
                                        _r2GOfRec[ifPP].Add(new(ofmap, new(false, false)));
                                    }
                                }
                            }
                        }

                        // store
                        if (!recStat)
                        {
                            int ofmap_set_cnt = _ofmapRec[ifPP][0].Item2.IsLastSlice ? 0 : 1;
                            _ofmapRec[ifPP].RemoveAt(0);
                            List<CcrSet> ccr_set_ofmap = new();
                            if (ofmap_set_cnt > 0)
                            {
                                ccr_set_ofmap.Add(new CcrSet(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.OfmapFake, ifPP)), 1));
                            }

                            List<CcrClr> ccr_clr_ofmap = new List<CcrClr> { new CcrClr(ccrHandler.GetCcrItem(ccrHandler.GetName(ItemName.Ofmap, ifPP))) };
                            actionUpdater.UpdateStoreT(ofmap, st, ifPP, ddrOf, 0, null!, ccr_set_ofmap, ccr_clr_ofmap);
                        }
                        else
                        {
                            _ofmapRec[ifPP].Add(new(ofmap, new(false, false)));
                        }

                        ifPP = (ifPP + 1) % 2;
                    }
                }
            }
        }

        Assert(ccrHandler.CcrSanityCheck());

        return actions;
    }

    private TileAi2DResizeGlb SearchGlbParameters(Call ld, Call st)
    {
        var input = ld[GNNELoad.Input];
        var inShape = new GNNEShape(input.CheckedShape.ToValueArray().Select(x => (int)x).ToArray());
        var outputShape = new GNNEShape(st.CheckedShape.ToValueArray().Select(x => (int)x).ToArray());
        var n = 1;
        var c = Math.Min(4, (int)outputShape[1]);

        // todo: split hw if needed
        var oh = outputShape[2];
        var ow = outputShape[3];
        var h = inShape[2];
        var w = inShape[3];

        // pre allocate
        AllocateResult allocation;
        allocation = HandleAllocate(ld, st, n, c, h, w, oh, ow);
        if (!allocation.IsOk)
        {
            c = 1;
        }

        while (c < (int)outputShape[1])
        {
            var next_c = Math.Min(c + 4, (int)outputShape[1]);
            allocation = HandleAllocate(ld, st, n, next_c, h, w, oh, ow);
            if (allocation.IsOk)
            {
                c = next_c;
            }
            else
            {
                break;
            }
        }

        while (c < (int)outputShape[1])
        {
            var next_c = Math.Min(c + 1, (int)outputShape[1]);
            allocation = HandleAllocate(ld, st, n, next_c, h, w, oh, ow);
            if (allocation.IsOk)
            {
                c = next_c;
            }
            else
            {
                break;
            }
        }

        allocation = HandleAllocate(ld, st, n, c, h, w, oh, ow, true);
        Assert(allocation.IsOk);

        var lastOutputShape = new GNNEShape((int)n, (int)c, (int)oh, (int)ow);
        return new TileAi2DResizeGlb(allocation.GlbMap, allocation.Items, lastOutputShape.Dims, GNNEEnv.NPingPongSplit, allocation.GlbMap[ItemName.Ifmap], allocation.GlbMap[ItemName.Ofmap]);
    }

    private AllocateResult HandleAllocate(Call ld, Call st, int n, int c, int h, int w, int e, int f, bool isFinal = false)
    {
        var bp = new BoxPacker(16);
        Dictionary<ItemName, TensorOnGlb> glbMap = new();

        int bytesPerElement = GetBytesPerElement(ld.CheckedDataType);
        var ifGlb = new TensorOnGlb(new[] { n, c, h, w }, ld.CheckedDataType, 0);
        int ifmapSize = ifGlb.GlbNByte;
        ifmapSize = GetAlignedNum(ifmapSize, GNNEEnv.OfmapBankWidth * GNNEEnv.GlbBankWidth);
        ifGlb.AllocatedBytes = ifmapSize;
        ifmapSize *= GNNEEnv.NPingPongSplit;

        var ofGlb = new TensorOnGlb(new[] { n, c, e, f }, st.CheckedDataType, 0);
        int ofmapSize = ofGlb.GlbNByte;
        ofmapSize = GetAlignedNum(ofmapSize, GNNEEnv.OfmapBankWidth * GNNEEnv.GlbBankWidth);
        ofGlb.AllocatedBytes = ofmapSize;
        ofmapSize *= GNNEEnv.NPingPongSplit;

        int basementSize = SpaceSearcher.GetBasementSize();
        basementSize = GetAlignedNum(basementSize, GNNEEnv.GlbWidth * GNNEEnv.GlbBankWidth);

        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.GlbWidth, basementSize / GNNEEnv.GlbWidth / GNNEEnv.GlbBankWidth }, ItemName.Basement));
        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.IfmapBankWidth, ifmapSize / GNNEEnv.IfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ifmap));
        bp.Boxes.Add(new BoxOnGlb(new[] { GNNEEnv.OfmapBankWidth, ofmapSize / GNNEEnv.OfmapBankWidth / GNNEEnv.GlbBankWidth }, ItemName.Ofmap));

        var allocation = SpaceSearcher.TryAllocate(bp);

#if DEBUG
        if (isFinal && allocation.IsOk)
        {
            bp.DisplayFinalAllocation();
        }
#endif

        if (allocation.IsOk)
        {
            ifGlb.Mmu = allocation.Items[ItemName.Ifmap];
            ofGlb.Mmu = allocation.Items[ItemName.Ofmap];
        }

        glbMap.Add(ItemName.Ifmap, ifGlb);
        glbMap.Add(ItemName.Ofmap, ofGlb);
        allocation.GlbMap = glbMap;
        return allocation;
    }

    private void ItemRecStatusInit()
    {
        for (int i = 0; i < _r2GOfRec.Length; i++)
        {
            if (_r2GOfRec[i] is not null)
            {
                _r2GOfRec[i].Clear();
            }
            else
            {
                _r2GOfRec[i] = new tensor_ccr_stat();
            }
        }

        for (int i = 0; i < _ofmapRec.Length; i++)
        {
            if (_ofmapRec[i] is not null)
            {
                _ofmapRec[i].Clear();
            }
            else
            {
                _ofmapRec[i] = new tensor_ccr_stat();
            }
        }
    }
}

internal class TileAi2DResizeGlb : TiledGlb
{
    private TensorOnGlb _ifGlb;
    private TensorOnGlb _ofGlb;

    public TileAi2DResizeGlb(Dictionary<ItemName, TensorOnGlb> glbMap, Dictionary<ItemName, MmuItem> items, int[] lastOutShape, int nPingPongSplit, TensorOnGlb ifGlb, TensorOnGlb ofGlb)
        : base(glbMap, items, lastOutShape, nPingPongSplit)
    {
        _ifGlb = ifGlb;
        _ofGlb = ofGlb;
    }
}
