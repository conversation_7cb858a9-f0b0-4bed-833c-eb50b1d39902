﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using Nncase.Utilities;
using static Nncase.Evaluator.K230Kernels;
using static Nncase.IR.F.Tensors;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.PatternMatch.F.K230;
using static Nncase.PatternMatch.F.Tensors;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

// public class lstm_quant_conf
// {
//     public DataType? quant_type_;
//     public DataType? w_quant_type_;
//     public bool use_mse_quant_w_;
// }
[RuleGenerator]
public sealed partial class ToGNNELSTM : IRewriteRule
{
    private QuantizeManager? _quantizeIf;
    private QuantizeManager? _quantizeH;
    private QuantizeManager? _quantizeWXc;
    private QuantizeManager? _quantizeWRc;
    private QuantizeManager? _quantizeOf;
    private ConvQuantConf? _conf;

    public IPattern Pattern { get; }
        = IsWrappedLSTM(
            IsFakeLSTM(
                target_name: "fakeLstm",
                call_name: "call",
                _ => true,
                IsRangeOfMarker("inputMarker", IsWildcard("input"), IsConst("inputRange")),
                IsRangeOfMarker("wXcMarker", IsTensorConst("wXc"), IsConst("wXcRange")),
                IsTensorConst("actXc"),
                IsRangeOfMarker("wRcMarker", IsTensorConst("wRc"), IsConst("wRcRange")),
                IsTensorConst("actRc"),
                IsRangeOfMarker("initialHMarker", IsWildcard("initialH"), IsConst("initialHRange")),
                IsRangeOfMarker("initialCMarker", IsWildcard("initialC"), IsConst("initialCRange")),
                IsTensorConst("segFittingParamFt"),
                IsTensorConst("segFittingParamGt"),
                IsTensorConst("hasStatic"),
                IsTensorConst("outputSize")),
            (t, i) => IsRangeOfMarker($"outputMarker_{i}", IsAlt(IsReshape(t, IsTensorConst($"shapes_{i}")), t), IsTensorConst($"outputRange_{i}")));

    private void LowerInit(FakeLSTM fakeLstm, Expr inputMarker, Tensor<float> inputRange, Expr wXcMarker, Expr wRcMarker, Tensor<float> wXcRange, Tensor<float> wRcRange, Expr initialHMarker, Tensor<float> initialHRange, Expr initialCMarker, Tensor<float> initialCRange, Expr initialH, Expr initialC, Tensor wXc, Tensor wRc, Tensor<float> outputRange)
    {
        var fineTunedInfoWXc = wXc;
        var fineTunedInfoWRc = wRc;

        // var fineTunedInfoInitialH = initial_h;
        // var fineTunedInfoInitialC = initial_c;
        var numDirection = fakeLstm.Direction == LSTMDirection.Bidirectional ? 2 : 1;

        var fineTunedWXcRangesByChannel = ReplaceWeightRangeToByChannel(wXc, numDirection);
        var fineTunedWRcRangesByChannel = ReplaceWeightRangeToByChannel(wRc, numDirection);

        // var fineTunedInitialHRangesByChannel = replaceWeightRangeToByChannel(initial_h_range);
        // var fineTunedInitialCRangesByChannel = replaceWeightRangeToByChannel(initial_c_range);
        var op = new CompileOptions();
        bool useMixQuant = op.QuantizeOptions.BindQuantMethod;

        var mixQuantInfoInput = ((Marker)inputMarker).MixQuantInfo;
        var mixQuantInfoWXC = ((Marker)wXcMarker).MixQuantInfo;

        // var mixQuantInfoWXc = ((Marker)w_xc_marker).MixQuantInfo;
        // var mixQuantInfoWRc = ((Marker)w_rc_marker).MixQuantInfo;
        // var mixQuantInfoinitial_h = ((Marker)initial_h_marker).MixQuantInfo;
        // var mixQuantInfoinitial_c = ((Marker)initial_c_marker).MixQuantInfo;
        var quantType = mixQuantInfoInput?.MarkerQuantType == null ? op.QuantizeOptions.QuantType : mixQuantInfoInput!.MarkerQuantType;
        var wQuantType = mixQuantInfoWXC?.MarkerQuantType == null ? op.QuantizeOptions.WQuantType : mixQuantInfoWXC?.MarkerQuantType;
        _conf = new ConvQuantConf { QuantType = quantType, UseMseQuantW = false, WQuantType = wQuantType };

        // todo mixQuant
        // if (mixQuantInfoinitial_h?.DoSquant == true)
        // {
        //     if (wQuantType == DataTypes.UInt8)
        //         fineTunedInfoInitialH = (((K230Target.K230MixQuantInfo)mixQuantInfoinitial_h).U8FineTunedWeights.Value);
        //     else if (wQuantType == DataTypes.Int8)
        //         fineTunedInfoInitialH = (((K230Target.K230MixQuantInfo)mixQuantInfoinitial_h).I8FineTunedWeights.Value);
        //     else
        //         fineTunedInfoInitialH =
        //             (((K230Target.K230MixQuantInfo)mixQuantInfoinitial_h).I16FineTunedWeights.Value);
        // }

        // if (wQuantType == DataTypes.UInt8 &&
        //     ((K230Target.K230MixQuantInfo)mixQuantInfoinitial_h)?.U8FineTunedWeightsRangesByChannel != null)
        //     fineTunedInitialHRangesByChannel = (Tensor<float>)(((K230Target.K230MixQuantInfo)mixQuantInfoinitial_h)
        //         .U8FineTunedWeightsRangesByChannel.Value);
        // if (wQuantType == DataTypes.Int8 &&
        //     ((K230Target.K230MixQuantInfo)mixQuantInfoinitial_h)?.I8FineTunedWeightsRangesByChannel != null)
        //     fineTunedInitialHRangesByChannel = (Tensor<float>)(((K230Target.K230MixQuantInfo)mixQuantInfoinitial_h)
        //         .I8FineTunedWeightsRangesByChannel.Value);
        // if (wQuantType == DataTypes.Int16 &&
        //     ((K230Target.K230MixQuantInfo)mixQuantInfoinitial_h)?.I16FineTunedWeightsRangesByChannel != null)
        //     fineTunedInitialHRangesByChannel = (Tensor<float>)((K230Target.K230MixQuantInfo)mixQuantInfoinitial_h)
        //         .I16FineTunedWeightsRangesByChannel?.Value;

        // if (mixQuantInfoinitial_c?.DoSquant == true)
        // {
        //     if (wQuantType == DataTypes.UInt8)
        //         fineTunedInfoInitialC = (((K230Target.K230MixQuantInfo)mixQuantInfoinitial_c).U8FineTunedWeights.Value);
        //     else if (wQuantType == DataTypes.Int8)
        //         fineTunedInfoInitialC = (((K230Target.K230MixQuantInfo)mixQuantInfoinitial_c).I8FineTunedWeights.Value);
        //     else
        //         fineTunedInfoInitialC =
        //             (((K230Target.K230MixQuantInfo)mixQuantInfoinitial_c).I16FineTunedWeights.Value);
        // }

        // if (wQuantType == DataTypes.UInt8 &&
        //     (((K230Target.K230MixQuantInfo)mixQuantInfoinitial_c)?.U8FineTunedWeightsRangesByChannel) != null)
        //     fineTunedInitialCRangesByChannel = (Tensor<float>)(((K230Target.K230MixQuantInfo)mixQuantInfoinitial_c)
        //         .U8FineTunedWeightsRangesByChannel.Value);
        // if (wQuantType == DataTypes.Int8 &&
        //     (((K230Target.K230MixQuantInfo)mixQuantInfoinitial_c)?.I8FineTunedWeightsRangesByChannel) != null)
        //     fineTunedInitialCRangesByChannel = (Tensor<float>)(((K230Target.K230MixQuantInfo)mixQuantInfoinitial_c)
        //         .I8FineTunedWeightsRangesByChannel.Value);
        // if (wQuantType == DataTypes.Int16 &&
        //     (((K230Target.K230MixQuantInfo)mixQuantInfoinitial_c)?.I16FineTunedWeightsRangesByChannel) != null)
        //     fineTunedInitialCRangesByChannel = (Tensor<float>)(((K230Target.K230MixQuantInfo)mixQuantInfoinitial_c)
        //         .I16FineTunedWeightsRangesByChannel.Value);

        // if (mixQuantInfoWXc?.DoSquant == true)
        // {
        //     if (wQuantType == DataTypes.UInt8)
        //         fineTunedInfoWXc = (((K230Target.K230MixQuantInfo)mixQuantInfoWXc).U8FineTunedWeights.Value);
        //     else if (wQuantType == DataTypes.Int8)
        //         fineTunedInfoWXc = (((K230Target.K230MixQuantInfo)mixQuantInfoWXc).I8FineTunedWeights.Value);
        //     else
        //         fineTunedInfoWXc =
        //             (((K230Target.K230MixQuantInfo)mixQuantInfoWXc).I16FineTunedWeights.Value);
        // }
        //
        // if (wQuantType == DataTypes.UInt8 &&
        //     (((K230Target.K230MixQuantInfo)mixQuantInfoWXc)?.U8FineTunedWeightsRangesByChannel) != null)
        //     fineTunedWXcRangesByChannel = (Tensor<float>)(((K230Target.K230MixQuantInfo)mixQuantInfoWXc)
        //         .U8FineTunedWeightsRangesByChannel.Value);
        // if (wQuantType == DataTypes.Int8 &&
        //     (((K230Target.K230MixQuantInfo)mixQuantInfoWXc)?.I8FineTunedWeightsRangesByChannel) != null)
        //     fineTunedWXcRangesByChannel = (Tensor<float>)(((K230Target.K230MixQuantInfo)mixQuantInfoWXc)
        //         .I8FineTunedWeightsRangesByChannel.Value);
        // if (wQuantType == DataTypes.Int16 &&
        //     (((K230Target.K230MixQuantInfo)mixQuantInfoWXc)?.I16FineTunedWeightsRangesByChannel) != null)
        //     fineTunedWXcRangesByChannel = (Tensor<float>)(((K230Target.K230MixQuantInfo)mixQuantInfoWXc)
        //         .I16FineTunedWeightsRangesByChannel.Value);
        //
        // if (mixQuantInfoWRc?.DoSquant == true)
        // {
        //     if (wQuantType == DataTypes.UInt8)
        //         fineTunedInfoWRc = (((K230Target.K230MixQuantInfo)mixQuantInfoWRc).U8FineTunedWeights.Value);
        //     else if (wQuantType == DataTypes.Int8)
        //         fineTunedInfoWRc = (((K230Target.K230MixQuantInfo)mixQuantInfoWRc).I8FineTunedWeights.Value);
        //     else
        //         fineTunedInfoWRc =
        //             (((K230Target.K230MixQuantInfo)mixQuantInfoWRc).I16FineTunedWeights.Value);
        // }
        //
        // if (wQuantType == DataTypes.UInt8 &&
        //     (((K230Target.K230MixQuantInfo)mixQuantInfoWRc)?.U8FineTunedWeightsRangesByChannel) != null)
        //     fineTunedWRcRangesByChannel = (Tensor<float>)(((K230Target.K230MixQuantInfo)mixQuantInfoWRc)
        //         .U8FineTunedWeightsRangesByChannel.Value);
        // if (wQuantType == DataTypes.Int8 &&
        //     (((K230Target.K230MixQuantInfo)mixQuantInfoWRc)?.I8FineTunedWeightsRangesByChannel) != null)
        //     fineTunedWRcRangesByChannel = (Tensor<float>)(((K230Target.K230MixQuantInfo)mixQuantInfoWRc)
        //         .I8FineTunedWeightsRangesByChannel.Value);
        // if (wQuantType == DataTypes.Int16 &&
        //     (((K230Target.K230MixQuantInfo)mixQuantInfoWRc)?.I16FineTunedWeightsRangesByChannel) != null)
        //     fineTunedWRcRangesByChannel = (Tensor<float>)(((K230Target.K230MixQuantInfo)mixQuantInfoWRc)
        //         .I16FineTunedWeightsRangesByChannel.Value);
        _quantizeIf = new QuantizeManager(inputRange, fineTunedWXcRangesByChannel, outputRange, fineTunedInfoWXc, _conf, true);
        _quantizeH = new QuantizeManager(initialHRange, initialH is TensorConst ? new Tensor<float>(initialHRange.ToArray(), new long[] { 1, 2 }) : fineTunedWRcRangesByChannel, outputRange, initialH is TensorConst tc ? tc.Value : fineTunedInfoWRc, _conf, false);
        _quantizeWXc = new QuantizeManager(inputRange, fineTunedWXcRangesByChannel, outputRange, fineTunedInfoWXc, _conf, true);
        _quantizeWRc = new QuantizeManager(initialHRange, fineTunedWRcRangesByChannel, outputRange, fineTunedInfoWRc, _conf, true);
        _quantizeOf = new QuantizeManager(inputRange, fineTunedWXcRangesByChannel, outputRange, fineTunedInfoWXc, _conf, true);

        // var quantArray = new[] { fineTunedInfoWXc, fineTunedInfoWRc };

        // var exprs = quantArray.Select(f =>
        //         (Expr)GNNEStore(((Call)f)[Nncase.IR.Tensors.GetItem.Index] != 2 ? DataTypes.UInt8 : DataTypes.Float16,
        //             f))
        //     .ToArray();
    }

    private Expr? GetReplace(FakeLSTM fakeLstm, Call call, Expr input, Expr inputMarker, Expr initialHMarker, Expr initialCMarker, Tensor<float> inputRange, Tensor wXc, Tensor<float> wXcRange, Tensor<float> wRcRange, Tensor wRc, Tensor<float> initialHRange, Tensor<float> initialCRange, Expr wRcMarker, Expr wXcMarker, Expr initialH, Expr initialC, Expr segFittingParamFt, Expr segFittingParamGt, Tensor<float> outputRange_0, Expr hasStatic, int outputSize, IMatchResult result)
    {
        LowerInit(fakeLstm, inputMarker, inputRange, wXcMarker, wRcMarker, wXcRange, wRcRange, initialHMarker, initialHRange, initialCMarker, initialCRange, initialH, initialC, wXc, wRc, outputRange_0);

        // if
        // QuantizeManager quantize_if;
        var iqP = _quantizeIf!.GetIfDeqQuantParam(_conf!.QuantType!);
        var loadIf = GNNELoad((PrimType)_conf.QuantType!, IR.F.Math.Quantize(inputMarker, new QuantParam(iqP.ZeroPoint, iqP.Scale), (PrimType)_conf.QuantType!));

        // initial_h
        QuantizeParam qHp0;
        Call loadH;
        if (initialH is TensorConst)
        {
            var initHTensor = ((TensorConst)initialH).Value;
            qHp0 = _quantizeH!.GetWeightsQuantParam()[0];
            Tensor cH;
            if (_conf.WQuantType == DataTypes.Int16)
            {
                short[] qH = _quantizeH.GetQuantWeightsI16();
                cH = Tensor.From(qH.Select(x => x).ToArray(), initHTensor.Shape);
            }
            else if (_conf.WQuantType == DataTypes.Int8)
            {
                var qH = _quantizeH.GetQuantWeights();
                cH = Tensor.From(qH.Select(x => x.I8).ToArray(), initHTensor.Shape);
            }
            else
            {
                var qH = _quantizeH.GetQuantWeights();
                cH = Tensor.From(qH.Select(x => x.U8).ToArray(), initHTensor.Shape);
            }

            loadH = GNNELoad((PrimType)_conf.WQuantType!, cH);
        }
        else
        {
            var qHP = _quantizeH!.GetIfDeqQuantParam(_conf.QuantType!);
            loadH = GNNELoad((PrimType)_conf.QuantType!, IR.F.Math.Quantize(initialH, new QuantParam(qHP.ZeroPoint, qHP.Scale), (PrimType)_conf.QuantType!));
            qHp0 = new QuantizeParam(qHP.ZeroPoint, 1.0f / qHP.Scale);
        }

        // initial_c
        Call loadC;
        if (initialC is TensorConst)
        {
            var initCTensor = ((TensorConst)initialC).Value;
            var vecC = new Half[ComputeSize(initCTensor.Shape)];
            for (int i = 0; i < vecC.Length; i++)
            {
                vecC[i] = initCTensor.ToArray<Half>()[i];
            }

            var newC = Tensor.From(vecC, initCTensor.Shape);
            loadC = GNNELoad(DataTypes.Float16, newC);
        }
        else
        {
            loadC = GNNELoad(DataTypes.Float16, initialC);
        }

        // w_xc
        Tensor cWeights;
        if (_conf.WQuantType == DataTypes.UInt8)
        {
            var qWeights = _quantizeIf.GetQuantWeights(true);
            cWeights = Tensor.From(qWeights.Select(x => x.U8).ToArray(), wXc.Shape);
        }
        else if (_conf.WQuantType == DataTypes.Int8)
        {
            var qWeights = _quantizeIf.GetQuantWeights(true);
            cWeights = Tensor.From(qWeights.Select(x => x.I8).ToArray(), wXc.Shape);
        }
        else
        {
            var qWeights = _quantizeIf.GetQuantWeightsI16(true);
            cWeights = Tensor.From(qWeights.Select(x => x).ToArray(), wXc.Shape);
        }

        var loadWXc = GNNELoadW((PrimType)_conf!.WQuantType!, cWeights);
        var weightsBias = _quantizeIf.GetWeightsQuantBiasQint8();
        if (_conf!.WQuantType == DataTypes.Int16)
        {
            weightsBias = Enumerable.Repeat((byte)0, weightsBias.Length).ToArray();
        }

        var wBias = Tensor.From(weightsBias.ToArray(), new long[] { 1, 1, 1, weightsBias.Length });
        var loadWXcBias = GNNELoadW(DataTypes.UInt8, wBias);

        // w_rc
        Tensor rcWeights;
        if (_conf.WQuantType == DataTypes.UInt8)
        {
            var qWeights = _quantizeWRc!.GetQuantWeights(true);
            rcWeights = Tensor.From(qWeights.Select(x => x.U8).ToArray(), wRc.Shape);
        }
        else if (_conf.WQuantType == DataTypes.Int8)
        {
            var qWeights = _quantizeWRc!.GetQuantWeights(true);
            rcWeights = Tensor.From(qWeights.Select(x => x.I8).ToArray(), wRc.Shape);
        }
        else
        {
            var qWeights = _quantizeWRc!.GetQuantWeightsI16(true);
            rcWeights = Tensor.From(qWeights.Select(x => x).ToArray(), wRc.Shape);
        }

        var loadWRc = GNNELoadW((PrimType)_conf.WQuantType!, rcWeights);
        var weightsBiasRc = _quantizeWRc.GetWeightsQuantBiasQint8();
        if (_conf!.WQuantType == DataTypes.Int16)
        {
            weightsBiasRc = Enumerable.Repeat((byte)0, weightsBiasRc.Length).ToArray();
        }

        var wBiasRc = Tensor.From(weightsBiasRc.ToArray(), new long[] { 1, 1, 1, weightsBiasRc.Length });
        var loadWRcBias = GNNELoadW(DataTypes.UInt8, wBiasRc);

        // act_xc
        var actParam2Xc = new ActParam2(fakeLstm.ActParamXc);
        var ifDeqParams = _quantizeWXc!.GetIfDeqQuantParam(_conf.QuantType!);
        float[] wDeqScale = _quantizeWXc.GetWeightsDeqScale();
        actParam2Xc.FusedScale(ifDeqParams.Scale);
        actParam2Xc.FusedChannelScale(wDeqScale);
        var act = actParam2Xc.ToAct0Data();
        var loadActXc = GNNELoadW(DataTypes.Float16, act);

        // act_rc_0, for seq loop's begin
        var actParam2Rc0 = new ActParam2(fakeLstm.ActParamRc);
        QuantizeParam hDeqParams;
        hDeqParams = initialH is TensorConst ? _quantizeH.GetWeightsQuantParam()[0] : _quantizeH.GetIfQuantParam(_conf.QuantType!);

        float[] wDeqScaleRc0 = _quantizeWRc.GetWeightsDeqScale();
        actParam2Rc0.FusedScale(1f / hDeqParams.Scale);
        actParam2Rc0.FusedChannelScale(wDeqScaleRc0);
        var loadActRc0 = GNNELoadW(DataTypes.Float16, actParam2Rc0.ToAct0Data());

        // act_rc
        var actParamRc1 = new ActParam2(fakeLstm.ActParamRc);
        var hDeqParamsRc = _quantizeWRc.GetOfDeqQuantParam(_conf.QuantType!);
        var qHP1 = _quantizeWRc.GetOfQuantParam(_conf.QuantType!);
        float[] wDeqScaleRc = _quantizeWRc.GetWeightsDeqScale();
        actParamRc1.FusedScale(hDeqParamsRc.Scale);
        actParamRc1.FusedChannelScale(wDeqScaleRc);
        var loadActRc1 = GNNELoadW(DataTypes.Float16, actParamRc1.ToAct0Data());

        // segfitting param ft
        var loadSegFittingParamFt = GNNELoadW(DataTypes.Float16, ((TensorConst)segFittingParamFt).Value.Cast<Half>());

        // segfitting param gt
        var loadSegFittingParamGt = GNNELoadW(DataTypes.Float16, ((TensorConst)segFittingParamGt).Value.Cast<Half>());

        // act for binary
        var actParamBin = new ActParam2((int)((TensorType)((TupleType)call.CheckedType)[0]).Shape[^1].FixedValue);
        var loadBinAct = GNNELoadW(DataTypes.Float16, actParamBin.ToAct1Data());

        // act for quantized binary
        var actParamBinQ = new ActParam2((int)((TensorType)((TupleType)call.CheckedType)[0]).Shape[^1].FixedValue);
        var ofQParams = _quantizeOf!.GetOfQuantParam(_conf.QuantType!);

        actParamBinQ.SetFusedClamp(ValueRange<Half>.Full);
        actParamBinQ.FusedQuantParam(new QuantParam(ofQParams.ZeroPoint, 1f / ofQParams.Scale));
        var loadBinQAct = GNNELoadW(DataTypes.Float16, actParamBinQ.ToAct1Data());

        var output = GNNELSTM((PrimType)_conf.QuantType!, (PrimType)_conf.QuantType!, loadIf, loadWXc, loadActXc, loadWRc, loadActRc0, loadActRc1, loadH, loadC, loadSegFittingParamFt, loadSegFittingParamGt, loadWXcBias, loadWRcBias, loadBinAct, loadBinQAct, actParam2Xc, actParam2Rc0, actParamRc1, iqP.ZeroPoint, 0, qHp0.ZeroPoint, qHP1.ZeroPoint, 0, 0, 0, 0, 0, actParamBin, actParamBinQ, fakeLstm.Direction, hasStatic, outputSize);

        IR.Tuple WrapOutput(Call c, int outputsize, int[][] shapes)
        {
            var outputs = Enumerable.Range(0, outputsize).Select(i => GetItem(c, i)).ToArray();
            var stores = new IR.Tuple(outputs.Select((gi, i) => i switch { 2 => GNNEStore(DataTypes.Float32, gi), _ => GNNEStore(_conf.QuantType!, gi), }).ToArray());
            var exprs = outputs.Select((_, i) => GetItem(stores, i))
                .Select((gi, i) => i switch
                {
                    2 => Reshape(gi, shapes[2]),
                    _ => (Expr)IR.F.Math.Dequantize(
                        Reshape(gi, shapes[i]), new QuantParam(ofQParams.ZeroPoint, 1f / ofQParams.Scale), DataTypes.Float32),
                }).ToArray();

            return new IR.Tuple(exprs);
        }

        // var shapes = Enumerable.Range(0, outputSize).Select(o => ((TensorConst)result[$"shapes_{o}"]).Value.ToArray<int>()).ToArray();
        var shapes = new int[outputSize][];
        for (int o = 0; o < outputSize; o++)
        {
            try
            {
                var tensor = (TensorConst)result[$"shapes_{o}"];
                shapes[o] = tensor.Value.ToArray<int>();
            }
            catch (KeyNotFoundException)
            {
                shapes[o] = ((Nncase.IR.Marker)result[$"outputMarker_{o}"]).CheckedShape.ToValueArray().Select(x => (int)x).ToArray();
            }
        }

        return (Expr)(BaseExpr)WrapOutput(output, outputSize, shapes);
    }

    private Tensor<float> ReplaceWeightRangeToByChannel(Tensor weights, int numDirection)
    {
        float[] weightsValue = ((TensorConst)weights).Value.ToArray<float>();
        var oc = weights.Shape[2];

        var minMaxArr = QuantUtility.GetWeightsRangesByChannel(weightsValue, (int)(numDirection * oc.FixedValue));

        var byChannelWeightsRanges = new Tensor<float>(minMaxArr.ToArray(), new[] { oc.FixedValue * numDirection, 2 });
        return byChannelWeightsRanges;
    }
}
