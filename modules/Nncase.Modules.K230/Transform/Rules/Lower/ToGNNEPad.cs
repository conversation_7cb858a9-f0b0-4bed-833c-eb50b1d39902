// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.TypePatternUtility;
using static Nncase.Passes.Rules.GetReplaceHelper;
using static Nncase.PatternMatch.F.NN;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class ToGNNEPad : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } =
        IsPad(
            "pad",
            "call",
            p => p.PadMode == PadMode.Constant,
            IsWildcard("input") with { TypePattern = GNNETypePatternUtility.ValidDType() & HasShape(sp => sp.Rank <= 4 && sp.IsFixed, "fixed gnne shape") },
            IsTensorConst("pads"),
            IsTensorConst("value"));

    private Expr? GetReplace(Expr input, TensorConst pads, TensorConst value, Call call)
    {
        var newInputShape = new[] { 1, 1, 1, 1 };
        Array.Copy(input.CheckedShape.ToValueArray(), 0, newInputShape, newInputShape.Length - input.CheckedShape.Rank, input.CheckedShape.Rank);
        var inReshape = IR.F.Tensors.Reshape(input, newInputShape);

        var oldPads = pads.Value.ToArray<int>();
        var newPads = Enumerable.Repeat(0, 8).ToArray();
        Array.Copy(oldPads, 0, newPads, newPads.Length - oldPads.Length, oldPads.Length);

        var midType = input.CheckedDataType == DataTypes.Float32 ? DataTypes.Float16 : input.CheckedDataType;
        var padValue = value.Value.Rank == 0 ? value : value[0];
        var newTranspose = GNNEStore(
            call.CheckedDataType,
            GNNEPad(
                GNNELoad((PrimType)midType, inReshape),
                Tensor.From(newPads, new long[] { 4, 2 }),
                Const.FromValue(IR.F.Tensors.Cast(padValue, midType).Evaluate())));

        return IR.F.Tensors.Reshape(newTranspose, call.CheckedShape);
    }
}
