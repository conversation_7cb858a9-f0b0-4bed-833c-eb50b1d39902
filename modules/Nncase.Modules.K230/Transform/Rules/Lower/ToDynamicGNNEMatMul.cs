﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.Diagnostics;
using Nncase.IR;
using Nncase.PatternMatch;
using static Nncase.PatternMatch.F.K230;
using static Nncase.PatternMatch.Utility;
using static Nncase.Quantization.K230.Utility;

namespace Nncase.Passes.Rules.K230;

#if false
/// <summary>
/// lower fake dynamic matmul to DynamicGNNEMatMul.
/// <seealso cref="IR.K230.FakeDynamicGNNEMatMul"/>
/// </summary>
[RuleGenerator]
public sealed partial class ToDynamicGNNEMatMul : RewriteRule<Pattern>
{
    private Const? _instructions;

    /// <inheritdoc/>
    public override Pattern Pattern { get; } = IsFakeDynamicGNNEMatMul(
        target_name: "fakeMatmul",
        call_name: "call",
        _ => true,
        IsRangeOfMarker("inputAMarker", <PERSON><PERSON><PERSON><PERSON><PERSON>("inputA"), <PERSON><PERSON><PERSON><PERSON>("inputARange")),
        <PERSON><PERSON>ange<PERSON><PERSON><PERSON>ark<PERSON>("inputBMarker", <PERSON><PERSON><PERSON><PERSON><PERSON>("inputB"), <PERSON><PERSON><PERSON><PERSON>("inputBRange")),
        <PERSON><PERSON>onst("act0"));

    /// <summary>
    /// prev_output -> load_quant_in_a.
    /// </summary>
    /// <param name="prevInput">prev_input.</param>
    /// <param name="inARange">in_a_range.</param>
    /// <param name="quanType">quan_type.</param>
    /// <returns>.</returns>
    private static Call LoadQuantInA(Expr prevInput, ValueRange<float> inARange, DataType quanType)
    {
        var iqP = NonConstQuantManager.GetInADeqQuantParam(quanType, inARange);
        int addDims = 4 - prevInput.CheckedShape.Rank;
        if (addDims > 0)
        {
            prevInput = IR.F.Tensors.Unsqueeze(prevInput, Enumerable.Range(0, addDims).ToArray());
        }

        return IR.F.Math.Quantize(prevInput, Tensor.FromScalar<QuantParam>(new(iqP.ZeroPoint, iqP.Scale)), quanType);
    }

    /// <summary>
    /// prev_output -> load_quant_in_b.
    /// </summary>
    /// <param name="prevInput">prev_input.</param>
    /// <param name="inBRange">in_b_range.</param>
    /// <param name="quanType">quan_type.</param>
    /// <returns>.</returns>
    private static Call LoadQuantInB(Expr prevInput, ValueRange<float> inBRange, DataType quanType)
    {
        // for non_const matmul, just in_a_quant_type_ is effective.
        var iqP = NonConstQuantManager.GetInBDeqQuantParam(quanType, inBRange);
        int addDims = 4 - prevInput.CheckedShape.Rank;
        if (addDims > 0)
        {
            prevInput = IR.F.Tensors.Unsqueeze(prevInput, Enumerable.Range(0, addDims).ToArray());
        }

        return IR.F.Math.Quantize(prevInput, Tensor.FromScalar<QuantParam>(new(iqP.ZeroPoint, iqP.Scale)), quanType);
    }

    private Expr? GetReplace(IR.K230.FakeDynamicGNNEMatMul fakeMatmul, Call call, Expr inputAMarker, Expr inputA, Tensor<float> inputARange, Expr inputBMarker, Expr inputB, Tensor<float> inputBRange, Tensor<float> act0, RunPassContext options)
    {
        // note for gnne_matmul_transform, quant_type is for inputA, w_quant_type and use_mse_quant_w are for inputB,
        // but when inputB is not constant node, w_quant_type and use_mse_quant_w useless, quant inputA and inputB with quant_type.
        var qoptions = CompileSession.CompileOptions.QuantizeOptions;
        var ap = new ActivationParameter<float>(act0);

        bool useMixQuant = qoptions.BindQuantMethod;
        var mixQuantInfoInputA = ((Marker)inputAMarker).MixQuantInfo;
        var mixQuantInfoInputB = ((Marker)inputBMarker).MixQuantInfo;
        var inputAQuantType = mixQuantInfoInputA?.MarkerQuantType == null ? qoptions.QuantType : mixQuantInfoInputA!.MarkerQuantType;
        var inputBQuantType = mixQuantInfoInputB?.MarkerQuantType == null ? qoptions.QuantType : mixQuantInfoInputB!.MarkerQuantType;

        var matmulQuantInA = LoadQuantInA(inputA, inputARange.AsValueRange(), inputAQuantType);
        var matmulQuantInB = LoadQuantInB(inputB, inputBRange.AsValueRange(), inputBQuantType);

        Expr inABias;
        Expr matmulAct = LoadAct(ap, inputARange.AsValueRange(), inputBRange.AsValueRange(), inputAQuantType, inputBQuantType);

        // the a bias need manual broadcast.
        if (inputA.CheckedShape[^2].IsFixed)
        {
            byte[] inABiasData = LoadInABias((int)inputA.CheckedShape[^2].FixedValue, inputARange.AsValueRange(), inputAQuantType);
            inABias = new TensorConst(Tensor.From(inABiasData, new[] { 1, 1, 1, inputA.CheckedShape[^2].FixedValue }));
        }
        else
        {
            byte[] inABiasData = LoadInABias(1, inputARange.AsValueRange(), inputAQuantType);
            var repeats = new RankedShape(inputA.CheckedShape[^2]);
            var shape = new RankedShape(1, 1, 1, repeats[0]);
            inABias = IR.F.Tensors.Reshape(
                IR.F.Tensors.Tile(new TensorConst(Tensor.From(inABiasData, new long[] { 1 })), repeats),
                shape);

            // matmul_act = IR.F.Tensors.Expand(matmul_act, IR.F.Tensors.Concat(new IR.Tuple(new long[] { 1, 1 }, repeats, new long[] { 7 }), 0));
        }

        var qP = NonConstQuantManager.GetInBQuantParam(inputBQuantType, inputBRange.AsValueRange());
        int inBBias = checked((byte)qP.ZeroPoint);

        // emit the matmul ccr gnne code.
        var instText = GetInstructions(options);

        var newCall = IR.F.NN.CustomCall(new IR.K230.DynamicGNNEMatMul((PrimType)call.CheckedDataType), instText, matmulQuantInA, matmulQuantInB, inABias, inBBias, matmulAct, 0, Convert.ToInt32(fakeMatmul.DynamicChannel));

        var addDims = 4 - call.CheckedShape.Rank;
        if (addDims > 0)
        {
            newCall = IR.F.Tensors.Squeeze(newCall, Enumerable.Range(0, addDims).ToArray());
        }

        return newCall;
    }

    /// <summary>
    /// get the instructions.
    /// </summary>
    /// <param name="passOptions">passOptions.</param>
    /// <returns>.</returns>
    private Const GetInstructions(RunPassContext passOptions)
    {
        if (_instructions is not null)
        {
            return _instructions;
        }

        var gmodel = new CodeGen.K230.CSourceGModelBuilder(Path.Join(Path.GetDirectoryName(GetType().Assembly.Location), "Functional", "dynamic_gnne_matmul.c"));

        // var gmodel = new CodeGen.K230.CSourceGModelBuilder("/home/<USER>/Code/k510-gnne-compiler/modules/Nncase.Modules.K230/Functional/dynamic_gnne_matmul.c");
        gmodel.Serialize();
        byte[] instText = File.ReadAllBytes(gmodel.GModelBuilder.BinFilePath);
        if (DumpScope.Current.IsEnabled(DumpFlags.Rewrite))
        {
            gmodel.Dump("ToDynamicGNNEMatMul", DumpScope.Current.Directory);
        }

        _instructions = Const.FromTensor(Tensor.From(instText));
        return _instructions;
    }

    private byte[] LoadInABias(int channels, ValueRange<float> inARange, DataType quanType)
    {
        // todo 后续可能需要在M固定时, 做weights by channel的量化, 现在实际上都是by layer.
        // NOTE by channel的话可能还得看硬件是否支持在in b 上添加bias.
        var iqP = NonConstQuantManager.GetInADeqQuantParam(quanType, inARange);
        byte[] inputABias = new byte[channels];
        for (int i = 0; i < inputABias.Length; i++)
        {
            inputABias[i] = checked((byte)iqP.ZeroPoint);
        }

        return inputABias;
    }

    private Tensor<Half> LoadAct(ActivationParameter<float> actParam, ValueRange<float> inARange, ValueRange<float> inBRange, DataType quanTypeA, DataType quanTypeB)
    {
        var inADeqParams = NonConstQuantManager.GetInADeqQuantParam(quanTypeA, inARange);
        var inBDeqParams = NonConstQuantManager.GetInBDeqQuantParam(quanTypeB, inBRange);
        actParam.FusedScale(inADeqParams.Scale);
        actParam.FusedScale(inBDeqParams.Scale);
        return actParam.ToAct0Data<Half>();
    }
}
#endif

internal static class NonConstQuantManager
{
    public static QuantizeParam GetInAQuantParam(DataType quantType, ValueRange<float> inARange)
    {
        return GetQuantParamFromDeqParam(GetInADeqQuantParam(quantType, inARange));
    }

    public static DeQuantizeParam GetInADeqQuantParam(DataType quantType, ValueRange<float> inARange)
    {
        // Options.CalibrationDataset
        var qm = quantType == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;
        return GetDeqParam(Utilities.QuantUtility.GetQuantParam(inARange, 8, qm));
    }

    public static QuantizeParam GetInBQuantParam(DataType quantType, ValueRange<float> inBRange)
    {
        return GetQuantParamFromDeqParam(GetInBDeqQuantParam(quantType, inBRange));
    }

    public static DeQuantizeParam GetInBDeqQuantParam(DataType quantType, ValueRange<float> inBRange)
    {
        var qm = quantType == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;
        return GetDeqParam(Utilities.QuantUtility.GetQuantParam(inBRange, 8, qm));
    }

    public static QuantizeParam GetOfQuantParam(DataType quantType, ValueRange<float> outRange)
    {
        return GetQuantParamFromDeqParam(GetOfDeqQuantParam(quantType, outRange));
    }

    public static DeQuantizeParam GetOfDeqQuantParam(DataType quantType, ValueRange<float> outRange)
    {
        var qm = quantType == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;
        var bits = quantType == DataTypes.Int16 ? 12 : 8;
        return GetDeqParam(Utilities.QuantUtility.GetQuantParam(outRange, bits, qm));
    }
}
