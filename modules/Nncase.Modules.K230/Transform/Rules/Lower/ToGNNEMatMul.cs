﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using static Nncase.IR.F.Math;
using static Nncase.PatternMatch.F.K230;
using static Nncase.PatternMatch.Utility;
using static Nncase.Quantization.K230.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class ToGNNEMatMul : GNNEDIFQuantRule
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } = IsRangeOfMarker(
        IsFakeMatMul(
            target_name: "fakeMatmul",
            call_name: "call",
            _ => true,
            IsRangeOfMarker("inputAMarker", IsWildcard("inputA"), Is<PERSON>onst("inputARange")),
            IsRangeOfMarker("inputBMarker", Is<PERSON><PERSON>dcard("inputB"), IsConst("inputBRange"))),
        IsTensor<PERSON>onst("outputRange"));

    private Expr? GetReplace(FakeMatMul fakeMatmul, Call call, Expr inputA, Tensor<float> inputARange, Expr inputAMarker, Expr inputB, Tensor<float> inputBRange, Expr inputBMarker, Tensor<float> outputRange)
    {
        var act = new ActParam2(fakeMatmul.ActParam2);

        var mixQuantInfoInputA = ((Marker)inputAMarker).MixQuantInfo;
        var mixQuantInfoInputB = ((Marker)inputBMarker).MixQuantInfo;
        var quantTypeA = mixQuantInfoInputA?.MarkerQuantType == null ? QuantType : mixQuantInfoInputA!.MarkerQuantType;
        if (quantTypeA == DataTypes.Int16)
        {
            quantTypeA = DataTypes.UInt8;
        }

        var quantTypeB = mixQuantInfoInputB?.MarkerQuantType == null ? QuantType : mixQuantInfoInputB!.MarkerQuantType;

        var matmulQuantInA = LoadQuantInA(inputARange, quantTypeA, inputAMarker);
        var matmulQuantInB = inputB is TensorConst ? LoadInB((TensorConst)inputB, inputBRange.AsValueRange(), quantTypeB) : LoadQuantInB(inputB, inputBRange, quantTypeB, inputBMarker);

        var inABias = LoadInABias(inputA, inputARange.AsValueRange(), quantTypeA);
        var matmulAct = LoadAct(act, inputARange.AsValueRange(), inputBRange.AsValueRange(), quantTypeA, quantTypeB);

        // var in_b_bias = load_in_b_bias(input_b, input_b_range.AsValueRange(), QuantType);
        var gnneMatmul = IR.K230.F.Tensors.GNNEMatMul(act, DataTypes.Float16, matmulQuantInA, matmulQuantInB, matmulAct, inABias, 0, 0, 0, Enumerable.Repeat(GetInBDeqQuantParam(quantTypeB, inputBRange.AsValueRange()).ZeroPoint, (int)call.CheckedShape[1].FixedValue).ToArray());
        var st = IR.K230.F.Tensors.GNNEStore(DataTypes.Float32, gnneMatmul);
        st.CheckedType = new TensorType(DataTypes.Float32, call.CheckedShape);
        return RangeOfMarker(st, outputRange);
    }

    private Call LoadQuantInA(Tensor<float> inARange, DataType quanType, Expr prevInputMarker)
    {
        var iqP = GetInADeqQuantParam(quanType, inARange.AsValueRange());
        var q = IR.F.Math.Quantize(prevInputMarker, Tensor.FromScalar<QuantParam>(new(iqP.ZeroPoint, iqP.Scale)), quanType);
        return IR.K230.F.Tensors.GNNELoad((PrimType)quanType, q);
    }

    private Call LoadQuantInB(Expr prevInput, Tensor<float> inBRange, DataType quanType, Expr prevInputMarker)
    {
        var iqP = GetInBDeqQuantParam(quanType, inBRange.AsValueRange());
        int addDims = 4 - prevInput.CheckedShape.Rank;
        if (addDims > 0)
        {
            IR.F.Tensors.Unsqueeze(prevInput, Enumerable.Range(0, addDims).ToArray());
        }

        var inB = IR.F.Math.Quantize(prevInputMarker, Tensor.FromScalar<QuantParam>(new(iqP.ZeroPoint, iqP.Scale)), quanType);
        return IR.K230.F.Tensors.GNNELoad((PrimType)quanType, inB);
    }

    private Call LoadInABias(Expr prevInput, ValueRange<float> inARange, DataType quanType)
    {
        var iqP = GetInADeqQuantParam(quanType, inARange);

        byte[] inputABias = new byte[prevInput.CheckedShape[1].FixedValue * prevInput.CheckedShape[2].FixedValue];
        for (int i = 0; i < inputABias.Length; i++)
        {
            inputABias[i] = checked((byte)iqP.ZeroPoint);
        }

        return IR.K230.F.Tensors.GNNELoadW(DataTypes.UInt8, inputABias);
    }

    // private Call Load_in_b_bias(Expr prevInput, ValueRange<float> inBRange, DataType quanType)
    // {
    //     var iqP = GetInBDeqQuantParam(quanType, inBRange);
    //
    //     byte[] inputBBias = new byte[prevInput.CheckedShape[1].FixedValue * prevInput.CheckedShape[2].FixedValue];
    //     for (int i = 0; i < inputBBias.Length; i++)
    //     {
    //         inputBBias[i] = checked((byte)iqP.ZeroPoint);
    //     }
    //
    //     return IR.K230.F.Tensors.GNNELoadW((PrimType)quanType, inputBBias);
    // }
    private Call LoadInB(TensorConst inputb, ValueRange<float> inBRange, DataType quanType)
    {
        if (quanType == DataTypes.Int8)
        {
            sbyte[] qInB = GetQuantInBI8(inputb, inBRange, quanType);
            var cInB = new Tensor<sbyte>(qInB, inputb.CheckedShape.ToValueArray());
            return IR.K230.F.Tensors.GNNELoad((PrimType)quanType, cInB);
        }
        else if (quanType == DataTypes.UInt8)
        {
            byte[] qInB = GetQuantInBu8(inputb, inBRange, quanType);
            var cInB = new Tensor<byte>(qInB, inputb.CheckedShape.ToValueArray());
            return IR.K230.F.Tensors.GNNELoad((PrimType)quanType, cInB);
        }
        else if (quanType == DataTypes.Int16)
        {
            short[] qInB = GetQuantInBi16(inputb, inBRange, quanType);
            var cInB = new Tensor<short>(qInB, inputb.CheckedShape.ToValueArray());
            return IR.K230.F.Tensors.GNNELoad((PrimType)quanType, cInB);
        }
        else
        {
            throw new NotSupportedException("Incalid in_b_quant_type");
        }
    }

    private byte[] GetQuantInBu8(TensorConst inputB, ValueRange<float> inBRange, DataType quanType)
    {
        var inputBShape = inputB.CheckedShape;
        float[] inputBData = inputB.Value.ToArray<float>();
        byte[] qInB = new byte[inputBData.Length];
        var inBQuantParam = GetInBQuantParam(quanType, inBRange);
        for (int batch = 0; batch < inputBShape[1].FixedValue; batch++)
        {
            for (int row = 0; row < inputBShape[2].FixedValue; row++)
            {
                for (int col = 0; col < inputBShape[3].FixedValue; col++)
                {
                    int curInBIndex = (int)((batch * inputBShape[2].FixedValue * inputBShape[3].FixedValue) +
                                      (row * inputBShape[3].FixedValue) + col);

                    qInB[curInBIndex] = Quantize<byte>(inputBData[curInBIndex], inBQuantParam);
                }
            }
        }

        return qInB;
    }

    private sbyte[] GetQuantInBI8(TensorConst inputB, ValueRange<float> inBRange, DataType quanType)
    {
        var inputBShape = inputB.CheckedShape;
        float[] inputBData = inputB.Value.ToArray<float>();
        sbyte[] qInB = new sbyte[inputBData.Length];
        var inBQuantParam = GetInBQuantParam(quanType, inBRange);
        for (int batch = 0; batch < inputBShape[1].FixedValue; batch++)
        {
            for (int row = 0; row < inputBShape[2].FixedValue; row++)
            {
                for (int col = 0; col < inputBShape[3].FixedValue; col++)
                {
                    int curInBIndex = (int)((batch * inputBShape[2].FixedValue * inputBShape[3].FixedValue) +
                                      (row * inputBShape[3].FixedValue) + col);

                    qInB[curInBIndex] = (sbyte)Math.Clamp(
                        Convert.ToInt32(Quantize<sbyte>(inputBData[curInBIndex], inBQuantParam)),
                        -127,
                        127);
                }
            }
        }

        return qInB;
    }

    private short[] GetQuantInBi16(TensorConst inputB, ValueRange<float> inBRange, DataType quanType)
    {
        var inputBShape = inputB.CheckedShape;
        float[] inputBData = inputB.Value.ToArray<float>();
        short[] qInB = new short[inputBData.Length];
        var inBQuantParam = GetInBQuantParam(quanType, inBRange);
        for (int batch = 0; batch < inputBShape[1].FixedValue; batch++)
        {
            for (int row = 0; row < inputBShape[2].FixedValue; row++)
            {
                for (int col = 0; col < inputBShape[3].FixedValue; col++)
                {
                    var curInBIndex = (batch * inputBShape[2].FixedValue * inputBShape[3].FixedValue) +
                                      (row * inputBShape[3].FixedValue) + col;

                    qInB[curInBIndex] = Quantize<short>(inputBData[curInBIndex], inBQuantParam);
                }
            }
        }

        return qInB;
    }

    private T Quantize<T>(float data, QuantizeParam qp)
    {
        var result = Math.Clamp(
            (int)Math.Round((data * qp.Scale) + qp.ZeroPoint),
            Convert.ToInt32(typeof(T).GetField("MinValue")!.GetValue(null)),
            Convert.ToInt32(typeof(T).GetField("MaxValue")!.GetValue(null)));
        var t = (T)Convert.ChangeType(result, typeof(T));
        return t;
    }

    private Call LoadAct(ActParam2 actParam, ValueRange<float> inARange, ValueRange<float> inBRange, DataType quanTypeA, DataType quanTypeB)
    {
        var inADeqParams = GetInADeqQuantParam(quanTypeA, inARange);
        var inBDeqParams = GetInBDeqQuantParam(quanTypeB, inBRange);
        actParam.FusedChannelScale(Enumerable.Repeat(inADeqParams.Scale, actParam.Channels).ToArray());
        actParam.FusedChannelScale(Enumerable.Repeat(inBDeqParams.Scale, actParam.Channels).ToArray());
        return IR.K230.F.Tensors.GNNELoadW(DataTypes.Float16, actParam.ToAct0Data());
    }

    // public QuantizeParam GetInAQuantParam(DataType quantType, ValueRange<float> inARange)
    // {
    //     return GetQuantParamFromDeqParam(GetInADeqQuantParam(quantType, inARange));
    // }
    private DeQuantizeParam GetInADeqQuantParam(DataType quantType, ValueRange<float> inARange)
    {
        var qm = quantType == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;
        var bits = quantType == DataTypes.Int16 ? 12 : 8;
        return GetDeqParam(Utilities.QuantUtility.GetQuantParam(inARange, bits, qm));
    }

    private QuantizeParam GetInBQuantParam(DataType quantType, ValueRange<float> inBRange)
    {
        return GetQuantParamFromDeqParam(GetInBDeqQuantParam(quantType, inBRange));
    }

    private DeQuantizeParam GetInBDeqQuantParam(DataType quantType, ValueRange<float> inBRange)
    {
        var qm = quantType == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;
        var bits = quantType == DataTypes.Int16 ? 12 : 8;
        return GetDeqParam(Utilities.QuantUtility.GetQuantParam(inBRange, bits, qm));
    }

    // public QuantizeParam GetOfQuantParam(DataType quantType, ValueRange<float> outRange)
    // {
    //     return GetQuantParamFromDeqParam(GetOfDeqQuantParam(quantType, outRange));
    // }
    // private DeQuantizeParam GetOfDeqQuantParam(DataType quantType, ValueRange<float> outRange)
    // {
    //     var qm = quantType == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;
    //     int bits = quantType == DataTypes.Int16 ? 12 : 8;
    //     return GetDeqParam(Utilities.QuantUtility.GetQuantParam(outRange, bits, qm));
    // }
}
