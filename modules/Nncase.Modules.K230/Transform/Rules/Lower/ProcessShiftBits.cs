// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using Nncase.PatternMatch.F;
using static Nncase.IR.K230.ShiftBitsHelper;
using static Nncase.Passes.Rules.GetReplaceHelper;
using static Nncase.PatternMatch.F.K230;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

#if false
[RuleGenerator]
public sealed partial class ProcessShiftBitsGNNEMatmul : IRewriteRule
{
    private static readonly OrPattern _actPattern = IsAlt(IsTensorConst("act"), Tensors.IsExpand(IsTensorConst("act"), IsWildcard("expand_shape")));

    public IPattern Pattern { get; } = IsDynamicGNNEMatMul(
        target_name: "dynamicGnneMatMul",
        call_name: null,
        _ => true,
        text: IsTensorConst("text"),
        inputa: <PERSON><PERSON><PERSON><PERSON><PERSON>("inputa"),
        inputb: <PERSON><PERSON>il<PERSON><PERSON>("inputb"),
        inputabias: IsWild<PERSON>("inputabias"),
        inputbbias: IsWildcard("inputbbias"),
        act: _actPattern,
        shiftbits: IsTensorConst(),
        dynamicchannel: IsTensorConst("dynamicchannel"));

    private Expr GetReplace(DynamicGNNEMatMul dynamicGnneMatMul, Expr text, Expr inputa, Expr inputb, Expr inputabias, Expr inputbbias, Tensor<Half> act, Expr dynamicchannel, IMatchResult result, RunPassContext options)
    {
        var ap = new ActivationParameter<Half>(act);
        sbyte shiftBits = ap.ShiftBits;
        ap.FusedShiftBits(shiftBits);
        Expr newAct = Const.FromTensor(ap.ToAct0Data<Half>());
        try
        {
            var expandShape = (Shape)result["expand_shape"];
            newAct = IR.F.Tensors.Expand(newAct, expandShape);
        }
        catch (KeyNotFoundException)
        {
        }

        var newCall = new Call(dynamicGnneMatMul, text, inputa, inputb, inputabias, inputbbias, newAct, (Dimension)shiftBits, dynamicchannel);
        options.MatchOptions.SuppressPattern(newCall, Pattern); // only invoke once
        return newCall;
    }
}
#endif

[RuleGenerator]
public sealed partial class ProcessConvShiftBits : IRewriteRule
{
    public IPattern Pattern { get; } = IsGNNEConv2D(
        "conv",
        "convCall",
        _ => true,
        IsWildcard("input"),
        IsWildcard("weights"),
        IsWildcard("weightsBias"),
        IsWildcard("weightsBiasQint8"),
        null,
        IsWildcard("actQint8"),
        IsWildcard("deqBias"),
        IsTensorConst("shiftBits"),
        IsWildcard("shiftBitsQint8"),
        IsWildcard("qint8Qp"),
        IsWildcard("padding"),
        IsWildcard("stride"),
        IsWildcard("dilation"),
        IsWildcard("groups"),
        IsWildcard("is16Quant"),
        IsWildcard("padValue"),
        IsWildcard("weightsQint8"));

    // Expr? GetReplace(GNNEConv2D conv, Call convCall, Expr input, Expr weights, Expr weightsBias, Expr weightsBiasQint8,
    //     Expr actQint8, Expr deqBias, Expr shiftBits, Expr shiftBitsQint8, Expr qint8Qp, Expr padding,
    //     Expr stride, Expr dilation, Expr groups, Expr fusedClamp, Expr is16Quant, Expr padValue, Expr weightsQint8,
    //     RunPassContext options)
    private Expr? GetReplace(GNNEConv2D conv, IMatchResult result, RunPassContext options)
    {
        var act = new ActParam2(conv.ActParam);
        int newShiftBits = act.FusedShiftBits();

        var newAct = LoadAct0(act);
        var newCall = new Call(
            conv,
            (Expr)result["input"],
            (Expr)result["weights"],
            (Expr)result["weightsBias"],
            (Expr)result["weightsBiasQint8"],
            newAct,
            (Expr)result["actQint8"],
            (Expr)result["deqBias"],
            (Dimension)newShiftBits,
            (Expr)result["shiftBitsQint8"],
            (Expr)result["qint8Qp"],
            (Expr)result["padding"],
            (Expr)result["stride"],
            (Expr)result["dilation"],
            (Expr)result["groups"],
            (Expr)result["is16Quant"],
            (Expr)result["padValue"],
            (Expr)result["weightsQint8"]);

        return SuppressPattern(options, newCall, Pattern);
    }
}

[RuleGenerator]
public sealed partial class ProcessConvTransposeShiftBits : IRewriteRule
{
    public IPattern Pattern { get; } = IsGNNEConv2DTranspose(
        "conv",
        "convCall",
        _ => true,
        IsWildcard("input"),
        IsWildcard("weights"),
        IsWildcard("weightsBias"),
        IsWildcard("weightsBiasQint8"),
        null,
        IsWildcard("actQint8"),
        IsWildcard("deqBias"),
        IsTensorConst("shiftBits"),
        IsWildcard("shiftBitsQint8"),
        IsWildcard("qint8Qp"),
        IsWildcard("padding"),
        IsWildcard("stride"),
        IsWildcard("dilation"),
        IsWildcard("groups"),
        IsWildcard("is16Quant"),
        IsWildcard("padValue"),
        IsWildcard("weightsQint8"),
        IsWildcard("outputPadding"),
        IsWildcard("outputShape"));

    private Expr? GetReplace(GNNEConv2DTranspose conv, IMatchResult result, RunPassContext options)
    {
        var act = new ActParam2(conv.ActParam);
        int newShiftBits = act.FusedShiftBits();

        var newAct = LoadAct0(act);
        var newCall = new Call(conv, (Expr)result["input"], (Expr)result["weights"], (Expr)result["weightsBias"], (Expr)result["weightsBiasQint8"], newAct, (Expr)result["actQint8"], (Expr)result["deqBias"], (Dimension)newShiftBits, (Expr)result["shiftBitsQint8"], (Expr)result["qint8Qp"], (Expr)result["padding"], (Expr)result["stride"], (Expr)result["dilation"], (Expr)result["groups"], (Expr)result["is16Quant"], (Expr)result["padValue"], (Expr)result["weightsQint8"], (Expr)result["outputPadding"], (Expr)result["outputShape"]);

        return SuppressPattern(options, newCall, Pattern);
    }
}

[RuleGenerator]
public sealed partial class ProcessPdp0DWShiftBits : IRewriteRule
{
    public IPattern Pattern { get; } = IsGNNEPdp0DW(
        "dw",
        "dwCall",
        _ => true,
        IsWildcard("input"),
        IsWildcard("weights"),
        IsWildcard("weightsBias"),
        IsWildcard("weightsBiasQint8"),
        null,
        IsWildcard("actQint8"),
        IsWildcard("deqBias"),
        IsTensorConst("shiftBits"),
        IsWildcard("shiftBitsQint8"),
        IsWildcard("qint8Qp"),
        IsWildcard("padding"),
        IsWildcard("stride"),
        IsWildcard("dilation"),
        IsWildcard("groups"),
        IsWildcard("is16Quant"),
        IsWildcard("padValue"),
        IsWildcard("weightsQint8"));

    private Expr? GetReplace(GNNEPdp0DW dw, IMatchResult result, RunPassContext options)
    {
        var act = new ActParam2(dw.ActParam);
        int newShiftBits = act.FusedShiftBits();

        var newAct = LoadAct0(act);
        var newCall = new Call(dw, (Expr)result["input"], (Expr)result["weights"], (Expr)result["weightsBias"], (Expr)result["weightsBiasQint8"], newAct, (Expr)result["actQint8"], (Expr)result["deqBias"], (Dimension)newShiftBits, (Expr)result["shiftBitsQint8"], (Expr)result["qint8Qp"], (Expr)result["padding"], (Expr)result["stride"], (Expr)result["dilation"], (Expr)result["groups"], (Expr)result["is16Quant"], (Expr)result["padValue"], (Expr)result["weightsQint8"]);

        return SuppressPattern(options, newCall, Pattern);
    }
}

[RuleGenerator]
public sealed partial class ProcessPdp0ReduceShiftBits : IRewriteRule
{
    public IPattern Pattern { get; } = IsGNNEPdp0Reduce(
        "pdp",
        "pdpCall",
        _ => true,
        IsWildcard("input"),
        IsWildcard("filter"),
        IsWildcard("stride"),
        IsWildcard("padding"),
        IsWildcard("dequantParam"),
        IsWildcard("value"),
        IsWildcard("shiftBits"),
        IsTensorConst("countIncludePad"));

    private Expr? GetReplace(GNNEPdp0Reduce pdp, IMatchResult result, RunPassContext options)
    {
        var act = new ActParam2(pdp.ActParam);
        int newShiftBits = act.FusedShiftBits();

        var newAct = LoadAct0(act);
        var newCall = new Call(pdp, (Expr)result["input"], (Expr)result["filter"], (Expr)result["stride"], (Expr)result["padding"], (Expr)result["dequantParam"], (Expr)result["value"], (Dimension)newShiftBits, (Expr)result["countIncludePad"], newAct);

        return SuppressPattern(options, newCall, Pattern);
    }
}

[RuleGenerator]
public sealed partial class ProcessAct1ShiftBits : IRewriteRule
{
    public IPattern Pattern { get; } = IsGNNEActivation(
        "gnneAct",
        "actCall",
        _ => true,
        IsWildcard("inputA"),
        IsWildcard("inputB"),
        null,
        IsWildcard("inAShiftbits"),
        IsWildcard("inBShiftbits"),
        IsWildcard("outShiftbits"),
        IsTensorConst("deqAParams"),
        IsTensorConst("deqBParams"),
        IsWildcard("outChannels"),
        IsWildcard("is16Segment"));

    private Expr? GetReplace(GNNEActivation gnneAct, IMatchResult result, RunPassContext options)
    {
        ActParamBase act = gnneAct.ActParam is ActParam2 param ? new ActParam2(param) : new ActParam16((ActParam16)gnneAct.ActParam);
        sbyte newShiftBits = GetAct1ShiftBits(act);
        act.FusedShiftBits(newShiftBits);

        var newAct = LoadAct1(act);
        var newCall = new Call(gnneAct, (Expr)result["inputA"], (Expr)result["inputB"], newAct, (Expr)result["inAShiftbits"], (Expr)result["inBShiftbits"], (Dimension)newShiftBits, (Expr)result["deqAParams"], (Expr)result["deqBParams"], (Expr)result["outChannels"], (Expr)result["is16Segment"]);
        return SuppressPattern(options, newCall, Pattern);
    }
}

[RuleGenerator]
public sealed partial class ProcessMatmulShiftBits : IRewriteRule
{
    public IPattern Pattern { get; } = IsGNNEMatMul(
        "mm",
        "mmCall",
        _ => true,
        IsWildcard("inputA"),
        IsWildcard("inputB"),
        null,
        IsWildcard("inputABias"),
        IsWildcard("inputAShiftbits"),
        IsTensorConst("inputBShiftbits"),
        IsWildcard("shiftbits"),
        IsWildcard("deqBBias"));

    private Expr? GetReplace(GNNEMatMul mm, IMatchResult result, RunPassContext options)
    {
        var act = new ActParam2(mm.ActParam);
        int newShiftBits = act.FusedShiftBits();

        var newAct = LoadAct0(act);
        var newCall = new Call(mm, (Expr)result["inputA"], (Expr)result["inputB"], newAct, (Expr)result["inputABias"], (Expr)result["inputAShiftbits"], (Expr)result["inputBShiftbits"], (Dimension)newShiftBits, (Expr)result["deqBBias"]);

        return SuppressPattern(options, newCall, Pattern);
    }
}

[RuleGenerator]
public sealed partial class ProcessLstmShiftBits : IRewriteRule
{
    public IPattern Pattern { get; } = IsGNNELSTM(
        "lstm",
        "lstmCall",
        _ => true,
        IsWildcard("input"),
        IsWildcard("wXc"),
        IsWildcard("actXc"),
        IsWildcard("wRc"),
        IsWildcard("actRc0"),
        IsWildcard("actRc1"),
        IsWildcard("initH"),
        IsWildcard("initC"),
        IsWildcard("segFittingParamFt"),
        IsWildcard("segFittingParamGt"),
        IsWildcard("wXcQarg"),
        IsWildcard("wRcQarg"),
        IsWildcard("actBin"),
        IsWildcard("actBinQ"),
        IsWildcard("ifDeqBias"),
        IsWildcard("xcShiftBits"),
        IsWildcard("hDeqBias0"),
        IsWildcard("hDeqBias1"),
        IsWildcard("cShiftBits"),
        IsWildcard("rcShiftBits0"),
        IsWildcard("rcShiftBits1"),
        IsWildcard("outHShiftBits"),
        IsWildcard("outCShiftBits"),
        IsWildcard("hasStatic"),
        IsWildcard("outputSize"));

    private Expr? GetReplace(GNNELSTM lstm, IMatchResult result, RunPassContext options)
    {
        var actParamXc = new ActParam2(lstm.ActivationParamXc);
        int shiftBitsXc = actParamXc.FusedShiftBits();
        var lactXc = LoadAct0(actParamXc);

        var actParamRc0 = new ActParam2(lstm.ActivationParamRc0);
        int shiftBitsRc0 = actParamRc0.FusedShiftBits();
        var lactRc0 = LoadAct0(actParamRc0);

        var actParamRc1 = new ActParam2(lstm.ActivationParamRc1);
        int shiftBitsRc1 = actParamRc1.FusedShiftBits();
        var lactRc1 = LoadAct0(actParamRc1);

        var newCall = new Call(lstm, (Expr)result["input"], (Expr)result["wXc"], lactXc, (Expr)result["wRc"], lactRc0, lactRc1, (Expr)result["initH"], (Expr)result["initC"], (Expr)result["segFittingParamFt"], (Expr)result["segFittingParamGt"], (Expr)result["wXcQarg"], (Expr)result["wRcQarg"], (Expr)result["actBin"], (Expr)result["actBinQ"], (Expr)result["ifDeqBias"], (Dimension)shiftBitsXc, (Expr)result["hDeqBias0"], (Expr)result["hDeqBias1"], (Expr)result["cShiftBits"], (Dimension)shiftBitsRc0, (Dimension)shiftBitsRc1, (Expr)result["outHShiftBits"], (Expr)result["outCShiftBits"], (Expr)result["hasStatic"], (Expr)result["outputSize"]);

        return SuppressPattern(options, newCall, Pattern);
    }
}
