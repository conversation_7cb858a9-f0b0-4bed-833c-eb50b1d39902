﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.K230.GNNETypePatternUtility;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Tensors;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class ToGNNETranspose : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } =
        IsTranspose(
            "transpose",
            "call",
            _ => true,
            IsWildcard("input") with { TypePattern = ValidDType() & HasShape(sp => sp.IsFixed && sp.Rank <= 4, "rank must <= 4") },
            IsTensorConst("perm"));

    private Expr? GetReplace(Expr input, int[] perm, Call call)
    {
        var newInputShape = new[] { 1, 1, 1, 1 };
        Array.Copy(input.CheckedShape.ToValueArray(), 0, newInputShape, newInputShape.Length - input.CheckedShape.Rank, input.CheckedShape.Rank);
        var inReshape = IR.F.Tensors.Reshape(input, newInputShape);

        var permValue = ToGNNEPerm(perm);
        var midType = input.CheckedDataType == DataTypes.Float32 ? DataTypes.Float16 : input.CheckedDataType;
        var newTranspose = GNNEStore(
            call.CheckedDataType,
            GNNETranspose(
                GNNELoad((PrimType)midType, perm.Length == 4 ? input : inReshape),
                ToMFUPerm(permValue)));

        return perm.Length == 4 ? newTranspose : IR.F.Tensors.Reshape(newTranspose, call.CheckedShape);
    }

    private int[] ToGNNEPerm(int[] oldPerm)
    {
        return Enumerable.Range(0, 4 - oldPerm.Length)
            .Concat(oldPerm.Select(x => x + (4 - oldPerm.Length)))
            .ToArray();
    }
}
