// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using DryIoc.ImTools;
using Nncase.Diagnostics;
using Nncase.Evaluator;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.NN;
using Nncase.IR.Tensors;
using Nncase.PatternMatch;
using Nncase.TIR;
using Nncase.Utilities;
using static Nncase.IR.F.Tensors;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Tensors;
using static Nncase.PatternMatch.Utility;
using Conv2D = Nncase.IR.NN.Conv2D;
using Conv2DTranspose = Nncase.IR.NN.Conv2DTranspose;
using GNNEActivation = Nncase.IR.K230.GNNEActivation;
using Math = System.Math;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class SplitLargeConv2D : RewriteRule<Pattern>
{
    public override Pattern Pattern => IsCallSpecific(
            "call",
            IsOp<Conv2D>(),
            (Conv2D.Input, IsWildcard("input") with { TypePattern = HasFixedShape() }),
            (Conv2D.Weights, IsWildcard("weights")),
            (Conv2D.Stride, IsTensorConst("strides")),
            (Conv2D.Padding, IsTensorConst("paddings")),
            (Conv2D.Dilation, IsTensorConst("dilation")))
        with
    {
        TypePattern = HasFixedShape(),
    };

    public Expr? GetReplace(Call call, Expr input, Expr weights, Tensor<int> paddings, int[] strides, int[] dilation)
    {
        var callShape = call.CheckedShape.ToValueArray().Select(x => (int)x).ToArray();
        var oh = callShape[2];
        var ow = callShape[3];

        return SplitLarge.Split(
            call,
            input,
            callShape,
            (pair) => pair.Dim > 65535,
            _ => 1,
            axis => axis,
            (param) =>
            {
                var (count, size, currentSize, index, axis) = param;
                var hOrW = axis - 2;
                var padding = new Padding(paddings[hOrW, 0], paddings[hOrW, 1]);
                var owSeg = TileUtilities.GetSegmentStartEndLength(0, currentSize, (int)input.CheckedShape[axis].FixedValue);
                var outLen = axis == 3 ? ow : oh;
                var iw = TileUtilities.GetInputRowSegment(
                    owSeg[index].Start,
                    Math.Min(currentSize, owSeg[index].Length),
                    (int)outLen,
                    (int)weights.CheckedShape[axis].FixedValue,
                    strides[hOrW],
                    dilation[hOrW],
                    padding);
                return (iw.Start, iw.End, iw.Padding);
            },
            (slice, paddings, axis) =>
            {
                var newPads = (BaseExpr)(Expr)Tensor.From(
                    Enumerable.Range(0, 2).SelectMany(i =>
                        i == axis - 2 ? new[] { paddings.Before, paddings.After } : new[] { 0, 0 }).ToArray(),
                    new long[] { 2, 2 });
                return ReplaceUtility.ReplaceCallParams(call.Target, call.Arguments.ToArray(), (call[Conv2D.Input], slice), (call[Conv2D.Padding], newPads));
            });
    }
}

[RuleGenerator]
public sealed partial class SplitLargeGlobalReduceWindow2D : RewriteRule<Pattern>
{
    public override Pattern Pattern => IsCallSpecific(
            "call",
            IsOp<ReduceWindow2D>("reduceWindow2D"),
            (ReduceWindow2D.Input, IsWildcard("input") with { TypePattern = HasFixedShape() & HasRank(4) }),
            (ReduceWindow2D.InitValue, IsTensorConst("initValue")),
            (ReduceWindow2D.Filter, IsTensorConst("filter")),
            (ReduceWindow2D.Stride, IsTensorConst("strides")),
            (ReduceWindow2D.Padding, IsTensorConst("padding")),
            (ReduceWindow2D.Dilation, IsTensorConst("dilation")),
            (ReduceWindow2D.CeilMode, IsTensorConst("ceilMode")),
            (ReduceWindow2D.CountIncludePad, IsTensorConst("countIncludePad")))
        with
    {
        TypePattern = HasFixedShape() & HasRank(4),
    };

    public Expr? GetReplace(Call call, ReduceWindow2D reduceWindow2D, Expr initValue, Expr input, int[] filter, int[] padding)
    {
        var inShape = input.CheckedShape.ToValueArray();
        var callShape = call.CheckedShape.ToValueArray();
        var oh = callShape[2];
        var ow = callShape[3];
        if (inShape[^2] == filter[0] && inShape[^1] == filter[1] && padding.All(p => p == 0) && oh == 1 && ow == 1 && filter[0] >= 320 && filter[1] >= 320)
        {
            var segsH = inShape[^2] < 640 ? 4 : 10;
            var segSize = inShape[^2] / segsH;
            var ifSlices = new Expr[segsH];
            var poolSlices = new Expr[segsH];
            for (var i = 0; i < segsH; i++)
            {
                ifSlices[i] = Slice(input, new[] { i * segSize }, new[] { (i * segSize) + segSize }, new[] { 2 }, new[] { 1 });
                poolSlices[i] = IR.F.NN.ReduceWindow2D(reduceWindow2D.ReduceOp, ifSlices[i], initValue, new[] { segSize, filter[1] }, new[] { 1, 1 }, new int[,] { { 0, 0 }, { 0, 0 } }, new[] { 1, 1 }, false, false);
            }

            var concat = Concat(new IR.Tuple(poolSlices), 2);
            var ret = IR.F.NN.ReduceWindow2D(reduceWindow2D.ReduceOp, concat, initValue, new[] { segsH, 1 }, new[] { 1, 1 }, new int[,] { { 0, 0 }, { 0, 0 } }, new[] { 1, 1 }, false, false);
            return ret;
        }

        return null;
    }
}

[RuleGenerator]
public sealed partial class SplitLargeConv2DTranspose : RewriteRule<Pattern>
{
    public override Pattern Pattern => IsCallSpecific(
            "call",
            IsOp<Conv2DTranspose>(),
            (Conv2DTranspose.Input, IsWildcard("input") with { TypePattern = HasFixedShape() }),
            (Conv2DTranspose.Weights, IsWildcard("weights")),
            (Conv2DTranspose.Stride, IsTensorConst("strides")),
            (Conv2DTranspose.Padding, IsTensorConst("paddings")),
            (Conv2DTranspose.Dilation, IsTensorConst("dilation")),
            (Conv2DTranspose.OutputPadding, IsTensorConst("outputPaddings")))
        with
    {
        TypePattern = HasFixedShape(),
    };

    public Expr? GetReplace(Call call, Expr input, Expr weights, Tensor<int> paddings, Tensor<int> outputPaddings, int[] strides, int[] dilation)
    {
        var outShape = call.CheckedShape.ToValueArray().Select(x => (int)x).ToArray();
        var inShape = input.CheckedShape.ToValueArray();
        var wShape = weights.CheckedShape.ToValueArray();
        var paddingsValue = paddings.ToArray();
        return SplitLarge.Split(
            call,
            input,
            outShape,
            (pair) =>
            {
                var hOrW = pair.Axis - 2;
                if (hOrW < 0)
                {
                    return false;
                }

                return outShape[pair.Axis] > 65535 && outputPaddings.Sum() == 0;
            },
            axis => strides[axis - 2],
            axis => axis,
            (param) =>
            {
                var padding = Padding.Zero();
                var (count, size, currentSize, index, axis) = param;
                var hOrW = axis - 2;

                // The last slice needs to include the padding in the lower right corner, and the padding in the upper left corner must be included in each slice
                if (index != count - 1)
                {
                    padding = new Padding(paddingsValue[(hOrW * 2) + 0], 0);
                }
                else
                {
                    padding = new Padding(paddingsValue[(hOrW * 2) + 0], paddingsValue[(hOrW * 2) + 1]);
                }

                size /= strides[hOrW];
                currentSize /= strides[hOrW];
                return (size * index, Math.Min((size * index) + currentSize, (int)outShape[axis]), padding);
            },
            (slice, newPaddings1, axis) =>
            {
                var newOutShape = outShape.ToArray();
                var sliceLen = slice.CheckedShape[axis].FixedValue;
                newOutShape[axis] = (int)sliceLen * strides[axis - 2];
                var paddingsValue = paddings.ToArray();
                var hOrW = axis - 2;
                paddingsValue[(2 * hOrW) + 0] = newPaddings1.Before;
                paddingsValue[(2 * hOrW) + 1] = newPaddings1.After;

                return ReplaceUtility.ReplaceCallParams(
                    call.Target,
                    call.Arguments.ToArray(),
                    (call[Conv2DTranspose.Input], slice),
                    (call[Conv2DTranspose.Padding], (Expr)Tensor.From(paddingsValue, paddings.Shape)),
                    (call[Conv2DTranspose.OutputShape], (RankedShape)newOutShape));
            });
    }
}

[RuleGenerator]
public sealed partial class SplitLargeActivation : RewriteRule<Pattern>
{
    public override Pattern Pattern => IsCallSpecific(
        "call",
        IsOp<FakeActivation>(),
        (FakeActivation.InputA, IsWildcard("input") with { TypePattern = HasFixedShape() }));

    public Expr? GetReplace(Call call, Expr input)
    {
        var rhs = call.Arguments[FakeActivation.InputB.Index];
        var t = (FakeActivation)call.Target;
        var splitRhs = false;
        if (rhs != None.Default)
        {
            // maybe broadcast
            if (!rhs.CheckedShape.SequenceEqual(input.CheckedShape))
            {
                // broadcast has large, not support
                if (rhs.CheckedShape.Any(x => x.FixedValue > 65535))
                {
                    return null;
                }
            }
            else
            {
                splitRhs = true;
            }
        }

        return SplitLarge.Split(
            call,
            input,
            input.CheckedShape.ToValueArray().Select(x => (int)x).ToArray(),
            (pair) => pair.Dim > 65535,
            _ => 1,
            axis => axis,
            (param) =>
            {
                return (param.Index * param.ChunkSize, (param.Index * param.ChunkSize) + param.CurrentSize, Padding.Zero());
            },
            (lhsSlice, padding, axis) =>
            {
                lhsSlice.InferenceType();
                var newActOp = new FakeActivation(t.Type, t.ActParam, lhsSlice.CheckedShape.ToValueArray().Select(x => (int)x).ToArray());
                if (splitRhs)
                {
                    Expr rhsSlice;
                    if (lhsSlice is Marker m)
                    {
                        rhsSlice = m.With(target: ReplaceUtility.ReplaceCallFirstParam(((Call)m.Target).Target, ((Call)m.Target).Arguments.ToArray(), rhs));
                    }
                    else
                    {
                        rhsSlice = ReplaceUtility.ReplaceCallFirstParam(((Call)lhsSlice).Target, ((Call)lhsSlice).Arguments.ToArray(), rhs);
                    }

                    return ReplaceUtility.ReplaceCallParams(
                        newActOp,
                        call.Arguments.ToArray(),
                        (FakeActivation.InputA, lhsSlice),
                        (FakeActivation.InputB, rhsSlice));
                }

                return ReplaceUtility.ReplaceCallFirstParam(newActOp, call.Arguments.ToArray(), lhsSlice);
            });
    }
}

[RuleGenerator]
public sealed partial class SplitLargeTranspose : RewriteRule<Pattern>
{
    public override Pattern Pattern => IsTranspose(
        "tr",
        "call",
        IsWildcard("input") with { TypePattern = HasFixedShape() },
        IsTensorConst("perm"));

    public Expr? GetReplace(Call call, Expr input, Tensor<int> perm)
    {
        var inShape = input.CheckedShape.ToValueArray().Select(x => (int)x).ToArray();
        return SplitLarge.Split(
            call,
            input,
            inShape,
            _ => true,
            _ => 1,
            axis => perm.ToArray().IndexOf(axis),
            param =>
            {
                var (count, chunkSize, currentSize, index, axis) = param;
                return (index * chunkSize, (index * chunkSize) + currentSize, Padding.Zero());
            },
            (slice, padding, i) =>
            {
                slice.InferenceType();
                return ReplaceUtility.ReplaceCallFirstParam(call.Target, call.Arguments.ToArray(), slice);
            });
    }
}

internal static class SplitLarge
{
    /// <summary>
    /// Used for SplitLargeCall.
    /// </summary>
    /// <param name="call">Call which be split.</param>
    /// <param name="input">Input.</param>
    /// <param name="splitShape">Which shape should be split.</param>
    /// <param name="shouldSplit">Func for check call should be split.</param>
    /// <param name="getRound">Get chunk align size.</param>
    /// <param name="getConcatAxis">Get output concat Axis.</param>
    /// <param name="computeNewInputSize">Func for compute new input size.</param>
    /// <param name="callMaker">Func for make a new Call.</param>
    /// <returns>new call.</returns>
    public static Expr? Split(Call call, Expr input, int[] splitShape, Func<(int Dim, int Axis), bool> shouldSplit, Func<int, int> getRound, Func<int, int> getConcatAxis, Func<(int Count, int ChunkSize, int CurrentSize, int Index, int Axis), (int NewBegin, int NewEnd, Padding Pads)> computeNewInputSize, Func<BaseExpr, Padding, int, BaseExpr> callMaker)
    {
        (int Dim, int Axis)[] largeDims = splitShape.Select((dim, axis) => (dim, axis)).Where(pair => pair.dim > 65535).ToArray();
        if (largeDims.Length != 1)
        {
            return null;
        }

        if (!shouldSplit(largeDims[0]))
        {
            return null;
        }

        var axis = largeDims[0].Axis;

        if (call.CheckedShape.Rank != 4)
        {
            return null;
        }

        var size = splitShape[axis];
        var (n, tmpW) = ComputeChunkSize(size);
        var round = getRound(axis);
        var w = tmpW / round * round;
        var last = size - (w * (n - 1));
        var list = Enumerable.Range(0, n - 1).Select(s => w).Append(last).Select((sliceW, i) =>
        {
            var inShape = input.CheckedShape.ToValueArray();
            var (newW, newWEnd, padding) = computeNewInputSize((n, w, sliceW, i, axis));
            var begin = Enumerable.Range(0, 4).Select(i => i == axis ? newW : 0).ToArray();
            var end = Enumerable.Range(0, 4).Select(i => i == axis ? newWEnd : inShape[i]).ToArray();
            Expr slice = Slice(input, begin, end, 4);
            if (input is Marker m)
            {
                slice = m.With(target: slice);
            }

            var newCall = callMaker(slice, padding, axis);
            if (call.Users.Any() && call.Users.First() is Marker outMarker)
            {
                newCall = outMarker.With(target: newCall);
            }

            return newCall;
        }).ToArray();

        var res = Concat(new IR.Tuple(list), getConcatAxis(axis));
        if (!res.CheckedShape.ToValueArray().SequenceEqual(call.CheckedShape.ToValueArray()))
        {
            throw new InvalidOperationException("SplitLargeCall result shape is not same as origin call shape");
        }

        return res;
    }

    private static (int N, int ChunkSize) ComputeChunkSize(int dim)
    {
        int w = 1;
        int n = 2;
        while (true)
        {
            if (Math.Ceiling((float)dim / n) < 65535f)
            {
                w = (int)Math.Ceiling((float)dim / n);
                break;
            }

            n++;
        }

        return (n, w);
    }
}
