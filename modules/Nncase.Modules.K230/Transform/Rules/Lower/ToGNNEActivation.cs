﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.PatternMatch;
using Nncase.Utilities;
using static Nncase.IR.F.Math;
using static Nncase.IR.F.Tensors;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.Passes.Rules.GetReplaceHelper;
using static Nncase.PatternMatch.F.K230;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class ToGNNEActivation : GNNEActLowerRule
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } =
        IsRangeOfMarker(
            name: "outputMarker",
            IsFakeActivation(
                "fakeActivation",
                "call",
                _ => true,
                IsRangeOfMarker("inputAMarker", IsWildcard("inputA"), IsTensorConst("inputARange")),
                <PERSON><PERSON>lt(IsRangeOfMarker("inputBMarker", <PERSON><PERSON><PERSON><PERSON><PERSON>("inputB"), IsTensorConst("inputBRange")), IsNone()),
                Is<PERSON>ensorConst("act"),
                IsTensorConst("outChannels"),
                IsWildcard(),
                IsWildcard(),
                IsWildcard(),
                IsWildcard("is16Segment")),
            IsTensorConst("outputRange"));

    public Expr? GetReplace(FakeActivation fakeActivation, Call call, Expr inputA, Tensor<float> inputARange, Tensor<int> outChannels, Expr is16Segment, Expr inputAMarker, Expr outputMarker, IMatchResult result, Expr outputRange)
    {
        var lAct = GNNELoadW(DataTypes.Float16, fakeActivation.ActParam.ToAct1Data());
        int[] newInAShape = { 1, 1, 1, 1 };
        Array.Copy(inputA.CheckedShape.ToValueArray(), 0, newInAShape, newInAShape.Length - inputA.CheckedShape.Rank, inputA.CheckedShape.Rank);
        int[] newInBShape = { 1, 1, 1, 1 };
        int[] newOutputShape = { 1, 1, 1, 1 };
        Array.Copy(call.CheckedShape.ToValueArray(), 0, newOutputShape, newOutputShape.Length - call.CheckedShape.Rank, call.CheckedShape.Rank);
        bool outputNeedReshape = newOutputShape.Length != call.CheckedShape.Rank;

        try
        {
            var inputB = (Expr)result["inputB"];
            var inputBMarker = (Expr)result["inputBMarker"];
            var inputBRange = ((TensorConst)result["inputBRange"]).Value;
            var inputABc = Reshape(inputAMarker, newInAShape);
            var inputBBc = Reshape(inputBMarker, newInBShape);
            Array.Copy(inputB.CheckedShape.ToValueArray(), 0, newInBShape, newInBShape.Length - inputB.CheckedShape.Rank, inputB.CheckedShape.Rank);

            if (inputB is TensorConst)
            {
                var mixQuantInfoInputA = ((Marker)inputAMarker).MixQuantInfo;
                var quantTypeA = mixQuantInfoInputA?.MarkerQuantType == null ? QuantType : mixQuantInfoInputA!.MarkerQuantType;
#if SKIP_QUANT
                quantTypeA = DataTypes.Float16;
#endif
                QuantMode qmA = quantTypeA == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;
                int bitsA = quantTypeA == DataTypes.Int16 ? 12 : 8;

                var inputARange1 = new ValueRange<float>(inputARange.ToArray<float>()[0], inputARange.ToArray<float>()[1]);
                var iqPA = (quantTypeA == DataTypes.Float16 || quantTypeA == DataTypes.Float32 || quantTypeA == DataTypes.Int16) ? new QuantParam(0, 1.0f) : QuantUtility.GetQuantParam(inputARange1, bitsA, qmA);
                var qA = (quantTypeA == DataTypes.Float16 || quantTypeA == DataTypes.Float32 || quantTypeA == DataTypes.Int16) ? inputABc : Quantize(inputABc, new QuantParam(iqPA.ZeroPoint, iqPA.Scale), quantTypeA);
                var loadA = (quantTypeA == DataTypes.Float16 || quantTypeA == DataTypes.Float32 || quantTypeA == DataTypes.Int16) ? GNNELoad(DataTypes.Float16, inputABc) : GNNELoad((PrimType)quantTypeA, qA);
                if (InputA is TensorConst)
                {
                    loadA = GNNELoad(DataTypes.Float16, Const.FromTensor(Tensor.From(((TensorConst)InputA).Value.ToArray<float>(), newInAShape.Select(x => (long)x).ToArray())));
                }

                var loadB = GNNELoad(DataTypes.Float16, inputBBc);
                if (InputB is TensorConst)
                {
                    loadB = GNNELoad(DataTypes.Float16, Const.FromTensor(Tensor.From(((TensorConst)InputB).Value.ToArray<float>(), newInBShape.Select(x => (long)x).ToArray())));
                }

                if (!ToScalar<bool>(is16Segment))
                {
                    var st = GNNEStore(DataTypes.Float32, GNNEActivation(loadA, loadB, lAct, 0, 0, 0, Tensor.FromArray(new[] { new DeQuantizeParam(iqPA.ZeroPoint, iqPA.Scale) }), Tensor.FromArray(new[] { new DeQuantizeParam(0, 1) }), outChannels, fakeActivation.Type, is16Segment, DataTypes.Float16, fakeActivation.ActParam, newOutputShape));
                    st.CheckedType = new TensorType(DataTypes.Float32, newOutputShape);
                    var outputReshape = Reshape(st, call.CheckedShape);
                    outputReshape.CheckedType = new TensorType(DataTypes.Float32, call.CheckedShape);
                    return RangeOfMarker(outputNeedReshape ? outputReshape : st, outputRange).With(
                        adaQuantInfo: ((Marker)outputMarker).AdaQuantInfo,
                        mixQuantInfo: ((Marker)outputMarker).MixQuantInfo);
                }
                else
                {
                    var st = GNNEStore(DataTypes.Float32, GNNEActivation(loadA, loadB, lAct, 0, 0, 0, Tensor.FromArray(new[] { new DeQuantizeParam(iqPA.ZeroPoint, iqPA.Scale) }), Tensor.FromArray(new[] { new DeQuantizeParam(0, 1) }), outChannels, fakeActivation.Type, is16Segment, DataTypes.Float16, fakeActivation.ActParam, newOutputShape));
                    st.CheckedType = new TensorType(DataTypes.Float32, newOutputShape);
                    var outputReshape = Reshape(st, call.CheckedShape);
                    outputReshape.CheckedType = new TensorType(DataTypes.Float32, call.CheckedShape);
                    return RangeOfMarker(outputNeedReshape ? outputReshape : st, outputRange).With(
                        adaQuantInfo: ((Marker)outputMarker).AdaQuantInfo,
                        mixQuantInfo: ((Marker)outputMarker).MixQuantInfo);
                }
            }
            else
            {
                var mixQuantInfoInputA = ((Marker)inputAMarker).MixQuantInfo;
                var quantTypeA = mixQuantInfoInputA?.MarkerQuantType == null ? QuantType : mixQuantInfoInputA!.MarkerQuantType;
#if SKIP_QUANT
                quantTypeA = DataTypes.Float16;
#endif
                var qmA = quantTypeA == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;
                int bitsA = quantTypeA == DataTypes.Int16 ? 12 : 8;

                var mixQuantInfoInputB = ((Marker)inputBMarker).MixQuantInfo;
                var quantTypeB = mixQuantInfoInputB?.MarkerQuantType == null ? QuantType : mixQuantInfoInputB!.MarkerQuantType;
#if SKIP_QUANT
                quantTypeB = DataTypes.Float16;
#endif
                var qmB = quantTypeB == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;
                int bitsB = quantTypeB == DataTypes.Int16 ? 12 : 8;

                var inputARange2 = new ValueRange<float>(inputARange.ToArray<float>()[0], inputARange.ToArray<float>()[1]);
                var iqPA = (quantTypeA == DataTypes.Float16 || quantTypeA == DataTypes.Float32 || quantTypeA == DataTypes.Int16) ? new QuantParam(0, 1.0f) : QuantUtility.GetQuantParam(inputARange2, bitsA, qmA);
                var qA = (quantTypeA == DataTypes.Float16 || quantTypeA == DataTypes.Float32 || quantTypeA == DataTypes.Int16) ? inputABc : Quantize(inputABc, new QuantParam(iqPA.ZeroPoint, iqPA.Scale), quantTypeA);
                var loadA = (quantTypeA == DataTypes.Float16 || quantTypeA == DataTypes.Float32 || quantTypeA == DataTypes.Int16) ? GNNELoad(DataTypes.Float16, inputABc) : GNNELoad((PrimType)quantTypeA, qA);
                var inputBRange1 = new ValueRange<float>(inputBRange.ToArray<float>()[0], inputBRange.ToArray<float>()[1]);
                var iqPB = (quantTypeB == DataTypes.Float16 || quantTypeB == DataTypes.Float32 || quantTypeB == DataTypes.Int16) ? new QuantParam(0, 1.0f) : QuantUtility.GetQuantParam(inputBRange1, bitsB, qmB);
                var qB = (quantTypeB == DataTypes.Float16 || quantTypeB == DataTypes.Float32 || quantTypeB == DataTypes.Int16) ? inputBBc : Quantize(inputBBc, new QuantParam(iqPB.ZeroPoint, iqPB.Scale), quantTypeB);
                var loadB = (quantTypeB == DataTypes.Float16 || quantTypeB == DataTypes.Float32 || quantTypeB == DataTypes.Int16) ? GNNELoad(DataTypes.Float16, inputBBc) : GNNELoad((PrimType)quantTypeB, qB);

                if (!ToScalar<bool>(is16Segment))
                {
                    var st = GNNEStore(DataTypes.Float32, GNNEActivation(loadA, loadB, lAct, 0, 0, 0, Tensor.FromArray(new[] { new DeQuantizeParam(iqPA.ZeroPoint, iqPA.Scale) }), Tensor.FromArray(new[] { new DeQuantizeParam(iqPB.ZeroPoint, iqPB.Scale) }), outChannels, fakeActivation.Type, is16Segment, DataTypes.Float16, fakeActivation.ActParam, newOutputShape));
                    st.CheckedType = new TensorType(DataTypes.Float32, newOutputShape);
                    var outputReshape = Reshape(st, call.CheckedShape);
                    outputReshape.CheckedType = new TensorType(DataTypes.Float32, call.CheckedShape);
                    return RangeOfMarker(outputNeedReshape ? outputReshape : st, outputRange).With(
                        adaQuantInfo: ((Marker)outputMarker).AdaQuantInfo,
                        mixQuantInfo: ((Marker)outputMarker).MixQuantInfo);
                }
                else
                {
                    var st = GNNEStore(DataTypes.Float32, GNNEActivation(loadA, loadB, lAct, 0, 0, 0, Tensor.FromArray(new[] { new DeQuantizeParam(iqPA.ZeroPoint, iqPA.Scale) }), Tensor.FromArray(new[] { new DeQuantizeParam(iqPB.ZeroPoint, iqPB.Scale) }), outChannels, fakeActivation.Type, is16Segment, DataTypes.Float16, fakeActivation.ActParam, newOutputShape));
                    st.CheckedType = new TensorType(DataTypes.Float32, newOutputShape);
                    var outputReshape = Reshape(st, call.CheckedShape);
                    outputReshape.CheckedType = new TensorType(DataTypes.Float32, call.CheckedShape);
                    return RangeOfMarker(outputNeedReshape ? outputReshape : st, outputRange).With(
                        adaQuantInfo: ((Marker)outputMarker).AdaQuantInfo,
                        mixQuantInfo: ((Marker)outputMarker).MixQuantInfo);
                }
            }
        }
        catch (KeyNotFoundException)
        {
            var mixQuantInfoInputA = ((Marker)inputAMarker).MixQuantInfo;
            var quantTypeA = mixQuantInfoInputA?.MarkerQuantType == null ? QuantType : mixQuantInfoInputA!.MarkerQuantType;
#if SKIP_QUANT
            quantTypeA = DataTypes.Float16;
#endif
            var qmA = quantTypeA == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedSymmetricMode;
            int bitsA = quantTypeA == DataTypes.Int16 ? 12 : 8;

            var inputABc = RangeOfMarker(Reshape(inputAMarker, newInAShape), inputARange);
            var inputARange3 = new ValueRange<float>(inputARange.ToArray<float>()[0], inputARange.ToArray<float>()[1]);
            var iqPA = (quantTypeA == DataTypes.Float16 || quantTypeA == DataTypes.Float32 || quantTypeA == DataTypes.Int16) ? new QuantParam(0, 1.0f) : QuantUtility.GetQuantParam(inputARange3, bitsA, qmA);
            var qA = (quantTypeA == DataTypes.Float16 || quantTypeA == DataTypes.Float32 || quantTypeA == DataTypes.Int16) ? (Expr)inputABc : Quantize(inputABc, new QuantParam(iqPA.ZeroPoint, iqPA.Scale), quantTypeA);
            var loadA = (quantTypeA == DataTypes.Float16 || quantTypeA == DataTypes.Float32 || quantTypeA == DataTypes.Int16) ? GNNELoad(DataTypes.Float16, inputABc) : GNNELoad((PrimType)quantTypeA, qA);

            // for pdp0reduce + act pattern, it shouldn't insert quant
            // var fakeActInputA = call[IR.K230.FakeActivation.InputA];
            // if (fakeActInputA is Marker)
            // {
            //     if (((Marker)fakeActInputA).Target is Call && ((Call)(((Marker)fakeActInputA).Target)).Target is GNNEStore)
            //     {
            //         if (((Call)(((Marker)fakeActInputA).Target)).Parameters[0] is Call && ((Call)(((Call)(((Marker)fakeActInputA).Target)).Parameters[0])).Target is GNNEPdp0Reduce)
            //         {
            //             load_a = GNNELoad(DataTypes.Float16, input_a_bc);
            //             iq_p_a.ZeroPoint = 0;
            //             iq_p_a.Scale = 1;
            //         }
            //     }
            // }
            if (!ToScalar<bool>(is16Segment))
            {
                var st = GNNEStore(DataTypes.Float32, GNNEActivation(loadA, None.Default, lAct, 0, 0, 0, Tensor.FromArray(new[] { new DeQuantizeParam(iqPA.ZeroPoint, iqPA.Scale) }), Tensor.FromArray(new[] { new DeQuantizeParam(0, 1) }), outChannels, fakeActivation.Type, is16Segment, DataTypes.Float16, fakeActivation.ActParam, newOutputShape));
                st.CheckedType = new TensorType(DataTypes.Float32, newOutputShape);
                var outputReshape = Reshape(st, call.CheckedShape);
                outputReshape.CheckedType = new TensorType(DataTypes.Float32, call.CheckedShape);
                return RangeOfMarker(outputNeedReshape ? outputReshape : st, outputRange).With(
                    adaQuantInfo: ((Marker)outputMarker).AdaQuantInfo,
                    mixQuantInfo: ((Marker)outputMarker).MixQuantInfo);
            }
            else
            {
                var st = GNNEStore(DataTypes.Float32, GNNEActivation(loadA, None.Default, lAct, 0, 0, 0, Tensor.FromArray(new[] { new DeQuantizeParam(iqPA.ZeroPoint, iqPA.Scale) }), Tensor.FromArray(new[] { new DeQuantizeParam(0, 1) }), outChannels, fakeActivation.Type, is16Segment, DataTypes.Float16, fakeActivation.ActParam, newOutputShape));
                st.CheckedType = new TensorType(DataTypes.Float32, newOutputShape);
                var outputReshape = Reshape(st, call.CheckedShape);
                outputReshape.CheckedType = new TensorType(DataTypes.Float32, call.CheckedShape);
                return RangeOfMarker(outputNeedReshape ? outputReshape : st, outputRange).With(
                    adaQuantInfo: ((Marker)outputMarker).AdaQuantInfo,
                    mixQuantInfo: ((Marker)outputMarker).MixQuantInfo);
            }
        }
    }
}
