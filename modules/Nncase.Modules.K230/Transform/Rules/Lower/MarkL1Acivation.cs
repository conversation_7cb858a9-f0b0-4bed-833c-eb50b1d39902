﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Math;
using Nncase.Passes.Analysis;
using Nncase.PatternMatch;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.Passes.Rules.GetReplaceHelper;
using static Nncase.PatternMatch.F.K230;
using static Nncase.PatternMatch.Utility;
using static Nncase.Utilities.ReplaceUtility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class MarkActivationInputAL1Fuse : RewriteRule<Pattern>, IRewriteRule
{
    public override Pattern Pattern { get; } =
        IsCallWildcard(
            "actCall",
            IsOp<GNNEActivation>("act"),
            IsGNNELoad(
                "ld",
                "ldCall",
                _ => true,
                IsCallWildcard(
                    "quantCall",
                    IsOp<Quantize>(),
                    IsCallWildcard(
                        "stCall",
                        IsOp<GNNEStore>(),
                        <PERSON><PERSON><PERSON>Wildcard(
                            "convCall",
                            IsOp<GNNEConv2D>(),
                            IsWildcard("input"))))),
            IsWildcard("inputB"));

    private Expr? GetReplace(GNNEActivation act, Call stCall, Call ldCall, Call actCall, Call convCall, IReadOnlyList<BaseExpr> actCallParams, RunPassContext context, Call quantCall)
    {
        var inputShape = convCall[IR.K230.GNNEConv2D.Input].CheckedShape.ToValueArray();
        var outputShape = convCall.CheckedShape.ToValueArray();
        var weightsShape = convCall[IR.K230.GNNEConv2D.Weights].CheckedShape.ToValueArray();
        var paddings = ((TensorConst)convCall[IR.K230.GNNEConv2D.Padding]).Value.ToArray<int>();
        var strideH = ((TensorConst)convCall[IR.K230.GNNEConv2D.Stride]).Value.ToArray<int>()[0];
        var strideW = ((TensorConst)convCall[IR.K230.GNNEConv2D.Stride]).Value.ToArray<int>()[1];
        var dilationH = ((TensorConst)convCall[IR.K230.GNNEConv2D.Dilation]).Value.ToArray<int>()[0];
        var dilationW = ((TensorConst)convCall[IR.K230.GNNEConv2D.Dilation]).Value.ToArray<int>()[1];
        var groups = ((TensorConst)convCall[IR.K230.GNNEConv2D.Groups]).Value.ToScalar<int>();

        if (inputShape[0] > 1 &&
            outputShape[2] == 1 && outputShape[3] == 1
            && inputShape[2] == 1 && inputShape[3] == 1
            && weightsShape[2] == 1 && weightsShape[3] == 1
            && strideH == 1 && strideW == 1
            && dilationH == 1 && dilationW == 1
            && paddings.Sum() == 0
            && groups == 1)
        {
            return null;
        }

        if (actCall[IR.K230.GNNEActivation.InputB] != None.Default && actCall[IR.K230.GNNEActivation.InputA] == actCall[IR.K230.GNNEActivation.InputB])
        {
            return null;
        }

        var userAnalysis = context.GetAnalysis<IExprUserAnalysisResult>();
        if (userAnalysis[stCall].Count() > 1 || userAnalysis[quantCall].Count() > 1 || userAnalysis[ldCall].Count() > 1 || act.InputFromL1[1] || actCall.CheckedShape != convCall.CheckedShape)
        {
            return null;
        }
        else
        {
            var newldCall = GNNELoad(DataTypes.Float16, stCall);
            return ReplaceCallParams(act.With(inputFromL1: new[] { true, false }), actCallParams, (ldCall, newldCall));
        }
    }
}

[RuleGenerator]
public sealed partial class MarkActivationInputBL1Fuse : RewriteRule<Pattern>, IRewriteRule
{
    public override Pattern Pattern { get; } =
        IsCallWildcard(
            "actCall",
            IsOp<GNNEActivation>("act"),
            IsWildcard("inputA"),
            IsGNNELoad(
                "ld",
                "ldCall",
                _ => true,
                IsCallWildcard(
                    "quantCall",
                    IsOp<Quantize>(),
                    IsCallWildcard(
                        "stCall",
                        IsOp<GNNEStore>(),
                        IsCallWildcard(
                            "convCall",
                            IsOp<GNNEConv2D>(),
                            IsWildcard("input"))))));

    private Expr? GetReplace(GNNEActivation act, Call stCall, Call ldCall, Call actCall, Call convCall, IReadOnlyList<BaseExpr> actCallParams, RunPassContext context, Call quantCall)
    {
        var inputShape = convCall[IR.K230.GNNEConv2D.Input].CheckedShape.ToValueArray();
        var outputShape = convCall.CheckedShape.ToValueArray();
        var weightsShape = convCall[IR.K230.GNNEConv2D.Weights].CheckedShape.ToValueArray();
        var paddings = ((TensorConst)convCall[IR.K230.GNNEConv2D.Padding]).Value.ToArray<int>();
        var strideH = ((TensorConst)convCall[IR.K230.GNNEConv2D.Stride]).Value.ToArray<int>()[0];
        var strideW = ((TensorConst)convCall[IR.K230.GNNEConv2D.Stride]).Value.ToArray<int>()[1];
        var dilationH = ((TensorConst)convCall[IR.K230.GNNEConv2D.Dilation]).Value.ToArray<int>()[0];
        var dilationW = ((TensorConst)convCall[IR.K230.GNNEConv2D.Dilation]).Value.ToArray<int>()[1];
        var groups = ((TensorConst)convCall[IR.K230.GNNEConv2D.Groups]).Value.ToScalar<int>();

        if (inputShape[0] > 1 &&
            outputShape[2] == 1 && outputShape[3] == 1
            && inputShape[2] == 1 && inputShape[3] == 1
            && weightsShape[2] == 1 && weightsShape[3] == 1
            && strideH == 1 && strideW == 1
            && dilationH == 1 && dilationW == 1
            && paddings.Sum() == 0
            && groups == 1)
        {
            return null;
        }

        if (actCall[IR.K230.GNNEActivation.InputB] != None.Default && actCall[IR.K230.GNNEActivation.InputA] == actCall[IR.K230.GNNEActivation.InputB])
        {
            return null;
        }

        var userAnalysis = context.GetAnalysis<IExprUserAnalysisResult>();
        if (userAnalysis[stCall].Count() > 1 || userAnalysis[quantCall].Count() > 1 || userAnalysis[ldCall].Count() > 1 || act.InputFromL1[0] || actCall.CheckedShape != convCall.CheckedShape)
        {
            return null;
        }
        else
        {
            var newldCall = GNNELoad(DataTypes.Float16, stCall);
            return ReplaceCallParams(act.With(inputFromL1: new[] { false, true }), actCallParams, (ldCall, newldCall));
        }
    }
}
