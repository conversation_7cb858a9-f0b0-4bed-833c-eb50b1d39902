// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.Passes.Rules.Neutral;
using Nncase.PatternMatch;
using Nncase.Targets;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

/// <summary>
/// FusionPattern.
/// </summary>
public static class FusionPattern
{
    public static Pattern IsGNNEFusion<T>(
        string midName = "call",
        string beginName = "ld",
        string endName = "st",
        string fusionName = "fusion")
        where T : Op
        =>
            IsFusion<T, GNNELoad, GNNEStore>(fusionName, K230Target.Kind, endName, midName, beginName, "input");

    public static Pattern IsL1Fusion<T>()
        where T : Op
        => IsPairLayerFusion<GNNEConv2D, T, GNNELoad, GNNEStore>("k230", "call");

    public static Pattern IsGNNEFusion(Pattern pattern) => IsFusion("k230", pattern);

    public static Pattern IsGNNEConvFusion() => IsGNNEFusion<GNNEConv2D>();

    public static Pattern IsGNNEPdp0DwFusion() => IsGNNEFusion<GNNEPdp0DW>();

    public static Pattern IsGNNEPdp0ReduceFusion() => IsGNNEFusion<GNNEPdp0Reduce>();

    public static Pattern IsGNNETransposeFusion() => IsGNNEFusion<GNNETranspose>();

    public static Pattern IsGNNEPDP1Fusion() => IsGNNEFusion<GNNEPdp1>();

    public static Pattern IsGNNEActivationFusion() => IsGNNEFusion<GNNEActivation>();

    public static Pattern IsL1Pdp0DwFusion() => IsL1Fusion<GNNEPdp0DW>();

    public static Pattern IsL1Pdp0ReduceFusion() => IsL1Fusion<GNNEPdp0Reduce>();

    public static Pattern IsL1Act1Fusion() => IsL1Fusion<GNNEActivation>();

    public static Pattern IsGNNEConv2DTransposeFusion() => IsGNNEFusion<GNNEConv2DTranspose>();

    public static Pattern IsGNNEPadFusion() => IsGNNEFusion<GNNEPad>();

    public static Pattern IsGNNEMatMulFusion() => IsGNNEFusion<GNNEMatMul>();

    public static Pattern IsGNNELSTMFusion() => IsGNNEFusion<GNNELSTM>();

    public static Pattern IsGNNELoadStoreFusion() => IsGNNEFusion(new DataTransferFusion<GNNELoad, GNNEStore>().Pattern);

    public static Pattern IsAi2DResizeFusion() => IsGNNEFusion<Ai2dResize>();
}

public sealed class StoreLoadFusion : DataTransferFusion<GNNELoad, GNNEStore>
{
    public override string ModuleKind { get; } = "k230";
}

public class GNNESingleInputFusion<T> : SingleInputFusion<T, GNNELoad, GNNEStore>
    where T : Op
{
    public override string ModuleKind { get; } = "k230";
}

public class GNNEDoubleInputFusion<T> : DoubleInputFusion<T, GNNELoad, GNNEStore>
    where T : Op
{
    public override string ModuleKind { get; } = "k230";
}

public sealed class ConvFusion : GNNESingleInputFusion<GNNEConv2D>
{
    public override string Name { get; } = "TileConv2dCase";
}

public sealed class ConvTransposeFusion : GNNESingleInputFusion<GNNEConv2DTranspose>
{
    public override string Name { get; } = "TileConv2dTransposeCase";
}

public sealed class TransposeFusion : GNNESingleInputFusion<GNNETranspose>
{
    public override string Name { get; } = "TileTransposeCase";
}

public class GNNELoadStoreFusion : DataTransferFusion<GNNELoad, GNNEStore>
{
    public override string Name { get; } = "TileLoadStoreCase";

    public override string ModuleKind { get; } = "k230";
}

public sealed class ActSIFFusion : GNNESingleInputFusion<GNNEActivation>
{
    public override string Name { get; } = "TileAct1Case";

    public override Pattern Pattern { get; } = IsCallWildcard(
        "endCall",
        IsOp<GNNEStore>("endOp"),
        IsCallWildcard(
            "midCall",
            IsOp<GNNEActivation>("midOp"),
            IsCallWildcard("beginCall", IsOp<GNNELoad>("beginOp"), IsWildcard("input")),
            IsNone()));
}

public sealed class ActDIFFusion : GNNEDoubleInputFusion<GNNEActivation>
{
    public override string Name { get; } = "TileAct1Case";
}

public sealed class PadFusion : GNNESingleInputFusion<GNNEPad>
{
    public override string Name { get; } = "TilePadCase";
}

public sealed class ResizeFusion : GNNESingleInputFusion<Ai2dResize>
{
    public override string Name { get; } = "TileResizeCase";
}

public sealed class MatMulFusion : GNNEDoubleInputFusion<GNNEMatMul>
{
    public override string Name { get; } = "TileMatMulCase";
}

public sealed class Pdp1Fusion : GNNESingleInputFusion<GNNEPdp1>
{
    public override string Name { get; } = "TilePdp1Case";
}

public sealed class Pdp0ReduceFusion : GNNESingleInputFusion<GNNEPdp0Reduce>
{
    public override string Name { get; } = "TilePdp0ReduceCase";
}

public sealed class Pdp0DwFusion : GNNESingleInputFusion<GNNEPdp0DW>
{
    public override string Name { get; } = "TilePdp0DwCase";
}

internal sealed class GNNELSTMFusion : ComplexFusion<GNNELSTM, GNNELoad, GNNEStore>
{
    public override (ParameterInfo, CallPattern)[] InputPatterns { get; } =
        GenerateInputPatterns(GNNELSTM.Input, GNNELSTM.InitialH, GNNELSTM.InitialC);

    public override string Name { get; } = "TileLSTMCase";

    public override string ModuleKind { get; } = "k230";
}

// [RuleGenerator]
// public class GNNEFuseTwoFusion : FuseTwoFusion
// {
//     public GNNEFuseTwoFusion(string name)
//     {
//         name_ = name;
//     }

// private string name_;
//     public override string ModuleKind => "k230";

// public override string Name => name_;

// /// <inheritdoc/>
//     public override Pattern Pattern =>
//         IsCall(
//             "caller",
//             IsAlt(
//                 FusionPattern.IsGNNEFusion<GNNEPdp0DW>(beginName: null, endName: "st", fusionName: "callerFuse"),
//                 FusionPattern.IsGNNEFusion<GNNEPdp0Reduce>(beginName: null, endName: "st", fusionName: "callerFuse")),
//             ParamsWithArg(CalleePattern)
//         );

// /// <inheritdoc/>
//     public Pattern CalleePattern =>
//         IsCall(
//             "callee",
//             FusionPattern.IsGNNEFusion<GNNEConv2D>(callName: null, beginName: "ld", endName: null, fusionName: "calleeFuse"),
//             WildcardVArgsPattern);


// public override Expr EliminateRedundancy(Expr newBodyWithRedundancy, RunPassContext passOptions)
//     {
//         return CompilerServices.Rewrite(newBodyWithRedundancy, new[] { new FoldStoreLoad() }, new RunPassContext(null, 0, null));
//     }
// }
