// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Reactive;
using Nncase.IR;
using Nncase.IR.Buffers;
using Nncase.Schedule;
using Nncase.TIR;

namespace Nncase.Mutators.K230;

/// <summary>
/// 消除所有的 Buffer的 BaseMentOf/DDrOf/MmuOF.
/// </summary>
internal sealed class FoldBufferSlot : ExprRewriter
{
    private readonly Dictionary<TIR.Buffer, int> _bufferIndexMap = new(ReferenceEqualityComparer.Instance);
    private readonly Dictionary<PrimFunction, TIR.Buffer[]> _primfuncBufferMap = new(ReferenceEqualityComparer.Instance);

    protected override Expr VisitPrimFunction(PrimFunction expr, Unit context)
    {
        if (expr.SchedResult.IsScheduled)
        {
            int inutIndex = 0;
            int outputIndex = 0;
            if (_primfuncBufferMap.TryGetValue(expr, out var bufferMap))
            {
                foreach (var param in bufferMap)
                {
                    switch (param.MemSpan.Buffer.Location)
                    {
                        case MemoryLocation.Input:
                            _bufferIndexMap.Add(param, inutIndex++);
                            break;
                        case MemoryLocation.Output:
                            _bufferIndexMap.Add(param, inutIndex + outputIndex++);
                            break;
                        default:
                            throw new NotSupportedException();
                    }
                }
            }

            return (Expr)base.VisitPrimFunction(expr, context);
        }

        return expr;
    }

    protected override Expr VisitCall(Call expr, Unit context)
    {
        if (expr is { Target: PrimFunction })
        {
            _primfuncBufferMap.Add((PrimFunction)expr.Target, expr.Arguments.ToArray().Select(p => (TIR.Buffer)p).ToArray());
            return (Expr)base.VisitCall(expr, context);
        }

        return expr;
    }

    protected override Expr RewriteLeafCall(Call expr)
    {
        if (expr.Target is BufferIndexOf)
        {
            var buffer = (TIR.Buffer)expr.Arguments[0];
            var locate = buffer.MemSpan.Buffer.Location;
            return locate switch
            {
                MemoryLocation.Input => _bufferIndexMap[buffer],
                MemoryLocation.Output => _bufferIndexMap[buffer],
                MemoryLocation.Rdata => _bufferIndexMap.Count,
                MemoryLocation.Data => _bufferIndexMap.Count + 1,
                _ => throw new ArgumentOutOfRangeException($"You Can't Assgin The BaseMent For {locate}!"),
            };
        }
        else if (expr.Target is IR.Buffers.AddressOf)
        {
            if (expr.Arguments[0] is TIR.MemSpan buf)
            {
                return buf.Buffer.Start;
            }
        }

        return expr;
    }
}
