// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using NetFabric.Hyperlinq;
using Nncase.Evaluator;
using Nncase.IR;
using Nncase.Passes;

namespace Nncase.Mutators.K230;

/// <summary>
/// 折叠除了需要保留的op.
/// </summary>
internal sealed class FoldConstCall : ExprRewriter
{
    protected override BaseExpr RewriteLeafTuple(IR.Tuple expr)
    {
        if (IsAllConst(expr.Fields))
        {
            return new TupleConst(new TupleValue(expr.Fields.AsValueEnumerable().Select(x => Value.FromConst((Const)x)).ToArray()));
        }

        return expr;
    }

    /// <inheritdoc/>
    protected override Expr RewriteLeafCall(Call expr)
    {
        if (expr.Target is Op { CanFoldConstCall: true } or Function)
        {
            return IsAllConst(expr.Arguments) ? Const.FromValue(expr.Evaluate()) : expr;
        }

        return expr;
    }

    private bool IsAllConst(ReadOnlySpan<BaseExpr> parameters) =>
      parameters.AsValueEnumerable()
        .All(e => e is Const);
}
