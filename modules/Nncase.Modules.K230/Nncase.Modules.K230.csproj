<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <RootNamespace>Nncase</RootNamespace>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="../../../nncase/src/Nncase.IO/Nncase.IO.csproj" />
    <ProjectReference Include="..\..\..\nncase\src\Nncase.Passes\Nncase.Passes.csproj" />
    <ProjectReference Include="..\..\..\nncase\src\Nncase.Quantization\Nncase.Quantization.csproj" />
  </ItemGroup>

  <ItemGroup>
	  <ProjectReference Include="..\..\..\nncase\tools\Nncase.SourceGenerator\Nncase.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
    <ProjectReference Include="..\..\..\nncase\modules\Nncase.Modules.NTT\Nncase.Modules.NTT.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Functional/Load_1.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/Load_2.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/Load_3.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/UnitTestCSourceBasic.cs" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/UnitTestCSourceTensor.cs" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/UnitTestCodeGenKernel.cs" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/UnitTestScModelDebug.cs" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/alloca.h" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/alloca_test_1.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/alloca_test_2.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/alloca_test_3.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/and_1.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/asm_for_loop.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/asm_local.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/assert.h" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/assert_1.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/basic_test.h" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/ccr_clr_1.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/ccr_clr_2.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/cond_1.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/cond_2.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/do_while_1.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/fence.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/for_1.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/get_rsp.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/global.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/if_1.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/if_2.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/inst.h" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/linked_list.h" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/linked_list_test_1.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/math.h" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/math_test_1.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/matmul.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/matmul.h" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/matmul_1.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/matmul_3.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/dynamic_gnne_matmul.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/matmul_test.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/mmu_conf_1.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/more_params.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/not_1.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/or_1.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/or_2.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/ptr_sub.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/regex_test.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/ret_buffer.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/runtime_types.h" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/runtime_utils.h" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/stdtypes.h" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/struct_1.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/struct_2.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/struct_3.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/test.h" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/vector.h" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/vector_test.c" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Functional/while_1.c" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

</Project>
