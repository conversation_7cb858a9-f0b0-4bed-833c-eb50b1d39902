/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once
#include "../padding.h"
#include "../shape.h"

namespace nncase::ntt::shape_infer {
/**
 * @brief get windowed output size
 *
 * @param size input size
 * @param filter filter size
 * @param stride stride size
 * @param dilation dilation size
 * @param padding padding size
 * @return constexpr auto
 */
template <Dimension TSize, Dimension TFilter, Dimension TStride,
          Dimension TDilation, Padding TPadding>
constexpr auto windowed_output_size(const TSize &size, const TFilter &filter,
                                    const TStride &stride,
                                    const TDilation &dilation,
                                    const TPadding &padding) noexcept {
    auto effective_filter_size = (filter - dim_one) * dilation + dim_one;
    return (size + padding.sum() - effective_filter_size + stride) / stride;
}
} // namespace nncase::ntt::shape_infer
