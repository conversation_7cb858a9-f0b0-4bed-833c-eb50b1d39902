
/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once
#include "dimension.h"
#include <utility>

namespace nncase::ntt {
namespace detail {
template <dim_t... Index, class Callable>
static constexpr void loop_impl(Callable &&f,
                                std::integer_sequence<dim_t, Index...>) {
    (f(fixed_dim_v<Index>), ...);
}

} // namespace detail

template <dim_t N, class Callable> static constexpr void loop(Callable &&f) {
    detail::loop_impl(std::forward<Callable>(f),
                      std::make_integer_sequence<dim_t, N>{});
}

} // namespace nncase::ntt
